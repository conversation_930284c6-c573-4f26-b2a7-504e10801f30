{"id": "fqQcmSdoVqnPeGHj", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "OpenAI Personal Shopper with RAG and WooCommerce", "tags": [], "nodes": [{"id": "635901e5-4afd-4c81-a63e-52f1b863a025", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-200, 280], "webhookId": "bd3a878c-50b0-4d92-906f-e768a65c1485", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "d11cd97c-1539-462d-858c-8758cf1a8278", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [620, 580], "parameters": {"sessionKey": "={{ $('Edit Fields').item.json.sessionId }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "02bb43e4-f26e-4906-8049-c49d3fecd817", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [760, 580], "parameters": {}, "typeVersion": 1}, {"id": "ad6058dd-b429-4f3c-b68a-7e3d98beec83", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [20, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7015c229-f9fe-4c77-b2b9-4ac09a3a3cb1", "name": "sessionId", "type": "string", "value": "={{ $json.sessionId }}"}, {"id": "f8fc0044-6a1a-455b-a435-58931a8c4c8e", "name": "chatInput", "type": "string", "value": "={{ $json.chatInput }}"}]}}, "typeVersion": 3.4}, {"id": "43f7ee25-4529-4558-b5ea-c2a722b0bce5", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [500, 580], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "8b5ec20d-8735-4030-8113-717d578928eb", "name": "RAG", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [1000, 580], "parameters": {"name": "informazioni_negozio", "description": "Informazioni relative al negozio: orari di apertura, indir<PERSON>zo, contatti, informazioni generali"}, "typeVersion": 1}, {"id": "0fd0f1d6-41df-43d4-9418-0685afad409a", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [900, 780], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "scarperia", "cachedResultName": "scarperia"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "72084a2e-0e47-4723-a004-585ae8b67ae3", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [840, 940], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "30d398a3-2331-4a3d-898d-c184779c7ef3", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1200, 800], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "e10a8024-51ec-4553-a1fa-dbaa49a4d2c2", "name": "personal_shopper", "type": "n8n-nodes-base.wooCommerceTool", "position": [880, 580], "parameters": {"options": {"sku": "={{ $('Information Extractor').item.json.output.SKU }}", "search": "={{ $('Information Extractor').item.json.output.keyword }}", "maxPrice": "={{ $('Information Extractor').item.json.output.price_max }}", "minPrice": "={{ $('Information Extractor').item.json.output.price_min }}", "stockStatus": "instock"}, "operation": "getAll"}, "credentials": {"wooCommerceApi": {"id": "d4EQtVORkOCNQZAm", "name": "WooCommerce (Scarperia)"}}, "typeVersion": 1}, {"id": "f0c53b0d-7173-4ec9-8fb4-f8f45d9ceedc", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [220, 280], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemPromptTemplate": "You are an intelligent assistant for a shoe and accessories store (mainly bags). Your task is to analyze the input text coming from a chat and determine if the user is looking for a product. If the user is looking for a product, you need to extract the following information:\n1. The keyword (keyword) useful for the search.\n2. Any minimum or maximum prices specified.\n3. An SKU (product code) if mentioned.\n4. The name of the category to search in, if specified.\n\nInstructions:\n1. Identify the intent: Determine if the user is looking for a specific product.\n2. Extract the information:\n- If the user is looking for a product, identify:\n- Set the type \"search\" to true. Otherwise, set it to false\n- The keywords.\n- Any minimum or maximum prices (e.g. \"less than 50 euros\", \"between 30 and 60 euros\").\n- An SKU (e.g. \"ABC123 code\").\n- The category name (e.g. \"t-shirts\", \"jeans\", \"women\", \"men\").\n3. Output format: Return a JSON object with the given structure"}, "schemaType": "manual", "inputSchema": "{\n       \"search_intent\": true,\n       \"search_params\": [\n         { \"type\": \"search\", \"value\": \"ture or false\" },\n         { \"type\": \"keyword\", \"value\": \"valore_keyword\" },\n         { \"type\": \"min_price\", \"value\": \"valore_min_price\" },\n         { \"type\": \"max_price\", \"value\": \"valore_max_price\" },\n         { \"type\": \"sku\", \"value\": \"valore_sku\" },\n         { \"type\": \"category\", \"value\": \"valore_categoria\" }\n       ]\n     }"}, "typeVersion": 1}, {"id": "8386e554-e2f1-42c8-881f-a06e8099f718", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 460], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "4ff30e15-1bf5-4750-a68a-e72f86a4f32c", "name": "Google Drive2", "type": "n8n-nodes-base.googleDrive", "position": [320, -440], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "1lmnqpLFKS-gXmXT92C5VG0P1XlcoeFOb", "cachedResultUrl": "https://drive.google.com/drive/folders/1lmnqpLFKS-gXmXT92C5VG0P1XlcoeFOb", "cachedResultName": "Scarperia Salò - RAG"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "b4ca79b2-220b-4290-a33a-596250c8fd2d", "name": "Google Drive1", "type": "n8n-nodes-base.googleDrive", "position": [520, -440], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "18f5e068-ad4a-4be7-987c-83ed5791f012", "name": "Embeddings OpenAI3", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [680, -260], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "43693ee0-a2a3-44d3-86de-4156af84e251", "name": "Default Data Loader2", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [880, -220], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "f0d351e5-faee-49a4-a43c-985785c3d2c8", "name": "Token Splitter1", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [960, -60], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "ff77338e-4dac-4261-87a1-10a21108f543", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-200, -440], "parameters": {}, "typeVersion": 1}, {"id": "********-875a-4e8b-83fc-ca137e812050", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [40, -440], "parameters": {"url": "https://QDRANTURL/collections/NAME/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "5837e3ac-e3d1-45b6-bd67-8c3d03bf0a1e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, -500], "parameters": {"width": 259.77408********, "height": 234.*************, "content": "Replace the URL and Collection name with your own"}, "typeVersion": 1}, {"id": "79baf424-e647-4a80-a19e-c023ad3b1860", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [760, -440], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "list", "value": "scarperia", "cachedResultName": "scarperia"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "17015f50-a3a8-4e62-9816-7e71127c1ea1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-220, -640], "parameters": {"color": 3, "width": 1301.************, "height": 105.*************, "content": "## Step 1 \nCreate a collectiopn on your Qdrant instance. Then create a basic RAG system with documents uploaded to Google Drive and embedded in the Qdrant vector database"}, "typeVersion": 1}, {"id": "0ddbf6be-fa2d-4412-8e85-fe108cd6e84d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1020, 980.*************], "parameters": {"color": 3, "width": 1301.************, "height": 105.*************, "content": "## Step 1 \nCreate a basic RAG system with documents uploaded to Google Drive and embedded in the Qdrant vector database"}, "typeVersion": 1}, {"id": "3782a22d-b3a7-44ea-ad36-fa4382c9fcfd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 120], "parameters": {"color": 3, "width": 1301.************, "height": 105.*************, "content": "## Step 2 \nThe Information Extractor tries to understand if the request is related to products and if so, it extracts the useful information to filter the products available on WooCommerce by calling the \"personal_shopper\". If it is a general question, the RAG system is called"}, "typeVersion": 1}, {"id": "d4d1fb16-3f54-4c1a-ab4e-bcf86d897e9d", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [580, 280], "parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "=You are an intelligent assistant for a clothing store. Your task is to analyze the input text from a chat and determine if the user is looking for a product.\n\nBehavior:\n- If the user is looking for a product the \"search\" field of the following JSON is set to true and you must pass the following JSON as input to the \"personal_shopper\" tool to extract:\n\n```json\n{{ JSON.stringify($json.output) }}\n```\n\n- If the user asks questions related to the store such as address or opening hours, you must use the \"RAG\" tool"}, "promptType": "define"}, "typeVersion": 1.7}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "47513e11-8e9f-4b7c-b3de-e15cf00a1200", "connections": {"RAG": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Google Drive2", "type": "main", "index": 0}]]}, "Google Drive1": {"main": [[{"node": "Qdrant Vector Store1", "type": "main", "index": 0}]]}, "Google Drive2": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Token Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader2", "type": "ai_textSplitter", "index": 0}]]}, "personal_shopper": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI3": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "RAG", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Qdrant Vector Store": {"ai_vectorStore": [[{"node": "RAG", "type": "ai_vectorStore", "index": 0}]]}, "Default Data Loader2": {"ai_document": [[{"node": "Qdrant Vector Store1", "type": "ai_document", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}