{"id": "ZBH1ExE58wsoodkZ", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6"}, "name": "OpenSea NFT Agent Tool", "tags": [], "nodes": [{"id": "33cb5db2-a023-4a6c-a4ad-3f4b3c35ce42", "name": "NFT Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1340, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "9d1fa8e4-3acf-4ace-965c-ea5cdfcdc366", "name": "NFT Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1520, 240], "parameters": {}, "typeVersion": 1.3}, {"id": "d396bb90-00a6-41da-898d-7815d8d25fe3", "name": "OpenSea NFT Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2140, -20], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "# **🛠 OpenSea NFT Agent – System Message**  \n\n## **🔹 Role & Purpose**\nThe **OpenSea NFT Agent** is a powerful AI-driven assistant specialized in retrieving, analyzing, and processing NFT-related data from **OpenSea's API**. It provides insights into:\n- User profiles\n- NFT collections and contracts\n- Individual NFTs, their metadata, traits, and ownership\n- Payment tokens used in NFT transactions\n- Bulk NFT retrievals (by account, collection, or contract)  \n\nThis agent is designed to interact **directly** with OpenSea’s API and follows strict formatting rules to ensure valid requests and accurate responses.\n\n---\n\n# **⚡ Available Tools & How to Use Them**\nThe **NFT Agent** has access to **multiple OpenSea API endpoints**, each serving a specific purpose.\n\n## **1️⃣ Get Account**\n📍 **Endpoint**: `/api/v2/accounts/{address_or_username}`  \n🔹 **Description**: Retrieves an OpenSea user profile, including:\n  - Bio  \n  - Social media usernames  \n  - Profile image  \n\n🔹 **Required Parameter**:  \n  - `address_or_username` → Public blockchain address or OpenSea username  \n\n🔹 **Example Query**:  \n  _\"Retrieve OpenSea profile for user `******************************************`.\"_\n\n---\n\n## **2️⃣ Get Collection**\n📍 **Endpoint**: `/api/v2/collections/{collection_slug}`  \n🔹 **Description**: Fetches details about a specific NFT collection, including:\n  - Collection metadata  \n  - Fees  \n  - Traits  \n  - Social media links  \n\n🔹 **Required Parameter**:  \n  - `collection_slug` → Unique identifier for the collection (found in OpenSea URL)  \n\n🔹 **Example Query**:  \n  _\"Retrieve details for the 'Bored Ape Yacht Club' collection.\"_\n\n---\n\n## **3️⃣ Get Collections**\n📍 **Endpoint**: `/api/v2/collections`  \n🔹 **Description**: Fetches a **list of NFT collections** with optional filters.  \n\n🔹 **Optional Parameters**:  \n  - `chain` → Filter by blockchain (**must be a valid chain** from the list below).  \n  - `creator_username` → Return collections from a specific OpenSea username.  \n  - `include_hidden` → Boolean (`true`/`false`) to include hidden collections.  \n  - `limit` → Number of results (1-100, default: 100).  \n  - `next` → Cursor for pagination.  \n  - `order_by` → Sorting option (`created_date`, `market_cap`, `num_owners`, `one_day_change`, `seven_day_change`, `seven_day_volume`).  \n\n🔹 **Example Query**:  \n  _\"List the top 10 NFT collections on Ethereum sorted by market cap.\"_\n\n---\n\n## **4️⃣ Get Contract**\n📍 **Endpoint**: `/api/v2/chain/{chain}/contract/{address}`  \n🔹 **Description**: Retrieves **smart contract details** for an NFT collection.  \n\n🔹 **Required Parameters**:  \n  - `chain` → Blockchain network (**must be valid, see list below**).  \n  - `address` → Smart contract address of the NFT collection.  \n\n🔹 **Example Query**:  \n  _\"Retrieve contract details for `0xABCDEF...` on Ethereum.\"_\n\n---\n\n## **5️⃣ Get NFT**\n📍 **Endpoint**: `/api/v2/chain/{chain}/contract/{address}/nfts/{identifier}`  \n🔹 **Description**: Retrieves **metadata, traits, ownership, and rarity** of a specific NFT.  \n\n🔹 **Required Parameters**:  \n  - `chain` → Blockchain network (**must be valid, see list below**).  \n  - `address` → Smart contract address of the NFT collection.  \n  - `identifier` → The **NFT Token ID**.  \n\n🔹 **Example Query**:  \n  _\"Retrieve metadata for NFT #1234 from Ethereum contract `0xABCDEF...`.\"_\n\n---\n\n## **6️⃣ Get NFTs (by Account)**\n📍 **Endpoint**: `/api/v2/chain/{chain}/account/{address}/nfts`  \n🔹 **Description**: Retrieves **all NFTs owned** by a given account address.  \n\n🔹 **Required Parameters**:  \n  - `chain` → Blockchain network (**must be valid, see list below**).  \n  - `address` → Public blockchain address of the owner.  \n\n🔹 **Optional Parameters**:  \n  - `collection` → Filter by specific NFT collection.  \n  - `limit` → Number of NFTs to return (1-200, default: 50).  \n  - `next` → Cursor for pagination.  \n\n🔹 **Example Query**:  \n  _\"Retrieve all NFTs owned by `0x123...` on Ethereum.\"_\n\n---\n\n## **⚠️ Important Rules & Restrictions**\n### **🚨 1. Only Allowed Blockchain Inputs**\n✅ **Valid Blockchains for Queries**:\n- `amoy`\n- `ape_chain`\n- `ape_curtis`\n- `arbitrum`\n- `arbitrum_nova`\n- `arbitrum_sepolia`\n- `avalanche`\n- `avalanche_fuji`\n- `b3`\n- `b3_sepolia`\n- `baobab`\n- `base`\n- `base_sepolia`\n- `bera_chain`\n- `blast`\n- `blast_sepolia`\n- `ethereum`\n- `flow`\n- `flow_testnet`\n- `klaytn`\n- **`matic`** _(Use this instead of \"polygon\")_\n- `monad_testnet`\n- `mumbai`\n- `optimism`\n- `optimism_sepolia`\n- `sei_testnet`\n- `sepolia`\n- `shape`\n- `solana`\n- `soldev`\n- `soneium`\n- `soneium_minato`\n- `unichain`\n- `zora`\n- `zora_sepolia`\n\n🚨 **Critical Rule:**\n- ❌ `\"polygon\"` **is NOT a valid chain input** and **must be replaced with** `\"matic\"`.  \n- ❌ Using an unsupported blockchain **will cause an error**.  \n- ✅ Always verify blockchain names before executing a query.\n\n---\n\n## **📌 Example Queries**\n✅ _\"Find the OpenSea profile for `0x123...`.\"_  \n✅ _\"List all NFT collections created by `CryptoArtistX`.\"_  \n✅ _\"Retrieve contract details for `0xABC...` on Ethereum.\"_  \n✅ _\"Fetch metadata for NFT #5678 in 'Azuki' collection.\"_  \n✅ _\"List the top 5 NFT collections on Solana, ordered by market cap.\"_  \n\n---\n\n## **⚠️ Error Handling**\nIf an OpenSea API request fails, **check for errors**:\n- ✅ `200` → Success  \n- ❌ `400` → Bad Request (Invalid input format)  \n- ❌ `404` → Not Found (Incorrect `collection_slug`, `address`, or `identifier`)  \n- ❌ `500` → Server Error (OpenSea API issue)  \n\n---\n\n# **🚀 Conclusion**\nThe **OpenSea NFT Agent** is a **specialized AI-powered assistant** designed to retrieve and analyze NFT-related data on OpenSea. Whether you are a **collector, investor, or analyst**, this agent helps you stay **ahead of the market** by providing **real-time, structured, and in-depth insights**.  \n\n**🔥 Follow all rules to ensure successful API queries! 🔥**"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "c055762a-8fe7-4141-a639-df2372f30060", "name": "Workflow Input Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1420, -20], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "e2b0e848-ae9a-4fce-bf74-11cdebca512e", "name": "OpenSea Get Account", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1720, 240], "parameters": {"url": "https://api.opensea.io/api/v2/accounts/{address_or_username}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves an OpenSea account profile, including bio, social media usernames, and profile image.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "a343aba5-6a1c-4a19-8054-d80a92b30db0", "name": "OpenSea Get Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1920, 240], "parameters": {"url": "https://api.opensea.io/api/v2/collections/{collection_slug}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves details of a specific NFT collection from OpenSea, including fees, traits, and links.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "c21482cd-bc29-47ee-9913-b200abdfd5bd", "name": "OpenSea Get Collections", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2120, 240], "parameters": {"url": "https://api.opensea.io/api/v2/collections", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "chain", "valueProvider": "modelOptional"}, {"name": "creator_username", "valueProvider": "modelOptional"}, {"name": "include_hidden", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}, {"name": "order_by", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves a list of OpenSea collections with filtering options for blockchain, creator, visibility, sorting, and pagination.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "ab41e2bc-8c99-41ae-bfa5-5a94c8068f33", "name": "OpenSea Get Contract", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2340, 240], "parameters": {"url": "https://api.opensea.io/api/v2/chain/{chain}/contract/{address}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves details of a smart contract from OpenSea based on a given blockchain and contract address.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "f938ea94-59f7-4e9b-a75f-02b8e606e8ed", "name": "OpenSea Get NFT", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2580, 260], "parameters": {"url": "https://api.opensea.io/api/v2/chain/{chain}/contract/{address}/nfts/{identifier}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves metadata, traits, ownership information, and rarity for a single NFT on OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "c62f092e-353e-4038-a875-d3c86b9e2a3d", "name": "OpenSea Get NFTs by Account", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2820, 260], "parameters": {"url": "https://api.opensea.io/api/v2/chain/{chain}/account/{address}/nfts", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "collection", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves NFTs owned by a given account address on OpenSea, allowing filtering by collection, blockchain, and pagination options.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "4603a4f0-a46b-4ea9-b226-726f9763d823", "name": "OpenSea Get NFTs by Collection", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [3080, 260], "parameters": {"url": "https://api.opensea.io/api/v2/collection/{collection_slug}/nfts", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves multiple NFTs for a given collection on OpenSea, allowing pagination and limit options.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "f27c8a55-230e-41a2-ba92-f8facb323e8d", "name": "OpenSea Get NFTs by Contract", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [3320, 260], "parameters": {"url": "https://api.opensea.io/api/v2/chain/{chain}/contract/{address}/nfts", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "limit", "valueProvider": "modelOptional"}, {"name": "next", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves multiple NFTs for a given smart contract on OpenSea, allowing pagination and limit options.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "6043b056-339b-4bdd-898e-91e3b5915afd", "name": "OpenSea Get Payment Token", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [3540, 260], "parameters": {"url": "https://api.opensea.io/api/v2/chain/{chain}/payment_token/{address}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves details of a payment token from OpenSea based on a given blockchain and token address.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "5601cd2a-45f8-4cb6-acd8-02e44b033dcb", "name": "OpenSea Get Traits", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [3760, 260], "parameters": {"url": "https://api.opensea.io/api/v2/traits/{collection_slug}", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "This tool retrieves the traits in a given NFT collection from OpenSea.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}, {"name": "x-api-key", "value": "YOUR_OPENSEA_API_KEY", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "3v99GVMGF4tKP5nM", "name": "OpenSea"}}, "typeVersion": 1.1}, {"id": "86a6c757-bbda-41a9-90d1-1e8af808ae66", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [160, -1240], "parameters": {"color": 2, "width": 920, "height": 1880, "content": "# OpenSea NFT Agent Tool (n8n Workflow) Guide\n\n## 🚀 Workflow Overview\nThe **OpenSea NFT Agent Tool** is a specialized **AI-powered assistant** designed to interact with **OpenSea's API** to fetch, analyze, and process NFT-related data. It helps users access **NFT ownership, metadata, traits, collections, contracts, and payment tokens** efficiently.\n\n### 🎯 **Key Features**:\n- Retrieve **OpenSea user profiles** by wallet address or username.\n- Fetch **NFT collections**, metadata, contracts, and smart contract details.\n- Access **individual NFT details**, including rarity and ownership.\n- Track **NFTs owned by an account**, collections, and smart contracts.\n- Retrieve **payment tokens** and NFT traits.\n- Ensure **API request validity** and structured responses.\n\n---\n\n## 🔗 **Nodes & Functions**\n\n### **1️⃣ NFT Agent Brain**\n- **Type**: AI Language Model (GPT-4o Mini)\n- **Purpose**: Processes NFT-related API requests and interprets OpenSea data queries.\n\n### **2️⃣ NFT Agent Memory**\n- **Type**: AI Memory Buffer\n- **Purpose**: Stores session data to maintain context across multiple queries.\n\n### **3️⃣ OpenSea Get Account**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/accounts/{address_or_username}`\n- **Function**: Fetches an OpenSea **user profile**, including bio, social links, and profile image.\n\n### **4️⃣ OpenSea Get Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/collections/{collection_slug}`\n- **Function**: Retrieves **collection metadata**, fees, and traits.\n\n### **5️⃣ OpenSea Get Collections**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/collections`\n- **Function**: Fetches **a list of NFT collections**, filtered by chain, creator, visibility, or ranking.\n\n### **6️⃣ OpenSea Get Contract**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/chain/{chain}/contract/{address}`\n- **Function**: Retrieves **NFT collection smart contract details**.\n\n### **7️⃣ OpenSea Get NFT**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/chain/{chain}/contract/{address}/nfts/{identifier}`\n- **Function**: Fetches **metadata, traits, rarity, and ownership** of a single NFT.\n\n### **8️⃣ OpenSea Get NFTs by Account**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/chain/{chain}/account/{address}/nfts`\n- **Function**: Retrieves **all NFTs owned** by a wallet address.\n\n### **9️⃣ OpenSea Get NFTs by Collection**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/collection/{collection_slug}/nfts`\n- **Function**: Fetches **all NFTs in a collection**.\n\n### **🔟 OpenSea Get NFTs by Contract**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/chain/{chain}/contract/{address}/nfts`\n- **Function**: Retrieves **NFTs linked to a smart contract**.\n\n### **11️⃣ OpenSea Get Payment Token**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/chain/{chain}/payment_token/{address}`\n- **Function**: Fetches details of a **cryptocurrency/token** used for NFT transactions.\n\n### **12️⃣ OpenSea Get Traits**\n- **Type**: API Request\n- **Endpoint**: `/api/v2/traits/{collection_slug}`\n- **Function**: Retrieves **trait categories and attributes** for a given collection.\n\n---\n\n"}, "typeVersion": 1}, {"id": "c35f3855-5167-4230-8017-f60297d1dcec", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1540, -1240], "parameters": {"color": 5, "width": 1000, "height": 1060, "content": "## 📌 **How to Use the Workflow**\n\n### ✅ **Step 1: Input Data**\n- Provide the necessary parameters like `collection_slug`, `chain`, `address`, or `identifier`.\n\n### ✅ **Step 2: Execute API Calls**\n- The workflow retrieves and structures **NFT ownership, metadata, and analytics**.\n\n### ✅ **Step 3: Analyze & Output Results**\n- Results can be integrated into dashboards, alerts, or Telegram notifications.\n\n---\n\n## ⚠️ **Common API Queries & Examples**\n\n### **1️⃣ Get OpenSea User Profile**\n```plaintext\nGET https://api.opensea.io/api/v2/accounts/******************************************\n```\n\n### **2️⃣ Get Collection Details**\n```plaintext\nGET https://api.opensea.io/api/v2/collections/boredapeyachtclub\n```\n\n### **3️⃣ Get NFT Metadata**\n```plaintext\nGET https://api.opensea.io/api/v2/chain/ethereum/contract/0xABCDEF.../nfts/1234\n```\n\n### **4️⃣ Get All NFTs Owned by a Wallet**\n```plaintext\nGET https://api.opensea.io/api/v2/chain/ethereum/account/0x123.../nfts\n```\n\n### **5️⃣ Get Payment Token Details**\n```plaintext\nGET https://api.opensea.io/api/v2/chain/ethereum/payment_token/0xABC...DEF\n```\n\n---\n\n"}, "typeVersion": 1}, {"id": "c96fc837-37b2-4351-9175-7b52fba1e38b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2940, -1240], "parameters": {"color": 3, "width": 840, "height": 480, "content": "## ⚡ **Error Handling & Troubleshooting**\n| **Error Code** | **Description** |\n|--------------|----------------|\n| `200` | Success |\n| `400` | Bad Request (Invalid input) |\n| `404` | Not Found (Incorrect slug, address, or identifier) |\n| `500` | Server Error (OpenSea API issue) |\n\n### 🔹 **Fixing Common Errors**\n- Ensure correct **wallet address**, **NFT identifier**, or **collection slug**.\n- Always use `\"matic\"` instead of `\"polygon\"` for chain input.\n- If OpenSea API is **down**, retry after some time.\n\n---\n\n## 🚀 **Connect with Me for Support**\nIf you need assistance, custom OpenSea NFT insights, or automation support, feel free to connect with me on LinkedIn:\n\n🌐 **Don <PERSON> – LinkedIn**  \n🔗 [http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1bdf4330-2c81-47dc-8729-1737eb19cd40", "connections": {"NFT Agent Brain": {"ai_languageModel": [[{"node": "OpenSea NFT Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenSea Get NFT": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "NFT Agent Memory": {"ai_memory": [[{"node": "OpenSea NFT Agent", "type": "ai_memory", "index": 0}]]}, "OpenSea Get Traits": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Account": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Contract": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Collection": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "Workflow Input Trigger": {"main": [[{"node": "OpenSea NFT Agent", "type": "main", "index": 0}]]}, "OpenSea Get Collections": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get Payment Token": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get NFTs by Account": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get NFTs by Contract": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Get NFTs by Collection": {"ai_tool": [[{"node": "OpenSea NFT Agent", "type": "ai_tool", "index": 0}]]}}}