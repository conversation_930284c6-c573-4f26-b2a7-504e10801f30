{"id": "qww129cm4TM9N8Ru", "meta": {"instanceId": "038da3428bba4563b42be267feeca21b4922693db254331ac640a5c56ee7cadf", "templateCredsSetupCompleted": true}, "name": "InstaTest", "tags": [{"id": "8PlqXsDyqVlHJ7RC", "name": "AI", "createdAt": "2024-07-10T14:12:10.657Z", "updatedAt": "2024-07-10T14:12:10.657Z"}], "nodes": [{"id": "51dcaa84-d1f9-4abc-aebc-24a06801e42d", "name": "Set your system promt for AI", "type": "n8n-nodes-base.set", "notes": "In this node in \"prompt\" variable you can set your system prompt", "position": [1120, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0b3c3d71-5627-4b8c-91f0-ac44eaedf196", "name": "prompt", "type": "string", "value": "=Persona: You are a instagram influencer.\nContext: You receive a messages from your subscribers\nTask: Answer questions in your writing style and patterns according to your previous posts text. Use your post only for style and patterns reference.\nStyle rules:\nsimple answers"}, {"id": "c2a9e272-5c0d-4685-ad0e-ce6995f92a1c", "name": "sessionId", "type": "string", "value": "={{ $json.body.session_id }}"}, {"id": "b3c20ee3-07a1-4584-b0d9-7310a2c6b723", "name": "chatInput", "type": "string", "value": "={{ $json.body.text }}"}]}}, "typeVersion": 3.3}, {"id": "0fb36573-d632-4403-8809-3973f9caa32a", "name": "Local n8n memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1500, 780], "parameters": {"sessionKey": "={{ $('Set your system promt for AI').last().json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 20}, "typeVersion": 1.3}, {"id": "2f0471a7-2a84-41ce-aab1-896d5ea95ac3", "name": "ChatGPT model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1360, 780], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "HxWZhtJcnqTXVHAA", "name": "General"}}, "typeVersion": 1}, {"id": "49abc3a3-faf9-4249-b874-908138a84aea", "name": "Send respond ", "type": "n8n-nodes-base.respondToWebhook", "position": [1720, 620], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "49382508-9307-4ffa-8b31-78fac3a7db10", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [320, 360], "parameters": {"color": 5, "width": 458.4028599661066, "height": 447.98321744507007, "content": "## Easy Instagram(via ManyChat) bot\n---\n### Description:\nThis template is a main part of Entire solution. It's getting new message from Instagram via ManyChat(Extra No-Code tool for getting and sending message in Instagram). Generating message using ChatGPT and send back to ManyChat that sends it to Instagrtam.\n\n### Logic:\n1. Getting message from Instagram(from ManyChat)\n2. Set you system prompt for AI\n3. Create simple answer for message in AI block\n4. Send answer to Instagram(to ManyChat)\n\n---\n*Helpful links:*\n- [Guide in Notion how to create full bot](https://shadowed-pound-d6e.notion.site/Instagram-GPT-light-version-Manychat-X-N8N-176293bddff880899a9ac255585d29f7?pvs=4)\n- [ManyChat](https://manychat.partnerlinks.io/vm4wkw8j81tc)"}, "typeVersion": 1}, {"id": "5d14544c-7039-435f-a53c-615b5722bb99", "name": "Getting message from Instagram", "type": "n8n-nodes-base.webhook", "position": [900, 620], "webhookId": "68d3fbc9-6e49-4bdc-851c-2a532be911ab", "parameters": {"path": "instagram_chat", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "3770f558-341b-4d67-a7f0-0bb2fecf51a3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, 300], "parameters": {"width": 313.9634922216307, "height": 614.7475040550845, "content": "## 3) AI block\n---\nThere is 3 nodes:\n- AI Agent\n- Chat GPT model\n- Memory for history messages\n\n### To do:\n- in ChatGPT node you can choose the best model for you\n- in Memory Block you can change number of messages in history\n\n"}, "typeVersion": 1}, {"id": "cbb6c5a2-9b96-4305-afce-5ac560ae2dec", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1340, 620], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "={{ $json.prompt }}"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "4e28119f-b1aa-4b20-a8ed-28bd137f9627", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, 360], "parameters": {"height": 440, "content": "## 1) HTTP Post webhook\n\n**To do:**\nJust copy production link from this node and insert to custom action in ManyChat\n\nNo edits needed"}, "typeVersion": 1}, {"id": "b18a8890-b420-4086-91c8-8edbc845c8af", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1080, 480], "parameters": {"width": 220, "height": 320, "content": "## 2) Edit prompt\n\n**To do:**\nGo inside and change input\n"}, "typeVersion": 1}, {"id": "74d4e6f5-069e-4b37-8005-8c03226b05df", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1660, 480], "parameters": {"height": 300, "content": "## 4) Respond webhook\n\nNo edits needed"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2f36fc7a-0a69-4af3-a958-25e9d278f058", "connections": {"AI Agent": {"main": [[{"node": "Send respond ", "type": "main", "index": 0}]]}, "ChatGPT model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Local n8n memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Set your system promt for AI": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Getting message from Instagram": {"main": [[{"node": "Set your system promt for AI", "type": "main", "index": 0}]]}}}