{"nodes": [{"id": "c17e444e-0a5e-4bfe-8de6-c3185de4465d", "name": "Grants to List", "type": "n8n-nodes-base.splitOut", "position": [-240, -180], "parameters": {"options": {}, "fieldToSplitOut": "oppHits"}, "typeVersion": 1}, {"id": "9251d39c-6098-42fa-aadd-3a22464dee64", "name": "Get Grant Details", "type": "n8n-nodes-base.httpRequest", "position": [280, -280], "parameters": {"url": "https://apply07.grants.gov/grantsws/rest/opportunity/details", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "oppId", "value": "={{ $json.id }}"}]}}, "typeVersion": 4.2}, {"id": "ade994d6-a1f8-45bf-a82e-83eb38da08d6", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [440, -120], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "4d81b20e-0038-48d3-840c-3fcf8b798a0d", "name": "Summarize Synopsis", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [460, -280], "parameters": {"text": "=Agency: {{ $json.synopsis.agencyName }}\nTitle: {{ $json.opportunityTitle }}\nSynopsis: {{ $json.synopsis.synopsisDesc }}", "options": {"systemPromptTemplate": "You've been given a grant opportunity listing. Help summarize the opportunity in simple terms."}, "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n \"goal\": { \"type\": [\"string\", \"null\"] },\n \"duration\": { \"type\": \"string\" },\n \"success_criteria\": {\n \"type\": \"array\",\n \"items\": { \"type\": \"string\" }\n },\n \"good_to_know\": {\n\t\t \"type\": \"array\",\n \"items\": { \"type\": \"string\" }\n }\n\t}\n}"}, "typeVersion": 1}, {"id": "71e1a2e9-6690-4247-aae3-f5bd61019553", "name": "Eligibility Factors", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [640, -120], "parameters": {"text": "=Agency: {{ $json.synopsis.agencyName }}\nTitle: {{ $json.opportunityTitle }}\nSynopsis: {{ $json.synopsis.synopsisDesc }}\nEligibility: {{ $json.synopsis.applicantEligibilityDesc }}", "options": {"systemPromptTemplate": "Help determine if we are eligible for this grant.\n\nWe are AI Consultants Limited (“Company”) and are the controllers of your personal data. Our registered office is Unit 29, Intelligent Park, Milton Road, Cambridge Cambridgeshire CB9 RDW, and our registered company number is 1234567.\n\nWe are part of a group of companies which provides consultancy services across the globe. Our other group companies are:\n\nAI Consultants Inc. of 2 Drydock Avenue, Suite 1210, Boston, MA 02210, USA\nAI Consultants (Singapore) Pte Ltd of 300 Beach Road, Singapore 199555\nAI Consultants Japan Inc, of 3-1-3 Minamiaoyama, Minato-ku, Tokyo, 107-0062\nIn the UK we are registered with the Information Commissioner’s Office under registration number Z9888888.\n\nIn the US we are registered with the Data Privacy Framework Program (DPF). To view the Company’s certification, please visit https://www.dataprivacyframework.gov/list.\n\nWe are a leading, worldwide product development service provider. We specialise in design engineering services, professional technical services and product technical support services (“Services”).\n\nAs the deep tech powerhouse of Capgemini, CC spearheads transformative projects to solve the toughest scientific and engineering challenges. Ambitious clients collaborate with us to create new-to-the-world technologies, services and products that have never been seen before. Our unique combination of technical, commercial and market expertise yields market-leading solutions that are hard to copy. This creates valuable intellectual property that generates protectable long-term value.\n\nWe work with some of the world’s biggest brands and most ambitious technology start-up ventures across a wide range of markets. From aerospace to agritech, consumer to industry, communications to healthcare, our knowledge of one sector can often be applied to another to create new breakthroughs. We focus on our clients’ success and we are trusted as integral partners in the future of their businesses.\n\nWe do important, difficult, radical and impactful things that benefit society. We helped develop the world's first 24/7 wrist-worn activity monitor, wireless pacemaker and wireless patient monitor, as well as the first connected drug inhaler. Our work led to the most densely packed cellular network in the world – orchestrating swarms of bots across highly automated warehouses. It produced the Bluetooth chip that connects your phone to your car and the latest satellite technology that lets people in remote locations across the world keep in touch."}, "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"eligibility_matches\": {\n\t\t \"type\": \"array\",\n \"items\": { \"type\": \"string\" }\n }\n\t}\n}"}, "typeVersion": 1}, {"id": "d741ef63-dcf3-452d-978c-8cbc27f55a33", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 20], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "7354ed6d-50f5-4234-90d8-2d9d0c7eccd4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1000, -120], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "2dffda98-18c6-4c7b-8fc3-0e6539642ea2", "name": "Save to <PERSON>er", "type": "n8n-nodes-base.airtable", "position": [1420, -20], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appiNoPRvhJxz9crl", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl", "cachedResultName": "US Grants.gov Tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblX93C9MNzizhibd", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl/tblX93C9MNzizhibd", "cachedResultName": "Table 1"}, "columns": {"value": {"URL": "=https://grants.gov/search-results-detail/{{ $('Get Grant Details').item.json.id }}", "Goal": "={{ $json.output.goal }}", "Notes": "={{ $json.output.good_to_know.join('\\n') }}", "Title": "={{ $('Get Grant Details').item.json.opportunityTitle }}", "Agency": "={{ $('Get Grant Details').item.json.synopsis.agencyContactName }}", "Status": "New", "Funding": "={{ $('Get Grant Details').item.json.synopsis.estimatedFunding }}", "Duration": "={{ $json.output.duration }}", "Award Floor": "={{ $('Get Grant Details').item.json.synopsis.awardFloor }}", "Posted Date": "={{ $('Get Grant Details').item.json.synopsis.postingDate }}", "Agency Email": "={{ $('Get Grant Details').item.json.synopsis.agencyContactEmail }}", "Agency Phone": "={{ $('Get Grant Details').item.json.synopsis.agencyContactPhone }}", "Eligibility?": "={{ $json.output.eligibility_matches.length > 0 ? 'Yes' : 'No' }}", "Award Ceiling": "={{ $('Get Grant Details').item.json.synopsis.awardCeiling }}", "Response Date": "={{ $('Get Grant Details').item.json.synopsis.responseDate }}", "Success Criteria": "={{ $json.output.success_criteria.join('\\n') }}", "Eligibility Notes": "={{ $json.output.eligibility_matches.join('\\n') }}", "Opportunity Number": "={{ $('Get Grant Details').item.json.opportunityNumber }}"}, "schema": [{"id": "Opportunity Number", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Opportunity Number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "New", "value": "New"}, {"name": "Under Review", "value": "Under Review"}, {"name": "Interested", "value": "Interested"}, {"name": "Not Interested", "value": "Not Interested"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Goal", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Goal", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Success Criteria", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Success Criteria", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Eligibility?", "type": "options", "display": true, "options": [{"name": "Yes", "value": "Yes"}, {"name": "No", "value": "No"}], "removed": false, "readOnly": false, "required": false, "displayName": "Eligibility?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Eligibility Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Eligibility Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Duration", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Duration", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Agency", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Agency", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Agency Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Agency Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Agency Phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Agency Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posted Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Posted Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Response Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Response Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Funding", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Funding", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Award Ceiling", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Award Ceiling", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Award Floor", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Award Floor", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "f0712788-b801-4070-a5c2-2f7ed620588e", "name": "Only New Grants", "type": "n8n-nodes-base.removeDuplicates", "position": [-60, -180], "parameters": {"options": {}, "operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.id }}"}, "typeVersion": 2}, {"id": "fb4ac14d-0bdd-40f7-9b31-3a23450b1f0b", "name": "AI Grants since Yesterday", "type": "n8n-nodes-base.httpRequest", "position": [-420, -180], "parameters": {"url": "https://apply07.grants.gov/grantsws/rest/opportunities/search", "method": "POST", "options": {}, "jsonBody": "{\n \"keyword\": \"ai\",\n \"cfda\": null,\n \"agencies\": null,\n \"sortBy\": \"openDate|desc\",\n \"rows\": 5000,\n \"eligibilities\": null,\n \"fundingCategories\": null,\n \"fundingInstruments\": null,\n \"dateRange\": \"1\",\n \"oppStatuses\": \"forecasted|posted\"\n}", "sendBody": true, "specifyBody": "json"}, "typeVersion": 4.2}, {"id": "0446c882-764a-4c94-8c49-f368c50586a0", "name": "Get New Eligible Grants Today", "type": "n8n-nodes-base.airtable", "position": [-400, 500], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appiNoPRvhJxz9crl", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl", "cachedResultName": "US Grants.gov Tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblX93C9MNzizhibd", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl/tblX93C9MNzizhibd", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "filterByFormula": "=AND(\n {Status} = 'New',\n {Eligibility?} = 'Yes',\n IS_SAME(DATETIME_FORMAT(Created, 'YYYY-MM-DD'), DATETIME_FORMAT(TODAY(), 'YYYY-MM-DD'))\n)"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "70bca43a-d00e-4ee6-828a-9926ba1d8fdb", "name": "Generate Email", "type": "n8n-nodes-base.html", "position": [-160, 500], "parameters": {"html": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional //EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\">\n<head>\n<!--[if gte mso 9]>\n<xml>\n <o:OfficeDocumentSettings>\n <o:AllowPNG/>\n <o:PixelsPerInch>96</o:PixelsPerInch>\n </o:OfficeDocumentSettings>\n</xml>\n<![endif]-->\n <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <meta name=\"x-apple-disable-message-reformatting\">\n <!--[if !mso]><!--><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><!--<![endif]-->\n <title></title>\n \n <style type=\"text/css\">\n @media only screen and (min-width: 520px) {\n .u-row {\n width: 500px !important;\n }\n .u-row .u-col {\n vertical-align: top;\n }\n\n .u-row .u-col-100 {\n width: 500px !important;\n }\n\n}\n\n@media (max-width: 520px) {\n .u-row-container {\n max-width: 100% !important;\n padding-left: 0px !important;\n padding-right: 0px !important;\n }\n .u-row .u-col {\n min-width: 320px !important;\n max-width: 100% !important;\n display: block !important;\n }\n .u-row {\n width: 100% !important;\n }\n .u-col {\n width: 100% !important;\n }\n .u-col > div {\n margin: 0 auto;\n }\n}\nbody {\n margin: 0;\n padding: 0;\n}\n\ntable,\ntr,\ntd {\n vertical-align: top;\n border-collapse: collapse;\n}\n\np {\n margin: 0;\n}\n\n.ie-container table,\n.mso-container table {\n table-layout: fixed;\n}\n\n* {\n line-height: inherit;\n}\n\na[x-apple-data-detectors='true'] {\n color: inherit !important;\n text-decoration: none !important;\n}\n\ntable, td { color: #000000; } </style>\n \n \n\n</head>\n\n<body class=\"clean-body u_body\" style=\"margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #F7F8F9;color: #000000\">\n <!--[if IE]><div class=\"ie-container\"><![endif]-->\n <!--[if mso]><div class=\"mso-container\"><![endif]-->\n <table style=\"border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #F7F8F9;width:100%\" cellpadding=\"0\" cellspacing=\"0\">\n <tbody>\n <tr style=\"vertical-align: top\">\n <td style=\"word-break: break-word;border-collapse: collapse !important;vertical-align: top\">\n <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td align=\"center\" style=\"background-color: #F7F8F9;\"><![endif]-->\n \n \n \n<div class=\"u-row-container\" style=\"padding: 0px;background-color: #f7f8f9\">\n <div class=\"u-row\" style=\"margin: 0 auto;min-width: 320px;max-width: 500px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;\">\n <div style=\"border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;\">\n <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding: 0px;background-color: #f7f8f9;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:500px;\"><tr style=\"background-color: #ffffff;\"><![endif]-->\n \n<!--[if (mso)|(IE)]><td align=\"center\" width=\"500\" style=\"background-color: #f7f8f9;width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px;min-width: 500px;display: table-cell;vertical-align: top;\">\n <div style=\"background-color: #f7f8f9;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\">\n <!--[if (!mso)&(!IE)]><!--><div style=\"box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\"><!--<![endif]-->\n \n<table style=\"font-family:arial,helvetica,sans-serif;\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" border=\"0\">\n <tbody>\n <tr>\n <td style=\"overflow-wrap:break-word;word-break:break-word;padding:32px 10px;font-family:arial,helvetica,sans-serif;\" align=\"left\">\n \n <!--[if mso]><table width=\"100%\"><tr><td><![endif]-->\n <h1 style=\"margin: 0px; line-height: 140%; text-align: center; word-wrap: break-word; font-family: arial black,AvenirNext-Heavy,avant garde,arial; font-size: 22px; font-weight: 400;\"><span><span><span><span><span><span>Latest AI Grants</span></span></span></span></span></span></h1>\n <!--[if mso]></td></tr></table><![endif]-->\n\n </td>\n </tr>\n </tbody>\n</table>\n\n <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->\n </div>\n</div>\n<!--[if (mso)|(IE)]></td><![endif]-->\n <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->\n </div>\n </div>\n </div>\n \n\n\n \n \n<div class=\"u-row-container\" style=\"padding: 0px;background-color: #f7f8f9\">\n <div class=\"u-row\" style=\"margin: 0 auto;min-width: 320px;max-width: 500px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;\">\n <div style=\"border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;\">\n <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding: 0px;background-color: #f7f8f9;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:500px;\"><tr style=\"background-color: transparent;\"><![endif]-->\n \n<!--[if (mso)|(IE)]><td align=\"center\" width=\"500\" style=\"background-color: #ffffff;width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px;min-width: 500px;display: table-cell;vertical-align: top;\">\n <div style=\"background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\">\n <!--[if (!mso)&(!IE)]><!--><div style=\"box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\"><!--<![endif]-->\n \n<table style=\"font-family:arial,helvetica,sans-serif;\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" border=\"0\">\n <tbody>\n <tr>\n <td style=\"overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:arial,helvetica,sans-serif;\" align=\"left\">\n{{\n$input.all().map((input,idx) => {\nreturn `\n <div>\n <div style=\"padding-top:14px;padding-bottom:24px\">\n <h3 style=\"margin-top:0;margin-bottom:7px;font-size:16px\">\n ${idx+1}. ${input.json.Title}\n </h3>\n <div style=\"margin-bottom:14px;font-size:12px;\">\n <strong>${input.json.Agency}</strong>\n &middot;\n <a href=\"${input.json.URL}\">See details</a>\n </div>\n <p style=\"margin-bottom:14px;font-size:14px\">\n <strong>Synopsis:</strong> ${input.json.Goal}\n </p>\n <ul style=\"font-size:14px;\">\n ${input.json['Success Criteria']\n .split('\\n')\n .map(text => `<li>${text}</li>`)\n .join('')\n }\n </ul>\n <div style=\"font-size:12px;\">\n <strong>Posted By</strong> ${input.json['Posted Date']\n .toDateTime()\n .format('EEE, dd MMM yyyy t')}\n <br/>\n <strong>Respond By</strong> ${input.json['Response Date']\n .toDateTime()\n .format('EEE, dd MMM yyyy t')}\n \n </div>\n</div> \n`\n}).join('<hr/>')\n}} \n </td>\n </tr>\n </tbody>\n</table>\n\n <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->\n </div>\n</div>\n<!--[if (mso)|(IE)]></td><![endif]-->\n <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->\n </div>\n </div>\n </div>\n \n\n\n \n \n<div class=\"u-row-container\" style=\"padding: 0px;background-color: transparent\">\n <div class=\"u-row\" style=\"margin: 0 auto;min-width: 320px;max-width: 500px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;\">\n <div style=\"border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;\">\n <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding: 0px;background-color: transparent;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:500px;\"><tr style=\"background-color: transparent;\"><![endif]-->\n \n<!--[if (mso)|(IE)]><td align=\"center\" width=\"500\" style=\"width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px;min-width: 500px;display: table-cell;vertical-align: top;\">\n <div style=\"height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\">\n <!--[if (!mso)&(!IE)]><!--><div style=\"box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\"><!--<![endif]-->\n \n<table style=\"font-family:arial,helvetica,sans-serif;\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" border=\"0\">\n <tbody>\n <tr>\n <td style=\"overflow-wrap:break-word;word-break:break-word;padding:24px 10px;font-family:arial,helvetica,sans-serif;\" align=\"left\">\n \n <div style=\"font-size: 14px; color: #7e8c8d; line-height: 140%; text-align: center; word-wrap: break-word;\">\n <p style=\"line-height: 140%;\">Autogenerated by n8n.</p>\n<p style=\"line-height: 140%;\">Brought to you by workflow #{{ $workflow.id }}</p>\n </div>\n\n </td>\n </tr>\n </tbody>\n</table>\n\n <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->\n </div>\n</div>\n<!--[if (mso)|(IE)]></td><![endif]-->\n <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->\n </div>\n </div>\n </div>\n \n\n\n <!--[if (mso)|(IE)]></td></tr></table><![endif]-->\n </td>\n </tr>\n </tbody>\n </table>\n <!--[if mso]></div><![endif]-->\n <!--[if IE]></div><![endif]-->\n</body>\n\n</html>\n"}, "executeOnce": true, "typeVersion": 1.2}, {"id": "12bd72f5-3028-4572-b59e-1cc143e44a86", "name": "Everyday @ 9am", "type": "n8n-nodes-base.scheduleTrigger", "position": [-720, 460], "parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "typeVersion": 1.2}, {"id": "ca62c507-bce5-4a63-be0e-e60591408668", "name": "Everyday @ 8.30am", "type": "n8n-nodes-base.scheduleTrigger", "position": [-720, -220], "parameters": {"rule": {"interval": [{"triggerAtHour": 8, "triggerAtMinute": 30}]}}, "typeVersion": 1.2}, {"id": "032bec7e-5aff-4103-b81e-e5bc4a88ddde", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -420], "parameters": {"color": 7, "width": 700, "height": 480, "content": "## 1. <PERSON>tch Latest AI Grants, Ignore Those Already Seen\n[Learn more about the Remove Duplicates node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.removeduplicates/)\n\nA cool feature of n8n's remove duplicates node is that it works across executions. What this means for this template is that the node will help us keep track of grant IDs to know if we've already processed them and if so, filter them out so we won't have duplicate alerts."}, "typeVersion": 1}, {"id": "********-3571-4512-adce-2727dcb95240", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, -520], "parameters": {"color": 7, "width": 1000, "height": 720, "content": "## 2. Quickly Determine Eligibility Using AI\n[Learn more about the Information Extractor node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor/)\n\nQualifying Leads requires a lot of contextual reasoning taking into account many factors such as commercials, location and eligibility criteria. Whilst it's not guaranteed AI can or will solve this for your particular requirements, it can however get you a good distance of the way there!\n\nAI in this template intends to reduce time (and therefore cost) for a team member needs to spend per grant listing or increase their coverage of grants which they would otherwise miss due to capacity."}, "typeVersion": 1}, {"id": "f4758b4d-727a-4ce8-b071-3388eb16b219", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1200, -280], "parameters": {"color": 7, "width": 520, "height": 480, "content": "## 3. Save Results to <PERSON>\n[Learn more about the Airtable Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable/)\n\nIn n8n, it's easy to send your data anywhere to manage yourself, share with your team or reuse with other workflows. Here for demonstration purposes, we'll just store each grant as a row in our Airtable database.\n\nCheck out the sample Airtable here: https://airtable.com/appiNoPRvhJxz9crl/shrRdP6zstgsxjDKL"}, "typeVersion": 1}, {"id": "a7861a21-021f-4629-b863-2163c7436d13", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-540, 240], "parameters": {"color": 7, "width": 620, "height": 500, "content": "## 4. Generate Latest AI Grants <PERSON><PERSON>ail\n[Learn more about the HTML Template node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.html/)\n\nUsing our freshly collected AI grants, it would be nice if we can share them with our team members via email. A nicely formatted email digest can be generated using the HTML template node, with added links for greater impact.\n\nHere in this demonstration, we will loop through all eligible new grants and compile them into a newsletter format using the HTML node.\n"}, "typeVersion": 1}, {"id": "4d09af53-92cb-4288-86d7-dcf695bfb358", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [100, 240], "parameters": {"color": 7, "width": 640, "height": 500, "content": "## 5. Send to a list of Subscribers\n[Learn more about the Gmail node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/)\n\nFinally, we can source a list of subscribers to send our generated email newsletter.\n\nHere, our subscriber list is another table alongside our grants table that we can import that list using the Airtable node. You can use any email provider that supports HTML but for this demonstration, we're using Gmail for simplicity sake."}, "typeVersion": 1}, {"id": "784d59f3-5b1f-4404-bc04-4bd58cf03585", "name": "Get Subscribers", "type": "n8n-nodes-base.airtable", "position": [240, 500], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appiNoPRvhJxz9crl", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl", "cachedResultName": "US Grants.gov Tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblaS91hyhguntfaC", "cachedResultUrl": "https://airtable.com/appiNoPRvhJxz9crl/tblaS91hyhguntfaC", "cachedResultName": "Subscribers"}, "options": {}, "operation": "search", "filterByFormula": "AND({Status} = 'Active')"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 2.1}, {"id": "3be0788b-90ef-4648-aa25-1170208a685d", "name": "Send Subscriber Email", "type": "n8n-nodes-base.gmail", "position": [480, 500], "webhookId": "37eeec7a-1982-4137-8473-313bfb6c5b42", "parameters": {"sendTo": "={{ $json.Email }}", "message": "={{ $('Generate Email').first().json.html }}", "options": {}, "subject": "Daily Newletter for Intersting US Grants"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "14a65482-b314-4a2f-9ce3-87e3aae126f9", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1280, 300], "parameters": {"color": 7, "width": 460, "height": 200, "content": "## Scheduled Triggers\n[Learn more about Scheduled Triggers](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger)\n\nScheduled triggers are a great way to run this template automatically in the morning ready for your team before they start their working day.\n\nFeel free to adjust the interval to a time which suits you!"}, "typeVersion": 1}, {"id": "b172eb7a-58bc-4d4a-be22-796d34a59897", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1280, -620], "parameters": {"width": 460, "height": 900, "content": "## Try It Out!\n\n### This n8n templates demonstrates how to automatically ingest a source of leads at regular intervals and take advantage of n8n's remove duplicates node to simplify duplicate detection.\nAdditionally after the leads are captured, a simple alerts notification can be generated and shared with team members.\n\n### How it works\n* A scheduled trigger is set to fetch a list of AI grants listed on the grants.gov website in the past day.\n* A Remove Duplicates node is used to track Grant IDs to filter out those already processed by the workflow.\n* New grants are summarized and analysed by AI nodes to determine eligibility and interest which is then saved to an Airtable database.\n* Another scheduled trigger starts a little later than the first to collect and summarize the new grants\n* The results are then compiled into an email template using the HTML node, in the form of a newsletter designed to alert and brief team members of new AI grants.\n* This email is then sent to a list of subscribers using the gmail node.\n\n## How to use\n* Make a copy of sample Airtable here: https://airtable.com/appiNoPRvhJxz9crl/shrRdP6zstgsxjDKL\n* The filters for fetching the grants is currently set to the \"AI\" category. Feel free to change this to include more categories.\n* Not interested in grants, this template can works for other sources of leads just change the endpoint and how you're defining the item ID to track.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "f9849413-4dad-44dc-92ec-8879d123bfd3", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [720, 40], "parameters": {"width": 320, "height": 120, "content": "### Add your company details here!\nCompany details are added in the system prompt to help the AI determine eligibility. The more details the better!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Save to <PERSON>er", "type": "main", "index": 0}]]}, "Everyday @ 9am": {"main": [[{"node": "Get New Eligible Grants Today", "type": "main", "index": 0}]]}, "Generate Email": {"main": [[{"node": "Get Subscribers", "type": "main", "index": 0}]]}, "Grants to List": {"main": [[{"node": "Only New Grants", "type": "main", "index": 0}]]}, "Get Subscribers": {"main": [[{"node": "Send Subscriber Email", "type": "main", "index": 0}]]}, "Only New Grants": {"main": [[{"node": "Get Grant Details", "type": "main", "index": 0}]]}, "Save to Tracker": {"main": [[]]}, "Everyday @ 8.30am": {"main": [[{"node": "AI Grants since Yesterday", "type": "main", "index": 0}]]}, "Get Grant Details": {"main": [[{"node": "Summarize Synopsis", "type": "main", "index": 0}, {"node": "Eligibility Factors", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Summarize Synopsis", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Eligibility Factors", "type": "ai_languageModel", "index": 0}]]}, "Summarize Synopsis": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Eligibility Factors": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "AI Grants since Yesterday": {"main": [[{"node": "Grants to List", "type": "main", "index": 0}]]}, "Get New Eligible Grants Today": {"main": [[{"node": "Generate Email", "type": "main", "index": 0}]]}}}