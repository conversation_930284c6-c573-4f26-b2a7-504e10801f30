{"id": "ly8aZhPk5ZI8uB0Y", "meta": {"instanceId": "8931e7db592c2960ce253801ea290c1dc66e447734ce3d968310365665cefc80", "templateCredsSetupCompleted": true}, "name": "Discord MCP Server", "tags": [], "nodes": [{"id": "6e87d612-3006-4683-b978-87718f89257d", "name": "Send Discord Message to Channel", "type": "n8n-nodes-base.discordTool", "position": [360, 280], "webhookId": "90b1dca9-c742-4c7e-aef3-ba5a47c5f86d", "parameters": {"content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Channel', ``, 'string') }}"}}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "32a17a73-8953-4474-a49f-9d1cc0cc3eb2", "name": "Add Role To Member", "type": "n8n-nodes-base.discordTool", "position": [-200, 560], "webhookId": "e41a85ec-3f16-44fc-ad87-4617c0d0f1c0", "parameters": {"role": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Role', ``, 'string') }}", "userId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('User', ``, 'string') }}"}, "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "resource": "member", "operation": "<PERSON><PERSON><PERSON>"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "688ea823-b8ea-4bbf-96cb-a64925fc29a9", "name": "Remove Role from member", "type": "n8n-nodes-base.discordTool", "position": [-20, 560], "webhookId": "e41a85ec-3f16-44fc-ad87-4617c0d0f1c0", "parameters": {"role": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Role', ``, 'string') }}", "userId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('User', ``, 'string') }}"}, "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "resource": "member", "operation": "roleRemove"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "1d8a14f1-8e63-4112-8076-15b4408c844f", "name": "Discord MCP Server Trigger", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [20, -280], "webhookId": "404f083e-f3f4-4358-83ef-9804099ee253", "parameters": {"path": "404f083e-f3f4-4358-83ef-9804099ee253"}, "typeVersion": 1}, {"id": "67602807-3126-4564-8fed-912551eb824b", "name": "Get channels of server by server ID", "type": "n8n-nodes-base.discordTool", "position": [420, 20], "webhookId": "73c49e13-24e9-4481-902d-a5f3e1f50032", "parameters": {"guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "options": {}, "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "5f1c3039-7042-48b8-997c-12bcaa6a1256", "name": "Get members of server by server ID", "type": "n8n-nodes-base.discordTool", "position": [-80, 20], "webhookId": "ebd6d7dd-bcfa-4546-b48d-5e7862129caa", "parameters": {"after": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "options": {}, "resource": "member", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "ece7a065-36bb-4667-aa61-610e54f0b22d", "name": "Send DM and Wait for reply", "type": "n8n-nodes-base.discordTool", "position": [-280, 280], "webhookId": "90b1dca9-c742-4c7e-aef3-ba5a47c5f86d", "parameters": {"sendTo": "user", "userId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('User', ``, 'string') }}"}, "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "resource": "message", "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "c37d0478-5b00-4d21-b0fd-7e2fa34708ec", "name": "Send to Channel and Wait for Reply", "type": "n8n-nodes-base.discordTool", "position": [580, 280], "webhookId": "90b1dca9-c742-4c7e-aef3-ba5a47c5f86d", "parameters": {"guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Channel', ``, 'string') }}"}, "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "fb8091b5-114b-422e-be5a-6413d9aec599", "name": "Send DM to User", "type": "n8n-nodes-base.discordTool", "position": [-60, 280], "webhookId": "90b1dca9-c742-4c7e-aef3-ba5a47c5f86d", "parameters": {"sendTo": "user", "userId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('User', ``, 'string') }}"}, "content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "guildId": {"__rl": true, "mode": "id", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Server', ``, 'string') }}"}, "options": {}, "resource": "message"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 2}, {"id": "6f61b18a-8d96-4acb-994a-45ef32c10f16", "name": "Get Discord Server IDs", "type": "n8n-nodes-base.httpRequestTool", "position": [180, -60], "parameters": {"url": "https://discord.com/api/v10/users/@me/guilds", "options": {}, "authentication": "predefinedCredentialType", "toolDescription": "Retrieves the ID of each discord server the bot is in.", "nodeCredentialType": "discordBotApi"}, "credentials": {"discordBotApi": {"id": "SJhr2V3Xw4B3fVqW", "name": "Gopher"}}, "typeVersion": 4.2}, {"id": "3fe9b9ab-d9c8-4414-b3a4-01dace75da77", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, -60], "parameters": {"height": 600, "content": "\n\n\n\n\n\n\n\n\n\n\n\nThis gets all of the servers that your discord bot is currently in. If you have a bot in more than one server, you will need to let it know or at least hint at which server it is, or it may get stuck. If you specify a channel through your natural language request, then the model may keep trying to get servers' channels via the get channel node or it may give up before getting to the right one).\n\nNote: This is a custom API call using the same Discord bot auth, not a built-in \"Discord tool\" - I encourage you to go well beyond the 15 included tools that n8n provides for you. It is *easy* to do, and there are, literally, no limits to what you can do with n8n!"}, "typeVersion": 1}, {"id": "c886422d-f034-49dc-ad77-bc6e1cd495a8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 280], "parameters": {"width": 300, "height": 240, "content": "\n\n\n\n\n\n\n\n\n\n\n\nThese nodes either send a basic message from your bot to a channel or sends a message and waits for a response from a human (HITL)."}, "typeVersion": 1}, {"id": "36d5bcc3-f9f2-4161-83d8-d76083ea9e8c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-280, 280], "parameters": {"width": 300, "height": 240, "content": "\n\n\n\n\n\n\n\n\n\n\n\nThese nodes either send a basic message from your bot to a user via DM or sends a DM and waits for a response from the human (HITL)."}, "typeVersion": 1}, {"id": "65b6d13e-6f97-4b4c-9a0d-85b07b666c81", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 560], "parameters": {"width": 520, "height": 360, "content": "\n\n\n\n\n\n\n\n\n\n\n\nProgrammatic role addition and removal are just two examples of the many, many tools/API calls that you can make to Discord.\n\nYou could monitor for spam, mod abuse, or anything else you wanted and respond automatically by removing perms until you can check it out.\n\nYou can create amazing workflows that include discord webhooks, your existing other workflows, or anything else you can imagine and have it running on your bot in minutes via a MCP client node in a new or existing workflow!"}, "typeVersion": 1}, {"id": "e39211f7-7471-4b0b-a253-79b1e3026354", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-280, -60], "parameters": {"width": 280, "content": "Once we have the server ID of the server we want to interact with, we can grab all members of the server that the bot can see."}, "typeVersion": 1}, {"id": "84f36c6d-c990-48d0-9328-32a40c803956", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [420, -60], "parameters": {"width": 280, "content": "Once we have the server ID of the server we want to interact with, we can grab all channels the bot can see."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "bedf1c45-22f0-422c-8d77-ab058c1cceab", "connections": {"Send DM to User": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Add Role To Member": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Get Discord Server IDs": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Remove Role from member": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Send DM and Wait for reply": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Send Discord Message to Channel": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Get members of server by server ID": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Send to Channel and Wait for Reply": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Get channels of server by server ID": {"ai_tool": [[{"node": "Discord MCP Server Trigger", "type": "ai_tool", "index": 0}]]}}}