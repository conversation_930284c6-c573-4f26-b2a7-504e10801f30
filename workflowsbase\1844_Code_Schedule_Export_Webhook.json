{"id": "eB4rTdZFvrdKK5VP", "meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76", "templateCredsSetupCompleted": true}, "name": "Backup Squarespace code Injections to Github", "tags": [{"id": "oIxDbURnjwrJFwau", "name": "Squarespace", "createdAt": "2025-03-06T05:49:51.612Z", "updatedAt": "2025-03-06T05:49:51.612Z"}], "nodes": [{"id": "e0811eee-5bfe-4e48-8bd2-76b415261e93", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [820, 486.1164603611751], "parameters": {}, "typeVersion": 1}, {"id": "e48028e0-d88a-4681-84b8-fa17fe695344", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2380, 580], "parameters": {"options": {}, "batchSize": 2}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "0dd2a2b1-8c19-4d7a-a631-d934320bcf74", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [820, 686.1164603611751], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "typeVersion": 1.2}, {"id": "a1bc1313-5993-4d10-b322-0b31dd005d00", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"color": 4, "width": 371.1995072042308, "height": 600.88409546716, "content": "## Backup to GitHub \nThis workflow will backup Squarespace header & footer Injections to Github\n\n\n### Setup\n👉 Edit the Squarespace node to place the website URL there\n\n👉 Open `Globals` node and update the values below 👇\n\n- **repo.owner:** your Github username\n- **repo.name:** the name of your repository\n- **repo.path:** the folder to use within the repository.\n\n\nIf your username was `john-doe` and your repository was called `n8n-backups` and you wanted the injections to go into a `squarespace-backup` folder you would set:\n\n- repo.owner - john-doe\n- repo.name - n8n-backups\n- repo.path - squarespace-backup/\n\nEach site's injections will be added into seperate folder\n"}, "typeVersion": 1}, {"id": "125b425c-1f59-4984-8658-e57d3145cccd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [780, 400], "parameters": {"color": 7, "width": 1066, "height": 435, "content": "## Main workflow loop"}, "typeVersion": 1}, {"id": "626d534f-f2fb-4493-af58-0f5e309c58d4", "name": "Get Squarespace data", "type": "n8n-nodes-base.httpRequest", "position": [1140, 600], "parameters": {"url": "https://beyondspace.studio", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "page-context"}]}}, "typeVersion": 4.2}, {"id": "e2b1481e-b9de-4006-92b6-4677b4e11213", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1080, 460], "parameters": {"color": 4, "width": 170, "height": 120, "content": "## Edit this node 👇\nSquarespace URL"}, "typeVersion": 1}, {"id": "************************************", "name": "If", "type": "n8n-nodes-base.if", "position": [3060, 600], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0f6f0b04-6d87-47fb-bee7-df2c93283d1c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.error }}", "rightValue": "The resource you are requesting could not be found"}]}}, "typeVersion": 2.2}, {"id": "679cd20a-c124-4f89-aea3-14dc5a739826", "name": "Clean up Footers", "type": "n8n-nodes-base.code", "position": [1620, 460], "parameters": {"jsCode": "const cheerio = require('cheerio');\n\ntry {\n    const $footers = cheerio.load($input.first().json.value);\n    let sqsFooters = '';\n\n    /** CLEAN FOOTERS */\n    // Remove all elements after and including .social-icons-svg\n    const socialIcons = $footers('[data-usage=social-icons-svg]');\n    if (socialIcons.length) {\n        socialIcons.nextAll().remove(); // Remove everything after it\n        socialIcons.remove(); // Remove the element itself\n    }\n\n    // Extract cleaned-up footer content\n    $footers('head').each((i, el) => {\n        sqsFooters += $footers(el).html();\n    });\n\n    // Remove excessive newlines caused by removed elements\n    sqsFooters = sqsFooters.replace(/\\n{2,}/g, '\\n').trim();\n\n    return [{\n        ...$input.first().json,\n        value: sqsFooters,\n    }];\n} catch (error) {\n    console.error('Error processing Squarespace footers:', error);\n\n    // Return the original full input in case of failure\n    return [{\n        ...$input.first().json,\n        value: $input.first().json.value,\n    }];\n}\n"}, "typeVersion": 2}, {"id": "92150603-acbd-41bf-8cf2-b6891e08e326", "name": "Clean up Headers", "type": "n8n-nodes-base.code", "position": [1620, 680], "parameters": {"jsCode": "const cheerio = require('cheerio');\n\ntry {\n    const $headers = cheerio.load($input.first().json.value);\n    let sqsHeaders = '';\n\n    // Find the Squarespace CSS link that marks the start of relevant headers\n    const $headerStart = $headers('link[href*=\"static1.squarespace.com/static/versioned-site-css\"][href*=\"site.css\"]');\n\n    // Remove all elements before and including the header start\n    if ($headerStart.length) {\n        $headers($headerStart).prevAll().remove();\n        $headers($headerStart).remove();\n    }\n\n    // Remove Squarespace's cookie banner script (marks the end) and any following elements\n    const cookieBannerScript = $headers('script').filter((_, el) => \n        $headers(el).html().includes('Static.COOKIE_BANNER_CAPABLE = true;')\n    );\n    if (cookieBannerScript.length) {\n        cookieBannerScript.nextAll().remove(); // Remove everything after it\n        cookieBannerScript.remove(); // Remove the script itself\n    }\n\n    // Extract cleaned-up headers\n    $headers('head').each((i, el) => {\n        sqsHeaders += $headers(el).html();\n    });\n\n    // Remove any unwanted comments or placeholders\n    sqsHeaders = sqsHeaders.replace('<!-- End of Squarespace Headers -->', '');\n\n    // Remove excessive newlines caused by removed elements\n    sqsHeaders = sqsHeaders.replace(/\\n{2,}/g, '\\n').trim();\n\n    return [{\n        ...$input.first().json,\n        value: sqsHeaders,\n    }];\n} catch (error) {\n    console.log('Error processing Squarespace headers:', error);\n    \n    // Return the original full input in case of failure\n    return [{\n        ...$input.first().json,\n        value: $input.first().json.value,\n    }];\n}\n"}, "typeVersion": 2}, {"id": "a777d9ca-2841-47dc-a4d5-5ae36713d4a2", "name": "<PERSON> Footer Injection", "type": "n8n-nodes-base.set", "position": [1380, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "49e2fd3c-d46e-42a0-84d5-578166c1fbae", "name": "value", "type": "string", "value": "={{ $json[\"squarespace-footers\"] }}"}, {"id": "************************************", "name": "id", "type": "string", "value": "footers"}, {"id": "14de0bc4-6a40-488d-a17e-0ff940a8d38b", "name": "name", "type": "string", "value": "footers"}, {"id": "065cb5cd-b9fd-4aab-90be-5191e96d9b91", "name": "timestamp", "type": "string", "value": "={{ new Date().getTime() }}"}, {"id": "41016a39-abf7-46ce-88b9-985553210983", "name": "domain", "type": "string", "value": "={{ ($json.website.primaryDomain || $json.website.authenticUrl || $json.website.internalUrl).replace(/^(?:https?:\\/\\/)?(?:www\\.)?/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "c8cdf09b-0218-4dbc-9564-e84d123ad5d7", "name": "<PERSON> Header Injection", "type": "n8n-nodes-base.set", "position": [1380, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "49e2fd3c-d46e-42a0-84d5-578166c1fbae", "name": "value", "type": "string", "value": "={{ $json[\"squarespace-headers\"] }}"}, {"id": "************************************", "name": "id", "type": "string", "value": "headers"}, {"id": "42d56c77-3f2b-4ea6-b3c9-754e293c0615", "name": "name", "type": "string", "value": "headers"}, {"id": "b3d13c06-6a4a-4478-ad14-8e0b7a2998e0", "name": "timestamp", "type": "string", "value": "={{ new Date().getTime() }}"}, {"id": "b0977682-f135-4546-a390-0a3777907e4f", "name": "domain", "type": "string", "value": "={{ ($json.website.primaryDomain || $json.website.authenticUrl || $json.website.internalUrl).replace(/^(?:https?:\\/\\/)?(?:www\\.)?/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "7ca88a4d-f438-4d3a-b3ce-e66f1dd3ae00", "name": "Edit Injection data", "type": "n8n-nodes-base.github", "position": [2840, 600], "parameters": {"owner": {"__rl": true, "mode": "name", "value": "={{ $json.repo.owner }}"}, "filePath": "={{ $json.repo.path }}{{ $('Loop Over Items').item.json.id }}.html", "resource": "file", "operation": "edit", "repository": {"__rl": true, "mode": "name", "value": "={{ $json.repo.name }}"}, "fileContent": "={{ $('Loop Over Items').item.json.value }}", "commitMessage": "=Backup at {{ new DateTime($('Loop Over Items').item.json.timestamp) }}"}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 1, "continueOnFail": true, "alwaysOutputData": true}, {"id": "34ae1cf2-0abb-4fec-b60c-35c44f92020d", "name": "Create Injection data", "type": "n8n-nodes-base.github", "position": [3280, 620], "parameters": {"owner": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.owner }}"}, "filePath": "={{ $('Globals').item.json.repo.path }}{{ $('Loop Over Items').item.json.id }}.html", "resource": "file", "repository": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.name }}"}, "fileContent": "={{ $('Loop Over Items').item.json.value }}", "commitMessage": "=Backup at {{ new DateTime($('Loop Over Items').item.json.timestamp) }}"}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 1, "continueOnFail": true, "alwaysOutputData": true}, {"id": "40ae14ee-ce39-4706-898c-51957f52328d", "name": "Globals", "type": "n8n-nodes-base.set", "position": [2640, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6cf546c5-5737-4dbd-851b-17d68e0a3780", "name": "repo.owner", "type": "string", "value": "BeyondspaceStudio"}, {"id": "452efa28-2dc6-4ea3-a7a2-c35d100d0382", "name": "repo.name", "type": "string", "value": "n8n-backup"}, {"id": "81c4dc54-86bf-4432-a23f-22c7ea831e74", "name": "repo.path", "type": "string", "value": "=squarespace-backup/{{ $json.domain }}/"}]}}, "typeVersion": 3.4}, {"id": "d20a5274-66e8-4053-b29d-e814e67a3f0e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2600, 480], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "db43654b-22bc-4c0e-9b6a-6f1dded7deb8", "name": "Merge Injections", "type": "n8n-nodes-base.merge", "position": [2140, 580], "parameters": {}, "typeVersion": 3}], "active": true, "pinData": {}, "settings": {}, "versionId": "dfe7ec9f-5b7a-4a9c-8b53-b0cc4bcc2d27", "connections": {"If": {"main": [[{"node": "Create Injection data", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Globals": {"main": [[{"node": "Edit Injection data", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Globals", "type": "main", "index": 0}]]}, "Clean up Footers": {"main": [[{"node": "Merge Injections", "type": "main", "index": 0}]]}, "Clean up Headers": {"main": [[{"node": "Merge Injections", "type": "main", "index": 1}]]}, "Merge Injections": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Squarespace data", "type": "main", "index": 0}]]}, "Edit Injection data": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get Footer Injection": {"main": [[{"node": "Clean up Footers", "type": "main", "index": 0}]]}, "Get Header Injection": {"main": [[{"node": "Clean up Headers", "type": "main", "index": 0}]]}, "Get Squarespace data": {"main": [[{"node": "<PERSON> Header Injection", "type": "main", "index": 0}, {"node": "<PERSON> Footer Injection", "type": "main", "index": 0}]]}, "Create Injection data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Get Squarespace data", "type": "main", "index": 0}]]}}}