{"id": "AAjX1BuwhyXpo8xP", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "name": "Google Analytics: Weekly Report", "tags": [], "nodes": [{"id": "91ba5982-e226-4f0b-af0d-8c9a44b08279", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1740, 300], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "62c38eaf-2222-4d22-8589-677f36bce10d", "name": "Google Analytics Letzte 7 Tage", "type": "n8n-nodes-base.googleAnalytics", "position": [-1540, 300], "parameters": {"metricsGA4": {"metricValues": [{"listName": "screenPageViews"}, {}, {"listName": "sessions"}, {"listName": "<PERSON><PERSON>er<PERSON>ser"}, {"name": "averageSessionDuration", "listName": "other"}, {"name": "ecommercePurchases", "listName": "other"}, {"name": "averagePurchaseRevenue", "listName": "other"}, {"name": "purchaseRevenue", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "list", "value": "*********", "cachedResultUrl": "https://analytics.google.com/analytics/web/#/p*********/", "cachedResultName": "https://www.ep-reisen.de  – GA4"}, "dimensionsGA4": {"dimensionValues": [{}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "onRKXREI8izfGzv0", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "0a51c2f3-a487-4226-884f-63d4cb2bf4e4", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [420, 80], "parameters": {"html": "={{ $json.message.content }}", "options": {}, "subject": "Weekly Report: Google Analytics: Last 7 days", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "A71x7hx6lKj7nxp1", "name": "SMTP account"}}, "typeVersion": 2.1}, {"id": "********-f455-4983-afea-e94b316d8532", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [420, 420], "parameters": {"text": "={{ $json.message.content }}", "chatId": "**********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "0hnyvxyUMN77sBmU", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "3b6b4902-15b3-4bbc-8427-c35471a7431b", "name": "Processing for Telegram", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [60, 420], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Convert the following text from HTML to normal text:\n\n{{ $json.message.content }}\n\nPlease format the table so that each metric is a separate paragraph!\n\nExample:\n\nTotal views: xx.xxx\nTotal views previous year: xx,xxx\nDifference: x.xx %\n\nTotal users: xx,xxx\nTotal users previous year: xx,xxx\nDifference: -x.xx %"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "d761980c-0327-4d4e-92aa-d0342b2e249e", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [140, 300], "parameters": {}, "typeVersion": 1}, {"id": "ce7ba356-80bb-4b17-9445-fb535267cdf0", "name": "Google Analytics: Past 7 days of the previous year", "type": "n8n-nodes-base.googleAnalytics", "position": [-600, 300], "parameters": {"endDate": "={{ $json.endDate }}", "dateRange": "custom", "startDate": "={{ $json.startDate }}", "metricsGA4": {"metricValues": [{"listName": "screenPageViews"}, {}, {"listName": "sessions"}, {"listName": "<PERSON><PERSON>er<PERSON>ser"}, {"name": "averageSessionDuration", "listName": "other"}, {"name": "ecommercePurchases", "listName": "other"}, {"name": "averagePurchaseRevenue", "listName": "other"}, {"name": "purchaseRevenue", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "list", "value": "*********", "cachedResultUrl": "https://analytics.google.com/analytics/web/#/p*********/", "cachedResultName": "https://www.ep-reisen.de  – GA4"}, "dimensionsGA4": {"dimensionValues": [{}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "onRKXREI8izfGzv0", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "d2062aaa-e41b-4405-8470-9e7b4cd77245", "name": "Summarize Data", "type": "n8n-nodes-base.summarize", "position": [-1080, 300], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Auf<PERSON><PERSON>", "aggregation": "sum"}, {"field": "<PERSON><PERSON><PERSON>", "aggregation": "sum"}, {"field": "Sitzungen", "aggregation": "sum"}, {"field": "Sitzungen pro Nutzer", "aggregation": "average"}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aggregation": "average"}, {"field": "Käufe", "aggregation": "sum"}, {"field": "Revenue pro Kauf", "aggregation": "average"}, {"field": "Revenue", "aggregation": "sum"}, {"field": "date"}]}}, "typeVersion": 1}, {"id": "d1f48d36-9f27-4cda-af53-e6d430d1a8db", "name": "Summarize Data1", "type": "n8n-nodes-base.summarize", "position": [-220, 300], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Auf<PERSON><PERSON>", "aggregation": "sum"}, {"field": "<PERSON><PERSON><PERSON>", "aggregation": "sum"}, {"field": "Sitzungen", "aggregation": "sum"}, {"field": "Sitzungen pro Nutzer", "aggregation": "average"}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aggregation": "average"}, {"field": "Käufe", "aggregation": "sum"}, {"field": "Revenue pro Kauf", "aggregation": "average"}, {"field": "Revenue", "aggregation": "sum"}, {"field": "date"}]}}, "typeVersion": 1}, {"id": "5b6a0644-3839-4a62-8ff3-bf866aa4568c", "name": "Calculation same period previous year", "type": "n8n-nodes-base.code", "position": [-840, 300], "parameters": {"jsCode": "return {\n // Berechnung des Startdatums: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 7 Tage zurück\n startDate: (() => {\n const date = new Date();\n date.setFullYear(date.getFullYear() - 1); // Zurück ins Vorjahr\n date.setDate(date.getDate() - 7); // 7 Tage zurück\n return date.toISOString().split('T')[0];\n })(),\n \n // Berechnung des Enddatums: Vorjahr, heutiges Datum\n endDate: (() => {\n const date = new Date();\n date.setFullYear(date.getFullYear() - 1); // Zurück ins Vorjahr\n return date.toISOString().split('T')[0];\n })(),\n};\n"}, "typeVersion": 2}, {"id": "ab813532-cbe6-4c41-b20b-7efaa1ae4389", "name": "Assign data", "type": "n8n-nodes-base.set", "position": [-1300, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Auf<PERSON><PERSON>", "type": "number", "value": "={{ $json.screenPageViews }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "<PERSON><PERSON><PERSON>", "type": "number", "value": "={{ $json.totalUsers }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "Sitzungen", "type": "number", "value": "={{ $json.sessions }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "Sitzungen pro Nutzer", "type": "number", "value": "={{ $json.sessionsPerUser }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "value": "={{ $json.averageSessionDuration }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Käufe", "type": "number", "value": "={{ $json.ecommercePurchases }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Revenue pro Kauf", "type": "number", "value": "={{ $json.averagePurchaseRevenue }}"}, {"id": "94835d43-2fc8-49c0-97f0-6f0f8699337a", "name": "Revenue", "type": "number", "value": "={{ $json.purchaseRevenue }}"}, {"id": "d70f8138-3b84-4b88-a98f-eb929e1cc29a", "name": "date", "type": "string", "value": "={{ $json.date }}"}]}}, "typeVersion": 3.4}, {"id": "2454fe8a-005d-46dc-ae22-1044c1b793b7", "name": "Assign data1", "type": "n8n-nodes-base.set", "position": [-400, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Auf<PERSON><PERSON>", "type": "number", "value": "={{ $json.screenPageViews }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "<PERSON><PERSON><PERSON>", "type": "number", "value": "={{ $json.totalUsers }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "Sitzungen", "type": "number", "value": "={{ $json.sessions }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "Sitzungen pro Nutzer", "type": "number", "value": "={{ $json.sessionsPerUser }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "value": "={{ $json.averageSessionDuration }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Käufe", "type": "number", "value": "={{ $json.ecommercePurchases }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Revenue pro Kauf", "type": "number", "value": "={{ $json.averagePurchaseRevenue }}"}, {"id": "94835d43-2fc8-49c0-97f0-6f0f8699337a", "name": "Revenue", "type": "number", "value": "={{ $json.purchaseRevenue }}"}, {"id": "dd8255c6-65b1-41ce-b596-70c09108d6e2", "name": "=date", "type": "string", "value": "={{ $json.date }}"}]}}, "typeVersion": 3.4}, {"id": "0a48cbb0-3d4c-4ac8-8dba-08213f7fc430", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2220, 80], "parameters": {"width": 440, "height": 560, "content": "Welcome to my Google Analytics Weekly Report Workflow!\n\nThis workflow has the following sequence:\n\n1. time trigger (e.g. every Monday at 7 a.m.)\n2. retrieval of Google Analytics data from the last 7 days\n3. assignment and summary of the data\n4. retrieval of Google Analytics data from the last 7 days of the previous year\n5. allocation and summary of the data\n6. preparation in tabular form and brief analysis by AI.\n7. sending the report as an email\n8. preparation in short form by AI for Telegram (optional)\n9. sending as Telegram message.\n\nThe following accesses are required for the workflow:\n- Google Analytics (via Google Analytics API): https://docs.n8n.io/integrations/builtin/credentials/google/\n- AI API access (e.g. via OpenAI, Anthropic, Google or Ollama)\n- SMTP access data (for sending the mail)\n- Telegram access data (optional for sending as Telegram message): https://docs.n8n.io/integrations/builtin/credentials/telegram/\n\nYou can contact me via LinkedIn, if you have any questions: https://www.linkedin.com/in/friedemann-schuetz"}, "typeVersion": 1}, {"id": "c87bc648-8fe8-4cec-84d4-2742060f9c53", "name": "Processing for email", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [60, 80], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Please analyze the following data and output the results in tabular form:\n\n| Metrics | Last 7 days | Previous year | Percentage change |\n|-------------------------------|---------------|---------|\n| Total page views | {{ $('Summarize Data').item.json.sum_Aufrufe }} | {{ $('Summarize Data1').item.json.sum_Aufrufe }} | Percentage change |\n| total users | {{ $('Summarize Data').item.json.sum_Nutzer }} | {{ $('Summarize Data1').item.json.sum_Nutzer }} | Percentage change |\n| Total sessions | {{ $('Summarize Data').item.json.sum_Sitzungen }} | {{ $('Summarize Data1').item.json.sum_Sitzungen }} | Percentage change |\n| Average sessions/user | {{ $('Summarize Data').item.json.average_Sitzungen_pro_Nutzer }} | {{ $('Summarize Data1').item.json.average_Sitzungen_pro_Nutzer }} | Percentage change |\n| Average session duration | {{ $('Summarize Data').item.json.average_Sitzungsdauer }} | {{ $('Summarize Data1').item.json.average_Sitzungsdauer }} | Percentage change |\n| Total purchases | {{ $('Summarize Data').item.json['sum_Käufe'] }} | {{ $('Summarize Data1').item.json['sum_Käufe'] }} | Percentage change |\n| Average revenue/purchase | {{ $('Summarize Data').item.json.average_Revenue_pro_Kauf }} | {{ $('Summarize Data1').item.json.average_Revenue_pro_Kauf }} | Percentage change |\n| Total revenue | {{ $('Summarize Data').item.json.sum_Revenue }} | {{ $('Summarize Data1').item.json.sum_Revenue }} | Percentage change |\n\nFormat for numbers:\n- Dot (.) for numbers in thousands (e.g. 4,000)\n- Comma (,) for decimal numbers (e.g. 3.4)\n- Conversion of average session duration in minutes instead of seconds\n- Average turnover/purchase and total turnover in €\n\nPlease write a short summary of the analyzed data above the table (in a maximum of 3 sentences!)\n\nPlease format to a sleek and modern HTML format so that the result can be sent as HTML mail!\n\nStructure of the e-mail:\n\n“Hello! Here is the Weekly Report: Google Analytics of the last 7 days!\n[Summary]\n[Table]”"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "556c3292-0d40-4c75-8037-90bacf1b2ccb", "connections": {"Telegram": {"main": [[]]}, "Calculator": {"ai_tool": [[{"node": "Processing for email", "type": "ai_tool", "index": 0}]]}, "Assign data": {"main": [[{"node": "Summarize Data", "type": "main", "index": 0}]]}, "Assign data1": {"main": [[{"node": "Summarize Data1", "type": "main", "index": 0}]]}, "Summarize Data": {"main": [[{"node": "Calculation same period previous year", "type": "main", "index": 0}]]}, "Summarize Data1": {"main": [[{"node": "Processing for email", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Google Analytics Letzte 7 Tage", "type": "main", "index": 0}]]}, "Processing for email": {"main": [[{"node": "Send Email", "type": "main", "index": 0}, {"node": "Processing for Telegram", "type": "main", "index": 0}]]}, "Processing for Telegram": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Google Analytics Letzte 7 Tage": {"main": [[{"node": "Assign data", "type": "main", "index": 0}]]}, "Calculation same period previous year": {"main": [[{"node": "Google Analytics: Past 7 days of the previous year", "type": "main", "index": 0}]]}, "Google Analytics: Past 7 days of the previous year": {"main": [[{"node": "Assign data1", "type": "main", "index": 0}]]}}}