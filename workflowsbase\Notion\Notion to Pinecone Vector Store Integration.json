{"id": "vOSQYz747gtzj1zF", "meta": {"instanceId": "d16fb7d4b3eb9b9d4ad2ee6a7fbae593d73e9715e51f583c2a0e9acd1781c08e", "templateId": "2290"}, "name": "Prod: Notion to Vector Store - Dimension 768", "tags": [{"id": "Vs70y1mj5s2XzUap", "name": "Production", "createdAt": "2024-12-24T14:42:00.549Z", "updatedAt": "2024-12-24T14:42:00.549Z"}], "nodes": [{"id": "6d2579b8-376f-44c3-82e8-9dc608efd98b", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [2200, 800], "parameters": {"chunkSize": 256, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "79b3c147-08ca-4db4-9116-958a868cbfd9", "name": "Notion - Page Added Trigger", "type": "n8n-nodes-base.notionTrigger", "position": [1080, 360], "parameters": {"simple": false, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "databaseId": {"__rl": true, "mode": "list", "value": "17b11930-c10f-8000-a545-ece7cade03f9", "cachedResultUrl": "https://www.notion.so/17b11930c10f8000a545ece7cade03f9", "cachedResultName": "Embeddings"}}, "credentials": {"notionApi": {"id": "oktwaKqpFztx5hYX", "name": "Auto: Notion"}}, "typeVersion": 1}, {"id": "e4a6f524-e3f5-4d02-949a-8523f2d21965", "name": "Notion - Retrieve Page Content", "type": "n8n-nodes-base.notion", "position": [1300, 360], "parameters": {"blockId": {"__rl": true, "mode": "url", "value": "={{ $json.url }}"}, "resource": "block", "operation": "getAll", "returnAll": true}, "credentials": {"notionApi": {"id": "oktwaKqpFztx5hYX", "name": "Auto: Notion"}}, "typeVersion": 2.2}, {"id": "bfebc173-8d4b-4f8f-a625-4622949dd545", "name": "Filter Non-Text Content", "type": "n8n-nodes-base.filter", "position": [1520, 360], "parameters": {"options": {}, "conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e5b605e5-6d05-4bca-8f19-a859e474620f", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.type }}", "rightValue": "image"}, {"id": "c7415859-5ffd-4c78-b497-91a3d6303b6f", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.type }}", "rightValue": "video"}]}}, "typeVersion": 2}, {"id": "b04939f9-355a-430b-a069-b11800066313", "name": "Summarize - Concatenate Notion's blocks content", "type": "n8n-nodes-base.summarize", "position": [1780, 360], "parameters": {"options": {"outputFormat": "separateItems"}, "fieldsToSummarize": {"values": [{"field": "content", "separateBy": "\n", "aggregation": "concatenate"}]}}, "typeVersion": 1}, {"id": "0e64dbb5-20c1-4b90-b818-a1726aaf5112", "name": "Create metadata and load content", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [2180, 600], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "pageId", "value": "={{ $('Notion - Page Added Trigger').item.json.id }}"}, {"name": "createdTime", "value": "={{ $('Notion - Page Added Trigger').item.json.created_time }}"}, {"name": "pageTitle", "value": "={{ $('Notion - Page Added Trigger').item.json.properties.Name.title[0].text.content }}"}]}}, "jsonData": "={{ $json.concatenated_content }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "1f93c3e6-2d53-46b4-9ce9-1350e660ba82", "name": "Embeddings Google Gemini", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [1940, 580], "parameters": {"modelName": "models/text-embedding-004"}, "credentials": {"googlePalmApi": {"id": "9idxGZRZ3BAKDoxq", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b804b3fc-161c-40c1-ad9c-3022a09c4a0a", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [2060, 360], "parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "notion-pages", "cachedResultName": "notion-pages"}}, "credentials": {"pineconeApi": {"id": "R3QGXSEIRTEAZttK", "name": "Auto: PineconeApi"}}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "245f016a-7538-4f45-94f0-d8b7e5c9c891", "connections": {"Token Splitter": {"ai_textSplitter": [[{"node": "Create metadata and load content", "type": "ai_textSplitter", "index": 0}]]}, "Filter Non-Text Content": {"main": [[{"node": "Summarize - Concatenate Notion's blocks content", "type": "main", "index": 0}]]}, "Embeddings Google Gemini": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Notion - Page Added Trigger": {"main": [[{"node": "Notion - Retrieve Page Content", "type": "main", "index": 0}]]}, "Notion - Retrieve Page Content": {"main": [[{"node": "Filter Non-Text Content", "type": "main", "index": 0}]]}, "Create metadata and load content": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Summarize - Concatenate Notion's blocks content": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}}}