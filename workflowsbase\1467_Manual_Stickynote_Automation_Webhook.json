{"id": "GcSlNHOnN39cPhRA", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Google Search Engine Results Page Extraction with Bright Data", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "c40156b9-b7ba-449b-8362-f8b8cd27a36d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, -440], "parameters": {}, "typeVersion": 1}, {"id": "d98ae28e-a94f-43a1-9bfe-362adbc61c69", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [960, -240], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "984acfe6-acd7-4817-b2d5-6d2aab511bae", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1320, -440], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "6b5e26bf-8802-40d4-bc44-62c086c00f7c", "name": "Google Gemini Chat Model For Summarization", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1320, -260], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "1669f59a-eff8-41ad-a6eb-758eec7ed74a", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1620, -200], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "ad6c4a15-13e0-49fa-9048-bc1838ba0ef9", "name": "Webhook HTTP Request", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1960, -200], "parameters": {"url": "https://webhook.site/ce41e056-c097-48c8-a096-9b876d3abbf7", "method": "POST", "sendBody": true, "parametersBody": {"values": [{"name": "search_summary", "value": "={{ $json.response.text }}", "valueProvider": "fieldValue"}, {"name": "search_result"}]}, "toolDescription": "Extract the response and format a structured JSON response"}, "typeVersion": 1.1}, {"id": "dc5985c2-02cd-47d0-b518-8dc9d8302998", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [220, -780], "parameters": {"width": 400, "height": 300, "content": "## Bright Data Google Search SERP (Search Engine Results Page)\n\nDeals with the Google Search using the Bright Data Web Scraper API.\n\nThe Information Extraction, Summarization and AI Agent are being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to Set the Google Search Query and update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "38b1a20b-9d62-45d9-9399-0b927a6e882a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [720, -780], "parameters": {"width": 480, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nGoogle Search Data Extractor using the n8n Infromation Extractor node.\n\nSummarization Chain is being used for the summarization of search results.\n\nThe AI Agent formats the search result and pushes it to the Webhook via HTTP Request"}, "typeVersion": 1}, {"id": "3019d6eb-cf84-43fd-bb98-f7eed6c9c75f", "name": "Google Search Data Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [960, -440], "parameters": {"text": "={{ $json.data }}", "options": {"systemPromptTemplate": "You are an expert HTML extractor. Your job is to analyze the search result and \nstrip out the html, css, scripts and produce a textual data."}, "attributes": {"attributes": [{"name": "textual_response", "description": "Textual Response"}]}}, "typeVersion": 1}, {"id": "e82e62cf-6618-405a-943f-d2933771e051", "name": "Perform Google Search Request", "type": "n8n-nodes-base.httpRequest", "position": [720, -440], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "=https://www.google.com/search?q={{ encodeURI($json.search_query) }}"}, {"name": "format", "value": "raw"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "0d4baa4c-4f6d-4bb2-8964-73d9cf2a391c", "name": "Google Search Expert AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1680, -440], "parameters": {"text": "=You are an expert Google Search Expert. You need to format the search result  and push it to the Webhook via HTTP Request. Here is the search result - {{ $('Google Search Data Extractor').item.json.output.textual_response }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "433d4369-f750-40bd-8e46-8368f535e99f", "name": "Set Google Search Query", "type": "n8n-nodes-base.set", "position": [440, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3aedba66-f447-4d7a-93c0-8158c5e795f9", "name": "search_query", "type": "string", "value": "Bright Data"}, {"id": "4e7ee31d-da89-422f-8079-2ff2d357a0ba", "name": "zone", "type": "string", "value": "serp_api1"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3573d57f-de02-4ce6-bfdf-5e83a8a5d7d0", "connections": {"Summarization Chain": {"main": [[{"node": "Google Search Expert AI Agent", "type": "main", "index": 0}]]}, "Webhook HTTP Request": {"ai_tool": [[{"node": "Google Search Expert AI Agent", "type": "ai_tool", "index": 0}]]}, "Set Google Search Query": {"main": [[{"node": "Perform Google Search Request", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Google Search Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Google Search Expert AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Search Data Extractor": {"main": [[{"node": "Summarization Chain", "type": "main", "index": 0}]]}, "Google Search Expert AI Agent": {"main": [[]]}, "Perform Google Search Request": {"main": [[{"node": "Google Search Data Extractor", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Google Search Query", "type": "main", "index": 0}]]}, "Google Gemini Chat Model For Summarization": {"ai_languageModel": [[{"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}]]}}}