{"id": "PHp3gKoyYfSztbTB", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa", "templateCredsSetupCompleted": true}, "name": "Automated Daily Weather Data Fetcher and Storage", "tags": [{"id": "uScnF9NzR3PLIyvU", "name": "Published", "createdAt": "2025-03-21T07:22:28.491Z", "updatedAt": "2025-03-21T07:22:28.491Z"}], "nodes": [{"id": "871fd9fd-de44-4c9f-aef4-0c731c5685f1", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [40, 100], "parameters": {"rule": {"interval": [{"triggerAtHour": 10}]}}, "typeVersion": 1.2}, {"id": "0b721c2a-6301-4a08-9602-990598d0f7a3", "name": "Store Weather Data", "type": "n8n-nodes-base.airtable", "notes": "Store weather data in table\n", "position": [480, 100], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appKtypfMptBIKStp", "cachedResultUrl": "", "cachedResultName": "WeatherData"}, "table": {"__rl": true, "mode": "list", "value": "tblfb3sJ84eQUlYJd", "cachedResultUrl": "", "cachedResultName": "Data"}, "columns": {"value": {"Temp": "={{ $json.main.temp }}", "Humidity": "={{ $json.main.humidity }}", "Location": "={{ $json.name }}", "Timezone": "={{ $json.timezone }}", "Wind Speed": "={{ $json.wind.speed }}"}, "schema": [{"id": "Location", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Timezone", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Timezone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Temp", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Temp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Wind Speed", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Wind Speed", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 2.1}, {"id": "052a47c1-d167-432c-93f2-117a1c129c51", "name": "Get Weather Data", "type": "n8n-nodes-base.httpRequest", "notes": "Fetching the weather data", "position": [260, 100], "parameters": {"url": "https://api.openweathermap.org/data/2.5/weather?lat=23.0059&lon=72.5547", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "units", "value": "metric"}]}}, "credentials": {"httpBasicAuth": {"id": "zowZrB19NtOy4lxp", "name": "OpenWeatherAPi"}, "httpQueryAuth": {"id": "OVXHUjaqzUxECHEG", "name": "OpenWeatherMap Query <PERSON>"}, "httpHeaderAuth": {"id": "glJ33a6G5lqhm1CW", "name": "Shopify GraphQL Cred"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "525f3e92-c620-47f2-b97e-53cb98d63406", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 6, "width": 680, "height": 320, "content": "Automated Daily Weather Data Fetcher and Storage\n\n"}, "typeVersion": 1}, {"id": "cff8dbb0-3639-45a6-a06d-9ab63b2dfce8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 340], "parameters": {"color": 6, "width": 680, "height": 120, "content": "This workflow fetches weather data daily using the OpenWeatherMap API and stores the weather information in Airtable. The data can include current temperature, humidity, wind speed, and other relevant weather details. This automation ensures that the weather data is updated every day and stored for future reference, providing an easy-to-access historical record of the weather patterns."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ef874403-4189-4b92-a963-a02fc585cb77", "connections": {"Get Weather Data": {"main": [[{"node": "Store Weather Data", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get Weather Data", "type": "main", "index": 0}]]}}}