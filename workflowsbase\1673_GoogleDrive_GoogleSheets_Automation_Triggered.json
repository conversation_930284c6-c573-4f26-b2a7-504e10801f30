{"id": "s8YgrWCxnGJxbctt", "meta": {"instanceId": "2b1c62c6d8c9216d51c1f40c64044e24b558ea8311c19d032d1278472159cfec", "templateId": "1750"}, "name": "Google Doc Summarizer to Google Sheets", "tags": [], "nodes": [{"id": "9098b59a-68b1-48bd-9b52-41a971e689b3", "name": "Google Docs", "type": "n8n-nodes-base.googleDocs", "position": [340, 240], "parameters": {"operation": "get", "documentURL": "={{ $json.id }}", "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "Xx4ObVZ3yYoA5XCx", "name": "Google Drive account"}}, "typeVersion": 2}, {"id": "a7f224d4-232b-4201-82a0-d762830b546a", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [680, 180], "parameters": {}, "typeVersion": 1}, {"id": "12bb798e-fe7e-4340-846b-5caeb824959b", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [940, 180], "parameters": {}, "typeVersion": 1}, {"id": "7d479725-f973-45c5-a798-d1868aefdd82", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1280, 280], "parameters": {"columns": {"value": {"Name": "={{ $('Google Drive ').item.json.lastModifyingUser.displayName }}", "Email ": "={{ $('Google Drive ').item.json.lastModifyingUser.emailAddress }}", "Summarise Conetent data ": "={{ $json.message.content }}"}, "schema": [{"id": "Email ", "type": "string", "display": true, "required": false, "displayName": "Email ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Summarise Conetent data ", "type": "string", "display": true, "required": false, "displayName": "Summarise Conetent data ", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s1v58pqGaVha9g_evNX4UEMchzteO7CyLNp87tcKJ1Q/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1s1v58pqGaVha9g_evNX4UEMchzteO7CyLNp87tcKJ1Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1s1v58pqGaVha9g_evNX4UEMchzteO7CyLNp87tcKJ1Q/edit?usp=drivesdk", "cachedResultName": "Docs Summarise Data"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "A2b2I9leWjfYSzSW", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "35716e44-14e7-4cc3-a273-2ba2e749892f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-80, -80], "parameters": {"color": 5, "height": 260, "content": "## Get Latest File\n"}, "typeVersion": 1}, {"id": "fc3ac84f-887f-4908-a870-e6c3d46f4576", "name": "Google Drive ", "type": "n8n-nodes-base.googleDriveTrigger", "notes": "Received the doc", "position": [0, 0], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1H8Xe2uIO0sI-QdxFsDH0Yg_w9RaPOoD_", "cachedResultUrl": "https://drive.google.com/drive/folders/1H8Xe2uIO0sI-QdxFsDH0Yg_w9RaPOoD_", "cachedResultName": "yash<PERSON>"}, "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "Xx4ObVZ3yYoA5XCx", "name": "Google Drive account"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "14f0c78f-73c7-42c4-8916-284a876659cb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [260, 140], "parameters": {"color": 5, "width": 260, "height": 260, "content": "## Get Document Content\n"}, "typeVersion": 1}, {"id": "6c87fc48-6b22-46fb-a509-d2037dc302bc", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [620, -60], "parameters": {"color": 5, "width": 440, "height": 380, "content": "## AI Summarization\n"}, "typeVersion": 1}, {"id": "bcf259bd-df2a-4a16-a679-3a5d3ee68122", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1160, 160], "parameters": {"color": 5, "width": 300, "height": 280, "content": "## Store Summary in Sheet\n"}, "typeVersion": 1}, {"id": "81f80bd2-aa10-49a8-ae63-3a3322bcac80", "name": "Generate Summary AI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [700, 20], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Summarise the below content\n {{ $json.content }}"}]}}, "credentials": {"openAiApi": {"id": "aMNetdb7Sh3K62cJ", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "f7379ef9-9940-4aec-9717-b7df688fd2df", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [240, -260], "parameters": {"color": 5, "width": 800, "height": 80, "content": "# Google Doc Summarizer to Google Sheets\n"}, "typeVersion": 1}, {"id": "0bf7d344-64ad-4074-8e7c-20055a3bf082", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-20, 500], "parameters": {"color": 5, "width": 1280, "content": "## Description\nThis workflow is created by WeblineIndia, it streamlines and automates the end-to-end process of managing recently added document files in Google Drive. It begins by identifying the most recently uploaded .doc file in a designated folder within Google Drive. The document's content is then directly retrieved and passed through an AI-powered summarization model that condenses the content into a concise and meaningful summary. Finally, the summarized content, along with relevant metadata such as the document's name, upload date, and other details, is systematically stored in a Google Sheet. This ensures easy reference, enhanced organization, and quick access to key information, making it an ideal solution for managing and summarizing large volumes of document data efficiently."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e3318ab1-ef09-4207-9419-411208c35aab", "connections": {"Wikipedia": {"ai_tool": [[{"node": "Generate Summary AI", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Generate Summary AI", "type": "ai_tool", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Generate Summary AI", "type": "main", "index": 0}]]}, "Google Drive ": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Generate Summary AI": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}