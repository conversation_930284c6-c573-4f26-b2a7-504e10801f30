{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "d9bae984-2ce7-4f6b-ab53-527ac9dfea3d", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [680, 320], "parameters": {}, "typeVersion": 1}, {"id": "32ecf73c-b6e9-4bd6-9ecc-d82c4c50d7b5", "name": "Reddit", "type": "n8n-nodes-base.reddit", "position": [880, 320], "parameters": {"keyword": "n8n", "location": "allReddit", "operation": "search", "additionalFields": {"sort": "new"}}, "credentials": {}, "typeVersion": 1}, {"id": "4b560620-a101-4566-b066-4ce3f44d8b0c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [120, 180], "parameters": {"width": 507.1052631578949, "height": 210.99380804953552, "content": "## What this workflow does\n✔︎ 1) Get posts from reddit that might be about n8n\n - Filter for the most relevant posts (posted in last 7 days and more than 5 upvotes and is original content)\n\n✔︎ 2) Check if the post is actually about n8n\n\n✔︎ 3) if it is, categorise with OpenAi.\n"}, "typeVersion": 1}, {"id": "f3be9af5-b4ff-4f4e-a726-fc05fab94521", "name": "Set", "type": "n8n-nodes-base.set", "position": [1260, 320], "parameters": {"values": {"number": [{"name": "upvotes", "value": "={{ $json.ups }}"}, {"name": "subredditSize", "value": "={{ $json.subreddit_subscribers }}"}], "string": [{"name": "selftextTrimmed", "value": "={{ $json.selftext.substring(0,500) }}"}, {"name": "subreddit", "value": "={{ $json.subreddit }}"}, {"name": "date", "value": "={{ DateTime.fromSeconds($json.created).toLocaleString() }}"}, {"name": "url", "value": "={{ $json.url }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "b1dbf78f-c7c6-4ab7-a957-78d58c5e13e3", "name": "IF", "type": "n8n-nodes-base.if", "position": [1060, 320], "parameters": {"conditions": {"number": [{"value1": "={{ $json.ups }}", "value2": "=5", "operation": "largerEqual"}], "string": [{"value1": "={{ $json.selftext }}", "operation": "isNotEmpty"}], "dateTime": [{"value1": "={{ DateTime.fromSeconds($json.created).toISO() }}", "value2": "={{ $today.minus({days: 7}).toISO() }}"}]}}, "typeVersion": 1}, {"id": "a3aa9e43-a824-4cc1-b4e6-d41a2e8e56cd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [120, 660], "parameters": {"width": 504.4736842105267, "height": 116.77974205725066, "content": "## Drawbacks\n🤔 Workflow only considers first 500 characters of each reddit post. So if n8n is mentioned after this amount, it won't register as being a post about n8n.io."}, "typeVersion": 1}, {"id": "b3d566aa-1645-4c2c-9704-15aa2e42bb12", "name": "IF1", "type": "n8n-nodes-base.if", "position": [1880, 340], "parameters": {"conditions": {"string": [{"value1": "={{ $json.choices[0].text }}", "value2": "No", "operation": "contains"}]}}, "typeVersion": 1}, {"id": "0ad54272-08b9-46d4-8e6a-1fb55a92d3e4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1680, 520], "parameters": {"mode": "combine", "options": {"fuzzyCompare": false, "includeUnpaired": true}, "combinationMode": "mergeByPosition"}, "typeVersion": 2}, {"id": "288f53cc-0e53-4683-ac0e-debe0a3691b8", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [2340, 540], "parameters": {"mode": "combine", "options": {"fuzzyCompare": false, "includeUnpaired": true}, "combinationMode": "mergeByPosition"}, "typeVersion": 2}, {"id": "46280db5-e4b0-4108-958a-763b6410caa0", "name": "SetFinal", "type": "n8n-nodes-base.set", "position": [2560, 540], "parameters": {"values": {"number": [{"name": "upvotes", "value": "={{ $json.upvotes }}"}, {"name": "subredditSize", "value": "={{ $json.subredditSize }}"}], "string": [{"name": "subreddit", "value": "={{ $json.subreddit }}"}, {"name": "bulletSummary", "value": "={{ $json.text }}"}, {"name": "date", "value": "={{ $json.date }}"}, {"name": "url", "value": "={{ $json.url }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "ac8c4847-4d73-4dce-9543-a199e8b11b51", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [120, 400], "parameters": {"width": 507.1052631578949, "height": 247.53869969040255, "content": "## Next steps\n* Improve OpenAI Summary node prompt to return cleaner summaries.\n* Extend to **more platforms/sources** - e.g. it would be really cool to monitor larger slack communities in this way. \n* Do some classification on type of user to highlight users likely to be in our **ICP**.\n* Separate a list of data sources (reddit, twitter, slack, discord etc.), extract messages from there and have them go to a **sub workflow for classification and summarisation.**"}, "typeVersion": 1}, {"id": "12ab5ba4-d24d-4fa1-a0d1-d1e81e2d5dee", "name": "OpenAI Summary", "type": "n8n-nodes-base.openAi", "notes": "A one sentence summary of what the post is about.", "disabled": true, "position": [2160, 160], "parameters": {"input": "={{ $json.selftextTrimmed }}", "options": {"temperature": 0.3}, "operation": "edit", "instruction": "Summarise what this is talking about in a meta way less than 20 words. Ignore punctuation in your summary and return a short, human readable summary."}, "credentials": {}, "typeVersion": 1}, {"id": "e303a1aa-ee93-4f8f-b834-19aa8da7fe95", "name": "OpenAI Classify", "type": "n8n-nodes-base.openAi", "notes": "Is the post about n8n?", "position": [1460, 320], "parameters": {"prompt": "=Decide whether a reddit post is about n8n.io, a workflow automation low code tool that can be self-hosted, or not.\nReddit Post: {{ $json.selftextTrimmed }}\nAbout n8n?: Yes/No", "options": {"maxTokens": 32}, "simplifyOutput": false}, "credentials": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "f56cb8b6-4c28-448e-b259-8946ffc4c1f7", "name": "OpenAI Summary Backup", "type": "n8n-nodes-base.openAi", "notes": "A one sentence summary of what the post is about.", "position": [2160, 340], "parameters": {"prompt": "=Summarise what this is talking about in a meta way in only 1 sentence.\n\n {{ $json.selftextTrimmed }}", "options": {"maxTokens": 128}}, "credentials": {}, "typeVersion": 1}, {"id": "d1eacbf2-9cc8-482d-a7d2-34c351f20871", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [640, 520], "parameters": {"width": 843.411496498402, "height": 258.************, "content": "## What we learned\n- 🪶 **Writing prompts**: small changes in the type of prompt result in very different results. e.g. for Summarising OpenAI would use multiple sentences even if we asked it to use only 1. We got better results by following OpenAI's documentation.\n - We could make OpenAI node easier to work with for new users by the node inputs being oriented not to sending parameters to api but by following [their suggestions](https://platform.openai.com/docs/guides/completion/prompt-design) - e.g. have a field for expected output format rather than just for prompt.\n- ↕️ **Changing the max_tokens parameter** drastically changes results - sometimes making it smaller even improves results (e.g. when you want a yes/no response in the OpenAI Classify node). In their [docs](https://platform.openai.com/docs/guides/completion/inserting-text) they recommend using max_tokens>256 but [n8n by default](https://community.n8n.io/t/openai-result-not-complete/21533) uses max_tokens=16. We should probably update this."}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "IF1": {"main": [null, [{"node": "OpenAI Summary Backup", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Set": {"main": [[{"node": "OpenAI Classify", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "SetFinal", "type": "main", "index": 0}]]}, "Reddit": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "OpenAI Classify": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI Summary Backup": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Reddit", "type": "main", "index": 0}]]}}}