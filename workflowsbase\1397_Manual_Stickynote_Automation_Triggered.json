{"id": "q2MJWAqpKF2BCJkq", "meta": {"instanceId": "021d3c82ba2d3bc090cbf4fc81c9312668bcc34297e022bb3438c5c88a43a5ff"}, "name": "LangChain - Example - Code Node Example", "tags": [{"id": "snf16n0p2UrGP838", "name": "LangChain - Example", "createdAt": "2023-09-25T16:21:55.962Z", "updatedAt": "2023-09-25T16:21:55.962Z"}], "nodes": [{"id": "ad1a920e-1048-4b58-9c4a-a0469a1f189d", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmOpenAi", "position": [900, 628], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4jRB4A20cPycBqP5", "name": "OpenAI account - n8n"}}, "typeVersion": 1}, {"id": "7dd04ecd-f169-455c-9c90-140140e37542", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [800, 340], "parameters": {"width": 432, "height": 237, "content": "## Self-coded LLM Chain Node"}, "typeVersion": 1}, {"id": "05ad7d68-5dc8-42f2-8274-fcb5bdeb68cb", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [280, 428], "parameters": {}, "typeVersion": 1}, {"id": "39e2fd34-3261-44a1-aa55-96f169d55aad", "name": "Set", "type": "n8n-nodes-base.set", "position": [620, 428], "parameters": {"values": {"string": [{"name": "input", "value": "Tell me a joke"}]}, "options": {}}, "typeVersion": 2}, {"id": "42a3184c-0c62-4e79-9220-7a93e313317e", "name": "Set1", "type": "n8n-nodes-base.set", "position": [620, 820], "parameters": {"values": {"string": [{"name": "input", "value": "What year was <PERSON> born?"}]}, "options": {}}, "typeVersion": 2}, {"id": "4e2af29d-7fc4-484b-8028-1b9a84d60172", "name": "Chat OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [731, 1108], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4jRB4A20cPycBqP5", "name": "OpenAI account - n8n"}}, "typeVersion": 1}, {"id": "334e9176-3a18-4838-84cb-70e8154f1a30", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [880, 1028], "parameters": {"width": 320.*************, "height": 231, "content": "## Self-coded Tool Node"}, "typeVersion": 1}, {"id": "05e0d5c6-df18-42ba-99b6-a2b65633a14d", "name": "Custom - Wikipedia", "type": "@n8n/n8n-nodes-langchain.code", "position": [971, 1108], "parameters": {"code": {"supplyData": {"code": "console.log('Custom Wikipedia Node runs');\nconst { WikipediaQueryRun } = require('langchain/tools');\nreturn new WikipediaQueryRun();"}}, "outputs": {"output": [{"type": "ai_tool"}]}}, "typeVersion": 1}, {"id": "9c729e9a-f173-430c-8bcd-74101b614891", "name": "Custom - LLM Chain Node", "type": "@n8n/n8n-nodes-langchain.code", "position": [880, 428], "parameters": {"code": {"execute": {"code": "const { PromptTemplate } = require('langchain/prompts');\n\nconst query = $input.item.json.input;\nconst prompt = PromptTemplate.fromTemplate(query);\nconst llm = await this.getInputConnectionData('ai_languageModel', 0);\nlet chain = prompt.pipe(llm);\nconst output = await chain.invoke();\nreturn [ {json: { output } } ];"}}, "inputs": {"input": [{"type": "main"}, {"type": "ai_languageModel", "required": true, "maxConnections": 1}]}, "outputs": {"output": [{"type": "main"}]}}, "typeVersion": 1}, {"id": "6427bbf0-49a6-4810-9744-87d88151e914", "name": "Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [880, 820], "parameters": {"options": {}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e14a709d-08fe-4ed7-903a-fb2bae80b28a", "connections": {"Set": {"main": [[{"node": "Custom - LLM Chain Node", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Agent", "type": "main", "index": 0}]]}, "OpenAI": {"ai_languageModel": [[{"node": "Custom - LLM Chain Node", "type": "ai_languageModel", "index": 0}]]}, "Chat OpenAI": {"ai_languageModel": [[{"node": "Agent", "type": "ai_languageModel", "index": 0}]]}, "Custom - Wikipedia": {"ai_tool": [[{"node": "Agent", "type": "ai_tool", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set", "type": "main", "index": 0}, {"node": "Set1", "type": "main", "index": 0}]]}}}