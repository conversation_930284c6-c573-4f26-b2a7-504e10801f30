{"id": "knmxcsujuHmViJl4", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "name": "Online Marketing Weekly Report", "tags": [], "nodes": [{"id": "f145b442-3036-4769-b7ad-62c97fa5d662", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1580, -220], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "9b25dfe7-cabe-4051-8d03-5f9b1f42621a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1380, 120], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "f04d67a6-fb37-434d-8fd7-988db33f8cde", "name": "Google_Ads", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-400, 120], "parameters": {"name": "Google_Ads", "workflowId": {"__rl": true, "mode": "list", "value": "nRGs7Ogv7eU1mJQl", "cachedResultName": "Google Ads Report: Weekly Subflow ROAS"}, "description": "Call this tool to get the output of the Google Ads Workflow"}, "typeVersion": 1.3}, {"id": "179c2464-bf17-4a9b-8c5b-508a4acf746f", "name": "Meta_Ads", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-300, 120], "parameters": {"name": "Meta_Ads", "workflowId": {"__rl": true, "mode": "list", "value": "9sC80Rt1eqgrfphk", "cachedResultName": "Meta Ads Report: Weekly Subflow ROAS"}, "description": "Call this tool to get the output of the Meta Ads Workflow"}, "typeVersion": 1.3}, {"id": "f963f694-cf30-4b81-9ee1-3ad383714770", "name": "Analytics_Domain_1", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-1180, 120], "parameters": {"name": "EP_Data", "workflowId": {"__rl": true, "mode": "list", "value": "ploQFf5BtgCC6ryu", "cachedResultName": "GA Report: EP Subflow Weekly"}, "description": "Call this tool to get the output of the SER Data Workflow"}, "typeVersion": 1.3}, {"id": "093d83fd-6788-418b-ac38-925b04be2515", "name": "Analytics_Domain_3", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-860, 120], "parameters": {"name": "SBW_Data", "workflowId": {"__rl": true, "mode": "list", "value": "ECmFUVocSLqB3afJ", "cachedResultName": "GA Report: SBW Subflow Weekly"}, "description": "Call this tool to get the output of the SBW Data Workflow"}, "typeVersion": 1.3}, {"id": "57612276-4a48-4a6c-997c-f6c4f1196ed7", "name": "Analytics_Domain_2", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-1020, 120], "parameters": {"name": "SER_Data", "workflowId": {"__rl": true, "mode": "list", "value": "EWAE7Qx70cHZuXte", "cachedResultName": "GA Report: SER Subflow Weekly"}, "description": "Call this tool to get the output of the SER Data Workflow"}, "typeVersion": 1.3}, {"id": "3dfea4cf-87fb-44d5-9b95-304406da6b18", "name": "Analytics_Domain_4", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-700, 120], "parameters": {"name": "SZO_Data", "workflowId": {"__rl": true, "mode": "list", "value": "eyPh3eaqrBLAcLKF", "cachedResultName": "GA Report: SZO Subflow Weekly"}, "description": "Call this tool to get the output of the SZO Data Workflow"}, "typeVersion": 1.3}, {"id": "79f1cd20-6133-4c72-8942-5b1f55adbb7f", "name": "Analytics_Domain_5", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-540, 120], "parameters": {"name": "UCH_Data", "workflowId": {"__rl": true, "mode": "list", "value": "ErIeoUuyF4fqMbhL", "cachedResultName": "GA Report: UCH Subflow Weekly"}, "description": "Call this tool to get the output of the UCH Data Workflow"}, "typeVersion": 1.3}, {"id": "0740d70f-a582-4a12-ab12-08b46504e8f8", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-1580, 460], "parameters": {}, "typeVersion": 1}, {"id": "2449b1be-0ba8-41ae-9ea5-86028da736f0", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [380, 680], "parameters": {}, "typeVersion": 1}, {"id": "e8b884f1-2013-4c25-a641-8181528db173", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 360], "parameters": {"color": 6, "width": 2340, "height": 460, "content": "## Sub-Workflow: Google Analytics Data"}, "typeVersion": 1}, {"id": "f7d290eb-07d1-413d-ba12-184229e519c0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 1440], "parameters": {"color": 5, "width": 2340, "height": 460, "content": "## Sub-Workflow: Meta Ads Data"}, "typeVersion": 1}, {"id": "c78da3ec-fb47-4373-8850-e1af89d7330e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1660, -280], "parameters": {"width": 2340, "height": 540, "content": "## Main Workflow: Weekly Report Assistant"}, "typeVersion": 1}, {"id": "79ae6b22-0067-4d51-bc64-e0360d2096f3", "name": "Calculator1", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [440, 1200], "parameters": {}, "typeVersion": 1}, {"id": "d7608c67-4b10-4860-b683-de265b95cb03", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1660, 880], "parameters": {"color": 4, "width": 2340, "height": 500, "content": "## Sub-Workflow: Google Ads Data"}, "typeVersion": 1}, {"id": "c047cc54-1598-4294-a398-cdb23ea8be4d", "name": "Calculator3", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [460, 1760], "parameters": {}, "typeVersion": 1}, {"id": "165717a8-b215-4e0e-a5e9-586feaec261e", "name": "Call Google Analytics data: Last 7 days", "type": "n8n-nodes-base.googleAnalytics", "position": [-1320, 460], "parameters": {"metricsGA4": {"metricValues": [{"listName": "screenPageViews"}, {}, {"listName": "sessions"}, {"listName": "<PERSON><PERSON>er<PERSON>ser"}, {"name": "averageSessionDuration", "listName": "other"}, {"name": "ecommercePurchases", "listName": "other"}, {"name": "averagePurchaseRevenue", "listName": "other"}, {"name": "purchaseRevenue", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "list", "value": "*********", "cachedResultUrl": "https://analytics.google.com/analytics/web/#/p*********/", "cachedResultName": "https://www.ep-reisen.de  – GA4"}, "dimensionsGA4": {"dimensionValues": [{}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "KJj8aAwdrmU2STrI", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "da636e2a-4396-4809-98ea-c04abdfc6b73", "name": "Call Google Analytics data: Last 7 days (previous year)", "type": "n8n-nodes-base.googleAnalytics", "position": [-400, 460], "parameters": {"endDate": "={{ $json.endDate }}", "dateRange": "custom", "startDate": "={{ $json.startDate }}", "metricsGA4": {"metricValues": [{"listName": "screenPageViews"}, {}, {"listName": "sessions"}, {"listName": "<PERSON><PERSON>er<PERSON>ser"}, {"name": "averageSessionDuration", "listName": "other"}, {"name": "ecommercePurchases", "listName": "other"}, {"name": "averagePurchaseRevenue", "listName": "other"}, {"name": "purchaseRevenue", "listName": "other"}]}, "propertyId": {"__rl": true, "mode": "list", "value": "*********", "cachedResultUrl": "https://analytics.google.com/analytics/web/#/p*********/", "cachedResultName": "https://www.ep-reisen.de  – GA4"}, "dimensionsGA4": {"dimensionValues": [{}]}, "additionalFields": {}}, "credentials": {"googleAnalyticsOAuth2": {"id": "KJj8aAwdrmU2STrI", "name": "Google Analytics account"}}, "typeVersion": 2}, {"id": "f1ac5ff4-1b09-442f-96b0-4f1562dffebb", "name": "Calculation same period previous year", "type": "n8n-nodes-base.code", "position": [-620, 980], "parameters": {"jsCode": "// Aktuelles Datum\nconst today = new Date();\n\n// Berechnung des Enddatums (letzter Tag vor dem aktuellen Datum im Vorjahr)\nconst end = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate() - 1);\n\n// Berechnung des Startdatums (7 Tage vor dem Enddatum)\nconst start = new Date(end);\nstart.setDate(end.getDate() - 6);\n\n// Formatierung zu YYYYMMDD\nfunction formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}${month}${day}`;\n}\n\n// Ausgabe\nconst startDate = formatDate(start);\nconst endDate = formatDate(end);\n\nreturn {\n    startDate,\n    endDate\n};"}, "typeVersion": 2}, {"id": "bc224327-ca75-451a-841f-7cd8c59c5925", "name": "Format data input (previous year)", "type": "n8n-nodes-base.code", "position": [-240, 980], "parameters": {"jsCode": "const inputData = items[0].json.results;\nconst totals = {\n   impressions: 0,\n   clicks: 0,\n   conversions: 0,\n   costMicros: 0,\n   conversionsValue: 0\n};\n\ninputData.forEach(campaign => {\n   totals.impressions += parseInt(campaign.metrics.impressions) || 0;\n   totals.clicks += parseInt(campaign.metrics.clicks) || 0;\n   totals.conversions += parseFloat(campaign.metrics.conversions) || 0;\n   totals.costMicros += parseInt(campaign.metrics.costMicros) || 0;\n   totals.conversionsValue += parseFloat(campaign.metrics.conversionsValue) || 0;\n});\n\nconst results = {\n   impressions: totals.impressions,\n   clicks: totals.clicks,\n   conversions: totals.conversions,\n   cost_micros: totals.costMicros,\n   ctr: totals.clicks / totals.impressions,\n   cost_per_conversion: totals.costMicros / totals.conversions,\n   cpm: (totals.costMicros / (totals.impressions / 1000)),\n   roas: totals.conversionsValue / (totals.costMicros / 1000000)\n};\nreturn results;"}, "typeVersion": 2}, {"id": "5b9dbc30-4a92-43d2-b9bf-477141aa4024", "name": "Format data input (current year)", "type": "n8n-nodes-base.code", "position": [-1200, 980], "parameters": {"jsCode": "const inputData = items[0].json.results;\nconst totals = {\n    impressions: 0,\n    clicks: 0,\n    conversions: 0,\n    costMicros: 0,\n    conversionsValue: 0\n};\n\ninputData.forEach(campaign => {\n    totals.impressions += parseInt(campaign.metrics.impressions) || 0;\n    totals.clicks += parseInt(campaign.metrics.clicks) || 0; \n    totals.conversions += parseFloat(campaign.metrics.conversions) || 0;\n    totals.costMicros += parseInt(campaign.metrics.costMicros) || 0;\n    totals.conversionsValue += parseFloat(campaign.metrics.conversionsValue) || 0;\n});\n\nconst results = {\n    impressions: totals.impressions,\n    clicks: totals.clicks,\n    conversions: totals.conversions, \n    cost_micros: totals.costMicros,\n    ctr: totals.clicks / totals.impressions,\n    cost_per_conversion: totals.costMicros / totals.conversions,\n    cpm: (totals.costMicros / (totals.impressions / 1000)),\n    roas: totals.conversionsValue / (totals.costMicros / 1000000)\n};\n\nreturn results;"}, "typeVersion": 2}, {"id": "fdb70b1a-8891-469f-9920-3623cb40760e", "name": "Assign data from input (current year)", "type": "n8n-nodes-base.set", "position": [-1020, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Impressions", "type": "number", "value": "={{ $json.impressions }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "CPM", "type": "number", "value": "={{ $json.cpm }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "<PERSON>licks", "type": "number", "value": "={{ $json.clicks }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "CTR", "type": "number", "value": "={{ $json.ctr }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Conversions", "type": "number", "value": "={{ $json.conversions }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Cost per Conversion", "type": "number", "value": "={{ $json.cost_per_conversion }}"}, {"id": "4b5d569a-26c9-4a2f-be48-6814860d33c1", "name": "ROAS", "type": "number", "value": "={{ $json.roas }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Spend", "type": "number", "value": "={{ $json.cost_micros }}"}]}}, "typeVersion": 3.4}, {"id": "bef77ef4-b6ef-48c3-b0b7-5a12a3f75b05", "name": "Summarize input (current year)", "type": "n8n-nodes-base.summarize", "position": [-820, 980], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Impressions", "aggregation": "sum"}, {"field": "CPM", "aggregation": "average"}, {"field": "<PERSON>licks", "aggregation": "sum"}, {"field": "CTR", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Cost per Conversion", "aggregation": "average"}, {"field": "Spend", "aggregation": "sum"}, {"field": "ROAS", "aggregation": "average"}]}}, "typeVersion": 1}, {"id": "2be072fe-24fd-47f5-8026-31b98666de84", "name": "Assign input (previous year)", "type": "n8n-nodes-base.set", "position": [-80, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Impressions", "type": "number", "value": "={{ $json.impressions }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "CPM", "type": "number", "value": "={{ $json.cpm }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "<PERSON>licks", "type": "number", "value": "={{ $json.clicks }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "CTR", "type": "number", "value": "={{ $json.ctr }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Conversions", "type": "number", "value": "={{ $json.conversions }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Cost per conversion", "type": "number", "value": "={{ $json.cost_per_conversion }}"}, {"id": "76bda144-0cb4-4614-a658-ddb31726ecb9", "name": "ROAS", "type": "number", "value": "={{ $json.roas }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Spend", "type": "number", "value": "={{ $json.cost_micros }}"}]}}, "typeVersion": 3.4}, {"id": "87381409-c9e5-4d1f-a118-a9e592fa8805", "name": "Summarize input (previous year)", "type": "n8n-nodes-base.summarize", "position": [100, 980], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Impressions", "aggregation": "sum"}, {"field": "CPM", "aggregation": "average"}, {"field": "<PERSON>licks", "aggregation": "sum"}, {"field": "CTR", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Cost per conversion", "aggregation": "average"}, {"field": "Spend", "aggregation": "sum"}, {"field": "ROAS", "aggregation": "average"}]}}, "typeVersion": 1}, {"id": "000507e0-7806-4686-b157-56e9d58bd9db", "name": "Calculate date format for Google Ads request (last 7 days)", "type": "n8n-nodes-base.code", "position": [-1600, 980], "parameters": {"jsCode": "// Aktuelles Datum\nconst today = new Date();\n\n// Berechnung des Enddatums (letzter Tag vor dem aktuellen Datum)\nconst end = new Date(today);\nend.setDate(today.getDate() - 1);\n\n// Berechnung des Startdatums (7 Tage vor dem Enddatum)\nconst start = new Date(end);\nstart.setDate(end.getDate() - 6);\n\n// Formatierung zu YYYYMMDD\nfunction formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}${month}${day}`;\n}\n\n// Ausgabe\nconst startDate = formatDate(start);\nconst endDate = formatDate(end);\n\nreturn { startDate, endDate };"}, "typeVersion": 2}, {"id": "a1b4d36a-c322-4771-bcf3-226870fcf000", "name": "Call Google Ads Data: Last 7 days", "type": "n8n-nodes-base.httpRequest", "position": [-1380, 980], "parameters": {"url": "https://googleads.googleapis.com/v17/customers/**********/googleAds:search", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "query", "value": "=SELECT\n   campaign.name,\n   metrics.impressions,\n   metrics.average_cpm,\n   metrics.clicks, \n   metrics.ctr,\n   metrics.conversions,\n   metrics.cost_per_conversion,\n   metrics.cost_micros,\n   metrics.conversions_value\nFROM campaign\nWHERE segments.date >= '{{ $json.startDate }}' AND segments.date <= '{{ $json.endDate }}'"}]}, "headerParameters": {"parameters": [{"name": "developer-token", "value": "fzQ2U5IcU4ZH0vBDn4Slww"}]}, "nodeCredentialType": "googleAdsOAuth2Api"}, "credentials": {"googleOAuth2Api": {"id": "yNLz0qjgW7jN4Osv", "name": "Google account"}, "googleAdsOAuth2Api": {"id": "gEPlb6nlwRX35x6R", "name": "Google Ads account"}}, "typeVersion": 4.2}, {"id": "7246c0af-4e9d-4037-bc1d-c4fd16aae7d3", "name": "Call Google Ads Data: Last 7 days (previous year)", "type": "n8n-nodes-base.httpRequest", "position": [-420, 980], "parameters": {"url": "https://googleads.googleapis.com/v17/customers/**********/googleAds:search", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "query", "value": "=SELECT\n   campaign.name,\n   metrics.impressions,\n   metrics.average_cpm, \n   metrics.clicks,\n   metrics.ctr,\n   metrics.conversions,\n   metrics.cost_per_conversion,\n   metrics.cost_micros,\n   metrics.conversions_value\nFROM campaign\nWHERE segments.date >= '{{ $json.startDate }}' AND segments.date <= '{{ $json.endDate }}'"}]}, "headerParameters": {"parameters": [{"name": "developer-token", "value": "fzQ2U5IcU4ZH0vBDn4Slww"}]}, "nodeCredentialType": "googleAdsOAuth2Api"}, "credentials": {"googleOAuth2Api": {"id": "yNLz0qjgW7jN4Osv", "name": "Google account"}, "googleAdsOAuth2Api": {"id": "gEPlb6nlwRX35x6R", "name": "Google Ads account"}}, "typeVersion": 4.2}, {"id": "13074a3d-6cf4-4ad7-9e26-002f8120e640", "name": "Call Meta Ads Data: Last 7 days", "type": "n8n-nodes-base.facebookGraphApi", "position": [-1380, 1540], "parameters": {"edge": "insights", "node": "act_54337533", "options": {"queryParametersJson": "={\n \"fields\": \"impressions,cpm,inline_link_clicks,inline_link_click_ctr,conversions,cost_per_conversion,spend,action_values,purchase_roas\",\n \"time_range\": {\n   \"since\": \"{{ $json.currentPeriod.since }}\",\n   \"until\": \"{{ $json.currentPeriod.until }}\"\n }\n}"}, "graphApiVersion": "v20.0"}, "credentials": {"facebookGraphApi": {"id": "9q3YgP6zvgKBO4aj", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "06a32368-f9f1-4311-bd17-3fea6b0d2888", "name": "Call Meta Ads Data: Last 7 days (previous year)", "type": "n8n-nodes-base.facebookGraphApi", "position": [-640, 1540], "parameters": {"edge": "insights", "node": "act_54337533", "options": {"queryParametersJson": "={\n \"fields\": \"impressions,cpm,inline_link_clicks,inline_link_click_ctr,conversions,cost_per_conversion,spend,action_values,purchase_roas\",\n \"time_range\": {\n   \"since\": \"{{$node['Calculate date format for meta ads request s'].json.lastYear.since}}\",\n   \"until\": \"{{$node['Calculate date format for meta ads request s'].json.lastYear.until}}\"\n }\n}"}, "graphApiVersion": "v20.0"}, "credentials": {"facebookGraphApi": {"id": "9q3YgP6zvgKBO4aj", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "a13919d1-5680-44ea-826b-44afc0034702", "name": "Summarize Meta input (current year)", "type": "n8n-nodes-base.summarize", "position": [-920, 1540], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Impressions", "aggregation": "sum"}, {"field": "CPM", "aggregation": "average"}, {"field": "<PERSON>licks", "aggregation": "sum"}, {"field": "CTR", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Cost per conversion", "aggregation": "average"}, {"field": "Spend", "aggregation": "sum"}, {"field": "ROAS", "aggregation": "average"}]}}, "typeVersion": 1}, {"id": "1afce576-0d2a-41bb-a94b-5d6eaae1fe3e", "name": "Assign Meta data from input (current year)", "type": "n8n-nodes-base.set", "position": [-1140, 1540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Impressions", "type": "number", "value": "={{ $json.data[0].impressions }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "CPM", "type": "number", "value": "={{ $json.data[0].cpm }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "<PERSON>licks", "type": "number", "value": "={{ $json.data[0].inline_link_clicks }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "CTR", "type": "number", "value": "={{ $json.data[0].inline_link_click_ctr }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Conversions", "type": "number", "value": "={{ $json.data[0].conversions[0].value }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Cost per conversion", "type": "number", "value": "={{ $json.data[0].cost_per_conversion[0].value }}"}, {"id": "c6cac2d8-b8f8-4b2a-9bcc-1c325db78799", "name": "ROAS", "type": "number", "value": "={{ $json.data[0].purchase_roas[0].value }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Spend", "type": "number", "value": "={{ $json.data[0].spend }}"}]}}, "typeVersion": 3.4}, {"id": "0d400918-eb14-4bb1-8950-27b39e651264", "name": "Assign Meta data input (previous year)", "type": "n8n-nodes-base.set", "position": [-340, 1540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Impressions", "type": "number", "value": "={{ $json.data[0].impressions }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "CPM", "type": "number", "value": "={{ $json.data[0].cpm }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "<PERSON>licks", "type": "number", "value": "={{ $json.data[0].inline_link_clicks }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "CTR", "type": "number", "value": "={{ $json.data[0].inline_link_click_ctr }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Conversions", "type": "number", "value": "={{ $json.data[0].conversions[0].value }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Cost per conversion", "type": "number", "value": "={{ $json.data[0].cost_per_conversion[0].value }}"}, {"id": "b866032c-07ac-440b-a81a-d9787926b9d6", "name": "ROAS", "type": "number", "value": "={{ $json.data[0].purchase_roas[0].value }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Spend", "type": "number", "value": "={{ $json.data[0].spend }}"}]}}, "typeVersion": 3.4}, {"id": "faf34f2a-f6d3-4510-aeee-6b25174abc08", "name": "Summarize Meta data input (previous year)", "type": "n8n-nodes-base.summarize", "position": [-40, 1540], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Impressions", "aggregation": "sum"}, {"field": "CPM", "aggregation": "average"}, {"field": "<PERSON>licks", "aggregation": "sum"}, {"field": "CTR", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Cost per conversion", "aggregation": "average"}, {"field": "Spend", "aggregation": "sum"}, {"field": "ROAS", "aggregation": "average"}]}}, "typeVersion": 1}, {"id": "3985f30a-94ab-45be-a511-b9ae0d4f3bb0", "name": "Format all Meta data for LLM", "type": "n8n-nodes-base.code", "position": [160, 1540], "parameters": {"jsCode": "const currentData = $('Summarize Meta input (current year)').first().json;\nconst previousData = $('Summarize Meta data input (previous year)').first().json;\n\nfunction parseNumber(value) {\n    if (typeof value === 'string') {\n        return parseFloat(value.replace(/\\./g, '').replace(',', '.'));\n    }\n    return value || 0;\n}\n\nfunction formatNumber(number, decimals = 0) {\n    if (number === 0) return \"0\";\n    return parseNumber(number).toLocaleString('de-DE', {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    });\n}\n\nfunction calculateCPM(cpm) {\n    return parseNumber(cpm);\n}\n\nreturn [{\n    current_impressions: formatNumber(currentData.sum_Impressions),\n    current_cpm: formatNumber(calculateCPM(currentData.average_CPM), 2),\n    current_clicks: formatNumber(currentData.sum_Clicks),\n    current_ctr: formatNumber(parseNumber(currentData.average_CTR), 2),\n    current_conversions: formatNumber(currentData.sum_Conversions, 2),\n    current_cost_per_conversion: formatNumber(currentData.average_Cost_per_conversion, 2),\n    current_cost: formatNumber(currentData.sum_Spend, 2),\n    current_roas: formatNumber(parseNumber(currentData.average_ROAS), 2),\n    \n    previous_impressions: formatNumber(previousData.sum_Impressions),\n    previous_cpm: formatNumber(calculateCPM(previousData.average_CPM), 2),\n    previous_clicks: formatNumber(previousData.sum_Clicks),\n    previous_ctr: formatNumber(parseNumber(previousData.average_CTR), 2),\n    previous_conversions: formatNumber(previousData.sum_Conversions, 2),\n    previous_cost_per_conversion: formatNumber(previousData.average_Cost_per_conversion || 0, 2),\n    previous_cost: formatNumber(previousData.sum_Spend, 2),\n    previous_roas: formatNumber(parseNumber(previousData.average_ROAS), 2)\n}];"}, "typeVersion": 2}, {"id": "146d6907-66af-41ff-ab32-b2a3ec5787b6", "name": "Processing for Google Ads report", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [400, 980], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Please analyze the following data and output the results in table form:\n\n| Metric | Last 7 Days | Previous Year | Percentage Change |\n|--------|-------------|---------------|-------------------|\n| Total Impressions | {{$json.current_impressions}} | {{$json.previous_impressions}} | Percentage Change |\n| CPM | {{$json.current_cpm}} | {{$json.previous_cpm}} | Percentage Change |\n| Total Clicks | {{$json.current_clicks}} | {{$json.previous_clicks}} | Percentage Change |\n| CTR | {{$json.current_ctr}} | {{$json.previous_ctr}} | Percentage Change |\n| Conversions | {{$json.current_conversions}} | {{$json.previous_conversions}} | Percentage Change |\n| Cost per Conversion | {{$json.current_cost_per_conversion}} | {{$json.previous_cost_per_conversion}} | Percentage Change |\n| ROAS | {{ $json.current_roas }} | {{ $json.previous_roas }} | Percentage Change |\n| Costs | {{$json.current_cost}} | {{$json.previous_cost}} | Percentage Change |\n\nNumber format:\n- Period (.) for thousands (e.g. 4.000)\n- Comma (,) for decimal numbers (e.g. 3,4)\n- Display CPM, Cost per Conversion, ROAS and Costs in €\n- CTR in percent\n\nPlease write a brief summary of the analyzed data above the table (in max 3 sentences!)\n\nIMPORTANT:\nWrite nothing except the summary and the table below it!\nNO INTRODUCTION, NO CONCLUSION!"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "2fd2a795-caa4-4194-8049-4955c1d54dd9", "name": "Format all Google data for LLM", "type": "n8n-nodes-base.code", "position": [260, 980], "parameters": {"jsCode": "const currentData = $('Summarize input (current year)').first().json;\nconst previousData = $('Summarize input (previous year)').first().json;\n\nfunction formatNumber(number, decimals = 0) {\n   if (number === 0) return \"0\";\n   return number.toLocaleString('de-DE', {\n       minimumFractionDigits: decimals,\n       maximumFractionDigits: decimals\n   });\n}\n\nfunction calculateCPM(costMicros, impressions) {\n   if (impressions === 0) return 0;\n   return (costMicros / 1000000 / impressions) * 1000;\n}\n\nreturn [{\n   current_impressions: formatNumber(currentData.sum_Impressions),\n   current_cpm: formatNumber(calculateCPM(currentData.sum_Spend, currentData.sum_Impressions), 2),\n   current_clicks: formatNumber(currentData.sum_Clicks),\n   current_ctr: formatNumber(currentData.average_CTR * 100, 2),\n   current_conversions: formatNumber(currentData.sum_Conversions, 2),\n   current_cost_per_conversion: formatNumber(currentData.average_Cost_per_Conversion / 1000000, 2),\n   current_cost: formatNumber(currentData.sum_Spend / 1000000, 2),\n   current_roas: formatNumber(currentData.average_ROAS, 2),\n   \n   previous_impressions: formatNumber(previousData.sum_Impressions),\n   previous_cpm: formatNumber(calculateCPM(previousData.sum_Spend, previousData.sum_Impressions), 2),\n   previous_clicks: formatNumber(previousData.sum_Clicks),\n   previous_ctr: formatNumber(previousData.average_CTR * 100, 2),\n   previous_conversions: formatNumber(previousData.sum_Conversions, 2),\n   previous_cost_per_conversion: formatNumber(previousData.average_Cost_per_conversion / 1000000, 2),\n   previous_cost: formatNumber(previousData.sum_Spend / 1000000, 2),\n   previous_roas: formatNumber(previousData.average_ROAS, 2)\n}];"}, "typeVersion": 2}, {"id": "e7e357e7-3f16-4b6e-ae50-c6741bbe8357", "name": "Assign Google Analytics data input (current year)", "type": "n8n-nodes-base.set", "position": [-1080, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Page views", "type": "number", "value": "={{ $json.screenPageViews }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "Users", "type": "number", "value": "={{ $json.totalUsers }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "Sessions", "type": "number", "value": "={{ $json.sessions }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "Sessions per user", "type": "number", "value": "={{ $json.sessionsPerUser }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Session duration", "type": "number", "value": "={{ $json.averageSessionDuration }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Conversions", "type": "number", "value": "={{ $json.ecommercePurchases }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Value per Conversion", "type": "number", "value": "={{ $json.averagePurchaseRevenue }}"}, {"id": "94835d43-2fc8-49c0-97f0-6f0f8699337a", "name": "Revenue", "type": "number", "value": "={{ $json.purchaseRevenue }}"}, {"id": "d70f8138-3b84-4b88-a98f-eb929e1cc29a", "name": "date", "type": "string", "value": "={{ $json.date }}"}]}}, "typeVersion": 3.4}, {"id": "9f16d330-9288-42bc-9a21-beb002c0ce4f", "name": "Summarize Google Analytics input (current year)", "type": "n8n-nodes-base.summarize", "position": [-860, 460], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Page views", "aggregation": "sum"}, {"field": "Users", "aggregation": "sum"}, {"field": "Sessions", "aggregation": "sum"}, {"field": "Sessions per user", "aggregation": "average"}, {"field": "Session duration", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Value per Conversion", "aggregation": "average"}, {"field": "Revenue", "aggregation": "sum"}, {"field": "date"}]}}, "typeVersion": 1}, {"id": "9355325f-35c2-4663-922e-d7ccec873fea", "name": "Calculate date format for meta ads request s", "type": "n8n-nodes-base.code", "position": [-1600, 1540], "parameters": {"jsCode": "// Aktuelles Datum\nconst now = new Date();\n\n// Gestern als Ende-<PERSON>tum\nconst yesterday = new Date(now);\nyesterday.setDate(now.getDate() - 1);\n\n// Start-Datum (7 Tage vor gestern)\nconst weekStart = new Date(yesterday);\nweekStart.setDate(yesterday.getDate() - 6);\n\n// Vorjahreszeitraum\nconst lastYearStart = new Date(weekStart);\nlastYearStart.setFullYear(weekStart.getFullYear() - 1);\n\nconst lastYearEnd = new Date(yesterday);\nlastYearEnd.setFullYear(yesterday.getFullYear() - 1);\n\n// Formatierung YYYY-MM-DD\nfunction formatDate(date) {\n    return date.toISOString().split('T')[0];\n}\n\n// Ausgabe\nitems[0] = {\n  json: {\n    currentPeriod: {\n      since: formatDate(weekStart),\n      until: formatDate(yesterday)\n    },\n    lastYear: {\n      since: formatDate(lastYearStart),\n      until: formatDate(lastYearEnd)\n    }\n  }\n};\n\nreturn items;"}, "typeVersion": 2}, {"id": "7677727b-606b-4377-9057-032e91d1faae", "name": "Calculation same period previous year1", "type": "n8n-nodes-base.code", "position": [-620, 460], "parameters": {"jsCode": "return {\n  // Berechnung des Startdatums: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 7 Tage zurück\n  startDate: (() => {\n    const date = new Date();\n    date.setFullYear(date.getFullYear() - 1); // Zurück ins Vorjahr\n    date.setDate(date.getDate() - 7); // 7 Tage zurück\n    return date.toISOString().split('T')[0];\n  })(),\n  \n  // Berechnung des Enddatums: Vorjahr, heutiges Datum\n  endDate: (() => {\n    const date = new Date();\n    date.setFullYear(date.getFullYear() - 1); // Zurück ins Vorjahr\n    return date.toISOString().split('T')[0];\n  })(),\n};\n"}, "typeVersion": 2}, {"id": "d49d00f4-7336-40b7-8833-d2e8c9eacb5b", "name": "Assign Google Analytics data input (previous year)", "type": "n8n-nodes-base.set", "position": [-180, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c2f8b9a-e964-49a0-8837-efb0dfd7bcae", "name": "Page views", "type": "number", "value": "={{ $json.screenPageViews }}"}, {"id": "8b524518-1268-4971-b5c9-ae7da09d94f9", "name": "Users", "type": "number", "value": "={{ $json.totalUsers }}"}, {"id": "ca7279b9-c643-425f-aa99-cb17146e9994", "name": "Sessions", "type": "number", "value": "={{ $json.sessions }}"}, {"id": "591288f7-e8cf-445e-872a-5b83f997b825", "name": "Sessions per user", "type": "number", "value": "={{ $json.sessionsPerUser }}"}, {"id": "dc1a43da-3f3a-4dca-bbde-904222d7f693", "name": "Session duration", "type": "number", "value": "={{ $json.averageSessionDuration }}"}, {"id": "eac0b53e-c452-40b8-92bc-8af8ea349984", "name": "=Conversions", "type": "number", "value": "={{ $json.ecommercePurchases }}"}, {"id": "b96439be-189d-4ebe-b49e-d5c31fefe9f0", "name": "Value per conversion", "type": "number", "value": "={{ $json.averagePurchaseRevenue }}"}, {"id": "94835d43-2fc8-49c0-97f0-6f0f8699337a", "name": "Revenue", "type": "number", "value": "={{ $json.purchaseRevenue }}"}, {"id": "dd8255c6-65b1-41ce-b596-70c09108d6e2", "name": "=date", "type": "string", "value": "={{ $json.date }}"}]}}, "typeVersion": 3.4}, {"id": "a6f72675-f055-4270-8fff-05d61da6f3f9", "name": "Summarize Google Analytics input (previous year)", "type": "n8n-nodes-base.summarize", "position": [40, 460], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "Page views", "aggregation": "sum"}, {"field": "Users", "aggregation": "sum"}, {"field": "Sessions", "aggregation": "sum"}, {"field": "Sessions per user", "aggregation": "average"}, {"field": "Session duration", "aggregation": "average"}, {"field": "Conversions", "aggregation": "sum"}, {"field": "Value per conversion", "aggregation": "average"}, {"field": "Revenue", "aggregation": "sum"}, {"field": "date"}]}}, "typeVersion": 1}, {"id": "61311c11-59d2-4b77-afc5-52b1d66afbd7", "name": "Send mail report", "type": "n8n-nodes-base.emailSend", "position": [560, -220], "parameters": {"html": "={{ $json.output }}", "options": {}, "subject": "Weekly Report: Online Marketing Report", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "A71x7hx6lKj7nxp1", "name": "SMTP account"}}, "typeVersion": 2.1}, {"id": "928d15f2-141c-4e88-abcc-60799793bde7", "name": "Processing for Meta Ads Report", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [400, 1540], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Please analyze the following data and output the results in table form:\n\n| Metric | Last 7 Days | Previous Year | Percentage Change |\n|--------|-------------|---------------|-------------------|\n| Total Impressions | {{$json.current_impressions}} | {{$json.previous_impressions}} | Percentage Change |\n| CPM | {{$json.current_cpm}} | {{$json.previous_cpm}} | Percentage Change |\n| Total Clicks | {{$json.current_clicks}} | {{$json.previous_clicks}} | Percentage Change |\n| CTR | {{$json.current_ctr}} | {{$json.previous_ctr}} | Percentage Change |\n| Conversions | {{$json.current_conversions}} | {{$json.previous_conversions}} | Percentage Change |\n| Cost per Conversion | {{$json.current_cost_per_conversion}} | {{$json.previous_cost_per_conversion}} | Percentage Change |\n| ROAS | {{ $json.current_roas }} | {{ $json.previous_roas }} | Percentage Change |\n| Costs | {{$json.current_cost}} | {{$json.previous_cost}} | Percentage Change |\n\nNumber format:\n- Period (.) for thousands (e.g. 4.000)\n- Comma (,) for decimal numbers (e.g. 3,4)\n- Display CPM, Cost per Conversion, ROAS and Costs in €\n- CTR in percent\n\nPlease write a brief summary of the analyzed data above the table (in max 3 sentences!)\n\nIMPORTANT:\nWrite nothing except the summary and the table below it!\nNO INTRODUCTION, NO CONCLUSION!"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "ce9175bd-b355-47eb-8dd8-dd26292e0061", "name": "Processing for Google Analytics Report", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [400, 460], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Please analyze the following data and output the results in table form:\n| Metric                        | Last 7 Days | Previous Year | Percentage Change |\n|-------------------------------|-------------|---------------|-------------------|\n| Total Page Views              | {{ $('Summarize Google Analytics input (current year)').item.json.sum_Page_views }} | {{ $json.sum_Page_views }} | Percentage Change |\n| Total Users                   | {{ $('Summarize Google Analytics input (current year)').item.json.sum_Users }} | {{ $json.sum_Users }} | Percentage Change |\n| Total Sessions                | {{ $('Summarize Google Analytics input (current year)').item.json.sum_Sessions }} | {{ $json.sum_Sessions }} | Percentage Change |\n| Average Sessions per User     | {{ $('Summarize Google Analytics input (current year)').item.json.average_Sessions_per_user }} | {{ $json.average_Sessions_per_user }} | Percentage Change |\n| Average Session Duration      | {{ $('Summarize Google Analytics input (current year)').item.json.average_Session_duration }} | {{ $json.average_Session_duration }} | Percentage Change |\n| Total Purchases               | {{ $('Summarize Google Analytics input (current year)').item.json.sum_Conversions }} | {{ $json.sum_Conversions }} | Percentage Change |\n| Average Revenue per Purchase  | {{ $('Summarize Google Analytics input (current year)').item.json.average_Value_per_Conversion }} | {{ $json.average_Value_per_conversion }} | Percentage Change |\n| Total Revenue                 | {{ $('Summarize Google Analytics input (current year)').item.json.sum_Revenue }} | {{ $('Summarize Google Analytics input (previous year)').item.json.sum_Revenue }} | Percentage Change |\n\nNumber format:\n- Period (.) for thousands (e.g. 4.000)\n- Comma (,) for decimal numbers (e.g. 3,4)\n- Convert Average Session Duration to minutes instead of seconds\n- Average Revenue per Purchase and Total Revenue in €\n\nPlease write a brief summary of the analyzed data above the table (in max 3 sentences!)\n\nIMPORTANT:\nWrite nothing except the summary and the table below it!\nNO INTRODUCTION, NO CONCLUSION!"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "c960aed4-83c9-4f7f-b341-0eb29d4c3733", "name": "Processing for Telegram Report", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [0, 0], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Convert the following text from HTML to plain text:\n\n{{ $json.output }}\n\nPlease format the table so that each evaluation (Domain 1, Domain 2, Domain 3, Domain 4, Domain 5, Google Ads, Meta Ads) is its own paragraph! Also, please display only the summary (without the table), but incorporate the numbers into the running text!\n\nExample:\n\nDomain 1:\nxx,xxx page views (+x.xx%)\nxx,xxx users (-x.xx%)\n\nDomain 2:\nxx,xxx page views (+x.xx%)\nxx,xxx users (-x.xx%)\n\n<userStyle>Normal</userStyle>"}]}}, "credentials": {"openAiApi": {"id": "niikB3HA4fT5WAqt", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "542bad2f-fad2-4e6d-b7af-499c7cd2b35c", "name": "Weekly Report Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-900, -220], "parameters": {"text": "=# Expert in Data Processing and Email Generation in N8N\nYOU ARE AN EXPERT IN DATA PROCESSING AND EMAIL GENERATION IN N8N. YOUR TASK IS TO CREATE AN ENGLISH-LANGUAGE HTML EMAIL THAT COMPREHENSIVELY SUMMARIZES GOOGLE ANALYTICS AND GOO<PERSON>LE ADS DATA FROM VARIOUS WORKFLOWS. YOU MUST FORMAT THE CAPTURED DATA ACCORDING TO THE SPECIFIED SPECIFICATIONS AND DESIGN THE EMAIL FOR OPTIMAL READABILITY.\n\n## Instructions\n\n### 1. Collect Summary\n- First, retrieve the summary and table for `Analytics_Domain_1` from the `Analytics_Domain_1` workflow.\n- Second, retrieve the summary and table for `Analytics_Domain_2` from the `Analytics_Domain_2` workflow.\n- Third, retrieve the summary and table for `Analytics_Domain_3` from the `Analytics_Domain_3` workflow.\n- Fourth, retrieve the summary and table for `Analytics_Domain_4` from the `Analytics_Domain_4` workflow.\n- Fifth, retrieve the summary and table for `Analytics_Domain_5` from the `Analytics_Domain_5` workflow.\n- Sixth, retrieve the summary and table for `Google_Ads` from the `Google_Ads` workflow.\n- Seventh, retrieve the summary and table for `Meta_Ads` from the `Meta_Ads` workflow.\n\n### 2. Email Structure\n- Begin the email with the following introduction:  \n  \"Hi Freddy,  \n  Here is your weekly Online Marketing Report for the last 7 days from Domain 1, Domain 2, Domain 3, Domain 4, Domain 5, as well as Google Ads and Meta Ads!\"\n\n- Create a separate section for each dataset:  \n  - **Domain 1**: Contains the summary and table from `Analytics_Domain_1`\n  - **Domain 2**: Contains the summary and table from `Analytics_Domain_2`\n  - **Domain 3**: Contains the summary and table from `Analytics_Domain_3`\n  - **Domain 4**: Contains the summary and table from `Analytics_Domain_4`\n  - **Domain 5**: Contains the summary and table from `Analytics_Domain_5`\n  - **Google Ads**: Contains the summary and table from `Google Ads`\n  - **Meta Ads**: Contains the summary and table from `Meta Ads`\n\n- Present the sections clearly and readably as HTML.\n\n### 3. Design and Formatting\n- Use simple HTML structures with clear section titles (e.g., `<h2>` for titles).\n- Present the summary as a paragraph (e.g., `<p>`).\n- The table should be cleanly formatted (e.g., with `<table>`, `<tr>`, `<td>`).\n- Keep the presentation clear and easy to read.\n\n### 4. No Conclusion, No Signature\n\n### HTML Output Structure\n\n```html\n<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            line-height: 1.6;\n        }\n        h2 {\n            color: #333;\n        }\n        table {\n            width: 100%;\n            border-collapse: collapse;\n            margin: 10px 0;\n        }\n        table, th, td {\n            border: 1px solid #ddd;\n        }\n        th, td {\n            padding: 8px;\n            text-align: left;\n        }\n        th {\n            background-color: #f4f4f4;\n        }\n    </style>\n</head>\n<body>\n    <p>Hi,</p>\n    <p>Here is your weekly Online Marketing Report for the last 7 days from Domain 1, Domain 2, Domain 3, Domain 4, Domain 5, as well as Google Ads and Meta Ads!</p>\n    \n    <h2>Domain 1</h2>\n    <p>[Summary from Analytics_Domain_1 will be inserted here]</p>\n    <table>\n        [Table content from Analytics_Domain_1 will be inserted here]\n    </table>\n    \n    <h2>Domain 2</h2>\n    <p>[Summary from Analytics_Domain_2 will be inserted here]</p>\n    <table>\n        [Table content from Analytics_Domain_2 will be inserted here]\n    </table>\n</body>\n</html>\n```\n\n### 5. Output\n- Format the entire HTML content as a string for email transmission.\n\n### What Not to Do\n- No unwanted closings or signatures\n- No unstructured, hard-to-read data formatting\n- No missing sections or titles\n- No copying of data without HTML formatting", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "2a405e35-5d34-4f3c-a5e8-81e958cc550a", "name": "Send Telegram report", "type": "n8n-nodes-base.telegram", "position": [560, 0], "parameters": {"text": "={{ $json.message.content }}", "chatId": "**********", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "0hnyvxyUMN77sBmU", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "eb8e59fe-2dbd-49e6-a055-a01f7419d8f3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2120, -280], "parameters": {"width": 440, "height": 2180, "content": "Welcome to Online Marketing Weekly Report Workflow!\n\nThis workflow has the following sequence:\n\n1. time trigger (e.g. every Monday at 7 a.m.)\n2. retrieval of Online Marketing data from the last 7 days (via sub workflows)\n3. assignment and summary of the data\n4. retrieval of Online Marketing data from the same time period of the previous year\n5. allocation and summary of the data\n6. preparation in tabular form and brief analysis by AI.\n7. sending the report as an email\n8. preparation in short form by AI for Telegram (optional)\n9. sending as Telegram message.\n\nThe following accesses are required for the workflow:\n- Google Analytics (via Google Analytics API): [Documentation](https://docs.n8n.io/integrations/builtin/credentials/google/)\n- Google Ads (via HTTP Request -&gt; Google Ads API):[Documentation](https://docs.n8n.io/integrations/builtin/credentials/google/)\n- Meta Ads (via Facebook Graph API): [Documentation](https://docs.n8n.io/integrations/builtin/credentials/facebookgraph/)\n- AI API access (e.g. via OpenAI, Anthropic, Google or Ollama)\n- SMTP access data (for sending the mail)\n- Telegram access data (optional for sending as Telegram message): [Documentation](https://docs.n8n.io/integrations/builtin/credentials/telegram/)\n\nYou can contact me via LinkedIn, if you have any questions: https://www.linkedin.com/in/friedemann-schuetz"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Berlin", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "0595b0a6-3130-4041-b3b1-41e820edd985", "connections": {"Meta_Ads": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Processing for Google Analytics Report", "type": "ai_tool", "index": 0}]]}, "Google_Ads": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Calculator1": {"ai_tool": [[{"node": "Processing for Google Ads report", "type": "ai_tool", "index": 0}]]}, "Calculator3": {"ai_tool": [[{"node": "Processing for Meta Ads Report", "type": "ai_tool", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Weekly Report Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Weekly Report Agent", "type": "ai_languageModel", "index": 0}]]}, "Analytics_Domain_1": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Analytics_Domain_2": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Analytics_Domain_3": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Analytics_Domain_4": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Analytics_Domain_5": {"ai_tool": [[{"node": "Weekly Report Agent", "type": "ai_tool", "index": 0}]]}, "Weekly Report Agent": {"main": [[{"node": "Processing for Telegram Report", "type": "main", "index": 0}, {"node": "Send mail report", "type": "main", "index": 0}]]}, "Send Telegram report": {"main": [[]]}, "Execute Workflow Trigger": {"main": [[{"node": "Call Google Analytics data: Last 7 days", "type": "main", "index": 0}]]}, "Assign input (previous year)": {"main": [[{"node": "Summarize input (previous year)", "type": "main", "index": 0}]]}, "Format all Meta data for LLM": {"main": [[{"node": "Processing for Meta Ads Report", "type": "main", "index": 0}]]}, "Format all Google data for LLM": {"main": [[{"node": "Processing for Google Ads report", "type": "main", "index": 0}]]}, "Processing for Telegram Report": {"main": [[{"node": "Send Telegram report", "type": "main", "index": 0}]]}, "Summarize input (current year)": {"main": [[{"node": "Calculation same period previous year", "type": "main", "index": 0}]]}, "Call Meta Ads Data: Last 7 days": {"main": [[{"node": "Assign Meta data from input (current year)", "type": "main", "index": 0}]]}, "Summarize input (previous year)": {"main": [[{"node": "Format all Google data for LLM", "type": "main", "index": 0}]]}, "Format data input (current year)": {"main": [[{"node": "Assign data from input (current year)", "type": "main", "index": 0}]]}, "Call Google Ads Data: Last 7 days": {"main": [[{"node": "Format data input (current year)", "type": "main", "index": 0}]]}, "Format data input (previous year)": {"main": [[{"node": "Assign input (previous year)", "type": "main", "index": 0}]]}, "Summarize Meta input (current year)": {"main": [[{"node": "Call Meta Ads Data: Last 7 days (previous year)", "type": "main", "index": 0}]]}, "Assign data from input (current year)": {"main": [[{"node": "Summarize input (current year)", "type": "main", "index": 0}]]}, "Calculation same period previous year": {"main": [[{"node": "Call Google Ads Data: Last 7 days (previous year)", "type": "main", "index": 0}]]}, "Assign Meta data input (previous year)": {"main": [[{"node": "Summarize Meta data input (previous year)", "type": "main", "index": 0}]]}, "Calculation same period previous year1": {"main": [[{"node": "Call Google Analytics data: Last 7 days (previous year)", "type": "main", "index": 0}]]}, "Call Google Analytics data: Last 7 days": {"main": [[{"node": "Assign Google Analytics data input (current year)", "type": "main", "index": 0}]]}, "Summarize Meta data input (previous year)": {"main": [[{"node": "Format all Meta data for LLM", "type": "main", "index": 0}]]}, "Assign Meta data from input (current year)": {"main": [[{"node": "Summarize Meta input (current year)", "type": "main", "index": 0}]]}, "Calculate date format for meta ads request s": {"main": [[{"node": "Call Meta Ads Data: Last 7 days", "type": "main", "index": 0}]]}, "Call Meta Ads Data: Last 7 days (previous year)": {"main": [[{"node": "Assign Meta data input (previous year)", "type": "main", "index": 0}]]}, "Summarize Google Analytics input (current year)": {"main": [[{"node": "Calculation same period previous year1", "type": "main", "index": 0}]]}, "Summarize Google Analytics input (previous year)": {"main": [[{"node": "Processing for Google Analytics Report", "type": "main", "index": 0}]]}, "Assign Google Analytics data input (current year)": {"main": [[{"node": "Summarize Google Analytics input (current year)", "type": "main", "index": 0}]]}, "Call Google Ads Data: Last 7 days (previous year)": {"main": [[{"node": "Format data input (previous year)", "type": "main", "index": 0}]]}, "Assign Google Analytics data input (previous year)": {"main": [[{"node": "Summarize Google Analytics input (previous year)", "type": "main", "index": 0}]]}, "Call Google Analytics data: Last 7 days (previous year)": {"main": [[{"node": "Assign Google Analytics data input (previous year)", "type": "main", "index": 0}]]}, "Calculate date format for Google Ads request (last 7 days)": {"main": [[{"node": "Call Google Ads Data: Last 7 days", "type": "main", "index": 0}]]}}}