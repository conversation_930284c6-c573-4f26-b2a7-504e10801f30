{"id": "WsksMHrmAQrG32db", "meta": {"instanceId": "92fc20bda79393649a623da4b0a65937bcc52015ab24e5a11633573bf81c05ba"}, "name": "ClockifyBlockiaWorkflow", "tags": [{"id": "0zJNrNQJD49aoFYO", "name": "Clockify", "createdAt": "2024-12-02T21:53:54.940Z", "updatedAt": "2024-12-02T21:53:54.940Z"}, {"id": "JvuYX1WC7uT4mYl7", "name": "<PERSON><PERSON>ck", "createdAt": "2024-12-02T21:53:56.825Z", "updatedAt": "2024-12-02T21:53:56.825Z"}], "nodes": [{"id": "98efbcb6-7d13-436d-bb78-22999944b4da", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-800, 260], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "oMvpfPJuOGdwct9R", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "1e32c1a0-3fb6-4fb6-b706-805b16f95528", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [-20, 260], "parameters": {}, "typeVersion": 1}, {"id": "363b9823-5c97-4da6-889f-4fa83fac539f", "name": "Create New Time Entry", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-120, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/time-entries", "method": "POST", "jsonBody": "{\n  \"billable\": true,\n  \"description\": \"{logDescription}\",\n  \"end\": \"{endTime}\",\n  \"projectId\": \"{projectId}\",\n  \"start\": \"{startTime}\",\n  \"type\": \"REGULAR\"\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "toolDescription": "Call this workflow whenever you need to work with Clockify for create a new time entry.", "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "logDescription", "type": "string", "description": "Description of the time log entry"}, {"name": "endTime", "type": "string", "description": "Represents an end date of the time log in yyyy-MM-ddThh:mm:ssZ format."}, {"name": "projectId", "type": "string", "description": "Represents project unique identifier across the system."}, {"name": "startTime", "type": "string", "description": "Represents a start date of the time log in yyyy-MM-ddThh:mm:ssZ format."}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "dac03c89-823e-46c5-af76-39b09b0b073b", "name": "GetClientsTool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-380, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/clients", "fields": "id,name,workspaceId", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "name", "valueProvider": "modelOptional"}]}, "toolDescription": "Call this tool whenever you need to get or filter clients on Clockify, especially if you need to find client id by name.", "optimizeResponse": true, "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "name", "type": "string", "description": "Filters client results that matches with the string provided in their client name."}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "3a615bf3-c237-423b-919d-62c161dd0a50", "name": "Get All Time Entries", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-260, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/user/{userId}/time-entries", "fields": "id,description,userId,projectId,workspaceId,timeInterval", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "start", "value": "{startTime}", "valueProvider": "fieldValue"}, {"name": "end", "value": "{endTime}", "valueProvider": "fieldValue"}]}, "toolDescription": "Call this tool whenever you need to get time entries on Clockify for a given user identified by its user Id (not name).\nBy default fetch entries from the last month if no dates are provided.", "optimizeResponse": true, "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "startTime", "type": "string", "description": "Represents start date in yyyy-MM-ddThh:mm:ssZ format. Optional"}, {"name": "endTime", "type": "string", "description": "Represents end date in yyyy-MM-ddThh:mm:ssZ format. Optional"}, {"name": "userId", "type": "string", "description": "Id of the user that is logged in, for which we are retrieving time logs"}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "29bc1d81-07b2-42e3-bc3b-0bd30aaa796a", "name": "Current loggedin user", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [100, 260], "parameters": {"url": "https://api.clockify.me/api/v1/user", "fields": "id,email,name", "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "toolDescription": "Gel the current logged in user that you are operating from. Use it to get user profikle information, especially its id, email, name etc.", "optimizeResponse": true, "nodeCredentialType": "clockifyApi"}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "5472ac8d-b843-4031-acfb-4b5f60d8e84f", "name": "GetProjectsTool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-520, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/projects", "fields": "id,name,currency,clientId,workspaceId", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "name", "value": "{projectName}", "valueProvider": "fieldValue"}, {"name": "clients", "value": "{clientId}", "valueProvider": "fieldValue"}]}, "toolDescription": "Call this tool whenever you need to get projects on Clockify. \nThis is a tool to use if you want to find all projects and project ids for the logged in user.\n\nOptionally you can filter projects by clients or by project names.", "optimizeResponse": true, "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "projectName", "type": "string", "description": "Project name; not project ID; not client ID; can be empty"}, {"name": "clientId", "type": "string", "description": "Client unique identifier, this is not a name of the client; so if you fail you need to try again with client id; can be empty"}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "89e935ef-34c8-4b9b-9182-ccf4c339e7d1", "name": "Update Time Entry", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [240, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/time-entries/{id}", "method": "PUT", "jsonBody": "{\n  \"billable\": true,\n  \"description\": \"{logDescription}\",\n  \"end\": \"{endTime}\",\n  \"projectId\": \"{projectId}\",\n  \"start\": \"{startTime}\",\n  \"type\": \"REGULAR\"\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "toolDescription": "Call this workflow whenever you need to work with Clockify for update an existing time entry.", "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "logDescription", "type": "string", "description": "Description of the time log entry"}, {"name": "endTime", "type": "string", "description": "Represents an end date of the time log in yyyy-MM-ddThh:mm:ssZ format."}, {"name": "projectId", "type": "string", "description": "Represents project unique identifier across the system."}, {"name": "startTime", "type": "string", "description": "Represents a start date of the time log in yyyy-MM-ddThh:mm:ssZ format."}, {"name": "id", "type": "string", "description": "Id of the time entry"}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "88a0e515-ee3e-4546-a053-a11b135bde18", "name": "Delete Time Entry", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [380, 260], "parameters": {"url": "=https://api.clockify.me/api/v1/workspaces/6735b75fe9244e75e2123fba/time-entries/{id}", "method": "DELETE", "authentication": "predefinedCredentialType", "toolDescription": "Call this workflow whenever you need to work with Clockify for deleating an existing time entry.", "nodeCredentialType": "clockifyApi", "placeholderDefinitions": {"values": [{"name": "id", "type": "string", "description": "Id of the time entry"}]}}, "credentials": {"clockifyApi": {"id": "sArgwd7fRqmYH3Pc", "name": "Clockify account"}}, "typeVersion": 1.1}, {"id": "19ddc949-1ee3-4871-8c5c-415e9d560d26", "name": "DateConverter", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [540, 260], "parameters": {"name": "DateToMilisecondsConvertorTool", "jsCode": "// Example: convert the incoming query to uppercase and return it\nreturn (new Date(query)).getTime() ", "description": "Call this tool to convert dates from format to miliseconds.\nExample 2024-12-02T21:04:23.623Z date is converted into time miliseconds.\n\nthis ideally is combinated with calculator to calculate duration periods."}, "typeVersion": 1.1}, {"id": "ec208aa4-8f58-4837-8bf7-42e11cd10ab9", "name": "ClockifyBlockia", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-600, 20], "parameters": {"text": "={{ $json.text }}", "options": {"systemMessage": "=You are an smart assistant assisting engineers in the time logging management on Clockify for an agency called Blockia Labs using conversational chat system. Be precise and concise, you are engineers best friend copilot.\nYour goals is to guide and help engineers in the logging process; by proactively guiding them one step again in the process and giving them options and proactive guidance, not passively waiting only on instructions.\n\nNote that todays day is {{ $now.toUTC() }}\n\nFor date duration calculation use the date convertor tool along with the calculator.\nUse step by step guidance and reasoning for this process.\n\nFor creates, updates and delete operations always double confirm with the user. Especially when deleting, make the confirmation one by one since deleting is critical ops.\n\nMake sure the descriptions are checked, ethic and gramatically correct. \nMake sure there is no overlap in time entries or logging when one user is working on more projects at the same time. \nReturn a simple mardown resposne (no bold or ****)", "passthroughBinaryImages": true}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "75718cd6-00e1-4d7f-91a8-150c5cf5522f", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slackTrigger", "position": [-1140, -240], "webhookId": "cc2d1f3f-c366-48a5-9285-b424f00504cf", "parameters": {"options": {"resolveIds": true}, "trigger": ["app_mention"], "watchWorkspace": true}, "credentials": {"slackApi": {"id": "FAG5NeHyfVWEAZBF", "name": "Slack account 2"}}, "typeVersion": 1}, {"id": "638463bd-4e71-4260-91b4-b5068db7abe6", "name": "Execution Data", "type": "n8n-nodes-base.executionData", "position": [-820, -240], "parameters": {}, "typeVersion": 1}, {"id": "4c0d8360-a353-4ae5-901a-f66065b00258", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-660, 260], "parameters": {"sessionKey": "={{ $json.user }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}, {"id": "81bc56b4-2864-4b3e-801c-2c96244d524c", "name": "Add reaction", "type": "n8n-nodes-base.slack", "position": [-500, -400], "webhookId": "81d9777e-2ea1-4938-966a-e2fe491d0bba", "parameters": {"name": "+1", "resource": "reaction", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Execution Data').item.json.channel }}"}, "timestamp": "={{ $json.ts }}"}, "credentials": {"slackApi": {"id": "FAG5NeHyfVWEAZBF", "name": "Slack account 2"}}, "typeVersion": 2.2}, {"id": "b330f17f-b0c1-4025-813f-d20ca1b1d896", "name": "Send reply", "type": "n8n-nodes-base.slack", "position": [-240, -240], "webhookId": "81d9777e-2ea1-4938-966a-e2fe491d0bba", "parameters": {"text": "={{ $json.output }}", "select": "channel", "channelId": {"__rl": true, "mode": "id", "value": "={{ $('Execution Data').item.json.channel }}"}, "otherOptions": {"mrkdwn": true, "thread_ts": {"replyValues": {"thread_ts": "={{ $('Execution Data').item.json.ts }}"}}, "link_names": false, "unfurl_links": false, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "FAG5NeHyfVWEAZBF", "name": "Slack account 2"}}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "91f32f58-1060-4473-9313-7a5b31dbcf26", "connections": {"Calculator": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "DateConverter": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "Slack Trigger": {"main": [[{"node": "Execution Data", "type": "main", "index": 0}]]}, "Execution Data": {"main": [[{"node": "ClockifyBlockia", "type": "main", "index": 0}, {"node": "Add reaction", "type": "main", "index": 0}]]}, "GetClientsTool": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "ClockifyBlockia": {"main": [[{"node": "Send reply", "type": "main", "index": 0}]]}, "GetProjectsTool": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "Delete Time Entry": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "ClockifyBlockia", "type": "ai_languageModel", "index": 0}]]}, "Update Time Entry": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "Get All Time Entries": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "ClockifyBlockia", "type": "ai_memory", "index": 0}]]}, "Create New Time Entry": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}, "Current loggedin user": {"ai_tool": [[{"node": "ClockifyBlockia", "type": "ai_tool", "index": 0}]]}}}