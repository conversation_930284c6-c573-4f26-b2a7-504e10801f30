{"id": "Nfh274NHoDy7pB4M", "meta": {"instanceId": "00493e38fecfc163cb182114bc2fab90114038eb9aad665a7a752d076920d3d5", "templateCredsSetupCompleted": true}, "name": "Integrating AI with Open-Meteo API for Enhanced Weather Forecasting", "tags": [], "nodes": [{"id": "80debfe0-c591-4ba1-aca1-068adac62aa9", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [100, -300], "webhookId": "4a44e974-db62-4727-9913-12a22bc88e01", "parameters": {"public": true, "options": {"title": "N8N 👋", "subtitle": "Weather Assistant: Example of Tools Using ChatGPT", "allowFileUploads": false, "loadPreviousSession": "memory"}, "initialMessages": "Type like this: Weather Forecast for the Next 7 Days in São Paulo"}, "typeVersion": 1.1}, {"id": "ec375027-1c0d-438b-9fca-7bc4fbef2479", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [420, -60], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "bhRvwBXztNmJVObo", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "bd2f5967-8188-4b1f-9255-8008870aaf7b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -640], "parameters": {"color": 5, "width": 500, "height": 720, "content": "## Integrating AI with Open-Meteo API for Enhanced Weather Forecasting\n\n## Use case\n\n### Workshop\n\nWe are using this workflow in our workshops to teach how to use Tools a.k.a functions with artificial intelligence. In this specific case, we will use a generic \"AI Agent\" node to illustrate that it could use other models from different data providers.\n\n### Enhanced Weather Forecasting\n\nIn this small example, it's easy to demonstrate how to obtain weather forecast results from the Open-Meteo site to accurately display the upcoming days.\n\nThis can be used to plan travel decisions, for example.\n\n## What this workflow does\n\n1. We will make an HTTP request to find out the geographic coordinates of a city.\n2. Then, we will make other HTTP requests to discover the weather for the upcoming days.\n\nIn this workshop, we demonstrate that the AI will be able to determine which tool to call first—it will first call the geolocation tool and then the weather forecast tool. All of this within a single client conversation call.\n\n\n## Setup\n\nInsert an OpenAI Key and activate the workflow.\n\nby <PERSON><PERSON>\nhttps://www.linkedin.com/in/mesquitadavi/"}, "typeVersion": 1}, {"id": "3cfeea52-a310-4101-8377-0f393bf54c8d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [60, -440], "parameters": {"width": 340, "height": 220, "content": "## Create an Hosted Web Chat\n\n### And setup the trigger!\n\nExample: https://website/webhook/4a4..../chat"}, "typeVersion": 1}, {"id": "55713ffc-da61-4594-99f4-ca6b448cbee2", "name": "Generic AI Tool Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [440, -300], "parameters": {"options": {}}, "typeVersion": 1.7}, {"id": "7f608ddc-87bb-4e54-84a8-4db6b7f95011", "name": "Chat <PERSON> Buffer", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [200, -60], "parameters": {}, "typeVersion": 1.3}, {"id": "77f82443-1efe-47d3-92ec-aa193853c8a5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [320, 0], "parameters": {"width": 260, "content": "-\n\n\n## Setup OpenAI Key"}, "typeVersion": 1}, {"id": "ed37ea94-3cff-47cb-bf45-bce620b0f056", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [780, 60], "parameters": {"color": 4, "width": 280, "height": 360, "content": "### Open Meteo SPEC - City Geolocation\n\nThis tool will go to the URL https://geocoding-api.open-meteo.com/v1/search to fetch the geolocation data of the city, and I only need to get the name of the city.\n\nSo, I will ask the user to input the name of the city and pass some pre-existing information, such as returning only the first city and returning in JSON format.\n\n- name (By Model) - And placeholder - The parameter that the AI will need to fill in as required.\n\n- count - 1 by default because I want only the first city.\n\n- format - Putting JSON for no specific reason, but OpenAI could figure out how to process that information."}, "typeVersion": 1}, {"id": "f9b0e65d-a85e-4511-bdd2-adf54b1c039d", "name": "A tool to get the weather forecast based on geolocation", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1100, -160], "parameters": {"url": "https://api.open-meteo.com/v1/forecast", "sendQuery": true, "parametersQuery": {"values": [{"name": "latitude"}, {"name": "longitude"}, {"name": "daily", "value": "temperature_2m_max,precipitation_sum", "valueProvider": "fieldValue"}, {"name": "timezone", "value": "GMT", "valueProvider": "fieldValue"}, {"name": "forecast_days"}]}, "toolDescription": "To get forecast of next [forecast_days] input the geolocation of an City", "placeholderDefinitions": {"values": [{"name": "longitude", "type": "number", "description": "longitude"}, {"name": "latitude", "type": "number", "description": "latitude"}, {"name": "forecast_days", "type": "number", "description": "forecast_days number of days ahead"}]}}, "typeVersion": 1.1}, {"id": "76382491-dd75-4b51-a2d8-cb9782246af8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1240, -220], "parameters": {"color": 4, "width": 280, "height": 320, "content": "### Open Meteo SPEC - Weather Forecast\n\nThis tool will go to the Open Meteo site with the geolocation information at https://api.open-meteo.com/v1/forecast\n\nIt will pass the information on latitude, longitude, and the number of days for which it will bring data.\n\nThere are many default pieces of information within, but the focus is not to explain the Open Meteo API.\n\nVariables like latitude, longitude, and forecast_days are self-explanatory for OpenAI, making it the easiest tool to configure.\n\n- latitude (By Model) and Placeholder\n- longitude (By Model) and Placeholder\n- forecast_days (By Model) and Placeholder\n"}, "typeVersion": 1}, {"id": "1c8087ce-6800-4ece-8234-23914e21a692", "name": "A tool for inputting the city and obtaining geolocation", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [820, -100], "parameters": {"url": "=https://geocoding-api.open-meteo.com/v1/search", "sendQuery": true, "parametersQuery": {"values": [{"name": "name"}, {"name": "count", "value": "1", "valueProvider": "fieldValue"}, {"name": "format", "value": "json", "valueProvider": "fieldValue"}]}, "toolDescription": "Input the City and get geolocation, geocode or coordinates from Requested City", "placeholderDefinitions": {"values": [{"name": "name", "type": "string", "description": "Requested City"}]}}, "typeVersion": 1.1}, {"id": "15ae7421-eff9-4677-b8cf-b7bbb5d2385e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-100, 340], "parameters": {"color": 3, "width": 840, "height": 80, "content": "## Within N8N, there will be a chat button to test, or you can use the external chat link from the trigger."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "778e2544-db78-4836-8bd1-771f333a621c", "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generic AI Tool Agent", "type": "ai_languageModel", "index": 0}]]}, "Chat Memory Buffer": {"ai_memory": [[{"node": "When chat message received", "type": "ai_memory", "index": 0}, {"node": "Generic AI Tool Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Generic AI Tool Agent", "type": "main", "index": 0}]]}, "A tool for inputting the city and obtaining geolocation": {"ai_tool": [[{"node": "Generic AI Tool Agent", "type": "ai_tool", "index": 0}]]}, "A tool to get the weather forecast based on geolocation": {"ai_tool": [[{"node": "Generic AI Tool Agent", "type": "ai_tool", "index": 0}]]}}}