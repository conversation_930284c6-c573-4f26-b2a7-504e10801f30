{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "645799b0-7ddb-4acb-a95d-3b04eadff445", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1480, 20], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "e2923385-2f73-439c-9d5c-5a3c560993cb", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2040, 420], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "c24728f9-73b9-45f7-9c4e-aee872c59714", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3180, -80], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0bc19e46-4a65-45fb-9571-d1f00d204c63", "name": "OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2060, -261], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0c631234-125d-476b-b97a-2837d6a32f2b", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-272, -180], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "96c9931d-d286-42f8-9629-2641eaa368b9", "name": "Get Issue Comments", "type": "n8n-nodes-base.jira", "position": [748, -180], "parameters": {"options": {}, "issueKey": "={{ $json.key }}", "resource": "issueComment", "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "18a2770d-5240-4837-8837-4821f73ec560", "name": "Close Issue", "type": "n8n-nodes-base.jira", "position": [2660, -741], "parameters": {"issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "operation": "update", "updateFields": {"statusId": {"__rl": true, "mode": "list", "value": "31", "cachedResultName": "Done"}}}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "83e81448-26c7-4c29-a17a-409c53e05881", "name": "Send Reminder", "type": "n8n-nodes-base.jira", "position": [3500, -220], "parameters": {"comment": "={{ $json.text }}\n(this is an automated message)", "options": {}, "issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "5fed9245-4af9-4de7-b021-750d2ba39e63", "name": "Join <PERSON>nts", "type": "n8n-nodes-base.aggregate", "position": [928, -180], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "34712dd3-0348-4709-8a68-07279242910c", "name": "Add Autoclose Message", "type": "n8n-nodes-base.jira", "position": [2460, -561], "parameters": {"comment": "=Autoclosing due to inactivity. Please create a new ticket if you require additional support. Thank you!\n(this is an automated message)", "options": {}, "issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "c43a3b66-838b-4970-a85f-dc0370437388", "name": "Ask For Feedback Message", "type": "n8n-nodes-base.jira", "position": [2460, -741], "parameters": {"comment": "=[~accountid:{{ $('Get Issue Metadata').item.json.reporter_accountId }}]\n\nWe think the issue is resolved so we're autoclosing it. If you've been satisified with our service, please leave us a 5 start review here: [link](link/to/review_site)\n\nPlease feel free to create another ticket if you need further assistance.\n(this is an automated message)", "options": {}, "issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "3223ce45-9e5e-471c-9015-75e9f28088e9", "name": "Simplify Thread For AI", "type": "n8n-nodes-base.set", "position": [1108, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f65c5971-c90d-47f2-823f-37fd03d8e9c7", "name": "thread", "type": "array", "value": "={{\n$json.data.map(comment => {\n const { accountId, displayName } = comment.author;\n\n const message = comment.body.content.map(item =>\n `<${item.type}>${item.content\n .filter(c => c.text || c.content)\n .map(c => c.content\n ? c.content\n .filter(cc => c.text || c.content)\n .map(cc => cc.text)\n .join(' ')\n : c.text\n )}</${item.type}>`\n ).join('');\n return `${displayName} (accountId: ${accountId}) says: ${message}`;\n})\n\n}}"}, {"id": "7b98b2db-3417-472f-bea2-a7aebe30184c", "name": "topic", "type": "string", "value": "={{\n[\n `title: ${$('Get Issue Metadata').item.json.title}`,\n `original message: ${$('Get Issue Metadata').item.json.description.replaceAll(/\\n/g, ' ')}`,\n `reported by: ${$('Get Issue Metadata').item.json.reporter}`\n].join('\\n')\n}}"}]}}, "typeVersion": 3.4}, {"id": "e6f91099-1fe6-4930-8dda-b19330edb599", "name": "Solution Found?", "type": "n8n-nodes-base.if", "position": [2440, 220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e71783b-3072-421a-852c-58940d0dd7cd", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.output.solution_found }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "696348a5-c955-47eb-ab44-f56652587944", "name": "Reply to Issue", "type": "n8n-nodes-base.jira", "position": [2760, 220], "parameters": {"comment": "=Hey there!\n{{ $('KnowledgeBase Agent').item.json.output.response }}\nWe'll close this issue now but feel free to create a new one if needed.\n(this is an automated message)", "options": {}, "issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "4d4562c7-f5ed-44b8-9292-9c1a75d51173", "name": "Last Message is Not Bot", "type": "n8n-nodes-base.if", "position": [3000, -220], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6e07d5dc-01b2-4735-8fc1-983fc57dfaaf", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ !$('Simplify Thread For AI').item.json.thread.last().includes('this is an automated message') }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "e1ca19da-c030-478b-a488-dcb08d9be97e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2400, 420], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"solution_found\": {\n\t\t\t\"type\": \"boolean\"\n\t\t},\n \"short_summary_of_issue\": {\n \"type\": \"string\"\n },\n\t\t\"response\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "596ef421-beb0-4523-a313-3f6ccd9e8f0c", "name": "Get Issue Metadata", "type": "n8n-nodes-base.set", "position": [568, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "200706ea-6936-48ae-a46c-38d6e2eff558", "name": "key", "type": "string", "value": "={{ $json.key }}"}, {"id": "3e3584bf-dc5c-408a-896c-1660710860f6", "name": "title", "type": "string", "value": "={{ $json.fields.summary }}"}, {"id": "e1d89014-5e07-4752-9e7c-ae8d4cba6f6e", "name": "url", "type": "string", "value": "={{\n[\n 'https:/',\n $json.self.extractDomain(),\n 'browse',\n $json.key\n ].join('/')\n}}"}, {"id": "df1cca88-1c57-475d-968e-999f6c25dba7", "name": "date", "type": "string", "value": "={{ DateTime.fromISO($json.fields.created).format('yyyy-MM-dd') }}"}, {"id": "7fc9c625-e741-43bb-9223-b8024fc86cc7", "name": "reporter", "type": "string", "value": "={{ $json.fields.reporter.displayName }}"}, {"id": "17bf06ae-fcad-4eb3-add8-11ac85e9a68e", "name": "reporter_url", "type": "string", "value": "={{\n[\n 'https:/',\n $json.fields.reporter.self.extractDomain(),\n 'jira',\n 'people',\n $json.fields.reporter.accountId\n ].join('/')\n}}"}, {"id": "7624642f-f76b-41ec-b402-280b64d46400", "name": "reporter_accountId", "type": "string", "value": "={{ $json.fields.reporter.accountId }}"}, {"id": "0fa1d73f-4e8b-435b-a78d-37e95c85c87c", "name": "description", "type": "string", "value": "={{ $json.fields.description }}"}]}}, "typeVersion": 3.4}, {"id": "23bb0cf8-c682-416c-a809-e9ca6fc480ef", "name": "Notify Slack Channel", "type": "n8n-nodes-base.slack", "position": [2600, 380], "parameters": {"select": "channel", "blocksUi": "={{\n{\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Hey there 👋\\nI found a zombie ticket that no one has taken a look at yet.\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": `*[${$('Get Issue Metadata').item.json.key}] ${$('Get Issue Metadata').item.json.title}*\\n${$('KnowledgeBase Agent').item.json.output.short_summary_of_issue}\\n👤 <${$('Get Issue Metadata').item.json.reporter_url}|${$('Get Issue Metadata').item.json.reporter}> 📅 ${$('Get Issue Metadata').item.json.date} 🔗 <${$('Get Issue Metadata').item.json.url}|Link to Issue>\\n`\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"I couldn't find an answer in the knowledgebase so I've notified the user and closed the ticket. Thanks!\"\n\t\t\t}\n\t\t}\n\t]\n}\n}}", "channelId": {"__rl": true, "mode": "list", "value": "C07S0NQ04D7", "cachedResultName": "n8n-jira"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.2}, {"id": "21076f8f-8462-4a5a-8831-709a138639c5", "name": "Close Issue2", "type": "n8n-nodes-base.jira", "position": [2920, 220], "parameters": {"issueKey": "={{ $('Get Issue Metadata').item.json.key }}", "operation": "update", "updateFields": {"statusId": {"__rl": true, "mode": "list", "value": "31", "cachedResultName": "Done"}}}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "6c9b30c5-d061-4b4d-b4fa-596ca0768297", "name": "Get List of Unresolved Long Lived Issues", "type": "n8n-nodes-base.jira", "position": [-72, -180], "parameters": {"limit": 10, "options": {"jql": "status IN (\"To Do\", \"In Progress\") AND created <= -7d"}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "1c6c2919-c48b-47bb-a975-f184bd9e95dd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-337.*************, -425.*************], "parameters": {"color": 7, "width": 640.*************, "height": 484.************, "content": "## 1. Search For Unresolved Long-lived JIRA Issues\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira)\n\nIn this demonstration, we'll define \"long-lived\" as any issue which is unresolved after 7 days. Adjust to fit your own criteria.\n\nWe'll also use the Execute Workflow node to run the issues separate in parallel. This is a performance optimisation and if not required, the alternative is to use a loop node instead."}, "typeVersion": 1}, {"id": "f21d95a7-0cef-4110-a3b9-59c562b2ea24", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [128, -180], "parameters": {"mode": "each", "options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}}, "typeVersion": 1.1}, {"id": "e9f9e6e6-c66d-4e50-b4d4-3931b8cf40c9", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [388, -180], "parameters": {}, "typeVersion": 1}, {"id": "91b5e024-6141-47e8-99ff-9ac25df7df48", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [320, -353.43597793972225], "parameters": {"color": 7, "width": 956.5422324510927, "height": 411.91054640922755, "content": "## 2. Retrieves and Combine JIRA Issue Comments\n[Learn more about the JIRA node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira)\n\nTo provide the necessary information for our AI agents, we'll fetch and combine all the issue's comments along with our issue. This gives a accurate history of the the issues progress (or lack thereof!)."}, "typeVersion": 1}, {"id": "9b545aa8-d2df-4500-8af0-ee55b0fcc736", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1300, -381.8893508540474], "parameters": {"color": 7, "width": 653.0761795166852, "height": 583.0290516595711, "content": "## 3. Classify the Current State of the Issue\n[Learn more about the Text Classifier node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.text-classifier)\n\nToday's AI/LLMs are well suited for solving contextual problems like determining issue state. Here, we can use the text classifier node to analyse the issue as a whole to determine our next move. Almost like a really, really smart Switch node!\n\nThere are 3 branches we want to take: Check if a resolution was reached, blocked issues and auto-resolving when no team member has yet to respond."}, "typeVersion": 1}, {"id": "abe0da8f-4107-4641-b992-1a31f71ce530", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1980, -820], "parameters": {"color": 7, "width": 896.1509781357872, "height": 726.4699654775604, "content": "## 4. Sentiment Analysis on Issue Resolution\n[Read more about the Sentiment Analysis node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.sentimentanalysis)\n\nThe Sentiment Analysis node is a convenient method of assessing\ncustomer satisfaction from resolved issues. Here, when resolution\nis detected as positive, we can ask use the opportunity to\ncapitalise of the favourable experience which in this example,\nis to ask for a review. In the opposite vein, if the exchange has\nbeen negative, we can escalate in an attempt to improve\nthe situation before closing the ticket.\n\nAI can equip teams to provide unrivalled customer support\nwhich can differentiate themselves significantly against\nthe competition."}, "typeVersion": 1}, {"id": "d9c97501-e2cf-4a7e-86cc-c295d69db939", "name": "Customer Satisfaction Agent", "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "position": [2060, -400], "parameters": {"options": {}, "inputText": "=issue:\n{{ $('Simplify Thread For AI').item.json.topic }}\n\ncomments:\n{{ $('Simplify Thread For AI').item.json.thread.join('\\n') }}"}, "typeVersion": 1}, {"id": "2829d591-8347-4683-be10-663872c08546", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1980, -60], "parameters": {"color": 7, "width": 1120.504487917144, "height": 675.5857025907994, "content": "## 5. Attempt to Resolve The Issue With KnowledgeBase\n[Read more about the AI Agent node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nWhen the issue is unaddressed, we can attempt to resolve the issue automatically using AI. Here an AI agent can easily be deployed with\naccess to knowledge tools to research and generate solutions for the user. Since n8n v1.62.1, AI Tools Agents can attach nodes directly as\ntools providing a very easy way to linking documents to the LLM.\n\nHere, we use both the JIRA tool to search for similar issues and the notion tool to query for product pages. If a solution can be generated,\nwe create a new comment with the solution and attach it to the issue. If not, then we can leave a simple message notifying the user that we could not do so. Finally, we close the issue as no further action can likely be taken in this case."}, "typeVersion": 1}, {"id": "112c9fd3-c104-4a68-8e58-96a317fef854", "name": "KnowledgeBase Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2060, 220], "parameters": {"text": "=issue:\n{{ $('Simplify Thread For AI').item.json.topic }}\n\ncomments:\n{{ $('Simplify Thread For AI').item.json.thread.join('\\n') }}", "options": {"systemMessage": "Help the user answer their question using the company's knowledgebase. Your answer must be based factually on documents retrieved from the knowledge. If no relevant information is found or the information is insufficent to answer the user's query, you must tell the user so and not mislead the user. If you don't know the answer, it is okay to say you don't know."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "c27e0679-29a0-45d7-ada7-9727975b5069", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2900, -421.245651256349], "parameters": {"color": 7, "width": 801.0347525891818, "height": 507.581094640126, "content": "## 6. Notify for Unanswered Questions or Response Waiting\n[Read more about the Basic LLM Chain node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nIn this step, where signals indicate that the issue is not yet ready to be close, we can try to re-engage issue participants by summarize the conversation so far and sending a reminder comment for any pending actions that were requested. This action can help reduce the number of issues which linger for too long."}, "typeVersion": 1}, {"id": "0a7da82e-789b-401c-80d0-de3ade51942c", "name": "Issue Reminder Agent", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3180, -220], "parameters": {"text": "=issue:\n{{ $('Simplify Thread For AI').item.json.topic }}\n\ncomments:\n{{ $('Simplify Thread For AI').item.json.thread }}", "messages": {"messageValues": [{"message": "=The user has a pending issue and some time has passed since the last update. Analyse the last message in this thread and generate a short reminder message to add to the issue comments which summarizes and reiterates what pending action or information is required. Return only the message."}]}, "promptType": "define"}, "typeVersion": 1.4}, {"id": "2847136e-b95b-4906-89af-ceb180abb9b0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-820, -560], "parameters": {"width": 454.99286536248565, "height": 619.151728428442, "content": "## Try It Out!\n\n### This n8n template is designed to assist and improve customer support team member capacity by automating the resolution of long-lived and forgotten JIRA issues.\n\n* Schedule Trigger runs daily to check for long-lived unresolved issues and imports them into the workflow.\n* Each Issue is handled as a separate subworkflow by using an execute workflow node. This allows parallel processing.\n* A report is generated from the issue using its comment history allowing the issue to be classified by AI - determining the state and progress of the issue.\n* If determined to be resolved, sentiment analysis is performed to track customer satisfaction. If negative, a slack message is sent to escalate, otherwise the issue is closed automatically.\n* If no response has been initiated, an AI agent will attempt to search and resolve the issue itself using similar resolved issues or from the notion database. If a solution is found, it is posted to the issue and closed.\n* If the issue is blocked and waiting for responses, then a reminder message is added.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!"}, "typeVersion": 1}, {"id": "9edb0847-5dcf-4357-a1d4-537a126e277b", "name": "<PERSON>", "type": "n8n-nodes-base.jiraTool", "position": [2160, 420], "parameters": {"limit": 4, "options": {"jql": "=text ~ \"{{ $fromAI('title', 'the title of the current issue', 'string', '') }}\" AND status IN (\"In Progress\", \"Done\")"}, "operation": "getAll", "descriptionType": "manual", "toolDescription": "Call this tool to search for similar issues in JIRA."}, "credentials": {"jiraSoftwareCloudApi": {"id": "IH5V74q6PusewNjD", "name": "Jira SW Cloud account"}}, "typeVersion": 1}, {"id": "573c1b75-35ae-40f0-aa6e-c1372f83569b", "name": "Query KnowledgeBase", "type": "n8n-nodes-base.notionTool", "position": [2280, 420], "parameters": {"text": "={{ $fromAI('search_terms', 'relevant terms to search for information on the current issue', 'string', '') }}", "limit": 4, "options": {}, "operation": "search", "descriptionType": "manual", "toolDescription": "Search the knowledgebase for information relevant to the issue."}, "credentials": {"notionApi": {"id": "iHBHe7ypzz4mZExM", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "1274f6ff-16d9-4d86-b75a-59755390a07c", "name": "Report Unhappy Resolution", "type": "n8n-nodes-base.slack", "position": [2660, -400], "parameters": {"text": "=", "select": "channel", "blocksUi": "={{\n{\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Hey there 👋\\nI found a unclosed ticket which was resolved but thread overall has a negative sentiment score. Please address or close the ticket.\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": `*[${$('Get Issue Metadata').item.json.key}] ${$('Get Issue Metadata').item.json.title}*\\n${$('KnowledgeBase Agent').item.json.output.short_summary_of_issue}\\n👤 <${$('Get Issue Metadata').item.json.reporter_url}|${$('Get Issue Metadata').item.json.reporter}> 📅 ${$('Get Issue Metadata').item.json.date} 🔗 <${$('Get Issue Metadata').item.json.url}|Link to Issue>\\n`\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"divider\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\"text\": \"Thanks!\"\n\t\t\t}\n\t\t}\n\t]\n}\n}}", "channelId": {"__rl": true, "mode": "list", "value": "C07S0NQ04D7", "cachedResultName": "n8n-jira"}, "messageType": "block", "otherOptions": {}}, "credentials": {"slackApi": {"id": "VfK3js0YdqBdQLGP", "name": "Slack account"}}, "typeVersion": 2.2}, {"id": "3226d576-c3ae-444a-b0c5-ac797d25dd2e", "name": "Classify Current Issue State", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [1480, -140], "parameters": {"options": {}, "inputText": "=issue:\n{{ $('Simplify Thread For AI').item.json.topic }}\n\ncomments:\n{{ $('Simplify Thread For AI').item.json.thread.join('\\n') || 'There are no comments' }}", "categories": {"categories": [{"category": "resolved", "description": "There are human comments and a resolution was found and/or accepted"}, {"category": "pending more information", "description": "There are human comments but no resolution has been reached yet"}, {"category": "still waiting", "description": "Reporter is still waiting on a response. Ignoring automated messages, there are no comments."}]}}, "executeOnce": false, "typeVersion": 1}], "pinData": {}, "connections": {"Join Comments": {"main": [[{"node": "Simplify Thread For AI", "type": "main", "index": 0}]]}, "Reply to Issue": {"main": [[{"node": "Close Issue2", "type": "main", "index": 0}]]}, "Solution Found?": {"main": [[{"node": "Reply to Issue", "type": "main", "index": 0}], [{"node": "Notify Slack Channel", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get List of Unresolved Long Lived Issues", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Classify Current Issue State", "type": "ai_languageModel", "index": 0}]]}, "Find Simlar Issues": {"ai_tool": [[{"node": "KnowledgeBase Agent", "type": "ai_tool", "index": 0}]]}, "Get Issue Comments": {"main": [[{"node": "Join <PERSON>nts", "type": "main", "index": 0}]]}, "Get Issue Metadata": {"main": [[{"node": "Get Issue Comments", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "KnowledgeBase Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Issue Reminder Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "Customer Satisfaction Agent", "type": "ai_languageModel", "index": 0}]]}, "KnowledgeBase Agent": {"main": [[{"node": "Solution Found?", "type": "main", "index": 0}]]}, "Query KnowledgeBase": {"ai_tool": [[{"node": "KnowledgeBase Agent", "type": "ai_tool", "index": 0}]]}, "Issue Reminder Agent": {"main": [[{"node": "Send Reminder", "type": "main", "index": 0}]]}, "Notify Slack Channel": {"main": [[{"node": "Reply to Issue", "type": "main", "index": 0}]]}, "Add Autoclose Message": {"main": [[{"node": "Close Issue", "type": "main", "index": 0}]]}, "Simplify Thread For AI": {"main": [[{"node": "Classify Current Issue State", "type": "main", "index": 0}]]}, "Last Message is Not Bot": {"main": [[{"node": "Issue Reminder Agent", "type": "main", "index": 0}]]}, "Ask For Feedback Message": {"main": [[{"node": "Close Issue", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Get Issue Metadata", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "KnowledgeBase Agent", "type": "ai_outputParser", "index": 0}]]}, "Customer Satisfaction Agent": {"main": [[{"node": "Ask For Feedback Message", "type": "main", "index": 0}], [{"node": "Add Autoclose Message", "type": "main", "index": 0}], [{"node": "Report Unhappy Resolution", "type": "main", "index": 0}]]}, "Classify Current Issue State": {"main": [[{"node": "Customer Satisfaction Agent", "type": "main", "index": 0}], [{"node": "Last Message is Not Bot", "type": "main", "index": 0}], [{"node": "KnowledgeBase Agent", "type": "main", "index": 0}]]}, "Get List of Unresolved Long Lived Issues": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}}}