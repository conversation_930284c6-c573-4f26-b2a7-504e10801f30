{"id": "ImiznkEUWCkKbg1w", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "CoinMarketCap_DEXScan_Agent_Tool", "tags": [], "nodes": [{"id": "c055762a-8fe7-4141-a639-df2372f30060", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-60, 320], "parameters": {"workflowInputs": {"values": [{"name": "sessionId"}, {"name": "message"}]}}, "typeVersion": 1.1}, {"id": "427c5700-f6d4-4e98-b2ef-c8eac986a754", "name": "CoinMarketCap DEXScan Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [360, 320], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "You are an AI agent connected to CoinMarketCap's DEXScan API via a suite of HTTP tools. You can retrieve detailed, real-time, and historical data about decentralized exchanges, spot pairs, liquidity pools, trading activity, and more.\n\nEach tool includes parameter validation and usage guidance to prevent 400 Bad Request errors.\n\n🔧 Available Tools & Descriptions\n1. 📜 DEX Metadata\nPurpose: Retrieve static information (name, logo, URLs, launch date, etc.) for one or more DEXs.\nEndpoint: /v4/dex/listings/info\nRequired: id (one or more CMC DEX IDs)\nOptional: aux → urls, logo, description, date_launched, notice\n\n2. 🌐 DEX Networks List\nPurpose: Get all blockchain networks associated with DEX trading, including metadata.\nEndpoint: /v4/dex/networks/list\nOptional Query Parameters:\n\nstart, limit (pagination)\n\nsort: id, name\n\nsort_dir: asc, desc\n\naux: alternativeName, cryptocurrencyId, cryptocurrencySlug, wrappedTokenId, etc.\n\n3. 📊 DEX Listings Quotes\nPurpose: List all decentralized exchanges with live trading data.\nEndpoint: /v4/dex/listings/quotes\nOptional:\n\nstart, limit, sort, sort_dir, type, aux, convert_id\nSort options: volume_24h, market_share, name, num_markets\n\n4. 🔁 DEX Pair Quotes Latest\nPurpose: Return latest market quotes for one or more spot pairs.\nEndpoint: /v4/dex/pairs/quotes/latest\nRequired: contract_address or network_id or network_slug\nOptional: aux, convert_id, skip_invalid, reverse_order\n\n5. 📈 DEX OHLCV Historical\nPurpose: Retrieve historical OHLCV (Open, High, Low, Close, Volume) data for spot pairs.\nEndpoint: /v4/dex/pairs/ohlcv/historical\nRequired: contract_address or use network+asset identifiers\nOptional:\n\ntime_period: daily, hourly, 1m, 5m, etc.\n\ntime_start, time_end, count, interval\n\naux, convert_id, skip_invalid, reverse_order\n\n6. 🕒 DEX OHLCV Latest\nPurpose: Returns current-day OHLCV data for spot pairs (real-time snapshot).\nEndpoint: /v4/dex/pairs/ohlcv/latest\nRequired: contract_address or network_id or network_slug\nOptional: aux, convert_id, skip_invalid, reverse_order\n\n7. 🧾 DEX Trades Latest\nPurpose: View the latest trades (up to 100) for one or more spot pairs.\nEndpoint: /v4/dex/pairs/trade/latest\nRequired: contract_address or network_id or network_slug\nOptional: aux, convert_id, skip_invalid, reverse_order\n\n8. 🪙 DEX Spot Pairs Latest\nPurpose: List all active spot trading pairs with latest market data.\nEndpoint: /v4/dex/spot-pairs/latest\nRequired (any of):\n\nnetwork_id, network_slug\n\ndex_id, dex_slug\n\nbase_asset_id, base_asset_symbol, base_asset_contract_address\n\nquote_asset_id, quote_asset_symbol, quote_asset_contract_address\nOptional:\n\nlimit, scroll_id\n\nliquidity_min/max, volume_24h_min/max, percent_change_24h_min/max\n\nsort: name, price, volume_24h, etc.\n\nsort_dir: asc, desc\n\naux, convert_id, reverse_order\n\n⚠️ General Rules to Avoid 400 Errors\nAlways include at least one required identifier per endpoint.\n\nUse proper casing and valid slugs (e.g., ethereum, polygon).\n\nAvoid mixing convert and convert_id.\n\nUse comma-separated values when multiple inputs are allowed.\n\nOnly use documented values in aux, sort, and interval fields.\n\nDo not leave required parameters blank or use unsupported types."}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "3101c7f4-cf73-4150-8a17-e11366c07c80", "name": "DEXScan Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-400, 620], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a596e26a-61eb-4fe1-8a07-3a93694beca0", "name": "DEXScan Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-220, 620], "parameters": {}, "typeVersion": 1.3}, {"id": "49bb58dc-d68b-4091-86f1-12e1071b1fbc", "name": "DEX Metadata", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-40, 620], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/listings/info", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "id"}, {"name": "aux", "valueProvider": "modelOptional"}]}, "toolDescription": "Returns static metadata for one or more decentralized exchanges, including launch date, description, URLs, and logos.\n\nHere is a precise and complete **tool description** for the `GET /v4/dex/listings/info` endpoint, formatted for use in your n8n agent system or documentation:\n\n---\n\n### 🧭 **DEX Metadata Tool**\n\n**Purpose:**  \nRetrieve static metadata for one or more decentralized exchanges (DEXs) listed on CoinMarketCap. This includes key profile information such as name, status, description, launch date, logos, official URLs, and important notices.\n\n**Endpoint:**  \n`https://pro-api.coinmarketcap.com/v4/dex/listings/info`\n\n---\n\n**Query Parameters:**\n\n- `id` *(string, required)* – One or more comma-separated CoinMarketCap DEX IDs to fetch metadata for.\n- `aux` *(string, optional)* – A comma-separated list of supplemental fields to include in the response.  \n  Valid values:\n  - `\"urls\"` – Official website, social media, and documentation links\n  - `\"logo\"` – Hosted logo (default 64x64, customizable size via URL)\n  - `\"description\"` – Platform description\n  - `\"date_launched\"` – ISO 8601 launch date timestamp\n  - `\"notice\"` – Markdown-formatted warning about operational issues or status alerts\n\n---\n\n**Returns (200 OK):**\n\nFor each exchange:\n- `id`: Unique CoinMarketCap ID\n- `name`: Exchange name\n- `slug`: URL-friendly name\n- `status`: `\"active\"` or `\"inactive\"`\n- `description`: (if requested via `aux`)\n- `date_launched`: (if requested via `aux`)\n- `logo`: (if requested via `aux`)\n- `urls`: (if requested via `aux`)\n- `notice`: Operational message, if applicable\n\n---\n\n**Error Handling:**\n\n- **400 Bad Request**: Triggered if the `id` is missing or invalid.\n- **⚠️ Large Response Alert:** If multiple IDs return too much metadata and exceed the GPT model’s context window, notify with:  \n  > **\"⚠️ The returned metadata exceeds processing limits. Please reduce the number of IDs or narrow the `aux` parameters.\"**\n\n", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "f87838f8-0840-4a78-9d4b-96c79a137d48", "name": "DEX Networks List", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [140, 620], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/networks/list", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "start", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "sort", "valueProvider": "modelOptional"}, {"name": "sort_dir", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}]}, "toolDescription": "Returns a list of all decentralized exchange (DEX) blockchain networks along with their CoinMarketCap IDs and associated metadata.\n\nOptional query parameters (to avoid errors):\n\nstart (string) – Offset for pagination (1-based index)\n\nlimit (string) – Number of results to return (e.g. \"100\")\n\nsort (string) – Sort field. Valid values: \"id\", \"name\"\n\nsort_dir (string) – Sort order. Valid values: \"asc\", \"desc\"\n\naux (string) – Comma-separated list of extra fields. Valid values:\n\"alternativeName\", \"cryptocurrencyId\", \"cryptocurrenySlug\",\n\"wrappedTokenId\", \"wrappedTokenSlug\", \"tokenExplorerUrl\",\n\"poolExplorerUrl\", \"transactionHashUrl\"\n\nMake sure input values for sort, sort_dir, and aux are spelled exactly as shown to prevent a 400 error response.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "3f356c20-4efc-479b-b0fa-ab346c7340d8", "name": "DEX Listings Quotes", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [360, 620], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/listings/quotes", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "start", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "sort", "valueProvider": "modelOptional"}, {"name": "sort_dir", "valueProvider": "modelOptional"}, {"name": "type", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}]}, "toolDescription": "Returns a paginated list of all decentralized exchanges (DEXs) with their latest market data, including market share, quote volume, trading pairs, and multi-currency conversions.\n\n🛠️ Acceptable Query Inputs:\nstart – Offset pagination start (e.g., \"1\")\n\nlimit – Max results to return\n\nsort – Valid: \"name\", \"volume_24h\", \"market_share\", \"num_markets\"\n\nsort_dir – \"asc\" or \"desc\"\n\ntype – \"all\", \"orderbook\", \"swap\", \"aggregator\"\n\naux – Optional extras, e.g., \"date_launched\"\n\nconvert_id – Currency IDs to convert quote values (up to 30)", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "3fceb769-621f-4e7f-9351-4052d72b3346", "name": "DEX Pair Quotes Latest", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [560, 620], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/pairs/quotes/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "contract_address", "valueProvider": "modelOptional"}, {"name": "network_id", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}, {"name": "skip_invalid", "valueProvider": "modelOptional"}, {"name": "reverse_order", "valueProvider": "modelOptional"}, {"name": "network_slug", "valueProvider": "modelOptional"}]}, "toolDescription": "Returns the latest market quote for one or more DEX spot trading pairs, including metrics like:\n\nLiquidity\n\n24h buy/sell volume\n\nSecurity scans\n\nBuy/sell tax\n\nPercent pooled\n\nPool creation and supply stats\n\n🛠️ Query Parameters Supported:\n\ncontract_address – (Comma-separated smart contract addresses)\n\nnetwork_id – CMC network IDs\n\nnetwork_slug – Slug-formatted network names (e.g., ethereum, polygon)\n\naux – Comma-separated fields:\npool_created, percent_pooled_base_asset, num_transactions_24h,\npool_base_asset, pool_quote_asset, 24h_volume_quote_asset,\ntotal_supply_quote_asset, total_supply_base_asset, holders,\nbuy_tax, sell_tax, security_scan, 24h_no_of_buys, 24h_no_of_sells,\n24h_buy_volume, 24h_sell_volume\n\nconvert_id – Comma-separated CMC currency IDs for conversion\n\nskip_invalid – Pass \"true\" to ignore invalid results\n\nreverse_order – Pass \"true\" to invert base/quote pair display", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "134d7fc5-15cf-4897-92bb-97b18a121e41", "name": "DEX OHLCV Historical", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [740, 620], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/pairs/ohlcv/historical", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "contract_address", "valueProvider": "modelOptional"}, {"name": "network_id", "valueProvider": "modelOptional"}, {"name": "network_slug", "valueProvider": "modelOptional"}, {"name": "time_period", "valueProvider": "modelOptional"}, {"name": "time_start", "valueProvider": "modelOptional"}, {"name": "time_end", "valueProvider": "modelOptional"}, {"name": "count", "valueProvider": "modelOptional"}, {"name": "interval", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}, {"name": "skip_invalid", "valueProvider": "modelOptional"}, {"name": "reverse_order", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves historical OHLCV (Open, High, Low, Close, Volume) data for DEX spot trading pairs. It includes optional data on security risks, transaction activity, supply, and tax information. Supports flexible time range and sampling intervals for charting or backtesting.\n\n✅ Valid Query Parameters:\n\ncontract_address (string) – A single DEX spot pair’s smart contract address (required unless using network + asset options).\n\nnetwork_id (string) – One or more CMC network IDs.\n\nnetwork_slug (string) – Friendly name slug of the network (e.g., \"ethereum\").\n\ntime_period (string) – Time unit for OHLCV data.\nAllowed: \"daily\", \"hourly\", \"1m\", \"5m\", \"15m\", \"30m\", \"4h\", \"8h\", \"12h\", \"weekly\", \"monthly\"\n(Default: \"daily\")\n\ntime_start (string) – ISO or Unix timestamp for start (e.g., \"2024-01-01\").\n\ntime_end (string) – ISO or Unix timestamp for end (e.g., \"2024-03-01\"). Optional.\n\ncount (string) – Number of time intervals to return (default is 10, max is 500).\n\ninterval (string) – How frequently to sample the time_period. Same options as time_period.\n\naux (string) – Optional comma-separated extra fields to return: pool_created, percent_pooled_base_asset, num_transactions_24h,\npool_base_asset, pool_quote_asset, 24h_volume_quote_asset,\ntotal_supply_quote_asset, total_supply_base_asset, holders,\nbuy_tax, sell_tax, security_scan, 24h_no_of_buys, 24h_no_of_sells,\n24h_buy_volume, 24h_sell_volume\n\nconvert_id (string) – Comma-separated CMC currency IDs for market conversion (e.g., \"1,2781\").\n\nskip_invalid (string) – Pass \"true\" to skip failed lookups in bulk queries.\n\nreverse_order (string) – Pass \"true\" to reverse base/quote asset display order.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "bee1d41a-af4c-442d-b0c6-7be0e17c31a5", "name": "DEX OHLCV Latest", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [940, 640], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/pairs/ohlcv/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "contract_address", "valueProvider": "modelOptional"}, {"name": "network_id", "valueProvider": "modelOptional"}, {"name": "network_slug", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}, {"name": "skip_invalid", "valueProvider": "modelOptional"}, {"name": "reverse_order", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool retrieves the most recent OHLCV (Open, High, Low, Close, Volume) data for one or more spot trading pairs on decentralized exchanges. It reflects market activity during the current UTC day and is continuously updated. Suitable for real-time monitoring of decentralized market performance, this tool supports advanced filtering via network identifiers or contract addresses.\n\nRequired Input (at least one of the following):\ncontract_address (string)\nOne or more comma-separated smart contract addresses for the trading pair(s).\n\nExample: \"******************************************\"\n\nnetwork_id (string)\nOne or more CoinMarketCap internal network IDs.\n\nExample: \"1\" (Ethereum)\n\nnetwork_slug (string)\nThe lowercase, hyphenated identifier of a blockchain network.\n\nExample: \"ethereum\", \"binance-smart-chain\"\n\n🧩 Optional Query Parameters:\naux (string)\nComma-separated list of additional fields to enrich the returned data:\nValid values: pool_created, percent_pooled_base_asset, num_transactions_24h,\npool_base_asset, pool_quote_asset, 24h_volume_quote_asset,\ntotal_supply_quote_asset, total_supply_base_asset, holders,\nbuy_tax, sell_tax, security_scan, 24h_no_of_buys, 24h_no_of_sells,\n24h_buy_volume, 24h_sell_volume\n\nExample: \"security_scan,holders,pool_created\"\n\nconvert_id (string)\nCalculate quote values in one or more fiat/crypto currencies using CoinMarketCap IDs.\n\nExample: \"1\" (USD), \"2781\" (BTC)\n\nNote: Cannot be used with the convert symbol-based parameter.\n\nskip_invalid (string)\nAccepts \"true\" or \"false\" (default).\nIf set to \"true\", skips spot pairs with invalid or missing data instead of returning a 400 error.\n\nExample: \"true\"\n\nreverse_order (string)\nAccepts \"true\" or \"false\" (default).\nFlips the order of the token pair if the token contract uses reverse naming convention.\n\nExample: \"true\"\n\n⚠️ Tips to Avoid 400 Errors:\nEnsure at least one of contract_address, network_id, or network_slug is included.\n\nUse correct spelling and case for slugs (e.g., \"ethereum\" not \"Ethereum\").\n\nOnly include valid aux values in a comma-separated list.\n\nDo not use both convert and convert_id at the same time.\n\nAvoid empty strings for required parameters or unsupported values in optional fields.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "742a6f07-d85d-4b6c-9ba6-90940e2b365c", "name": "DEX Trades Latest", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1140, 640], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/pairs/trade/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "contract_address", "valueProvider": "modelOptional"}, {"name": "network_id", "valueProvider": "modelOptional"}, {"name": "network_slug", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}, {"name": "skip_invalid", "valueProvider": "modelOptional"}, {"name": "reverse_order", "valueProvider": "modelOptional"}]}, "toolDescription": "Returns up to the latest 100 trades for one or more DEX spot pairs using CoinMarketCap.\nQuery Parameters Accepted:\n\ncontract_address: (Required*) One or more comma-separated contract addresses.\n\nnetwork_id: CoinMarketCap network ID (alternative to contract_address).\n\nnetwork_slug: URL-friendly network name (alternative to network_id).\n\naux: Optional fields to include: \"transaction_hash\", \"blockchain_explorer_link\".\n\nconvert_id: One or more fiat/crypto IDs to return converted market values.\n\nskip_invalid: \"true\" to skip invalid pairs instead of erroring.\n\nreverse_order: \"true\" to reverse trading pair (e.g., A/B → B/A).\n\nUse at least one of contract_address, network_id, or network_slug to avoid a 400 error.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "3ddf229c-16b2-4610-982e-d5e3c07b0cb0", "name": "DEX Spot Pairs Latest", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1340, 640], "parameters": {"url": "https://pro-api.coinmarketcap.com/v4/dex/spot-pairs/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "network_id", "valueProvider": "modelOptional"}, {"name": "network_slug", "valueProvider": "modelOptional"}, {"name": "dex_id", "valueProvider": "modelOptional"}, {"name": "dex_slug", "valueProvider": "modelOptional"}, {"name": "base_asset_id", "valueProvider": "modelOptional"}, {"name": "base_asset_symbol", "valueProvider": "modelOptional"}, {"name": "base_asset_contract_address", "valueProvider": "modelOptional"}, {"name": "base_asset_ucid", "valueProvider": "modelOptional"}, {"name": "quote_asset_id", "valueProvider": "modelOptional"}, {"name": "quote_asset_symbol", "valueProvider": "modelOptional"}, {"name": "quote_asset_contract_address", "valueProvider": "modelOptional"}, {"name": "quote_asset_ucid", "valueProvider": "modelOptional"}, {"name": "scroll_id", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}, {"name": "liquidity_min", "valueProvider": "modelOptional"}, {"name": "liquidity_max", "valueProvider": "modelOptional"}, {"name": "volume_24h_min", "valueProvider": "modelOptional"}, {"name": "volume_24h_max", "valueProvider": "modelOptional"}, {"name": "no_of_transactions_24h_min", "valueProvider": "modelOptional"}, {"name": "no_of_transactions_24h_max", "valueProvider": "modelOptional"}, {"name": "percent_change_24h_min", "valueProvider": "modelOptional"}, {"name": "percent_change_24h_max", "valueProvider": "modelOptional"}, {"name": "sort", "valueProvider": "modelOptional"}, {"name": "sort_dir", "valueProvider": "modelOptional"}, {"name": "aux", "valueProvider": "modelOptional"}, {"name": "reverse_order", "valueProvider": "modelOptional"}, {"name": "convert_id", "valueProvider": "modelOptional"}]}, "toolDescription": "This tool calls the /v4/dex/spot-pairs/latest endpoint to retrieve a paginated list of all active decentralized exchange (DEX) spot pairs with the latest market data. This includes price, liquidity, trading volume, and more. You can filter, sort, and enrich the results using query parameters.\n\n⚠️ To avoid a 400 Bad Request error, you must include at least one of the following identifiers in your request:\n\nnetwork_id OR network_slug\n\ndex_id OR dex_slug\n\nbase_asset_id, base_asset_symbol, or base_asset_contract_address\n\nquote_asset_id, quote_asset_symbol, or quote_asset_contract_address\n\n✅ Supported Query Parameters\nCore Identifiers (At least one required):\nnetwork_id: One or more comma-separated CoinMarketCap network IDs.\n\nnetwork_slug: One or more comma-separated URL-friendly network slugs.\n\ndex_id: One or more CoinMarketCap DEX exchange IDs.\n\ndex_slug: One or more URL-friendly DEX slugs.\n\nbase_asset_id, base_asset_symbol, base_asset_contract_address: Identify the base asset of the pair.\n\nquote_asset_id, quote_asset_symbol, quote_asset_contract_address: Identify the quote asset of the pair.\n\nbase_asset_ucid / quote_asset_ucid: Optional unique CoinMarketCap identifiers.\n\nPagination:\nscroll_id: For continuous pagination. Use the scroll_id from the previous response to get the next set.\n\nlimit: Maximum number of results to return.\n\nFiltering:\nliquidity_min, liquidity_max: Minimum/maximum liquidity thresholds.\n\nvolume_24h_min, volume_24h_max: Minimum/maximum 24h volume thresholds.\n\nno_of_transactions_24h_min, no_of_transactions_24h_max: Filter by transaction count.\n\npercent_change_24h_min, percent_change_24h_max: 24-hour price change filters.\n\nSorting:\nsort: Sort the results by:\n\nname, date_added, price, volume_24h, percent_change_1h, percent_change_24h, liquidity, fully_diluted_value, no_of_transactions_24h\n\nsort_dir: Direction of sort (asc or desc, default: desc)\n\nAdditional Metadata (Optional):\nUse the aux parameter to enrich the response with supplemental fields:\n\npool_created, percent_pooled_base_asset, num_transactions_24h, pool_base_asset, pool_quote_asset, 24h_volume_quote_asset, total_supply_quote_asset, total_supply_base_asset, holders, buy_tax, sell_tax, security_scan, 24h_no_of_buys, 24h_no_of_sells, 24h_buy_volume, 24h_sell_volume\n\nQuote Conversion:\nconvert_id: Convert pricing to specific fiat or crypto by CMC ID (e.g. convert_id=1,2781 for BTC, USD).\n\n⚠️ Cannot be used with convert (symbol-based conversion).\n\nOther Options:\nreverse_order: Flip the base/quote asset order if needed.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "3dab3ebc-14c5-49a8-bb11-aa82564785a5", "name": "DEXScan Agent Guide", "type": "n8n-nodes-base.stickyNote", "position": [-1580, -760], "parameters": {"width": 1080, "height": 1420, "content": "# 📊 CoinMarketCap DEXScan AI Agent Tool (n8n Workflow)\n\n## 🧠 Multi-Agent System: DEXScan Agent\nThis workflow powers **DEX intelligence capabilities** in the CoinMarketCap AI Analyst ecosystem. It enables deep insights into **liquidity**, **volume**, **spot pairs**, and **trading activity** across decentralized exchanges.\n\n---\n\n### 🔧 Connected DEX Tools:\n1. **DEX Metadata** – `/v4/dex/listings/info`\n2. **DEX Networks List** – `/v4/dex/networks/list`\n3. **DEX Listings Quotes** – `/v4/dex/listings/quotes`\n4. **DEX Pair Quotes Latest** – `/v4/dex/pairs/quotes/latest`\n5. **DEX OHLCV Historical** – `/v4/dex/pairs/ohlcv/historical`\n6. **DEX OHLCV Latest** – `/v4/dex/pairs/ohlcv/latest`\n7. **DEX Trades Latest** – `/v4/dex/pairs/trade/latest`\n8. **DEX Spot Pairs Latest** – `/v4/dex/spot-pairs/latest`\n\n---\n\n## ✅ Key Capabilities:\n- View real-time DEX liquidity and 24h trading volume\n- Retrieve metadata (logos, URLs, launch info) for decentralized exchanges\n- Track market pair quotes, liquidity, and spot pair data\n- Monitor historical OHLCV data for technical analysis\n- Identify recent trades and pair-specific activity\n\n---\n\n## 🧠 Agent Structure\n### **1️⃣ DEXScan Brain**\n- **Type**: GPT-4o Mini\n- **Function**: Understands queries, routes tools, summarizes results\n\n### **2️⃣ Session Memory**\n- Maintains contextual state via memory buffer\n\n### **3️⃣ Tool Triggers**\n- **HTTP Tools:** Each CMC endpoint is mapped to one of 8 tool nodes with rich parameter control\n\n---\n\n## ⚠️ Notes:\n- Use **`contract_address`**, **`network_slug`**, or **`network_id`** in nearly all endpoints\n- Avoid using both `convert` and `convert_id` in the same query\n- Be cautious of high-volume requests which may exceed token limits (e.g., when requesting OHLCV for many intervals)\n\n---\n\n📎 API responses are designed for fine-tuned filtering. You can retrieve:\n- Top trading pairs on Polygon\n- Volume-based DEX rankings\n- Historical trade data for a Uniswap pair\n- Real-time liquidity on Solana spot pairs"}, "typeVersion": 1}, {"id": "af395be6-3683-478e-bffd-087ffa8dc8c9", "name": "DEX Usage + Examples", "type": "n8n-nodes-base.stickyNote", "position": [-100, -760], "parameters": {"color": 5, "width": 960, "height": 1000, "content": "## 📌 How to Use the Workflow\n\n### ✅ Step 1: Provide Inputs\nUse `contract_address`, `network_slug`, `dex_slug`, or `convert_id` as needed\n\n### ✅ Step 2: Trigger From Supervisor Agent\nParent agent forwards user message and sessionId to DEXScan\n\n### ✅ Step 3: DEX Data is Returned\nData can be streamed to Telegram, dashboards, or HTTP response\n\n---\n\n## 🔍 Example API Calls\n\n### 1️⃣ Get Top DEXs by 24h Volume\n```plaintext\nGET /v4/dex/listings/quotes?sort=volume_24h&limit=5\n```\n\n### 2️⃣ Query Historical OHLCV for SOL-USDT\n```plaintext\nGET /v4/dex/pairs/ohlcv/historical?network=solana&pair=SOL-USDT&interval=1d\n```\n\n### 3️⃣ Latest Trades on a Uniswap Pair\n```plaintext\nGET /v4/dex/pairs/trade/latest?contract_address=0x...&network_slug=ethereum\n```\n\n### 4️⃣ Spot Pairs on Polygon\n```plaintext\nGET /v4/dex/spot-pairs/latest?network_slug=polygon&sort=volume_24h\n```\n\n### 5️⃣ DEX Metadata for PancakeSwap\n```plaintext\nGET /v4/dex/listings/info?id=123&aux=logo,description,date_launched\n```"}, "typeVersion": 1}, {"id": "0fcffddb-d343-4739-917a-878925673295", "name": "DEX Errors + IP Notice", "type": "n8n-nodes-base.stickyNote", "position": [1220, -760], "parameters": {"color": 3, "width": 740, "height": 600, "content": "## ⚠️ DEXScan Error Codes & Fixes\n\n| Code | Message |\n|------|---------|\n| `200` | Success |\n| `400` | Bad Request – missing/invalid query |\n| `401` | Unauthorized – invalid API key |\n| `429` | Rate limit exceeded |\n| `500` | Server error |\n\n### 🔍 Fixes:\n- Always use at least 1 required param: `contract_address`, `network_slug`, etc.\n- Double-check slugs and convert ID formats\n- Don’t mix `convert` and `convert_id`\n- Avoid oversized `aux` and `interval` combinations\n\n---\n\n## 🚀 Support & Licensing\n\n🔗 **Don <PERSON> – LinkedIn**  \n[http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2507ec1c-d0e3-45ef-aca6-f16c81f9cf17", "connections": {"DEX Metadata": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEX OHLCV Latest": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEX Networks List": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEX Trades Latest": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEX Listings Quotes": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEXScan Agent Brain": {"ai_languageModel": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_languageModel", "index": 0}]]}, "DEX OHLCV Historical": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEXScan Agent Memory": {"ai_memory": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_memory", "index": 0}]]}, "DEX Spot Pairs Latest": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "DEX Pair Quotes Latest": {"ai_tool": [[{"node": "CoinMarketCap DEXScan Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "CoinMarketCap DEXScan Agent", "type": "main", "index": 0}]]}}}