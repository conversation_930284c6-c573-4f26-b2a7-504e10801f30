{"meta": {"instanceId": "82a17fa4a0b8e81bf77e5ab999d980f392150f2a9541fde626dc5f74857b1f54"}, "nodes": [{"id": "4ea39a4f-d8c1-438f-9738-bfbb906a3d7a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1200, 1020], "parameters": {"width": 253, "height": 342, "content": "## Send customer feedback to OpenAI for sentiment analysis"}, "typeVersion": 1}, {"id": "6962ea41-7d15-4932-919f-21ac94fa1269", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1960, 1180], "parameters": {"width": 253, "height": 342, "content": "## Add new feedback to google sheets"}, "typeVersion": 1}, {"id": "4c8a8984-2d8e-4139-866b-6f3536aced07", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [800, 1600], "parameters": {"width": 1407, "height": 254, "content": "## Instructions\n1. Connect Google sheets\n2. Connect your OpenAi account (api key + org Id)\n3. Create a customer feedback form, use an existing one or use the one below as example. \nAll set!\n\n\n- Here is the example google sheet being used in this workflow: https://docs.google.com/spreadsheets/d/1omWdRbiT6z6GNZ6JClu9gEsRhPQ6J0EJ2yXyFH9Zng4/edit?usp=sharing. You can download it to your account."}, "typeVersion": 1}, {"id": "d43a9574-626d-4817-87ba-d99bdd6f41dc", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [800, 1160], "parameters": {"width": 253, "height": 342, "content": "## Feedback form is submitted"}, "typeVersion": 1}, {"id": "76dab2dc-935f-416e-91aa-5a1b7017ec1b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1600, 1180], "parameters": {"width": 253, "height": 342, "content": "## Merge form data and OpenAI result"}, "typeVersion": 1}, {"id": "9772eac1-8df2-4305-9b2c-265d3c5a9a4a", "name": "Add customer feedback to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2020, 1320], "parameters": {"columns": {"value": {"Category": "={{ $json['What is your feedback about?'] }}", "Sentiment": "={{ $json.text }}", "Timestamp": "={{ $json.submittedAt }}", "Entered by": "=Form", "Customer Name": "={{ $json.Name }}", "Customer contact": "={{ $json['How do we get in touch with you?'] }}", "Customer Feedback": "={{ $json['Your feedback'] }}"}, "schema": [{"id": "Timestamp", "type": "string", "display": true, "required": false, "displayName": "Timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "string", "display": true, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer <PERSON><PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "Customer <PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer Name", "type": "string", "display": true, "required": false, "displayName": "Customer Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer contact", "type": "string", "display": true, "required": false, "displayName": "Customer contact", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Entered by", "type": "string", "display": true, "required": false, "displayName": "Entered by", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Urgent?", "type": "string", "display": true, "required": false, "displayName": "Urgent?", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Sentiment", "type": "string", "display": true, "required": false, "displayName": "Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1omWdRbiT6z6GNZ6JClu9gEsRhPQ6J0EJ2yXyFH9Zng4/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1omWdRbiT6z6GNZ6JClu9gEsRhPQ6J0EJ2yXyFH9Zng4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1omWdRbiT6z6GNZ6JClu9gEsRhPQ6J0EJ2yXyFH9Zng4/edit?usp=drivesdk", "cachedResultName": "CustomerFeedback"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "3", "name": "Google Sheets account"}}, "typeVersion": 4.1}, {"id": "********-c81b-4a0e-814e-************", "name": "Merge sentiment with form content", "type": "n8n-nodes-base.merge", "position": [1680, 1320], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}, {"id": "235edf5b-7724-4712-8dc5-d8327a0620b8", "name": "Classify feedback with OpenAI", "type": "n8n-nodes-base.openAi", "position": [1280, 1180], "parameters": {"prompt": "=Classify the sentiment in the following customer feedback: {{ $json['Your feedback'] }}", "options": {}}, "credentials": {"openAiApi": {"id": "s2iucY0IctjYNbrb", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "af4b22aa-0925-40b1-a9ac-298f9745a98e", "name": "Submit form with customer feedback", "type": "n8n-nodes-base.formTrigger", "position": [860, 1340], "webhookId": "e7bf682e-48e8-40de-9815-cd180cdd1480", "parameters": {"options": {"formSubmittedText": "Your response has been recorded"}, "formTitle": "Customer <PERSON><PERSON><PERSON>", "formFields": {"values": [{"fieldLabel": "Name", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "What is your feedback about?", "fieldOptions": {"values": [{"option": "Product"}, {"option": "Service"}, {"option": "Other"}]}, "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Your feedback", "requiredField": true}, {"fieldLabel": "How do we get in touch with you?"}]}, "formDescription": "Please give feedback about our company orproducts."}, "typeVersion": 1}], "connections": {"Classify feedback with OpenAI": {"main": [[{"node": "Merge sentiment with form content", "type": "main", "index": 0}]]}, "Merge sentiment with form content": {"main": [[{"node": "Add customer feedback to Google Sheets", "type": "main", "index": 0}]]}, "Submit form with customer feedback": {"main": [[{"node": "Classify feedback with OpenAI", "type": "main", "index": 0}, {"node": "Merge sentiment with form content", "type": "main", "index": 1}]]}}}