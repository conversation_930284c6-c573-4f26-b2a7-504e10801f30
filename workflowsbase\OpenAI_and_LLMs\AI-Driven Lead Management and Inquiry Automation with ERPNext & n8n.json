{
"meta": {
"instanceId": "e4f78845dfed9ddcfba1945ae00d12e9a7d76eab052afd19299228ce02349d86"
},
"nodes": [
{
"id": "23291d25-3e1a-4b0d-9b1d-d066e8c04a1f",
"name": "Customer Lead AI Agent",
"type": "@n8n/n8n-nodes-langchain.agent",
"position": [
-640,
460
],
"parameters": {
"text": "=**System Prompt:**\n\nYou are an AI assistant designed to process new leads and generate appropriate responses. Your role includes analyzing lead notes, categorizing them, and generating an email from the system to inform the relevant contact about the inquiry. Do not send the email as if it is directly from the customer; instead, draft it as a notification from the system summarizing the inquiry.\n\n### **Process Flow**\n\n1. **Analyzing Lead Notes:**\n - Extract key details such as the customer name, organization, contact information, and their specific request. \n - Determine if the inquiry relates to products, services, or solutions offered by the company.\n\n2. **Finding the Appropriate Contact(s):**\n - Search the contact database to find the responsible person(s) for the relevant product, service, or solution. \n - If one person is responsible, provide their email. \n - If multiple people are responsible, list all emails separated by commas.\n\n3. **Generating an Email Notification:**\n - Draft a professional email as a notification from the system.\n - Summarize the customer’s inquiry.\n - Include all relevant details to assist the recipient in addressing the inquiry.\n\n4. **Handling Invalid Leads:**\n - If the inquiry is unrelated to products, services, or solutions (e.g., job inquiries or general product inquiries), classify it as invalid and return: \n `\"Invalid Lead - Not related to products, services, or solutions.\"`\n\n### **Output Requirements**\n\n1. **For Relevant Leads:**\n - **Email Address(es):** Provide the appropriate email(s). \n - **Email Message Body:** Generate an email notification from the system summarizing the inquiry.\n\n2. **For Invalid Leads:**\n - Return: `\"Invalid Lead - Not related to products, services, or solutions.\"`\n\n\n### **Email Template for Relevant Leads**\n\n**Email Address(es):** [Relevant Email IDs]\n\n**Email Message Body:**\n\n_Subject: New Inquiry from Customer Regarding [Product/Service/Solution]_ \n\nDear [Recipient(s)], \n\nWe have received a new inquiry from a customer through our system. Below are the details: \n\n**Customer Name:** [Customer Name] \n**Organization:** [Organization Name] \n**Contact Information:** [Contact Details] \n\n**Inquiry Summary:** \n[Summarized description of the customer's request, e.g., “The customer is seeking to upgrade their restroom facilities with touchless soap dispensers and tissue holders installed behind mirrors. They have requested a site visit to assess the location and provide a proposal.”] \n\n**Action Required:** \nPlease prioritize this inquiry and reach out to the customer promptly to address their requirements. \n\nThank you, \n[Your System Name] \n\n\n### **Example Output**\n\n**Input Lead Notes:**\n*\"Dear Syncbricks, We are looking to Develop Workflow Automation Soluition for our company, can you let us know the details what do you offer in tems of this.\"*\n\n**Output:**\n\n- **Email Address(es):** <EMAIL>\n\n- **Email Message Body:** \n\n_Subject: Workflow Automation Platform Integration_ \n\nDear -Emploiyee Name (s) --, \n\nWe have received a new inquiry from a customer through our system. Below are the details: \n\n**Customer Name:** Amjid Ali \n**Organization:** Syncbricks LLC\n**Contact Information:** ********* \n\n**Inquiry Summary:** \nThe customer is asking for workflow automation for their company \n\n**Action Required:** \nPlease prioritize this inquiry and reach out to the customer promptly to address their requirements. \n\nThank you, \nSyncbricks LLC\n\n---\nHere are the Lead Details\nLead Name : {{ $json.data.lead_name }}\nCompany : {{ $json.data.company_name }}\nSource : {{ $json.data.source }}\nNotes : {{ $json.data.notes }}\nCity : {{ $json.data.city }}\nCountry : {{ $json.data.country }}\nMobile : {{ $json.data.mobile_no }}",
"options": {},
"promptType": "define"
},
"typeVersion": 1.7
},
{
"id": "1831dc36-910b-4a72-a90e-b411f105a8c3",
"name": "OpenAI Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-800,
800
],
"parameters": {
"options": {}
},
"credentials": {
"openAiApi": {
"id": "hTl3a2XqteCwExYY",
"name": "OpenAi account"
}
},
"typeVersion": 1
},
{
"id": "79713c56-2f7c-4872-90e4-331715f54048",
"name": "Abbriviations",
"type": "n8n-nodes-base.googleSheetsTool",
"position": [
-640,
800
],
"parameters": {
"options": {},
"sheetName": {
"__rl": true,
"mode": "list",
"value": "gid=0",
"cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gtdrAe-jjQH9gQdXA9PJ5y3dSAN4i6k_Rs5sDyALIfU/edit#gid=0",
"cachedResultName": "abbrivaitions"
},
"documentId": {
"__rl": true,
"mode": "list",
"value": "1gtdrAe-jjQH9gQdXA9PJ5y3dSAN4i6k_Rs5sDyALIfU",
"cachedResultUrl": "https://docs.google.com/spreadsheets/d/1gtdrAe-jjQH9gQdXA9PJ5y3dSAN4i6k_Rs5sDyALIfU/edit?usp=drivesdk",
"cachedResultName": "Abbriviations List"
}
},
"credentials": {
"googleSheetsOAuth2Api": {
"id": "L3lApjbQfMm36LLX",
"name": "Google Sheets account"
}
},
"typeVersion": 4.5
},
{
"id": "73b1e3c9-4703-4f87-8399-e7a9bf368d4c",
"name": "Lead Body",
"type": "n8n-nodes-base.set",
"position": [
-1640,
640
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "82a674a2-4d12-45f2-b276-cc95cf7b2e93",
"name": "body",
"type": "object",
"value": "={{ $json.body }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "5f25d846-c639-49e5-bea2-160000bfb104",
"name": "Source Website and Status Open",
"type": "n8n-nodes-base.if",
"position": [
-1920,
640
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "2b184de2-a64e-44e3-8f25-************",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.body.source }}",
"rightValue": "Website"
},
{
"id": "9632cf65-11a1-483c-95c8-94bfe84fb243",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.body.status }}",
"rightValue": "Open"
}
]
}
},
"typeVersion": 2.2
},
{
"id": "12ba65c9-0890-4862-9704-98492eb8f637",
"name": "Microsoft Outlook",
"type": "n8n-nodes-base.microsoftOutlook",
"position": [
1180,
580
],
"parameters": {
"subject": "={{ $('Fields for Outlook').item.json.subject }}",
"bodyContent": "={{ $json.html }}\n<a href=\"https://erpnext.syncbricks.com/app/lead/{{ $('Webhook').item.json.body.name }}\" target=\"_blank\" rel=\"noopener noreferrer\">Here is Lead {{ $('Source Website and Status Open').item.json.body.name }} </a>\n",
"toRecipients": "= {{ $('Fields for Outlook').item.json.email_addresses }}",
"additionalFields": {
"bodyContentType": "html"
}
},
"credentials": {
"microsoftOutlookOAuth2Api": {
"id": "9gy3uvf3pmBdpEsq",
"name": "Microsoft Outlook Al Ansari"
}
},
"typeVersion": 2
},
{
"id": "b1410997-3705-4234-918e-a14e4ccc6b70",
"name": "Email Body Text Generated by AI",
"type": "n8n-nodes-base.set",
"position": [
700,
580
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "cdce31fb-2ec9-45ce-a4ac-a6ff9c811dc3",
"name": "email_body",
"type": "string",
"value": "={{ $json.email_body }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "b10684b9-9f72-42b3-a9f9-c54e711ceb59",
"name": "Fields for Outlook",
"type": "n8n-nodes-base.code",
"position": [
360,
600
],
"parameters": {
"jsCode": "// Input text from the `output` field\nconst textOutput = $json?.output || '';\n\n// Function to extract values from the text\nfunction extractFields(text) {\n const fields = {};\n\n // Regular expressions to extract each field\n const emailMatch = text.match(/\\*\\*Email Address\\(es\\):\\*\\*\\s*([^\\n]+)/);\n const subjectMatch = text.match(/_Subject:\\s*([^_]+)/);\n const emailBodyMatch = text.match(/Dear[\\s\\S]+/);\n\n // Assign extracted values to the fields\n fields.email_addresses = emailMatch ? emailMatch[1].trim() : null;\n fields.subject = subjectMatch ? subjectMatch[1].trim() : null;\n fields.email_body = emailBodyMatch ? emailBodyMatch[0].trim() : null;\n\n return fields;\n}\n\n// Extract fields from the output\nconst extractedFields = extractFields(textOutput);\n\n// Return the fields as JSON\nreturn {\n json: extractedFields\n};\n"
},
"typeVersion": 2
},
{
"id": "e2c10569-fde2-425c-8b20-fdb32a6e2bd5",
"name": "Email Body for Outlook",
"type": "n8n-nodes-base.code",
"position": [
860,
580
],
"parameters": {
"jsCode": "// Input email body\nconst emailBody = $json.email_body || '';\n\n// Function to convert plain text email body into HTML\nfunction formatEmailBodyAsHtml(body) {\n // Replace markdown-like sections with corresponding HTML\n let htmlBody = body\n .replace(/\\*\\*Customer Name:\\*\\* (.+)/, '<p><strong>Customer Name:</strong> $1</p>')\n .replace(/\\*\\*Organization:\\*\\* (.+)/, '<p><strong>Organization:</strong> $1</p>')\n .replace(/\\*\\*Contact Information:\\*\\* (.+)/, '<p><strong>Contact Information:</strong> $1</p>')\n .replace(/\\*\\*Inquiry Summary:\\*\\*\\s*([\\s\\S]+?)(?=\\n\\n\\*\\*Action Required:)/, '<p><strong>Inquiry Summary:</strong> $1</p>')\n .replace(/\\*\\*Action Required:\\*\\*\\s*([\\s\\S]+)/, '<p><strong>Action Required:</strong> $1</p>');\n\n // Wrap each paragraph in `<p>` tags for better readability\n htmlBody = htmlBody\n .replace(/Dear (.+?),/, '<p>Dear <strong>$1</strong>,</p>')\n .replace(/Thank you,\\s+(.+)/, '<p>Thank you,<br><strong>$1</strong></p>');\n\n return htmlBody;\n}\n\n// Convert the email body into HTML\nconst formattedHtmlBody = formatEmailBodyAsHtml(emailBody);\n\n// Return the formatted HTML\nreturn {\n html: formattedHtmlBody\n};\n"
},
"typeVersion": 2
},
{
"id": "3297550b-ed78-4528-ad65-facdc879590a",
"name": "Inquiry has Notes",
"type": "n8n-nodes-base.if",
"position": [
-1080,
640
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "bc81994a-2ad8-4af7-8c58-2c7e58a0fd2e",
"operator": {
"type": "string",
"operation": "exists",
"singleValue": true
},
"leftValue": "={{ $json.data.notes }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "e2544a27-8b6d-4bb0-84f1-00c3a5e66978",
"name": "Inquiry is Valid?",
"type": "n8n-nodes-base.if",
"position": [
40,
620
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "ddd5e8a2-277f-4db6-b38d-28a7b91a2f66",
"operator": {
"type": "string",
"operation": "notEquals"
},
"leftValue": "={{ $json.output }}",
"rightValue": "**Invalid Lead - Not related to products, services, or solutions.**"
}
]
}
},
"typeVersion": 2.2
},
{
"id": "39cc73e7-ceb3-4e8e-a5bc-55648595f784",
"name": "Company Profile",
"type": "n8n-nodes-base.googleDocsTool",
"position": [
-540,
800
],
"parameters": {
"operation": "get",
"documentURL": "you-must-provide-the-doc-id"
},
"credentials": {
"googleDocsOAuth2Api": {
"id": "RdTuYvYpBqEKhIQ3",
"name": "Google Docs account"
}
},
"typeVersion": 2
},
{
"id": "8ee24c59-1acb-4d76-a136-74e69d694a49",
"name": "Company Policies",
"type": "n8n-nodes-base.googleDocsTool",
"position": [
-420,
780
],
"parameters": {
"operation": "get",
"documentURL": "you-must-provide-the-doc-id"
},
"credentials": {
"googleDocsOAuth2Api": {
"id": "RdTuYvYpBqEKhIQ3",
"name": "Google Docs account"
}
},
"typeVersion": 2
},
{
"id": "a5db3aa7-8a77-4553-9c13-a96c51f32745",
"name": "Company Contact Database",
"type": "n8n-nodes-base.googleSheetsTool",
"position": [
-300,
780
],
"parameters": {
"sheetName": {
"__rl": true,
"mode": "list",
"value": "",
"cachedResultUrl": "",
"cachedResultName": ""
},
"documentId": {
"__rl": true,
"mode": "id",
"value": "=Telephone Directory"
}
},
"credentials": {
"googleSheetsOAuth2Api": {
"id": "L3lApjbQfMm36LLX",
"name": "Google Sheets account"
}
},
"typeVersion": 4.5
},
{
"id": "f3e73266-faa4-4e6d-8c60-92669d64233b",
"name": "Sticky Note4",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2000,
257.**************
],
"parameters": {
"color": 6,
"width": 297.**************,
"height": 643.*************,
"content": "### Filter the Lead\nI have done only for theose which are open and where the source is Website. You can remove this if you want to have all leads."
},
"typeVersion": 1
},
{
"id": "0056e35c-4901-406d-9a95-f6da26808841",
"name": "Sticky Note6",
"type": "n8n-nodes-base.stickyNote",
"position": [
-60,
280
],
"parameters": {
"color": 3,
"width": 302.**************,
"height": 660,
"content": "### Output from AI Agent\nIf the INquiry is invalid, not related to the products and services offered, it will invalidate that you can optionnally link the invalid output to email or anything. Options are limitless"
},
"typeVersion": 1
},
{
"id": "5e0e9561-0fb8-4225-aa59-58e25abc8ca1",
"name": "Sticky Note",
"type": "n8n-nodes-base.stickyNote",
"position": [
-880,
280.0000000000002
],
"parameters": {
"color": 7,
"width": 764.2159851725196,
"height": 648.5051458745236,
"content": "### Customer Experience Agent (AI)\nNow this Node is an AI Agent who is speicalized to understand the Lead Source and the Inquiry sent by Cusomter. The Agent will look at company information, which has detials of producuts and services defined in Google Docs, and the Contacts Sheet where a column must be added mentioning that who is the person dealing in which products, solutions and services. Once the inquiry is about speicifc product solution and service it will look from the sheet and then will decide to whom the email has to be sent. Details is defined in the Agent.\nMake sure to drag fields from http request node"
},
"typeVersion": 1
},
{
"id": "5a3ca9c9-07c2-4c74-ba8c-6b14f487fc4d",
"name": "Sticky Note2",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2420,
260
],
"parameters": {
"color": 5,
"width": 398,
"height": 642,
"content": "### Once the Lead is generatd in ERP\n\nConsider creating creating an inquiry web form in ERPNext and let the Website Visitor fill that Inquiry form, as soon as the iqnuiry form is filled this workflow will start.\n\nMake sure to create a webhook in ERPNext. Follow Below steps in ERPNext.\n\nGo to Wehbooks \nDoctype : Lead\nTrigger : on_insert\n\nPaste this webhook there, as test first and finally production"
},
"typeVersion": 1
},
{
"id": "cf930f52-d06b-40c1-91f5-fa1c3dfee09a",
"name": "Sticky Note7",
"type": "n8n-nodes-base.stickyNote",
"position": [
618.1625654107004,
260
],
"parameters": {
"color": 4,
"width": 388.6432532629275,
"height": 662,
"content": "### Email Body\nGet only Email body from Previous Node and then Convert this to HTML Format so that it looks professional. \n"
},
"typeVersion": 1
},
{
"id": "a1023b2b-3e0d-486f-9050-8ff98ff060b5",
"name": "Sticky Note12",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1440,
260
],
"parameters": {
"color": 5,
"width": 248.905549047384,
"height": 654.6630436071407,
"content": "### Get Details of Lead from ERPNext. For us most important is Notes"
},
"typeVersion": 1
},
{
"id": "732046b2-967a-4e0c-85e4-ae04e8c0f9cf",
"name": "Sticky Note13",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1680,
260
],
"parameters": {
"color": 6,
"width": 222.5278407657604,
"height": 651.0941643427163,
"content": "### Get Lead ID\nThis will extract the Lead Name in ERPNext. Ensure to send doc.name from the webhook in ERPnext\n\nIt will then send this to next node to get full details of this lead."
},
"typeVersion": 1
},
{
"id": "b80448ee-5a15-4569-99e4-c3e616a5600d",
"name": "Sticky Note14",
"type": "n8n-nodes-base.stickyNote",
"position": [
1035.2592266730085,
260
],
"parameters": {
"color": 6,
"width": 399.43186296400074,
"height": 662,
"content": "### Send Email\n\nNow drag and drop the fields from Previous Nodes. Email Addresses Subject and Body.\n\nRemember all fields are selected by AI Agent, whom to send email, what to send and so on. \n\nYou can alternatively inform your employees by whatsapp for quick action."
},
"typeVersion": 1
},
{
"id": "3d190d34-f6e0-47bc-9216-d312d1d6ee38",
"name": "Sticky Note11",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2920,
260
],
"parameters": {
"color": 4,
"width": 475.27306699862953,
"height": 636.1483291619771,
"content": "## Developed by Amjid Ali\n\nThank you for using this workflow template. It has taken me countless hours of hard work, research, and dedication to develop, and I sincerely hope it adds value to your work.\n\nIf you find this template helpful, I kindly ask you to consider supporting my efforts. Your support will help me continue improving and creating more valuable resources.\n\nYou can contribute via PayPal here:\n\nhttp://paypal.me/pmptraining\n\nFor Full Course about ERPNext or Automation using AI follow below link\n\nhttp://lms.syncbricks.com\n\nAdditionally, when sharing this template, I would greatly appreciate it if you include my original information to ensure proper credit is given.\n\nThank you for your generosity and support!\nEmail : <EMAIL>\nhttps://linkedin.com/in/amjidali\nhttps://syncbricks.com\nhttps://youtube.com/@syncbricks"
},
"typeVersion": 1
},
{
"id": "cfd7effc-92aa-43c6-9fc5-054b53de74a2",
"name": "Sticky Note16",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1160,
280
],
"parameters": {
"color": 5,
"width": 248.905549047384,
"height": 654.6630436071407,
"content": "### Inquiry with Notes\nIf inquiry is having notes then only it will forward to next node."
},
"typeVersion": 1
},
{
"id": "e5b0992c-e360-4323-82cb-c7ddec45deb5",
"name": "Get Lead Data from ERPNext",
"type": "n8n-nodes-base.httpRequest",
"position": [
-1360,
640
],
"parameters": {
"url": "=https://erpnext.syncbricks.com/api/resource/Lead/{{ $('Source Website and Status Open').item.json.body.name }}",
"options": {},
"authentication": "predefinedCredentialType",
"nodeCredentialType": "erpNextApi"
},
"credentials": {
"erpNextApi": {
"id": "PInpnsxvPkvaiW0z",
"name": "ERPNext account"
}
},
"typeVersion": 4.2
},
{
"id": "********-baf5-4fa6-aa38-0f06881dc267",
"name": "Sticky Note10",
"type": "n8n-nodes-base.stickyNote",
"position": [
280,
280
],
"parameters": {
"color": 3,
"width": 302.**************,
"height": 660,
"content": "### Prepare for Email\nThis node will get approprate Fields for Email \nEmail Addresses:\nSubject : \nEmail Body : "
},
"typeVersion": 1
},
{
"id": "2b4c1e91-c64b-43cb-aba2-c6f8f5a17c79",
"name": "Webhook",
"type": "n8n-nodes-base.webhook",
"position": [
-2300,
640
],
"webhookId": "a39ea4e2-99b7-4ae1-baff-9fb370333e2a",
"parameters": {
"path": "new-lead-generated-in-erpnext",
"options": {},
"httpMethod": "POST"
},
"typeVersion": 2
}
],
"pinData": {},
"connections": {
"Webhook": {
"main": [
[
{
"node": "Source Website and Status Open",
"type": "main",
"index": 0
}
]
]
},
"Lead Body": {
"main": [
[
{
"node": "Get Lead Data from ERPNext",
"type": "main",
"index": 0
}
]
]
},
"Abbriviations": {
"ai_tool": [
[
{
"node": "Customer Lead AI Agent",
"type": "ai_tool",
"index": 0
}
]
]
},
"Company Profile": {
"ai_tool": [
[
{
"node": "Customer Lead AI Agent",
"type": "ai_tool",
"index": 0
}
]
]
},
"Company Policies": {
"ai_tool": [
[
{
"node": "Customer Lead AI Agent",
"type": "ai_tool",
"index": 0
}
]
]
},
"Inquiry has Notes": {
"main": [
[
{
"node": "Customer Lead AI Agent",
"type": "main",
"index": 0
}
]
]
},
"Inquiry is Valid?": {
"main": [
[
{
"node": "Fields for Outlook",
"type": "main",
"index": 0
}
]
]
},
"OpenAI Chat Model": {
"ai_languageModel": [
[
{
"node": "Customer Lead AI Agent",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Fields for Outlook": {
"main": [
[
{
"node": "Email Body Text Generated by AI",
"type": "main",
"index": 0
}
]
]
},
"Customer Lead AI Agent": {
"main": [
[
{
"node": "Inquiry is Valid?",
"type": "main",
"index": 0
}
]
]
},
"Email Body for Outlook": {
"main": [
[
{
"node": "Microsoft Outlook",
"type": "main",
"index": 0
}
]
]
},
"Company Contact Database": {
"ai_tool": [
[
{
"node": "Customer Lead AI Agent",
"type": "ai_tool",
"index": 0
}
]
]
},
"Get Lead Data from ERPNext": {
"main": [
[
{
"node": "Inquiry has Notes",
"type": "main",
"index": 0
}
]
]
},
"Source Website and Status Open": {
"main": [
[
{
"node": "Lead Body",
"type": "main",
"index": 0
}
]
]
},
"Email Body Text Generated by AI": {
"main": [
[
{
"node": "Email Body for Outlook",
"type": "main",
"index": 0
}
]
]
}
}
}AI-Driven Lead Management and Inquiry Automation with ERPNext & n8n