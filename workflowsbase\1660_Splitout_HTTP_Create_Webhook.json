{"id": "SiQUWOBCyXCAA5f9", "meta": {"instanceId": "db80165df40cb07c0377167c050b3f9ab0b0fb04f0e8cae0dc53f5a8527103ca"}, "name": "Generate New Keywords with Search Volumes⚒️⚒️🟢🟢", "tags": [{"id": "bNah9fcKNwQQBzJ1", "name": "SEO DOCTOR", "createdAt": "2024-12-04T12:32:00.284Z", "updatedAt": "2024-12-04T12:32:00.284Z"}, {"id": "L5zcJfTllY0jsuUO", "name": "SEO REPORTS", "createdAt": "2024-12-07T05:13:55.254Z", "updatedAt": "2024-12-07T05:13:55.254Z"}, {"id": "koKAFcp5uch8EPTB", "name": "Public", "createdAt": "2024-12-03T14:36:18.275Z", "updatedAt": "2024-12-03T14:36:18.275Z"}, {"id": "kOC8RBaSMppaZ55G", "name": "Template", "createdAt": "2024-12-14T05:16:52.018Z", "updatedAt": "2024-12-14T05:16:52.018Z"}, {"id": "ntzMTw91GMiOdxEa", "name": "Tools", "createdAt": "2024-12-08T05:39:07.599Z", "updatedAt": "2024-12-08T05:39:07.599Z"}], "nodes": [{"id": "43c6b3ba-ebea-4bd0-ac27-1468d953dc60", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [580, 60], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "fbae5d2f-cc74-40b1-bab5-67775f7b377b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 320], "parameters": {"color": 4, "width": 360, "height": 500, "content": "## Generate new keywords for SEO with the monthly Search volumes\n\nThis workflow is an improvement on the workflows below. It can be used to generate new keywords that you can use for your SEO campaigns or Google ads campaigns\n\n\n[Generate SEO Keyword Search Volume Data using Google API](https://n8n.io/workflows/2494-generate-seo-keyword-search-volume-data-using-google-api/) and [Generating Keywords using Google Autosuggest](https://n8n.io/workflows/2155-generating-keywords-using-google-autosuggest/)\n\n## Usage\n1. Send the keywords you need as an array to this workflow\n2. Pin the data and map it to the `set Keywords`  node\n3. Map the keywords to the Google ads API with the location and Language of your choice\n4. Split the results and set them data \n5. Pass this to the next nodes as needed for storage\n6. Make a copy of this [spreedsheet](https://docs.google.com/spreadsheets/d/10mXXLB987b7UySHtS9F4EilxeqbQjTkLOfMabnR2i5s/edit?usp=sharing) and update the data accordingly\n\n## Having challenges with the google Ads API? Read this [blog ](https://funautomations.io/workflows/automating-keyword-generation-with-n8n-google-ads-api/)\n\nMade by [@Imperol](https://www.linkedin.com/in/zacharia-kimotho/)"}, "typeVersion": 1}, {"id": "b7f0cd29-9475-4871-ad66-dc1bb58e7db3", "name": "Generate new keywords", "type": "n8n-nodes-base.httpRequest", "notes": "Call the endpoint: \n\nhttps://googleads.googleapis.com/v18/customers/{customer_id}:generateKeywordIdeas \n\nwith your Customer id", "position": [360, 60], "parameters": {"url": "https://googleads.googleapis.com/v18/customers/{customer-id}:generateKeywordIdeas", "method": "POST", "options": {}, "jsonBody": "={\n  \"geoTargetConstants\": [\"geoTargetConstants/2840\"], \n  \"includeAdultKeywords\": false,\n  \"pageToken\": \"\",\n  \"pageSize\": 2,\n  \"keywordPlanNetwork\": \"GOOGLE_SEARCH\",\n  \"language\": \"languageConstants/1000\", \n  \"keywordSeed\": {\n    \"keywords\": {{ $json.Keyword }}\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}, {"name": "developer-token", "value": "{developer-token}"}, {"name": "login-customer-id", "value": "{login-customer-id}"}]}, "nodeCredentialType": "googleAdsOAuth2Api"}, "credentials": {"googleAdsOAuth2Api": {"id": "8e6pmlvbsswPATxV", "name": "Google Ads account 2"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 4.2}, {"id": "26ab01fa-b16c-410b-b2cb-e31d81e40c1d", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [800, 60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7413e132-d68a-4f28-91f6-d6e814f95343", "name": "keyword", "type": "string", "value": "={{ $json.text }}"}, {"id": "21526a09-e58d-48e0-b7f7-9766772e3c1d", "name": "competition", "type": "string", "value": "={{ $json.keywordIdeaMetrics.competition }}"}, {"id": "88771e43-8429-49cb-bc49-90b10b3a399c", "name": "avgMonthlySearches", "type": "string", "value": "={{ $json.keywordIdeaMetrics.avgMonthlySearches }}"}, {"id": "41437fb7-4de4-4820-835d-c3fa459dc7ed", "name": "competitionIndex", "type": "string", "value": "={{ $json.keywordIdeaMetrics.competitionIndex }}"}, {"id": "6237440a-cf99-4c25-8b09-015df07f42ef", "name": "lowTopOfPageBidMicros", "type": "string", "value": "={{ ($json[\"keywordIdeaMetrics\"].lowTopOfPageBidMicros / 1000000).toFixed(2) }}"}, {"id": "68fc20e6-ffff-4e72-9da1-7132aad57ca1", "name": "highTopOfPageBidMicros", "type": "string", "value": "={{ ($json.keywordIdeaMetrics.highTopOfPageBidMicros  / 1000000).toFixed(2) }}"}]}}, "typeVersion": 3.4}, {"id": "fa983780-9b3d-4213-b672-bf2f049b162a", "name": "Set Keywords", "type": "n8n-nodes-base.set", "position": [140, 60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "973e949e-1afd-4378-8482-d2168532eff6", "name": "Keyword", "type": "string", "value": "={{ $json.query.Keyword }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "2a6c342a-d471-4a88-aea0-382d4688632f", "name": "Upsert", "type": "n8n-nodes-base.googleSheets", "notes": "Upsert the new keywords to sheets", "position": [1000, 60], "parameters": {"columns": {"value": {}, "schema": [{"id": "keyword", "type": "string", "display": true, "required": false, "displayName": "keyword", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "domain", "type": "string", "display": true, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "uuid", "type": "string", "display": true, "required": false, "displayName": "uuid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "keywordAnnotations", "type": "string", "display": true, "removed": false, "required": false, "displayName": "keywordAnnotations", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "closeVariants", "type": "string", "display": true, "removed": false, "required": false, "displayName": "closeVariants", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "competition", "type": "string", "display": true, "removed": false, "required": false, "displayName": "competition", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "monthlySearchVolumes", "type": "string", "display": true, "removed": false, "required": false, "displayName": "monthlySearchVolumes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "avgMonthlySearches", "type": "string", "display": true, "removed": false, "required": false, "displayName": "avgMonthlySearches", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "competitionIndex", "type": "string", "display": true, "removed": false, "required": false, "displayName": "competitionIndex", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "lowTopOfPageBidMicros", "type": "string", "display": true, "removed": false, "required": false, "displayName": "lowTopOfPageBidMicros", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "highTopOfPageBidMicros", "type": "string", "display": true, "removed": false, "required": false, "displayName": "highTopOfPageBidMicros", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10mXXLB987b7UySHtS9F4EilxeqbQjTkLOfMabnR2i5s/edit#gid=**********", "cachedResultName": "Keyword"}, "documentId": {"__rl": true, "mode": "list", "value": "10mXXLB987b7UySHtS9F4EilxeqbQjTkLOfMabnR2i5s", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10mXXLB987b7UySHtS9F4EilxeqbQjTkLOfMabnR2i5s/edit?usp=drivesdk", "cachedResultName": "SEO DOCTOR: Keyword Tool"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "ZAI2a6Qt80kX5a9s", "name": "Google Sheets account✅ "}}, "typeVersion": 4.5}, {"id": "81f7aea8-8bd4-42da-8115-0e6ffb6a69d2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-80, 60], "parameters": {}, "typeVersion": 1}, {"id": "d043b3ab-682b-49d6-93b3-a65e1a52ce9d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, 320], "parameters": {"color": 4, "width": 340, "height": 500, "content": "## Setup\n\n1. Replace the trigger with your desired trigger eg a webhook or manual trigger\n\n2. Map the data correctly to the `set Keywords` node\n3. On the `Generate new keywords`, Update the `{customer_id} on the url and login-customer-id with your actual one. Update the `developer-token` also with your values. \n\nThe url should be corrected as below https://googleads.googleapis.com/v18/customers/{customer-id}:generateKeywordIdeas\n\nYou should send the headers as below\n\n```\n\n\n            {\n              \"name\": \"content-type\",\n              \"value\": \"application/json\"\n            },\n            {\n              \"name\": \"developer-token\",\n              \"value\": \"{developer-token}\"\n            },\n            {\n              \"name\": \"login-customer-id\",\n              \"value\": \"{login-customer-id}\"\n            }\n         \n    \n\n\n```\n\nand the json body should take the following format \n\n```\n\n{\n  \"geoTargetConstants\": [\"geoTargetConstants/2840\"], \n  \"includeAdultKeywords\": false,\n  \"pageToken\": \"\",\n  \"pageSize\": 2,\n  \"keywordPlanNetwork\": \"GOOGLE_SEARCH\",\n  \"language\": \"languageConstants/1000\", \n  \"keywordSeed\": {\n    \"keywords\": {{ $json.Keyword }}\n  }\n}\n\n```"}, "typeVersion": 1}, {"id": "b1403cba-2a5c-4e51-b230-166b40eb9b1b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [720, 320], "parameters": {"color": 3, "width": 320, "height": 500, "content": "## Troubleshooting\n\n1. If you get an error with the workflow, check the credentials you are using\n\n2. Check the account you are using eg the right customer id and developer token\n\n3. Follow the [guide ](https://funautomations.io/workflows/automating-keyword-generation-with-n8n-google-ads-api/)on the blog to set up your Google ads account "}, "typeVersion": 1}, {"id": "991eeabe-dc2b-49ad-9a00-354a3fc4e9f0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [300, -20], "parameters": {"color": 4, "width": 660, "height": 260, "content": "### Generate new keywords and split the data out to set only the keyword and average monthly search "}, "typeVersion": 1}, {"id": "ba21d189-e34d-468c-8694-7ed4fcc87335", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-120, -20], "parameters": {"color": 4, "width": 400, "height": 260, "content": "### Set up a new trigger and map the data with a column name as keyword"}, "typeVersion": 1}], "active": false, "pinData": {"Trigger": [{"json": {"query": {"Keyword": ["workflow automation software", "enterprise workflow automation", "finance automation software", "saas automation platform", "automation roi calculator", "hr process automation", "data synchronization software", "n8n workflow automation", "scalable business operations", "n8n vs zapier", "lead generation automation", "automation consulting services", "n8n automation", "marketing automation tools", "custom automation solutions", "ecommerce automation solutions", "business process automation", "small business automation", "no code automation", "crm automation integration"]}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "22da1523-1b93-4f95-af67-cd553a744835", "connections": {"Trigger": {"main": [[{"node": "Set Keywords", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Upsert", "type": "main", "index": 0}]]}, "Set Keywords": {"main": [[{"node": "Generate new keywords", "type": "main", "index": 0}]]}, "Generate new keywords": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}}}