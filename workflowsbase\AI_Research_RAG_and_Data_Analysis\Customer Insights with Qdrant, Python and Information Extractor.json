{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "63501cc8-77c9-4037-9f70-da23b6d20b03", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [280, 440], "parameters": {}, "typeVersion": 1}, {"id": "00de989c-d9e9-4b42-b5db-7097800a6017", "name": "Zip Entries", "type": "n8n-nodes-base.set", "position": [1380, 360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "833a554d-2b39-4160-9348-18b17b28ce30", "name": "data", "type": "array", "value": "={{ \n $json.review_author.map((review_author, idx) => ({\n review_author,\n review_author_reviews_count: $json.review_author_reviews_count[idx].replace(' reviews', '').toInt(),\n review_country: $json.review_country[idx],\n review_date: $json.review_date[idx].toDate(),\n review_date_of_experience: $json.review_date_of_experience[idx].replace('Date of experience: ', '').toDate(),\n review_rating: $json.review_rating[idx].toInt(),\n review_text: $json.review_text[idx],\n review_title: $json.review_title[idx],\n review_url: $('Get TrustPilot Page').params.url.match(/https:\\/\\/[^/]+/) + $json.review_url[idx],\n }))\n}}"}]}}, "typeVersion": 3.4}, {"id": "9290e116-c001-49d5-ae4c-d91cd246f2c2", "name": "Extract Reviews", "type": "n8n-nodes-base.html", "position": [1140, 520], "parameters": {"options": {"trimValues": true}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "review_author", "cssSelector": "[data-service-review-card-paper] [data-consumer-name-typography]", "returnArray": true}, {"key": "review_rating", "attribute": "data-service-review-rating", "cssSelector": "[data-service-review-rating]", "returnArray": true, "returnValue": "attribute"}, {"key": "review_title", "cssSelector": "[data-service-review-title-typography]", "returnArray": true}, {"key": "review_text", "cssSelector": "[data-service-review-text-typography]", "returnArray": true}, {"key": "review_date_of_experience", "cssSelector": "[data-service-review-date-of-experience-typography]", "returnArray": true}, {"key": "review_date", "attribute": "datetime", "cssSelector": "[data-service-review-date-time-ago]", "returnArray": true, "returnValue": "attribute"}, {"key": "review_country", "cssSelector": "[data-consumer-country-typography]", "returnArray": true}, {"key": "review_author_reviews_count", "cssSelector": "[data-consumer-reviews-count-typography]", "returnArray": true}, {"key": "review_url", "attribute": "href", "cssSelector": "a[data-review-title-typography]", "returnArray": true, "returnValue": "attribute"}]}}, "typeVersion": 1.2}, {"id": "4aa3e50d-fcce-48a7-8237-c12f8592f69e", "name": "Reviews to List", "type": "n8n-nodes-base.splitOut", "position": [1380, 520], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "a6b9abf9-a17a-4f30-9f90-6183770c4933", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1980, 520], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "review_author", "value": "={{ $json.review_author }}"}, {"name": "review_author_reviews_count", "value": "={{ $json.review_author_reviews_count }}"}, {"name": "review_country", "value": "={{ $json.review_country }}"}, {"name": "review_date", "value": "={{ $json.review_date }}"}, {"name": "review_date_of_experience", "value": "={{ $json.review_date_of_experience }}"}, {"name": "review_rating", "value": "={{ $json.review_rating }}"}, {"name": "review_date_month", "value": "={{ $json.review_date.toDateTime().format('M') }}"}, {"name": "review_date_year", "value": "={{ $json.review_date.toDateTime().format('yyyy') }}"}, {"name": "review_date_of_experience_month", "value": "={{ $json.review_date_of_experience.toDateTime().format('M') }}"}, {"name": "review_date_of_experience_year", "value": "={{ $json.review_date_of_experience.toDateTime().format('yyyy') }}"}, {"name": "company_id", "value": "={{ $('Set Variables').item.json.companyId }}"}, {"name": "review_url", "value": "={{ $json.review_url }}"}]}}, "jsonData": "={{ $json.review_title }}\n{{ $json.review_text }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "afd8907c-9a59-4dcc-94c5-2114fb2a7d5d", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1980, 660], "parameters": {"options": {}, "chunkSize": 4000}, "typeVersion": 1}, {"id": "e22d92b8-e8e9-42aa-9d02-2e70234f11ed", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1860, 520], "parameters": {"model": "text-embedding-3-small", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "f0ea6b63-c96d-4b3f-8a21-d0f2dbb4efc3", "name": "Set Variables", "type": "n8n-nodes-base.set", "position": [520, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2e58a9fa-a14d-4a6c-8cc8-8ec947c791fb", "name": "companyId", "type": "string", "value": "www.freddiesflowers.com"}]}}, "typeVersion": 3.4}, {"id": "0188986f-fbe9-4c06-892a-3cb71b52a309", "name": "Get Payload of Points", "type": "n8n-nodes-base.httpRequest", "position": [1740, 1120], "parameters": {"url": "=http://qdrant:6333/collections/trustpilot_reviews/points", "method": "POST", "options": {}, "jsonBody": "={{\n {\n \"ids\": $json.points,\n \"with_payload\": true\n }\n}}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "5fc6e0b6-507f-4cfd-951b-be3709b86ac2", "name": "Clusters To List", "type": "n8n-nodes-base.splitOut", "position": [1480, 1120], "parameters": {"options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1}, {"id": "f21369b9-1dd5-4b35-a1f3-00fd67794051", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2140, 1340], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "b0075699-6513-4781-b5de-81d1ab81dfe1", "name": "Only Clusters With 3+ points", "type": "n8n-nodes-base.filter", "position": [1480, 1300], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "328f806c-0792-4d90-9bee-a1e10049e78f", "operator": {"type": "array", "operation": "lengthGt", "rightType": "number"}, "leftValue": "={{ $json.points }}", "rightValue": 2}]}}, "typeVersion": 2}, {"id": "f6a6209c-d269-4238-8e92-230df7b41df9", "name": "Set Variables1", "type": "n8n-nodes-base.set", "position": [519, 1220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2e58a9fa-a14d-4a6c-8cc8-8ec947c791fb", "name": "companyId", "type": "string", "value": "={{ $json.companyId }}"}, {"id": "37cf8af2-6f0f-40b1-b822-c9bd6a620a3c", "name": "review_date_from", "type": "string", "value": "={{ $today.startOf('month').toISO() }}"}, {"id": "8d72f739-f832-4c25-b62a-2ae70ad2b1e7", "name": "review_date_to", "type": "string", "value": "={{ $today.endOf('month').toISO() }}"}]}}, "typeVersion": 3.4}, {"id": "85cb48b1-0ab9-4f88-88f3-82fcfb041ebe", "name": "Find Reviews", "type": "n8n-nodes-base.httpRequest", "position": [896, 1160], "parameters": {"url": "=http://qdrant:6333/collections/trustpilot_reviews/points/scroll", "method": "POST", "options": {}, "jsonBody": "={\n \"limit\": 500,\n \"filter\":{\n \"must\": [\n {\n \"key\": \"metadata.company_id\",\n \"match\": { \"value\": \"{{ $('Set Variables1').item.json.companyId }}\" }\n },\n {\n \"key\": \"metadata.review_date\",\n \"range\": {\n \"gte\": \"{{ $('Set Variables1').item.json.review_date_from }}\",\n \"gt\": null,\n \"lt\": null,\n \"lte\": \"{{ $('Set Variables1').item.json.review_date_to }}\"\n }\n }\n ]\n },\n \"with_vector\":true\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "69bbd197-c78f-4dae-9300-fe23d4d49855", "name": "Prep Output For Export", "type": "n8n-nodes-base.set", "position": [2720, 1203], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ {\n ...$json.output,\n \"CompanyID\": $('Set Variables1').item.json.companyId,\n \"From\": $('Set Variables1').item.json.review_date_from,\n \"To\": $('Set Variables1').item.json.review_date_to,\n \"Number of Responses\": $('Get Payload of Points').item.json.result.length,\n \"Raw Responses\": $('Get Payload of Points').item.json.result.map(item =>\n [\n item.payload.metadata.review_date,\n item.payload.metadata.review_author,\n item.payload.metadata.review_rating,\n item.payload.content.replaceAll('\"', '\\\"').replaceAll('\\n', ' '),\n item.payload.metadata.review_url,\n ]\n ).join('\\n')\n} }}\n"}, "typeVersion": 3.4}, {"id": "d77daa23-6acf-4daa-bf4c-33da4d05a54c", "name": "Export To Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2940, 1203], "parameters": {"columns": {"value": {}, "schema": [{"id": "CompanyID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "CompanyID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "From", "type": "string", "display": true, "removed": false, "required": false, "displayName": "From", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "To", "type": "string", "display": true, "removed": false, "required": false, "displayName": "To", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Insight", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Insight", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Sentiment", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Suggested Improvements", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Suggested Improvements", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Number of Responses", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Number of Responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Raw Responses", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Raw Responses", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "name", "value": "=Sheet1"}, "documentId": {"__rl": true, "mode": "id", "value": "=1wAwWCcIZod00IGtxwTbTgjIRbKHu3Yl9wYWJ8GeT2Os"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.4}, {"id": "1f60c3a5-a47a-4313-9b29-8ea652d573f7", "name": "Clear Existing Reviews", "type": "n8n-nodes-base.httpRequest", "position": [760, 440], "parameters": {"url": "http://qdrant:6333/collections/trustpilot_reviews/points/delete", "method": "POST", "options": {}, "jsonBody": "={\n \"filter\": {\n \"must\": [\n {\n \"key\": \"metadata.company_id\",\n \"match\": {\n \"value\": \"{{ $('Set Variables').item.json.companyId }}\"\n }\n }\n ]\n }\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 4.2}, {"id": "61c3117c-757c-45dd-b9d5-1122b793be30", "name": "Trigger Insights", "type": "n8n-nodes-base.executeWorkflow", "position": [2660, 440], "parameters": {"options": {}, "workflowId": "={{ $workflow.id }}"}, "typeVersion": 1}, {"id": "d3c6e81f-34bb-4be9-b869-2c219b87c4fb", "name": "Prep Values For Trigger", "type": "n8n-nodes-base.set", "position": [2460, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "24dd90ad-390f-444e-ba6c-8c06a41e836e", "name": "companyId", "type": "string", "value": "={{ $('Set Variables').item.json.companyId }}"}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "64af9cc7-a194-4427-ba78-d9a1136b962f", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [316, 1220], "parameters": {}, "typeVersion": 1}, {"id": "7b6ba502-36c2-41e6-9d67-781d0d40a569", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [186.*************, 263.2301011325764], "parameters": {"color": 7, "width": 787.3314861380661, "height": 465.52420584035275, "content": "## Step 1. Starting Fresh\nFor this demo, we'll clear any existing records in our Qdrant vector store for the selected company. We do this using the Qdrant's delete points API."}, "typeVersion": 1}, {"id": "a99389d4-8ea6-4379-b725-f30e92b0d29e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1006.3778510483207, 148.50042906971555], "parameters": {"color": 7, "width": 638.5221986278162, "height": 580.2538779032135, "content": "## Step 2. Scraping TrustPilot For Company Reviews\n[Read more about HTTP Request Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nWe'll scrape at the most recent 3 pages of reviews for illustrative purposes but we could easily scrape them all if required. The HTML node offers a convenient way to extract data from the returned html pages and using it, we'll retrieve all the reviews data."}, "typeVersion": 1}, {"id": "139ccadd-9135-4681-b2eb-403b8d8bd710", "name": "Get TrustPilot Page", "type": "n8n-nodes-base.httpRequest", "position": [1140, 360], "parameters": {"url": "=https://uk.trustpilot.com/review/{{ $('Set Variables').item.json.companyId }}?sort=recency", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "page", "value": "={{ $pageCount + 1 }}"}]}, "maxRequests": 3, "limitPagesFetched": true}}}}, "executeOnce": false, "typeVersion": 4.2}, {"id": "1c71db65-713b-4c31-9c11-5ff678fb327a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1680, 140], "parameters": {"color": 7, "width": 638.5221986278162, "height": 689.8000993522735, "content": "## Step 3. Store Reviews in Qdrant\n[Learn more about the Qdrant Vector Store](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreqdrant/)\n\nVector databases are a great way to store data if you're interested in perform similiarity searches which applies here as we want to group similar reviews to find patterns. Qdrant is a powerful vector database and tool of choice because of its robust API implementation and advanced filtering capabilities."}, "typeVersion": 1}, {"id": "a4f82a1b-5a76-46b6-a7a3-84ab09b46699", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1860, 360], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=trustpilot_reviews"}}, "credentials": {"qdrantApi": {"id": "NyinAS3Pgfik66w5", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "cbad9e73-c5b3-474c-95ef-7269addc4e62", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [216, 1000], "parameters": {"color": 7, "width": 543.*************, "height": 453.**************, "content": "## Step 5. The Insight Subworkflow\n[Learn more about Workflow Triggers](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflowtrigger)\n\nThis subworkflow takes the companyId to find the relevant records in our Qdrant vector store. It also takes a \"from\" and \"to\" date to scope the insights to a particular range - doing this we can say something like \"we only want insights for the past month of reviews\". "}, "typeVersion": 1}, {"id": "9c530716-63f4-4368-8d0e-0cdbe8f5b08e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [780, 920], "parameters": {"color": 7, "width": 557.*************, "height": 526.*************, "content": "## Step 6. Apply Clustering Algorithm to Reviews\n[Read more about using Python in n8n](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code)\n\nWe'll retrieve our vectors embeddings for the desired company reviews and perform an advanced clustering algorithm on them. This powerful echnique allows us to quickly group similar embeddings into clusters which we can then use to discover popular feedback, opinions and pain-points!"}, "typeVersion": 1}, {"id": "9790b3a5-cc7c-4e12-8038-fc661c8226f8", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1360, 920], "parameters": {"color": 7, "width": 598.5585287222906, "height": 605.9905193915599, "content": "## Step 7. Fetch Reviews By Cluster\n[Learn more about using the Code Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/)\n\nWith the Qdrant point IDs grouped and returned by our code node, all that's left is to fetch the payload of each. Note that the clustering algorithm isn't perfect and may require some tweaking depending on your data."}, "typeVersion": 1}, {"id": "267057b6-9727-4a45-9d87-5429da42f48e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1980, 969], "parameters": {"color": 7, "width": 587.6069484146701, "height": 552.9535170892194, "content": "## Step 8. Getting Insights from Grouped Reviews\n[Read more about using Information Extractor Node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor)\n\nNext, we'll use our state-of-the-art LLM to generate insights on our reviews. Doing it this way, we'll able to pull more granular results addressing many key topics within the reviews."}, "typeVersion": 1}, {"id": "b8cc07d0-ffa3-425f-ae74-76dcb68fa88f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2600, 980], "parameters": {"color": 7, "width": 572.5638733479158, "height": 464.4019616956416, "content": "## Step 9. Write To Insights Sheet\nFinally, our completed insights to appended to the Insights Sheet we created earlier in the workflow.\n\nYou can find a sample sheet here: https://docs.google.com/spreadsheets/d/e/2PACX-1vQ6ipJnXWXgr5wlUJnhioNpeYrxaIpsRYZCwN3C-fFXumkbh9TAsA_JzE0kbv7DcGAVIP7az0L46_2P/pubhtml"}, "typeVersion": 1}, {"id": "0dac0854-7106-44e3-bd68-fad7b201a6bc", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2340, 240], "parameters": {"color": 7, "width": 519.6419932444072, "height": 429.11782776909047, "content": "## Step 4. Trigger Insights SubWorkflow\n[Learn more about Workflow Triggers](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow)\n\nA subworkflow is used to trigger the analysis for the survey. This separation is optional but used here to better demonstrate the two part process."}, "typeVersion": 1}, {"id": "4aa7e73e-c29d-41df-b2f8-a62109285ccb", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [460, 380], "parameters": {"width": 226.36363118160727, "height": 327.0249036433755, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Set company here!\nTrustpilot must recognise it as part of the url."}, "typeVersion": 1}, {"id": "4d895cf9-452c-401e-a6f3-b9d3a359a96d", "name": "Apply K-means Clustering Algorithm", "type": "n8n-nodes-base.code", "position": [1116, 1160], "parameters": {"language": "python", "pythonCode": "import numpy as np\nfrom sklearn.cluster import KMeans\n\n# get vectors for all answers\npoint_ids = [item.id for item in _input.first().json.result.points]\nvectors = [item.vector.to_py() for item in _input.first().json.result.points]\nvectors_array = np.array(vectors)\n\n# apply k-means clustering where n_clusters = 5\n# this is a max and we'll discard some of these clusters later\nkmeans = KMeans(n_clusters=min(len(vectors), 5), random_state=42).fit(vectors_array)\nlabels = kmeans.labels_\nunique_labels = set(labels)\n\n# Extract and print points in each cluster\nclusters = {}\nfor label in set(labels):\n clusters[label] = vectors_array[labels == label]\n\n# return Qdrant point ids for each cluster\n# we'll use these ids to fetch the payloads from the vector store.\noutput = []\nfor cluster_id, cluster_points in clusters.items():\n points = [point_ids[i] for i in range(len(labels)) if labels[i] == cluster_id]\n output.append({\n \"id\": f\"Cluster {cluster_id}\",\n \"total\": len(cluster_points),\n \"points\": points\n })\n\nreturn {\"json\": {\"output\": output } }"}, "typeVersion": 2}, {"id": "95c57019-d9d7-4d9f-93dd-21d3d9708861", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-260, 40], "parameters": {"width": 400.381109509268, "height": 612.855812336249, "content": "## Try It Out!\n\n### This workflow generates highly-detailed customer insights from Trustpilot reviews. Works best when dealing with a large number of reviews.\n\n* Import Trustpilot reviews and vectorise in Qdrant vectorstore.\n* Identify clusters of popular topics in reviews using K-means clustering algorithm. \n* Each valid cluster is analysed and summarised by LLM.\n* Export LLM response and cluster results back into sheet.\n\nCheck out the reference google sheet here: https://docs.google.com/spreadsheets/d/e/2PACX-1vQ6ipJnXWXgr5wlUJnhioNpeYrxaIpsRYZCwN3C-fFXumkbh9TAsA_JzE0kbv7DcGAVIP7az0L46_2P/pubhtml\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "9bba9480-792e-48e3-ad9f-8809ce3aba09", "name": "Customer Insights Agent", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [2140, 1180], "parameters": {"text": "=The {{ $json.result.length }} reviews were:\n{{\n$json.result.map(item =>\n`* ${item.payload.metadata.review_author} gave ${item.payload.metadata.review_rating} stars: \"${item.payload.content.replaceAll('\"', '\\\"').replaceAll('\\n', ' ')}\"`\n).join('\\n')\n}}", "options": {"systemPromptTemplate": "=You help summarise a selection of trustpilot reviews for a company called \"{{ $json.result[0].payload.metadata.company_id }}\".\nThe {{ $json.result.length }} reviews were selected because their contents were similar in context.\n\nYour task is to: \n* summarise the given reviews into a short paragraph. Provide an insight from this summary and what we could learn from the reviews.\n* determine if the overall sentiment of all the listed responses to be either strongly negative, negative, neutral, positive or strongly positive."}, "schemaType": "fromJson", "jsonSchemaExample": "{\n\t\"Insight\": \"\",\n \"Sentiment\": \"\",\n \"Suggested Improvements\": \"\"\n}"}, "typeVersion": 1}, {"id": "4488deb9-27f6-4f9d-b17e-9b5e7a1bba33", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [180, 760], "parameters": {"color": 5, "width": 323.2987132716669, "height": 80, "content": "### Run this once! \nIf for any reason you need to run more than once, be sure to clear the existing data first."}, "typeVersion": 1}, {"id": "5cb3bd73-1e77-4eba-9d2e-634fdc374330", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [780, 1480], "parameters": {"color": 5, "width": 323.2987132716669, "height": 110.05160146874424, "content": "### First Time Running?\nThere is a slight delay on first run because the code node has to download the required packages."}, "typeVersion": 1}], "pinData": {}, "connections": {"Zip Entries": {"main": [[{"node": "Reviews to List", "type": "main", "index": 0}]]}, "Find Reviews": {"main": [[{"node": "Apply K-means Clustering Algorithm", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Clear Existing Reviews", "type": "main", "index": 0}]]}, "Set Variables1": {"main": [[{"node": "Find Reviews", "type": "main", "index": 0}]]}, "Extract Reviews": {"main": [[{"node": "Zip Entries", "type": "main", "index": 0}]]}, "Reviews to List": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Clusters To List": {"main": [[{"node": "Only Clusters With 3+ points", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Customer Insights Agent", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Get TrustPilot Page": {"main": [[{"node": "Extract Reviews", "type": "main", "index": 0}]]}, "Qdrant Vector Store": {"main": [[{"node": "Prep Values For Trigger", "type": "main", "index": 0}]]}, "Get Payload of Points": {"main": [[{"node": "Customer Insights Agent", "type": "main", "index": 0}]]}, "Clear Existing Reviews": {"main": [[{"node": "Get TrustPilot Page", "type": "main", "index": 0}]]}, "Prep Output For Export": {"main": [[{"node": "Export To Sheets", "type": "main", "index": 0}]]}, "Customer Insights Agent": {"main": [[{"node": "Prep Output For Export", "type": "main", "index": 0}]]}, "Prep Values For Trigger": {"main": [[{"node": "Trigger Insights", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Set Variables1", "type": "main", "index": 0}]]}, "Only Clusters With 3+ points": {"main": [[{"node": "Get Payload of Points", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Apply K-means Clustering Algorithm": {"main": [[{"node": "Clusters To List", "type": "main", "index": 0}]]}}}