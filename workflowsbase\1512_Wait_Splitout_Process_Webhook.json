{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "7076854e-c7e8-45b5-9e5e-16678bffa254", "name": "OpenAI Model", "type": "@n8n/n8n-nodes-langchain.lmOpenAi", "position": [2420, 480], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo-1106", "cachedResultName": "gpt-3.5-turbo-1106"}, "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "00819f1c-2c60-4b7c-b395-445ec05fd898", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2600, 480], "parameters": {"jsonSchema": "{\n \"Invoice date\": { \"type\": \"date\" },\n \"invoice number\": { \"type\": \"string\" },\n \"Purchase order number\": { \"type\": \"string\" },\n \"Supplier name\": { \"type\": \"string\" },\n \"Supplier address\": {\n \"type\": \"object\",\n \"properties\": {\n \"address 1\": { \"type\": \"string\" },\n \"address 2\": { \"type\": \"string\" },\n \"city\": { \"type\": \"string\" },\n \"postcode\": { \"type\": \"string\" }\n }\n },\n \"Supplier VAT identification number\": { \"type\": \"string\" },\n \"Customer name\": { \"type\": \"string\" },\n \"Customer address\": {\n \"type\": \"object\",\n \"properties\": {\n \"address 1\": { \"type\": \"string\" },\n \"address 2\": { \"type\": \"string\" },\n \"city\": { \"type\": \"string\" },\n \"postcode\": { \"type\": \"string\" }\n }\n },\n \"Customer VAT identification number\": { \"type\": \"string\" }, \n \"Shipping addresses\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"address 1\": { \"type\": \"string\" },\n \"address 2\": { \"type\": \"string\" },\n \"city\": { \"type\": \"string\" },\n \"postcode\": { \"type\": \"string\" }\n }\n }\n },\n \"Line items\": {\n \"type\": \"array\",\n \"items\": {\n \"name\": \"string\",\n \"description\": \"string\",\n \"price\": \"number\",\n \"discount\": \"number\"\n }\n },\n \"Subtotal without VAT\": { \"type\": \"number\" },\n \"Subtotal with VAT\": { \"type\": \"number\" },\n \"Total price\": { \"type\": \"number\" }\n}"}, "typeVersion": 1.1}, {"id": "3b40d506-aabc-4105-853a-a318375cea73", "name": "Upload to LlamaParse", "type": "n8n-nodes-base.httpRequest", "position": [1620, 420], "parameters": {"url": "https://api.cloud.llamaindex.ai/api/parsing/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "=attachment_0"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "pZ4YmwFIkyGnbUC7", "name": "LlamaIndex API"}}, "typeVersion": 4.2}, {"id": "57a5d331-8838-4d44-8fac-a44dba35fcc4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1540, 140], "parameters": {"color": 7, "width": 785.9525375246163, "height": 623.4951418211454, "content": "## 2. Advanced PDF Processing with LlamaParse\n[Read more about using HTTP Requests](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nLlamaIndex's LlamaCloud is a cloud-based service that allows you to upload,\nparse, and index document. LlamaParse is a tool offered by LlamaCloud\nto parse for complex PDFs with embedded objects ie PDF Tables and figures.\n\nAt time of writing, you can parse 1000 pdfs/day with LlamaCloud's free plan\nby signing up at [https://cloud.llamaindex.ai/](https://cloud.llamaindex.ai/?ref=n8n.io)."}, "typeVersion": 1}, {"id": "a4504d83-da3b-41bc-891f-f8f9314a6af5", "name": "Receiving Invoices", "type": "n8n-nodes-base.gmailTrigger", "position": [780, 400], "parameters": {"simple": false, "filters": {"q": "has:attachment", "sender": "<EMAIL>"}, "options": {"downloadAttachments": true}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 1}, {"id": "02bd4636-f35b-4a3a-8a5f-9ae7aeed2bf4", "name": "Append to Reconciliation Sheet", "type": "n8n-nodes-base.googleSheets", "position": [2960, 320], "parameters": {"columns": {"value": {}, "schema": [{"id": "Invoice date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Invoice date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "invoice number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "invoice number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Purchase order number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Purchase order number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Supplier name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Supplier name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Supplier address", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Supplier address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Supplier VAT identification number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Supplier VAT identification number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Customer name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer address", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Customer address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Customer VAT identification number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Customer VAT identification number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Shipping addresses", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Shipping addresses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Line items", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Line items", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subtotal without VAT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Subtotal without VAT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subtotal with VAT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Subtotal with VAT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Total price", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Total price", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["output"]}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "id", "value": "gid=0"}, "documentId": {"__rl": true, "mode": "list", "value": "1omHDl1jpjHyrtga2ZHBddUkbkdatEr1ga9vHc4fQ1pI", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1omHDl1jpjHyrtga2ZHBddUkbkdatEr1ga9vHc4fQ1pI/edit?usp=drivesdk", "cachedResultName": "Invoice Reconciliation"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "XHvC7jIRR8A2TlUl", "name": "Google Sheets account"}}, "typeVersion": 4.3}, {"id": "cdb0a7ee-068d-465a-b4ae-d5221d5e7400", "name": "Get Processing Status", "type": "n8n-nodes-base.httpRequest", "position": [1800, 420], "parameters": {"url": "=https://api.cloud.llamaindex.ai/api/parsing/job/{{ $json.id }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "pZ4YmwFIkyGnbUC7", "name": "LlamaIndex API"}}, "typeVersion": 4.2}, {"id": "b68a01ab-d8e6-42f4-ab1d-81e746695eef", "name": "Wait to stay within service limits", "type": "n8n-nodes-base.wait", "position": [2120, 560], "webhookId": "17a96ed6-b5ff-47bb-a8a2-39c1eb40185a", "parameters": {"amount": 1}, "typeVersion": 1.1}, {"id": "41bd28d2-665a-4f71-a456-98eeb26b6655", "name": "Is Job Ready?", "type": "n8n-nodes-base.switch", "position": [1960, 420], "parameters": {"rules": {"values": [{"outputKey": "SUCCESS", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "300fce8c-b19a-4d0c-86e8-f62853c70ce2", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "SUCCESS"}]}, "renameOutput": true}, {"outputKey": "ERROR", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e6058aa0-a3e2-4ce3-9bed-6ff41a5be052", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ERROR"}]}, "renameOutput": true}, {"outputKey": "CANCELED", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ceb6338f-4261-40ac-be11-91f61c7302ba", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "CANCELED"}]}, "renameOutput": true}, {"outputKey": "PENDING", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0fa97d86-432a-409a-917e-5f1a002b1ab9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "PENDING"}]}, "renameOutput": true}]}, "options": {"allMatchingOutputs": true}}, "typeVersion": 3}, {"id": "f7157abe-b1ee-46b3-adb2-1be056d9d75d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [694.0259411218055, 139.97202236910687], "parameters": {"color": 7, "width": 808.8727491350096, "height": 709.5781339256318, "content": "## 1. Watch for Invoice Emails\n[Read more about Gmail Triggers](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.gmailtrigger)\n\nThe Gmail node can watch for all incoming messages and filter based on a condition. We'll set our Gmail node to wait for:\n* a message from particular email address.\n* having an attachment which should be the invoice PDF\n* not having a label \"invoice synced\", which is what we use to avoid duplicate processing."}, "typeVersion": 1}, {"id": "ff7cb6e4-5a60-4f12-b15e-74e7a4a302ce", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2360, 70.4879**********], "parameters": {"color": 7, "width": 805.*************, "height": 656.*************, "content": "## 3. Use LLMs to Extract Values from Data\n[Read more about Basic LLM Chain](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nLarge language models are perfect for data extraction tasks as they can work across a range of document layouts without human intervention. The extracted data can then be sent to a variety of datastores such as spreadsheets, accounting systems and/or CRMs.\n\n**Tip:** The \"Structured Output Parser\" ensures the AI output can be\ninserted to our spreadsheet without additional clean up and/or formatting. "}, "typeVersion": 1}, {"id": "0d510631-440b-41f5-b1aa-9b7279e9c8e3", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1934, 774], "parameters": {"color": 5, "width": 394.**************, "height": 154.**************, "content": "### 🙋‍♂️ Why not just use the built-in PDF convertor?\nA common issue with PDF-to-text convertors are that they ignore important data structures like tables. These structures can be important for data extraction. For example, being able to distinguish between seperate line items in an invoice."}, "typeVersion": 1}, {"id": "fe7fdb90-3c85-4f29-a7d3-16f927f48682", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3200, 157.**************], "parameters": {"color": 7, "width": 362.*************, "height": 440.*************, "content": "## 4. Add Label to Avoid Duplication\n[Read more about working with Gmail](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/)\n\nTo finish off the workflow, we'll add the \"invoice synced\" label to the original invoice email to flag that the extraction was successful. This can be useful if working with a shared inbox and for quality control purposes later."}, "typeVersion": 1}, {"id": "1acf2c60-c2b9-4f78-94a4-0711c8bd71ab", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [300, 140], "parameters": {"width": 360.0244620907562, "height": 573.2443601155958, "content": "## Try Me Out!\n\n**This workflow does the following:**\n* Waits for email invoices with PDF attachments.\n* Uses the LlamaParse service to convert the invoice PDF into a markdown file.\n* Uses a LLM to extract invoice data from the Markdown file.\n* Exports the extracted data to a Google Sheet.\n\n### Follow along with the blog here\nhttps://blog.n8n.io/how-to-extract-data-from-pdf-to-excel-spreadsheet-advance-parsing-with-n8n-io-and-llamaparse/\n\n### Good to know\n* You'll need to create the label \"invoice synced\" in gmail before using this workflow.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "3802c538-acf9-48d8-b011-bfe2fb817350", "name": "Add \"invoice synced\" Label", "type": "n8n-nodes-base.gmail", "position": [3320, 400], "parameters": {"labelIds": ["Label_5511644430826409825"], "messageId": "={{ $('Receiving Invoices').item.json.id }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "ffabd8c5-c440-4473-8e44-b849426c70cf", "name": "Get Parsed Invoice Data", "type": "n8n-nodes-base.httpRequest", "position": [2160, 280], "parameters": {"url": "=https://api.cloud.llamaindex.ai/api/parsing/job/{{ $json.id }}/result/markdown", "options": {"redirect": {"redirect": {}}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pZ4YmwFIkyGnbUC7", "name": "LlamaIndex API"}}, "typeVersion": 4.2}, {"id": "5f9b507f-4dc1-4853-bf71-a64f2f4b55c1", "name": "Map Output", "type": "n8n-nodes-base.set", "position": [2760, 320], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $json.output }}"}, "typeVersion": 3.3}, {"id": "d22744cd-151d-4b92-b4f2-4a5b9ceb4ee7", "name": "Apply Data Extraction Rules", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2420, 320], "parameters": {"text": "=Given the following invoice in the <invoice> xml tags, extract the following information as listed below.\nIf you cannot the information for a specific item, then leave blank and skip to the next. \n\n* Invoice date\n* invoice number\n* Purchase order number\n* Supplier name\n* Supplier address\n* Supplier VAT identification number\n* Customer name\n* Customer address\n* Customer VAT identification number\n* Shipping addresses\n* Line items, including a description of the goods or services rendered\n* Price with and without VAT\n* Total price\n\n<invoice>{{ $json.markdown }}</invoice>", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "3735a124-9fab-4400-8b94-8b5aa9f951fe", "name": "Should Process Email?", "type": "n8n-nodes-base.if", "position": [1340, 400], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e5649a2b-6e12-4cc4-8001-4639cc9cc2c2", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $input.item.binary.attachment_0.mimeType }}", "rightValue": "application/pdf"}, {"id": "4c57ab9b-b11c-455a-a63d-daf48418b06e", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $json.labels }}", "rightValue": "invoice synced"}]}}, "typeVersion": 2}, {"id": "12a23527-39f3-4f72-8691-3d5cf59f9909", "name": "Split Out Labels", "type": "n8n-nodes-base.splitOut", "position": [980, 400], "parameters": {"options": {}, "fieldToSplitOut": "labelIds"}, "typeVersion": 1}, {"id": "88ff6e22-d3d3-403d-b0b2-2674487140a7", "name": "Get Labels Names", "type": "n8n-nodes-base.gmail", "position": [980, 540], "parameters": {"labelId": "={{ $json.labelIds }}", "resource": "label", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "88accb8e-6531-40be-8d35-1bba594149af", "name": "Combine Label Names", "type": "n8n-nodes-base.aggregate", "position": [980, 680], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "labels", "fieldToAggregate": "name"}]}}, "typeVersion": 1}, {"id": "d233ff33-cabf-434e-876d-879693ecaf58", "name": "Email with Label Names", "type": "n8n-nodes-base.merge", "position": [1160, 400], "parameters": {"mode": "combine", "options": {}, "combinationMode": "multiplex"}, "typeVersion": 2.1}, {"id": "733fc285-e069-4e4e-b13e-dfc1c259ac12", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2540, 460], "parameters": {"width": 192.**************, "height": 213.**************, "content": "\n\n\n\n\n\n\n\n\n\n\n\n**Need more attributes?**\nChange it here!"}, "typeVersion": 1}, {"id": "83aa6ed0-ce3b-48d7-aded-475c337ae86e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2880, 300], "parameters": {"width": 258.29345180972877, "height": 397.0641952938746, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Set Your Google Sheet URL here\n* Set the Name of your Sheet\n\n\n**Don't use GSheets?**\nSwap this for Excel, Airtable or a Database!"}, "typeVersion": 1}, {"id": "720070f6-2d6c-45ef-80c2-e950862a002b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [740, 380], "parameters": {"width": 174.50671517518518, "height": 274.6295678979021, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Change the email filters here!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Map Output": {"main": [[{"node": "Append to Reconciliation Sheet", "type": "main", "index": 0}]]}, "OpenAI Model": {"ai_languageModel": [[{"node": "Apply Data Extraction Rules", "type": "ai_languageModel", "index": 0}]]}, "Is Job Ready?": {"main": [[{"node": "Get Parsed Invoice Data", "type": "main", "index": 0}], null, null, [{"node": "Wait to stay within service limits", "type": "main", "index": 0}]]}, "Get Labels Names": {"main": [[{"node": "Combine Label Names", "type": "main", "index": 0}]]}, "Split Out Labels": {"main": [[{"node": "Get Labels Names", "type": "main", "index": 0}]]}, "Receiving Invoices": {"main": [[{"node": "Split Out Labels", "type": "main", "index": 0}, {"node": "Email with Label Names", "type": "main", "index": 0}]]}, "Combine Label Names": {"main": [[{"node": "Email with Label Names", "type": "main", "index": 1}]]}, "Upload to LlamaParse": {"main": [[{"node": "Get Processing Status", "type": "main", "index": 0}]]}, "Get Processing Status": {"main": [[{"node": "Is Job Ready?", "type": "main", "index": 0}]]}, "Should Process Email?": {"main": [[{"node": "Upload to LlamaParse", "type": "main", "index": 0}]]}, "Email with Label Names": {"main": [[{"node": "Should Process Email?", "type": "main", "index": 0}]]}, "Get Parsed Invoice Data": {"main": [[{"node": "Apply Data Extraction Rules", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Apply Data Extraction Rules", "type": "ai_outputParser", "index": 0}]]}, "Apply Data Extraction Rules": {"main": [[{"node": "Map Output", "type": "main", "index": 0}]]}, "Append to Reconciliation Sheet": {"main": [[{"node": "Add \"invoice synced\" Label", "type": "main", "index": 0}]]}, "Wait to stay within service limits": {"main": [[{"node": "Get Processing Status", "type": "main", "index": 0}]]}}}