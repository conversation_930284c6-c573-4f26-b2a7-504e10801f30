{"id": "gIZpJgLpUgdoNNDZ", "meta": {"instanceId": "9ee1559971f36d9785fb97b48d76ed3a8ac302ff00dd93d8b7301e3feab97aed", "templateCredsSetupCompleted": true}, "name": "YT New Video Upload", "tags": [], "nodes": [{"id": "add1cd70-4bf4-42f5-87d9-68fa31af7f56", "name": "Download New Video", "type": "n8n-nodes-base.googleDrive", "position": [-140, 220], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "8GbkH30Del5g4Kbq", "name": "<PERSON>"}}, "typeVersion": 3}, {"id": "1308e94d-1c68-4fae-9d94-ba6248947581", "name": "New Video?", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-360, 220], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "id", "value": "1EOMCzVDif4XnqCloYvhBNZdx7gyRNpTR"}}, "credentials": {"googleDriveOAuth2Api": {"id": "8GbkH30Del5g4Kbq", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "dfb073fc-4365-4669-a17c-73407a14a655", "name": "Create Description", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1000, 180], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1-nano", "cachedResultName": "GPT-4.1-NANO"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are a professional copywriter.  \nYou receive the transcript of an economics-related video and create a detailed but concise summary (with paragraphs) about its content.  \n\nWrite a detailed summary (with paragraphs) about the content of the podcast.  \n\nYour output will be used for the YouTube video description. Start with something like: \"In this video...\" or \"In this episode...\".  \nWrite from my perspective, using phrases like \"my opinion\" or \"in my view,\" in the first person, but never phrases like \"In this episode, I learn...\" or similar, as I always explain or discuss the content. YOU NEVER WRITE THINGS LIKE \"THE SPEAKER SAYS\"! Always from my position.  \n\nImportant: Use clear and assertive statements as formulated in the transcript. Avoid neutral or uncertain phrases like \"it could,\" \"I assume that,\" \"possibly,\" or similar. The statements should be confident and definitive to powerfully convey the podcast’s content.  \nInclude a few (2-4) emojis where appropriate.  \nEnd the post with 2-5 relevant hashtags. The hashtags should be broad, like #economics #money #gold, or similar, depending on what fits."}, {"content": "=Here is the transcript:\n\n{{ $json.transcript }}"}]}}, "credentials": {"openAiApi": {"id": "8VZkrnuHUTXNwnlP", "name": "Midgard#1"}}, "typeVersion": 1.7}, {"id": "25ba2d0b-3a7a-49d6-acfa-6e7fa2b98e86", "name": "2.5FlashPrev", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1340, 180], "parameters": {"options": {}, "modelName": "models/gemini-2.5-flash-preview-04-17"}, "credentials": {"googlePalmApi": {"id": "2Y7XbTPZaxPz6Vhw", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "23141199-5df7-47d0-8d0e-f5b3a7fe7668", "name": "YT Tags", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1300, -20], "parameters": {"text": "=Now follows the actual topic/transcript. Give me the YouTube tags for it:\n\n{{ $('Adjust Transcript Format').item.json.transcript }}", "options": {"systemMessage": "This video is about the future gold price and how it affects the returns of high-performing assets like stocks and bonds in their adjusted returns.\n\nExpected output:\nGold price, future gold price, gold investments, asset returns, stocks and bonds, investment returns, adjusted returns, gold market, financial markets, gold price forecast, economic trends, investing in gold, stock market analysis, bond market, investment strategies, inflation and gold, gold vs. stocks, financial analysis, precious metals, portfolio management, market outlook, investment tips"}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "c3ff8e2e-7ce6-4538-baea-91a375b98dcb", "name": "Get Transcript", "type": "n8n-nodes-base.httpRequest", "position": [500, 220], "parameters": {"url": "=https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/run-sync-get-dataset-items", "method": "POST", "options": {}, "jsonBody": "={\n  \"videoUrl\": \"https://www.youtube.com/watch?v={{ $json.id }}\"\n}", "sendBody": true, "sendQuery": true, "specifyBody": "json", "queryParameters": {"parameters": [{"name": "token", "value": "={{$json.token}}"}]}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "6a03cbf3-5718-4db8-b9cf-31d6d93f73e7", "name": "Adjust Transcript Format", "type": "n8n-nodes-base.code", "position": [680, 220], "parameters": {"jsCode": "const items = $input.all();\n\nconst transcriptStrings = items.flatMap(item => {\n  const dataArray = item.json.data;\n\n  if (!dataArray || !Array.isArray(dataArray)) {\n    return [];\n  }\n\n  const segmentTexts = dataArray.map(segment => {\n      if (segment && typeof segment.text === 'string') {\n          return segment.text;\n      } else {\n          return '';\n      }\n  });\n\n  return segmentTexts;\n});\n\nconst transcript = transcriptStrings.join(' ');\n\nreturn [\n  {\n    json: {\n      transcript: transcript,\n    },\n  },\n];"}, "typeVersion": 2}, {"id": "c6e0bf83-6eaa-41be-9ea1-0dba4cace444", "name": "Update Video's <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.youTube", "onError": "continueErrorOutput", "position": [2160, 240], "parameters": {"title": "={{ $('YT Title').item.json.title }}", "videoId": "={{ $('Upload Video to Youtube').item.json.uploadId }}", "resource": "video", "operation": "update", "categoryId": "25", "regionCode": "DE", "updateFields": {"tags": "={{ $('YT Tags').item.json.message.content }}", "description": "={{ $('Create Description').first().json.message.content }}\n\nDiese textbasierte Zusammenfassung des Videos wurde automatisch mit dem KI-Modell gpt-4.1-nano erstellt.]\n"}}, "credentials": {"youTubeOAuth2Api": {"id": "jKf1Vc4ZycKxf0im", "name": "YouTube Jim <PERSON>"}}, "typeVersion": 1}, {"id": "813fc8fc-ed3c-4cd7-8aa5-4c4084fd4e0d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-380, 160], "parameters": {"color": 4, "width": 700, "height": 240, "content": "# Upload New Video to Youtube 🎥⬆️"}, "typeVersion": 1}, {"id": "0fb360ba-f843-4820-b05d-3149d7516526", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [320, -100], "parameters": {"color": 4, "width": 2660, "height": 500, "content": "# Get Transcript for Context and Generate Metadata from It 📝🔍"}, "typeVersion": 1}, {"id": "bb494b9e-a707-492a-b88a-83fce01a742f", "name": "YT Title", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1720, -20], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1-nano", "cachedResultName": "GPT-4.1-NANO"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are a professional copywriter for SEO-optimized YouTube titles."}, {"content": "=Write me a suitable SEO YouTube title for the transcript of the following video transcript. Only the title, nothing else. Max 100 characters, so keep it short."}]}}, "credentials": {"openAiApi": {"id": "8VZkrnuHUTXNwnlP", "name": "Midgard#1"}}, "typeVersion": 1.7}, {"id": "a82d27b7-967e-48f8-8ead-a184872a7abc", "name": "Delete File from Upload Folder (Optional)", "type": "n8n-nodes-base.googleDrive", "position": [2460, -20], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Download New Video').item.json.id }}"}, "options": {}, "operation": "deleteFile"}, "credentials": {"googleDriveOAuth2Api": {"id": "8GbkH30Del5g4Kbq", "name": "GDRIVE ACCOUNT P"}}, "typeVersion": 3}, {"id": "4cff725b-cff0-45e0-9cab-24c2f233c94e", "name": "Upload Video to Youtube", "type": "n8n-nodes-base.youTube", "position": [140, 220], "parameters": {"title": "<PERSON><PERSON>da", "options": {"privacyStatus": "private", "selfDeclaredMadeForKids": false}, "resource": "video", "operation": "upload", "categoryId": "25", "regionCode": "DE"}, "credentials": {"youTubeOAuth2Api": {"id": "jKf1Vc4ZycKxf0im", "name": "YouTube Jim <PERSON>"}}, "typeVersion": 1}, {"id": "691fda3c-736c-4d6e-b4f3-8db68a581c79", "name": "ApifyToken", "type": "n8n-nodes-base.set", "position": [320, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2eb41a6f-d0ef-4ca2-be47-f93b1d5c1edb", "name": "token", "type": "string", "value": "YOURTOKENHERE"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3a879f43-d8a6-4a87-a233-bafeb296d21a", "connections": {"YT Tags": {"main": [[{"node": "YT Title", "type": "main", "index": 0}]]}, "YT Title": {"main": [[{"node": "Update Video's <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "ApifyToken": {"main": [[{"node": "Get Transcript", "type": "main", "index": 0}]]}, "New Video?": {"main": [[{"node": "Download New Video", "type": "main", "index": 0}]]}, "2.5FlashPrev": {"ai_languageModel": [[{"node": "YT Tags", "type": "ai_languageModel", "index": 0}]]}, "Get Transcript": {"main": [[{"node": "Adjust Transcript Format", "type": "main", "index": 0}]]}, "Create Description": {"main": [[{"node": "YT Tags", "type": "main", "index": 0}]]}, "Download New Video": {"main": [[{"node": "Upload Video to Youtube", "type": "main", "index": 0}]]}, "Update Video's Metadata": {"main": [[{"node": "Delete File from Upload Folder (Optional)", "type": "main", "index": 0}]]}, "Upload Video to Youtube": {"main": [[{"node": "ApifyToken", "type": "main", "index": 0}]]}, "Adjust Transcript Format": {"main": [[{"node": "Create Description", "type": "main", "index": 0}]]}}}