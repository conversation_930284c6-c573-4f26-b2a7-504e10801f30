{"id": "kZarev2IMUaKHhCI", "meta": {"instanceId": "f4b99447bb6b56ad425b30ab755dc982ee1c258e7ce783958190eabedd1bcbb0"}, "name": "Auto - Resume Disabled Workflows", "tags": [{"id": "YZS563bPtiBYp1aL", "name": "auto_resume:true", "createdAt": "2024-02-29T19:38:26.858Z", "updatedAt": "2024-02-29T19:38:26.858Z"}, {"id": "53XXAtg9v7XIaREI", "name": "owner:<PERSON><PERSON>", "createdAt": "2024-02-10T03:20:58.515Z", "updatedAt": "2024-02-10T03:20:58.515Z"}], "nodes": [{"id": "fc7224f0-96d6-4c6d-a7c5-afbd49d60fc8", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [820, 700], "parameters": {}, "typeVersion": 1}, {"id": "50661eea-325f-4131-92f6-0e3112ea6714", "name": "Get Auto Resume Workflows", "type": "n8n-nodes-base.n8n", "position": [1040, 600], "parameters": {"filters": {"tags": "auto_resume:true"}}, "credentials": {"n8nApi": {"id": "r2RZq6ObikiqFu1y", "name": "n8n account"}}, "retryOnFail": true, "typeVersion": 1}, {"id": "3c845ca2-4a6d-40ed-ad11-b92c425f852d", "name": "Find Deactivated Workflows", "type": "n8n-nodes-base.filter", "position": [1260, 600], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ce7b707c-d74e-4ca8-8081-53b15ff3f8a3", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.active }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "74b79f56-532e-4446-9b28-77874098ba10", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [820, 520], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 4}]}}, "notesInFlow": false, "typeVersion": 1.1}, {"id": "318960c8-e2bb-4486-ad5a-8c3a7e6db8a3", "name": "Activate Workflow", "type": "n8n-nodes-base.n8n", "position": [1460, 600], "parameters": {"operation": "activate", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}}, "credentials": {"n8nApi": {"id": "r2RZq6ObikiqFu1y", "name": "n8n account"}}, "retryOnFail": true, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ff4414a4-de17-43a6-9da2-144a6e4cb773", "connections": {"Schedule Trigger": {"main": [[{"node": "Get Auto Resume Workflows", "type": "main", "index": 0}]]}, "Get Auto Resume Workflows": {"main": [[{"node": "Find Deactivated Workflows", "type": "main", "index": 0}]]}, "Find Deactivated Workflows": {"main": [[{"node": "Activate Workflow", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Get Auto Resume Workflows", "type": "main", "index": 0}]]}}}