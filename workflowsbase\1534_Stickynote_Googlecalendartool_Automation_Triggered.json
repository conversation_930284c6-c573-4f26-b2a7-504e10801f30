{"id": "KhUd3rHKtZAImiXZ", "meta": {"instanceId": "9219ebc7795bea866f70aa3d977d54417fdf06c41944be95e20cfb60f992db19", "templateCredsSetupCompleted": true}, "name": "Personal Assistant MCP server", "tags": [], "nodes": [{"id": "f27f3d00-8019-401f-a1c4-5c9754ca5d7e", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-220, -60], "webhookId": "989c3a79-5a0c-4ca1-a542-55e060816121", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "49e4bd69-141f-47ae-bb97-f03a92e56131", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-80, 140], "parameters": {"options": {}, "modelName": "models/gemini-2.5-pro-preview-05-06"}, "credentials": {"googlePalmApi": {"id": "MF12DwQJWL1egyiN", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "aaa58803-52ad-439b-8876-05a84fc63eaf", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [120, 140], "parameters": {}, "typeVersion": 1.3}, {"id": "5c49309e-054c-4097-b8c3-1bf0b10539ec", "name": "MCP Server Trigger", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [20, 340], "webhookId": "b37ab045-0b99-4d57-af44-6ae1e9ac6381", "parameters": {"path": "b37ab045-0b99-4d57-af44-6ae1e9ac6381"}, "typeVersion": 1}, {"id": "24e5ee35-c53c-4e82-9d79-d48d9220d7ac", "name": "MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [480, 140], "parameters": {"sseEndpoint": "<set-your-url-here>"}, "typeVersion": 1}, {"id": "24d7de59-9db2-43e8-ad2a-923bbfc9877b", "name": "Create event", "type": "n8n-nodes-base.googleCalendarTool", "position": [820, 380], "parameters": {"calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "additionalFields": {}}, "credentials": {"googleCalendarOAuth2Api": {"id": "UfvyikkM1kt4EcMl", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "54a2e041-8c5c-40bb-ae6b-1494b8a5a198", "name": "Update event", "type": "n8n-nodes-base.googleCalendarTool", "position": [580, 720], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "update", "updateFields": {}}, "credentials": {"googleCalendarOAuth2Api": {"id": "UfvyikkM1kt4EcMl", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "7bebda2e-711f-478b-8ba3-36306b1ffb49", "name": "Draft email", "type": "n8n-nodes-base.gmailTool", "position": [260, 780], "webhookId": "4e76cb3d-4239-4030-a23a-544029535f70", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', `Sign off as \"Your name, company name\"`, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "resource": "draft"}, "credentials": {"gmailOAuth2": {"id": "q3P6IybvNdDiPZ52", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "26538db2-f3af-47a8-b97e-2afa7d9ea05d", "name": "Personal Assistant", "type": "@n8n/n8n-nodes-langchain.agent", "position": [40, -60], "parameters": {"options": {}}, "typeVersion": 1.9}, {"id": "04c5d14f-a80d-4113-b3ff-a6ee1ab3917e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [920, 280], "parameters": {"color": 5, "width": 560, "height": 620, "content": "# Calendar nodes\n\nYou could order your agent to create a new event in your Google Calendar, find a specific event, get multiple events or update an event's details. \n\n**The true power of these nodes regarding Email, CRM and Calendar remains in combining multiple into one set of instructions**.\n\n## Examples:\n\n- Find the contact for <PERSON><PERSON> for A. Corp and send him an email asking saying that you have scheduled the meeting for next Wednesday at 9AM. Draft an email to remind him of the details and the topic of discussion being the weekly update call and the main company bottlenecks.\n- Update the contact details for <PERSON><PERSON> since he changed his email and company to B corp and john[at]bcorpfakeemail[dot]com and please update me about my upcoming meetings with him next month.\n- Send me a summary for all my meetings today. Draft one email for each different person that I'll meet with today, reminding them about today's meeting\n"}, "typeVersion": 1}, {"id": "0de610d2-20bf-4fd4-b93e-60b082d22e56", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 700], "parameters": {"color": 3, "width": 460, "height": 500, "content": "# Email nodes\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nYour AI Agent will be able to search through your email inbox to find specific email content for you. Based on this records you can fetch information quickly and order to draft responses to review later.\n\n## Examples:\n\n- hey what were the last 5 emails sent to <PERSON> from X corp? \n- Draft an email with these details to <PERSON> sharing I can't make it today and propose a new time for 9AM tomorrow. "}, "typeVersion": 1}, {"id": "e084e23e-473b-4798-a39c-00529ef9e827", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-720, 360], "parameters": {"color": 4, "width": 660, "height": 480, "content": "# CRM nodes\n\nWith these node operations your \nAI agent will be able to do the following:\n\n- Add a new row with contact data\n- Find a row and its details in the table\n- Update a value or group of values\n\n\n## Examples:\n\n- Add a new contact data with <PERSON> as first name\n his cell is +1 XXX XXX XXXX. \nI will tell you the email later on.\n- Can you tell me the details and email for <PERSON>?\n I want to send him an email reminder.\n- Update <PERSON>'s email to rick[at]someemail[dot]com from X corp. please."}, "typeVersion": 1}, {"id": "dc9dcee5-35ec-4ea3-8c67-21c277705dec", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [360, -220], "parameters": {"width": 480, "height": 480, "content": "## MCP Client\n\nPaste your MCP client URL from the MCP server trigger node.\n\nCustomize your output node to receive the workflow completion notifications (eg. Telegram, Gmail) from your personal assistant"}, "typeVersion": 1}, {"id": "1764e9cd-7fc1-46e7-bc97-33d4b81d5141", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, 280], "parameters": {"color": 5, "width": 400, "height": 620, "content": ""}, "typeVersion": 1}, {"id": "b4dda81f-fa22-43ec-a841-7b924b8884e8", "name": "Add new row", "type": "n8n-nodes-base.googleSheetsTool", "position": [-340, 440], "parameters": {"columns": {"value": {}, "schema": [], "mappingMode": "autoMapInputData", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit?usp=drivesdk", "cachedResultName": "Contacts"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "twZdLFsI3kTnqtpG", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "e3bc61e0-1d95-4554-b7ba-f76c3f105339", "name": "Find row", "type": "n8n-nodes-base.googleSheetsTool", "position": [-280, 580], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit?usp=drivesdk", "cachedResultName": "Contacts"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "twZdLFsI3kTnqtpG", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "461469da-c47a-486f-98c2-71fcc9abc235", "name": "Update row", "type": "n8n-nodes-base.googleSheetsTool", "position": [-180, 680], "parameters": {"columns": {"value": {}, "schema": [{"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1JDoEkNqk1c_TrIht2n1XF-jmIWpk48DP3NUaNbhcFV8/edit?usp=drivesdk", "cachedResultName": "Contacts"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "twZdLFsI3kTnqtpG", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "01c0ba70-c1b1-454f-9b4e-0727c8280ace", "name": "Find emails", "type": "n8n-nodes-base.gmailTool", "position": [120, 780], "webhookId": "b36e3112-52b1-4e03-a2d3-74d5d4705891", "parameters": {"filters": {}, "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}"}, "credentials": {"gmailOAuth2": {"id": "q3P6IybvNdDiPZ52", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "40efd312-032f-496b-8485-a6a49001aa75", "name": "Find single event", "type": "n8n-nodes-base.googleCalendarTool", "position": [760, 520], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "get"}, "credentials": {"googleCalendarOAuth2Api": {"id": "UfvyikkM1kt4EcMl", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "8ac5c33e-f4fb-4627-98d5-b66838db3037", "name": "Find multiple events", "type": "n8n-nodes-base.googleCalendarTool", "position": [680, 620], "parameters": {"limit": 10, "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "UfvyikkM1kt4EcMl", "name": "Google Calendar account"}}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "99536a4c-8e1b-4b7e-9a2a-8baa404499fe", "connections": {"Find row": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "Personal Assistant", "type": "ai_tool", "index": 0}]]}, "Update row": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Add new row": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Draft email": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Find emails": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Create event": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Update event": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Personal Assistant", "type": "ai_memory", "index": 0}]]}, "Find single event": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Find multiple events": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Personal Assistant", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Personal Assistant", "type": "main", "index": 0}]]}}}