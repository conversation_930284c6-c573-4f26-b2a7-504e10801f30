{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "aa0c62d1-2a5e-4336-8783-a8a21cb23374", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1180, 760], "parameters": {"options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "VQtv7frm7eLiEDnd", "name": "OpenAi account 7"}}, "typeVersion": 1}, {"id": "0c7d21e6-5bf6-4927-ad23-008b22e2ffde", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [280, 560], "parameters": {}, "typeVersion": 1}, {"id": "352de912-3a36-4bf2-b013-b46e0ace38e9", "name": "Generate French Audio", "type": "n8n-nodes-base.httpRequest", "position": [720, 560], "parameters": {"url": "=https://api.elevenlabs.io/v1/text-to-speech/{{ $json.voice_id }}", "method": "POST", "options": {}, "jsonBody": "={\"text\":\"{{ $json.text }}\",\"model_id\":\"eleven_multilingual_v2\",\"voice_settings\":{\"stability\":0.5,\"similarity_boost\":0.5}}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "optimize_streaming_latency", "value": "1"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "audio/mpeg"}]}}, "credentials": {"httpHeaderAuth": {"id": "OMni1VQQclVYOmeZ", "name": "Eleven<PERSON><PERSON><PERSON> David"}}, "typeVersion": 4.1}, {"id": "0cde2e89-0669-41b4-8fe1-1a6aff14792f", "name": "Set ElevenLabs voice ID and text", "type": "n8n-nodes-base.set", "position": [500, 560], "parameters": {"fields": {"values": [{"name": "voice_id", "stringValue": "wl7sZxfTOitHVachQiUm"}, {"name": "text", "stringValue": "=Après, on a fait la sieste, <PERSON> a travaillé pour French Today et j’ai étudié un peu, et puis Camille a proposé de suivre une visite guidée de l’Abbaye de Beauport qui commençait à 17 heures. On a marché environ vingt minutes, et je m’arrêtais souvent pour prendre des photos : la baie de Pa<PERSON> est si jolie ! Mais Camille m’a dit : « Dépêche-toi Sunny ! La visite guidée commence dans cinq minutes. » <PERSON><PERSON>, j’ai bougé mes fesses et on est arrivées à l’abbaye"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "38aa323e-a899-4018-afb9-4d4682ac8ff1", "name": "Translate Text to English", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1180, 560], "parameters": {"prompt": "=Translate to English:\n{{ $json.text }}"}, "typeVersion": 1.2}, {"id": "f0b7adad-fa0b-4764-96e0-0883bbcc02d6", "name": "Translate English text to speech", "type": "n8n-nodes-base.httpRequest", "position": [1540, 560], "parameters": {"url": "=https://api.elevenlabs.io/v1/text-to-speech/{{ $('Set ElevenLabs voice ID and text').item.json.voice_id }}", "method": "POST", "options": {}, "jsonBody": "={\"text\":\"{{ $json[\"text\"].replaceAll('\"', '\\\\\"').trim() }}\",\"model_id\":\"eleven_multilingual_v2\",\"voice_settings\":{\"stability\":0.5,\"similarity_boost\":0.5}}", "sendBody": true, "sendQuery": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "optimize_streaming_latency", "value": "1"}]}, "headerParameters": {"parameters": [{"name": "accept", "value": "audio/mpeg"}]}}, "credentials": {"httpHeaderAuth": {"id": "OMni1VQQclVYOmeZ", "name": "Eleven<PERSON><PERSON><PERSON> David"}}, "typeVersion": 4.1}, {"id": "f8700266-5491-4ca7-b29a-3f5ec1e9b66f", "name": "Transcribe Audio", "type": "n8n-nodes-base.httpRequest", "position": [960, 560], "parameters": {"url": "https://api.openai.com/v1/audio/transcriptions", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "model", "value": "whisper-1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "VQtv7frm7eLiEDnd", "name": "OpenAi account 7"}}, "typeVersion": 4.1}, {"id": "25630b45-3827-4ee0-a77e-c30cadefe999", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [449.*************, 319.*************], "parameters": {"color": 7, "width": 199.**************, "height": 420.************, "content": "1] In ElevenLabs, add a voice to your [voice lab](https://elevenlabs.io/voice-lab) and copy its ID. Open this node and add the ID there"}, "typeVersion": 1}, {"id": "a41d2622-4476-44c2-bac6-212be237aa4b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [680, 320], "parameters": {"color": 7, "width": 192.**************, "height": 418.*************, "content": "2] Get your ElevenLabs API key (click your name in the bottom-left of [ElevenLabs](https://elevenlabs.io/voice-lab) and choose ‘profile’)\n\nIn this node, create a new header auth cred. Set the name to `xi-api-key` and the value to your API key"}, "typeVersion": 1}, {"id": "58143bb1-816f-4ff6-9cac-9ce7765e02be", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [920, 320], "parameters": {"color": 7, "width": 192.**************, "height": 414.59045768149747, "content": "3] In the 'credential' field of this node, create a new OpenAI cred with your [OpenAI API key](https://platform.openai.com/api-keys)"}, "typeVersion": 1}, {"id": "bd2ef5d2-c27d-45e4-a66e-a73168f94087", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [160, 273.1221160672591], "parameters": {"color": 7, "width": 230.39134868652621, "height": 233.3354221029769, "content": "### About\nThis workflow takes some French text, and translates it into spoken audio.\n\nIt then transcribes that audio back into text, translates it into English and generates an audio file of the English text"}, "typeVersion": 1}, {"id": "a1f207d4-dbed-4dfa-aad5-2b2f6e4e6271", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [440, 272.42998167622557], "parameters": {"color": 7, "width": 685.8541178336201, "height": 478.0993479050163, "content": "### Setup steps"}, "typeVersion": 1}], "pinData": {}, "connections": {"Transcribe Audio": {"main": [[{"node": "Translate Text to English", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Translate Text to English", "type": "ai_languageModel", "index": 0}]]}, "Generate French Audio": {"main": [[{"node": "Transcribe Audio", "type": "main", "index": 0}]]}, "Translate Text to English": {"main": [[{"node": "Translate English text to speech", "type": "main", "index": 0}]]}, "Set ElevenLabs voice ID and text": {"main": [[{"node": "Generate French Audio", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Set ElevenLabs voice ID and text", "type": "main", "index": 0}]]}}}