{"id": "a58HZKwcOy7lmz56", "meta": {"instanceId": "178ef8a5109fc76c716d40bcadb720c455319f7b7a3fd5a39e4f336a091f524a", "templateCredsSetupCompleted": true}, "name": "Building RAG Chatbot for Movie Recommendations with Qdrant and Open AI", "tags": [], "nodes": [{"id": "06a34e3b-519a-4b48-afd0-4f2b51d2105d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [4980, 740], "parameters": {}, "typeVersion": 1}, {"id": "9213003d-433f-41ab-838b-be93860261b2", "name": "GitHub", "type": "n8n-nodes-base.github", "position": [5200, 740], "parameters": {"owner": {"__rl": true, "mode": "name", "value": "mrscoopers"}, "filePath": "Top_1000_IMDB_movies.csv", "resource": "file", "operation": "get", "repository": {"__rl": true, "mode": "list", "value": "n8n_demo", "cachedResultUrl": "https://github.com/mrscoopers/n8n_demo", "cachedResultName": "n8n_demo"}, "additionalParameters": {}}, "credentials": {"githubApi": {"id": "VbfC0mqEq24vPIwq", "name": "GitHub n8n demo"}}, "typeVersion": 1}, {"id": "9850d1a9-3a6f-44c0-9f9d-4d20fda0b602", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [5360, 740], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "7704f993-b1c9-477a-8b5a-77dc2cb68161", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [5560, 940], "parameters": {"model": "text-embedding-3-small", "options": {}}, "credentials": {"openAiApi": {"id": "deYJUwkgL1Euu613", "name": "OpenAi account 2"}}, "typeVersion": 1}, {"id": "bc6dd8e5-0186-4bf9-9c60-2eab6d9b6520", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [5700, 960], "parameters": {"options": {"metadata": {"metadataValues": [{"name": "movie_name", "value": "={{ $('Extract from File').item.json['Movie Name'] }}"}, {"name": "movie_release_date", "value": "={{ $('Extract from File').item.json['Year of Release'] }}"}, {"name": "movie_description", "value": "={{ $('Extract from File').item.json.Description }}"}]}}, "jsonData": "={{ $('Extract from File').item.json.Description }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "f87ea014-fe79-444b-88ea-0c4773872b0a", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [5700, 1140], "parameters": {}, "typeVersion": 1}, {"id": "d8d28cec-c8e8-4350-9e98-cdbc6da54988", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [5600, 740], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "imdb"}}, "credentials": {"qdrantApi": {"id": "Zin08PA0RdXVUKK7", "name": "QdrantApi n8n demo"}}, "typeVersion": 1}, {"id": "f86e03dc-12ea-4929-9035-4ec3cf46e300", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [4920, 1140], "webhookId": "71bfe0f8-227e-466b-9d07-69fd9fe4a27b", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ead23ef6-2b6b-428d-b412-b3394bff8248", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [5040, 1340], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "deYJUwkgL1Euu613", "name": "OpenAi account 2"}}, "typeVersion": 1}, {"id": "7ab936e1-aac8-43bc-a497-f2d02c2c19e5", "name": "Call n8n Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [5320, 1340], "parameters": {"name": "movie_recommender", "schemaType": "manual", "workflowId": {"__rl": true, "mode": "id", "value": "a58HZKwcOy7lmz56"}, "description": "Call this tool to get a list of recommended movies from a vector database. ", "inputSchema": "{\n\"type\": \"object\",\n\"properties\": {\n\t\"positive_example\": {\n      \"type\": \"string\",\n      \"description\": \"A string with a movie description matching the user's positive recommendation request\"\n    },\n    \"negative_example\": {\n      \"type\": \"string\",\n      \"description\": \"A string with a movie description matching the user's negative anti-recommendation reuqest\"\n    }\n}\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "ce55f334-698b-45b1-9e12-0eaa473187d4", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [5160, 1340], "parameters": {}, "typeVersion": 1.2}, {"id": "41c1ee11-3117-4765-98fc-e56cc6fc8fb2", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [5640, 1600], "parameters": {}, "typeVersion": 1}, {"id": "db8d6ab6-8cd2-4a8c-993d-f1b7d7fdcffd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [6540, 1500], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "c7bc5e04-22b1-40db-ba74-1ab234e51375", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [7260, 1480], "parameters": {"options": {}, "fieldToSplitOut": "result"}, "typeVersion": 1}, {"id": "a2002d2e-362a-49eb-a42d-7b665ddd67a0", "name": "Split Out1", "type": "n8n-nodes-base.splitOut", "position": [7140, 1260], "parameters": {"options": {}, "fieldToSplitOut": "result.points"}, "typeVersion": 1}, {"id": "f69a87f1-bfb9-4337-9350-28d2416c1580", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [7520, 1400], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "id"}, "typeVersion": 3}, {"id": "b2f2529e-e260-4d72-88ef-09b804226004", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [7960, 1400], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "bedea10f-b4de-4f0e-9d60-cc8117a2b328", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [5140, 1140], "parameters": {"options": {"systemMessage": "You are a Movie Recommender Tool using a Vector Database under the hood. Provide top-3 movie recommendations returned by the database, ordered by their recommendation score, but not showing the score to the user."}}, "typeVersion": 1.6}, {"id": "e04276b5-7d69-437b-bf4f-9717808cc8f6", "name": "Embedding Recommendation Request with Open AI", "type": "n8n-nodes-base.httpRequest", "position": [5900, 1460], "parameters": {"url": "https://api.openai.com/v1/embeddings", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "input", "value": "={{ $json.query.positive_example }}"}, {"name": "model", "value": "text-embedding-3-small"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer $OPENAI_API_KEY"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "deYJUwkgL1Euu613", "name": "OpenAi account 2"}}, "typeVersion": 4.2}, {"id": "68e99f06-82f5-432c-8b31-8a1ae34981a6", "name": "Embedding Anti-Recommendation Request with Open AI", "type": "n8n-nodes-base.httpRequest", "position": [5920, 1660], "parameters": {"url": "https://api.openai.com/v1/embeddings", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "input", "value": "={{ $json.query.negative_example }}"}, {"name": "model", "value": "text-embedding-3-small"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer $OPENAI_API_KEY"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "deYJUwkgL1Euu613", "name": "OpenAi account 2"}}, "typeVersion": 4.2}, {"id": "ecb1d7e1-b389-48e8-a34a-176bfc923641", "name": "Extracting Embedding", "type": "n8n-nodes-base.set", "position": [6180, 1460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "01a28c9d-aeb1-48bb-8a73-f8bddbd73460", "name": "positive_example", "type": "array", "value": "={{ $json.data[0].embedding }}"}]}}, "typeVersion": 3.4}, {"id": "4ed11142-a734-435f-9f7a-f59e2d423076", "name": "Extracting Embedding1", "type": "n8n-nodes-base.set", "position": [6180, 1660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "01a28c9d-aeb1-48bb-8a73-f8bddbd73460", "name": "negative_example", "type": "array", "value": "={{ $json.data[0].embedding }}"}]}}, "typeVersion": 3.4}, {"id": "ce3aa9bc-a5b1-4529-bff5-e0dba43b99f3", "name": "Calling Qdrant Recommendation API", "type": "n8n-nodes-base.httpRequest", "position": [6840, 1500], "parameters": {"url": "https://edcc6735-2ffb-484f-b735-3467043828fe.europe-west3-0.gcp.cloud.qdrant.io:6333/collections/imdb_1000_open_ai/points/query", "method": "POST", "options": {}, "jsonBody": "={\n  \"query\": {\n    \"recommend\": {\n      \"positive\": [[{{ $json.positive_example }}]],\n      \"negative\": [[{{ $json.negative_example }}]],\n      \"strategy\": \"average_vector\"\n    }\n  },\n  \"limit\":3\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "Zin08PA0RdXVUKK7", "name": "QdrantApi n8n demo"}}, "typeVersion": 4.2}, {"id": "9b8a6bdb-16fe-4edc-86d0-136fe059a777", "name": "Retrieving Recommended Movies Meta Data", "type": "n8n-nodes-base.httpRequest", "position": [7060, 1460], "parameters": {"url": "https://edcc6735-2ffb-484f-b735-3467043828fe.europe-west3-0.gcp.cloud.qdrant.io:6333/collections/imdb_1000_open_ai/points", "method": "POST", "options": {}, "jsonBody": "={\n    \"ids\": [\"{{ $json.result.points[0].id }}\", \"{{ $json.result.points[1].id }}\", \"{{ $json.result.points[2].id }}\"],\n    \"with_payload\":true\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "qdrantApi"}, "credentials": {"qdrantApi": {"id": "Zin08PA0RdXVUKK7", "name": "QdrantApi n8n demo"}}, "typeVersion": 4.2}, {"id": "28cdcad5-3dca-48a1-b626-19eef657114c", "name": "Selecting Fields Relevant for Agent", "type": "n8n-nodes-base.set", "position": [7740, 1400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b4b520a5-d0e2-4dcb-af9d-0b7748fd44d6", "name": "movie_recommendation_score", "type": "number", "value": "={{ $json.score }}"}, {"id": "c9f0982e-bd4e-484b-9eab-7e69e333f706", "name": "movie_description", "type": "string", "value": "={{ $json.payload.content }}"}, {"id": "7c7baf11-89cd-4695-9f37-13eca7e01163", "name": "movie_name", "type": "string", "value": "={{ $json.payload.metadata.movie_name }}"}, {"id": "1d1d269e-43c7-47b0-859b-268adf2dbc21", "name": "movie_release_year", "type": "string", "value": "={{ $json.payload.metadata.release_year }}"}]}}, "typeVersion": 3.4}, {"id": "56e73f01-5557-460a-9a63-01357a1b456f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [5560, 1780], "parameters": {"content": "<PERSON><PERSON>, calling Qdrant's recommendation API based on user's request, transformed by AI agent"}, "typeVersion": 1}, {"id": "cce5250e-0285-4fd0-857f-4b117151cd8b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [4680, 720], "parameters": {"content": "Uploading data (movies and their descriptions) to Qdrant Vector Store\n"}, "typeVersion": 1}], "active": false, "pinData": {"Execute Workflow Trigger": [{"json": {"query": {"negative_example": "horror bloody movie", "positive_example": "romantic comedy"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "40d3669b-d333-435f-99fc-db623deda2cb", "connections": {"Merge": {"main": [[{"node": "Calling Qdrant Recommendation API", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Selecting Fields Relevant for Agent", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Split Out1": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Extracting Embedding": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Extracting Embedding1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Embedding Recommendation Request with Open AI", "type": "main", "index": 0}, {"node": "Embedding Anti-Recommendation Request with Open AI", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Calling Qdrant Recommendation API": {"main": [[{"node": "Retrieving Recommended Movies Meta Data", "type": "main", "index": 0}, {"node": "Split Out1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}, "Selecting Fields Relevant for Agent": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Retrieving Recommended Movies Meta Data": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Embedding Recommendation Request with Open AI": {"main": [[{"node": "Extracting Embedding", "type": "main", "index": 0}]]}, "Embedding Anti-Recommendation Request with Open AI": {"main": [[{"node": "Extracting Embedding1", "type": "main", "index": 0}]]}}}