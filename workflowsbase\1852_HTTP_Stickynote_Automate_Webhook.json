{"id": "f3BtfIEQ7lWiXBWQ", "meta": {"instanceId": "331d930239825586dfac8c04af3e03a7e86c895a3d1fbfa4ffab201148dc835e", "templateCredsSetupCompleted": true}, "name": "Connect Airtable Contacts to telli for Automated AI Voice Call Scheduling", "tags": [], "nodes": [{"id": "9562ed92-f04e-44b8-b2f1-3c9032788826", "name": "Airtable Trigger", "type": "n8n-nodes-base.airtableTrigger", "position": [-20, 0], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "appjsUaPrbH6ph7cB"}, "tableId": {"__rl": true, "mode": "id", "value": "tblVXEWTj7dErmNsa"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "=Created Time", "authentication": "airtableTokenApi", "additionalFields": {}}, "credentials": {"airtableTokenApi": {"id": "fcsJcjY4luV2FD5O", "name": "Airtable account"}}, "typeVersion": 1}, {"id": "d40f78ab-f96f-44eb-a2ac-1b16a66f94cb", "name": "Schedule Calls Request", "type": "n8n-nodes-base.httpRequest", "position": [500, 0], "parameters": {"url": "https://api.telli.com/v1/schedule-call", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "=contact_id", "value": "={{ $json.contact_id }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "<YOUR-API-KEY>"}]}}, "typeVersion": 4.2}, {"id": "b5d4b415-9363-4d2a-8689-4c789177d9c3", "name": "Add contact request", "type": "n8n-nodes-base.httpRequest", "position": [240, 0], "parameters": {"url": "https://api.telli.com/v1/add-contact", "method": "POST", "options": {}, "sendBody": true, "contentType": "=json", "sendHeaders": true, "bodyParameters": {"parameters": [{}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "<YOUR-API-KEY>"}]}}, "typeVersion": 4.2}, {"id": "********-838a-48db-a300-1a280d45b0f9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1620, -680], "parameters": {"color": 4, "width": 1420, "height": 2640, "content": "# Upload your CRM contacts to telli and schedule AI voice-agent calls\n\n## Introduction to telli and AI Voice-Agent Calls\n\ntelli is an innovative platform that provides AI-powered voice agents capable of making calls and performing tasks tailored to specific customer use cases. These AI voice-agents can handle a wide range of communication tasks, from appointment scheduling to customer support, with remarkable efficiency and natural conversation flow.\n\nThis template is designed for businesses and organizations looking to automate their outbound calling processes using telli's AI voice-agents in conjunction with Airtable as their CRM. It solves the problem of manual call scheduling and data transfer between your CRM and calling system, saving time and reducing human error.\n\n### Prerequisites\n\n- telli account\n- Airtable base with contact information\n- n8n instance\n\n### Step-by-Step Setup Guide\n\n1. **n8n Setup**:\n   - Create a new workflow in n8n.\n   - Add the Airtable node to connect to your CRM table.\n\n2. **telli API Configuration**:\n   - Log in to your telli dashboard.\n   - Locate and copy your API key under telli - Settings - API/Webhooks.\n\n3. **Workflow Configuration**:\n   - Add two HTTP Request nodes to your n8n workflow.\n   - Set the \"Authorization\" header in both POST requests, replacing the value with your telli API key.\n   - Configure the first request to use the `/add-contact` endpoint.\n   - Set up the second request to use the `/schedule-call` endpoint.\n\n4. **Data Mapping**:\n   - Map the relevant fields from your Airtable node to the telli API requests.\n\n5. **Testing and Activation**:\n   - Run a test execution of your workflow.\n   - Once satisfied with the results, activate the workflow.\n\n### API Endpoint Details\n\n#### Add Contact Endpoint\n\n- **URL**: `https://api.telli.com/v1/add-contact`\n- **Method**: POST\n- **Headers**:\n  - `Authorization: YOUR-API-KEY`\n  - `Content-Type: application/json`\n- **Payload**:\n```json\n{\n  \"external_contact_id\": \"string\",\n  \"salutation\": \"string\",\n  \"first_name\": \"string\",\n  \"last_name\": \"string\",\n  \"phone_number\": \"string\",\n  \"email\": \"<EMAIL>\",\n  \"contact_details\": {},\n  \"timezone\": \"string\"\n}\n```\n\n#### Schedule Call Endpoint\n\n- **URL**: `https://api.telli.com/v1/schedule-call`\n- **Method**: POST\n- **Headers**:\n  - `Authorization: YOUR-API-KEY`\n  - `Content-Type: application/json`\n- **Payload**:\n```json\n{\n  \"contact_id\": TELLI-CONTACT-ID,\n  \"agent_id\": \"string\",\n  \"max_retry_days\": 123,\n  \"call_details\": {\n    \"message\": \"Hello, this is your friendly reminder!\",\n    \"questions\": [\n      {\n        \"fieldName\": \"email\",\n        \"neededInformation\": \"email of the customer\",\n        \"exampleQuestion\": \"What is your email address?\",\n        \"responseFormat\": \"email string\"\n      }\n    ]\n  },\n  \"override_from_number\": \"string\"\n}\n```\n\n### Use Cases\n\nThis template is versatile and can be applied to various scenarios, including:\n\n***- Lead Qualification***: Automatically schedule calls to new leads entered in your CRM.\n\n***- Appointment Reminders***: Set up calls to remind clients of upcoming appointments.\n\n***- Customer Feedback***: Schedule follow-up calls after product deliveries or service completions.\n\n\n### Uploading Multiple Contacts\n\nFor bulk operations, you have two options:\n\n1. **Loop Node**: Include a Loop node in your n8n workflow to process multiple contacts sequentially.\n\n2. **Batch Endpoints**: Instead of `/add-contact` and `/schedule-call`, use telli's batch endpoints:\n   - `/add-contacts-batch`: Add multiple contacts within an array.\n   - `/schedule-calls-batch`: Schedule multiple calls at once.\n\nExample of batch endpoint usage:\n```json\n{\n  \"contacts\": [\n    {\"name\": \"John Doe\", \"phone\": \"+1234567890\"},\n    {\"name\": \"Jane Smith\", \"phone\": \"+1987654321\"}\n  ]\n}\n```\n\nBy leveraging this template, you can seamlessly integrate your Airtable CRM with telli's powerful AI voice-agents, automating your outbound calling process and enhancing your customer communication strategy.\n"}, "typeVersion": 1}, {"id": "7c31d739-c1a6-4b2b-a99d-2ab69a82b944", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-80, 160], "parameters": {"content": "## CRM node\n\nConnect it to the table where you store information about your leads or contacts in general."}, "typeVersion": 1}, {"id": "4cc74508-7323-4dce-b487-79404d9959bb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, -240], "parameters": {"height": 220, "content": "## Add contacts to telli\n\nHere you perform a POST request to telli's API to bring your CRM contacts into the telli system."}, "typeVersion": 1}, {"id": "ef541141-1aa7-4f45-96a3-2169d609ff6d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [480, 180], "parameters": {"height": 220, "content": "## Schedule calls for your new contacts\n\nRight after the contacts have been added, we perform another POST request to the telli API to schedule calls based on our smart calling strategy."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "7f4ed4a1-240c-45da-bc8c-940826d1a51a", "connections": {"Airtable Trigger": {"main": [[{"node": "Add contact request", "type": "main", "index": 0}]]}, "Add contact request": {"main": [[{"node": "Schedule Calls Request", "type": "main", "index": 0}]]}}}