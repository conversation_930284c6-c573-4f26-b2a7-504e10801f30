{"id": "QnVdtKiTf3nbrNkh", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Summarize emails with <PERSON><PERSON><PERSON><PERSON> then send to messenger", "tags": [], "nodes": [{"id": "50e12e63-df28-45ac-9208-48cbf5116d09", "name": "Read emails (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [340, 260], "parameters": {"options": {}, "postProcessAction": "nothing"}, "credentials": {"imap": {"id": "gXtdakU9M02LBQc3", "name": "IMAP account"}}, "typeVersion": 2}, {"id": "6565350b-2269-44e3-8f36-8797f32d3e09", "name": "Send email to A.I. to summarize", "type": "n8n-nodes-base.httpRequest", "position": [700, 260], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"meta-llama/llama-3.1-70b-instruct:free\",\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"I want you to read and summarize all the emails. If it's not rimportant, just give me a short summary with less than 10 words.\\n\\nHighlight as important if it is, add an emoji to indicate it is urgent:\\nFor the relevant content, find any action items and deadlines. Sometimes I need to sign up before a certain date or pay before a certain date, please highlight that in the summary for me.\\n\\nPut the deadline in BOLD at the top. If the email is not important, keep the summary short to 1 sentence only.\\n\\nHere's the email content for you to read:\\nSender email address: {{ encodeURIComponent($json.from) }}\\nSubject: {{ encodeURIComponent($json.subject) }}\\n{{ encodeURIComponent($json.textHtml) }}\"\n }\n ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "WY7UkF14ksPKq3S8", "name": "Head<PERSON> Au<PERSON> account 2"}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "d04c422a-c000-4e48-82d0-0bf44bcd9fff", "name": "Send summarized content to messenger", "type": "n8n-nodes-base.httpRequest", "position": [1100, 260], "parameters": {"url": "https://api.line.me/v2/bot/message/push", "method": "POST", "options": {}, "jsonBody": "={\n \"to\": \"U3ec262c49811f30cdc2d2f2b0a0df99a\",\n \"messages\": [\n {\n \"type\": \"text\",\n \"text\": \"{{ $json.choices[0].message.content.replace(/\\n/g, \"\\\\n\") }}\"\n }\n ]\n}\n\n\n ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "SzcKjO9Nn9vZPL2H", "name": "Head<PERSON> account 5"}}, "typeVersion": 4.2}, {"id": "57a1219c-4f40-407c-855b-86c4c7c468bb", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [180, 0], "parameters": {"width": 361, "height": 90, "content": "## Summarize emails with <PERSON><PERSON><PERSON><PERSON>\nYou can find out more about the [use case](https://rumjahn.com/how-a-i-saved-my-kids-school-life-and-my-marriage/)"}, "typeVersion": 1}, {"id": "********-56ac-419e-a32b-dc5c75f15f1f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [283, 141], "parameters": {"color": 5, "width": 229, "height": 280, "content": "Find your email server's IMAP Settings. \n- Link for [gmail](https://www.getmailspring.com/setup/access-gmail-via-imap-smtp)"}, "typeVersion": 1}, {"id": "1862abd6-7dca-4c66-90d6-110d4fcf4d99", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [580, 0], "parameters": {"color": 6, "width": 365, "height": 442, "content": "For the A.I. you can use Openrouter.ai. \n- Set up a free account\n- The A.I. model selected is FREE to use.\n## Credentials\n- Use header auth\n- Username: Authorization\n- Password: Bearer {insert your API key}.\n- The password is \"Bearer\" space plus your API key."}, "typeVersion": 1}, {"id": "c4a3a76f-539d-4bbf-8f95-d7aaebf39a55", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, 0], "parameters": {"color": 4, "width": 307, "height": 439, "content": "Don't use the official Line node. It's outdated.\n## Credentials\n- Use header auth\n- Username: Authorization\n- Password: Bearer {channel access token}\n\nYou can find your channel access token at the [Line API console](https://developers.line.biz/console/). Go to Messaging API and scroll to the bottom."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "81216e6a-2bd8-4215-8a96-376ee520469d", "connections": {"Read emails (IMAP)": {"main": [[{"node": "Send email to A.I. to summarize", "type": "main", "index": 0}]]}, "Send email to A.I. to summarize": {"main": [[{"node": "Send summarized content to messenger", "type": "main", "index": 0}]]}}}