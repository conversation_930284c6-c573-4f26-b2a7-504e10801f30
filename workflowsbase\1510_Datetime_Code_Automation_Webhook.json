{"id": "wa2uEnSIowqSrHoY", "meta": {"instanceId": "cca06617664f52c5a019ea575691fdbce675dd95dc0452af5f13dbe76d615b69"}, "name": "Intelligent Web Query and Semantic Re-Ranking Flow", "tags": [], "nodes": [{"id": "8e7dc5cb-6822-4ef6-9e5a-2b350a1526bf", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-640, -620], "parameters": {"color": 5, "width": 1172, "height": 970, "content": "\n## Step 1. Set Up a Free Brave Web Search Query API Key\n\nTo attain the free web search API tier from Brave, follow these steps:\n\n1. Visit api.search.brave.com\n2. Create an account\n3. Subscribe to the free plan (no charge)\n4. Navigate to the API Keys section\n5. Generate an API key. For the subscription type, choose \"Free\".\n6. Go to the \"Query\" Nodes and change the \"X-Subscription-Token\" value to your API Key.\n"}, "typeVersion": 1}, {"id": "5bb3e68f-7693-4d4b-b794-843f2c3535e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1580, -420], "parameters": {"color": 4, "width": 680, "height": 360, "content": "## If you require to change this Node to Webhook Or any Other Item:\n\n- In case you want to change the input type from Webhook to any other item, Make sure to go to the Query 1 and Query 1 Ranker and replace the Webhook Input to your Node's input."}, "typeVersion": 1}, {"id": "f2fc02f9-a78a-4e87-be85-0032492a9f3f", "name": "Date & Time", "type": "n8n-nodes-base.dateTime", "position": [-820, -240], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "6f18ebbd-83db-4900-bc2e-0a9f23d6e8c8", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-1340, -240], "webhookId": "************************************", "parameters": {"path": "************************************", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "ba5ea83e-1b47-475b-863f-269ae293729a", "name": "Auto-fixing Output Parser6", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [180, -140], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "ca426b6d-5412-4c5b-a55c-009a47c59a81", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [-580, -140], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "501d5390-5317-4973-a3e9-b0f502399c2b", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-460, -60], "parameters": {"jsonSchemaExample": "{\n \"reasoning_summary\": \"Detailed explanation of each analytical chain’s purpose and insights, including key terms and considerations for query formulation.\",\n \"final_search_query\": \"The single, best-fit search query derived from the meta-reasoning and multi-chain analysis, optimized to answer the research question.\"\n}"}, "typeVersion": 1.2}, {"id": "a27e75c7-0307-4d71-9266-5a56b297a6e3", "name": "Query-1 Combined", "type": "n8n-nodes-base.code", "position": [-80, -240], "parameters": {"jsCode": "// Initialize an empty string to store all title, url, and description pairs\nlet aggregatedOutputText = \"\";\n\n// Loop through all items passed to this Function node\nfor (let item of items) {\n // Access the JSON data from \"Query 1\" node for the current item\n const queryData = item.json;\n\n // Ensure there is a \"web.results\" array to process\n if (queryData.web?.results && Array.isArray(queryData.web.results)) {\n // Loop through all results in the \"web.results\" array\n for (let result of queryData.web.results) {\n // Extract the title, url, and description for each result\n const title = result.title || \"No Title\";\n const url = result.url || \"No URL\";\n const description = result.description || \"No Description\";\n\n // Append the values to the aggregated string\n aggregatedOutputText += `Title: ${title}\\nURL: ${url}\\nDescription: ${description}\\n\\n`;\n }\n } else {\n // If no results array, handle gracefully\n aggregatedOutputText += \"No results found for this item.\\n\\n\";\n }\n}\n\n// Trim the final string to remove any trailing newline and whitespace\naggregatedOutputText = aggregatedOutputText.trim();\n\n// Return a single item containing the aggregated output as a string\nreturn [\n {\n json: {\n aggregated_text: aggregatedOutputText\n }\n }\n];\n"}, "typeVersion": 2}, {"id": "acbdbe94-b5a7-4ec9-9fc8-c3ab147f42fa", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [640, -240], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={\n \"Highest_RANKEDURL_1\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_1']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_1']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_1']['description'] }}\"\n },\n \"Highest_RANKEDURL_2\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_2']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_2']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_2']['description'] }}\"\n },\n \"Highest_RANKEDURL_3\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_3']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_3']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_3']['description'] }}\"\n },\n \"Highest_RANKEDURL_4\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_4']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_4']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_4']['description'] }}\"\n },\n \"Highest_RANKEDURL_5\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_5']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_5']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_5']['description'] }}\"\n },\n \"Highest_RANKEDURL_6\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_6']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_6']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_6']['description'] }}\"\n },\n \"Highest_RANKEDURL_7\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_7']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_7']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_7']['description'] }}\"\n },\n \"Highest_RANKEDURL_8\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_8']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_8']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_8']['description'] }}\"\n },\n \"Highest_RANKEDURL_9\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_9']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_9']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_9']['description'] }}\"\n },\n \"Highest_RANKEDURL_10\": {\n \"title\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_10']['title'] }}\",\n \"link\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_10']['link'] }}\",\n \"description\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Highest_RANKEDURL_10']['description'] }}\"\n },\n \"Information_extracted\": \"{{ $item('0').$node['Semantic Search - Result Re-Ranker'].json['output']['Information_extracted'] }}\"\n}\n"}, "typeVersion": 1.1}, {"id": "b8b6ae73-586a-406f-9641-57e2625f800c", "name": "Semantic Search - <PERSON><PERSON>t Re-Ranker", "type": "@n8n/n8n-nodes-langchain.chainLlm", "onError": "continueRegularOutput", "position": [100, -240], "parameters": {"text": "=\n**Objective:**\n\nFor the user's query, web search results are provided. Your tasks are:\n\n1. **Rank the links** based on how well they match the user's query.\n2. **Extract relevant information** from the descriptions provided. If no relevant information is found, return \"N/A\".\n\n---\n\n**Task:**\n\n**Step 1: Understand the User's Intent**\n\n- Determine what the user is truly and technically looking for.\n- The user's request query is: \"{{ $('Webhook').item.json.query['Research Question'] }}\"\n- The serach results below, however their performance seem, have been based on this query \"{{ $item(\"0\").$node[\"Semantic Search -Query Maker\"].json[\"output\"][\"final_search_query\"] }}\". If the result are not satisfactory or missing due to bad query making, you should note that as well for the neww query making.\n- To nesure being time aware , realize todays date is: \"{{ $item(\"0\").$node[\"Date & Time\"].json[\"currentDate\"] }}\"\n\n- Follow a three-step chain of thought to comprehend the user's needs. Think out loud.\n\n---\n\n**Step 2: Rank the Links**\n\n- From the URLs and description snippets provided, **rank the top 10 websites** that are most likely to contain the required information.\n- Use the titles, descriptions, and sources to inform your ranking.\n\n**Links, Titles, and Descriptions:**\n\n{{ $json.aggregated_text }}\n\n---\n\nThis list completes the structure up to 20 results as you requested. Let me know if there’s anything more you need!\n\n---\n\n**Step 3: Analyze and Create a Follow-up Query**\n\n- Recognize that for the user's request:\n\n `\"{{ $('Webhook').item.json.query['Research Question'] }}\"`\n\n The results provided are based on the assistant's generated search query:\n\n `\"{{ $item(\"0\").$node[\"Semantic Search -Query Maker\"].json[\"output\"][\"final_search_query\"] }}\"`\n\n- Analyze and revise any issues or new insights through multi-step thinking to create a follow-up query.\n\n**Indications and Priorities:**\n\n1. **No Results Received:** If no search items are shared, the search query may have been ineffective (e.g., too specific, incorrect parameters).\n2. **Insufficient or Unpromising Results:** If fewer than 20 but more than 5 results are provided, and none seem promising, the search query may need refinement.\n3. **Successful Results with Potential Follow-up:** If none of the above issues occurred and the search results provide answers or suggest a follow-up, create a new query. This could be a new topic, a deep dive, or a parallel factor that offers additional benefits.\n\n- Provide your chain of thought that connects the user's request to the actual information.\n\n- Deliver precise, detailed, and value-oriented information relevant to the user's query.\n\n**Step 4: Query making notes and examples**: \n\nThe queries must not be long tails , as they result in 0 websearch reutrns. We give you some examples of good web search queries:\nExamples:\n\nUser Question: \"What is the current state of the U.S. economy in 2024?\"\nEffective Search Query: \"U.S. Economy Analysis Report 2024\"\n\nUser Question: \"What are the recent advancements in artificial intelligence?\"\nEffective Search Query: \"2024 Artificial Intelligence Developments\"\n\nUser Question: \"How is climate change affecting agriculture globally?\"\nEffective Search Query: \"Global Impact of Climate Change on Agriculture 2024\"\n\nUser Question: \"What are the latest trends in cybersecurity threats?\"\nEffective Search Query: \"Cybersecurity Threats and Trends 2024\"\n\nUser Question: \"What is the outlook for renewable energy investments?\"\nEffective Search Query: \"Renewable Energy Investment Outlook 2024\"\n\n**Step 5: Query making*: \nor query making remember as we said:\n - **Today's Date:** \"{{ $item(\"0\").$node[\"Date & Time\"].json[\"currentDate\"] }}\"\n **Search Inquiry:** \n - **Search Topic to create the query upon it:**{{ $item(\"0\").$node[\"Webhook\"].json[\"query\"][\"Research Question\"] }}\"\"\n\n\n---\n\n**Step 6: Output Format**\n\nEnsure the response is in the following JSON format:\n\n{\n \"chain_of_thought\": \"Insert your step-by-step reasoning here.\",\n \"Highest_RANKEDURL_1\": {\n \"title\": \"Insert the First Ranked URL's Title here.\",\n \"link\": \"Insert the First Ranked URL here.\",\n \"description\": \"Insert the First Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_2\": {\n \"title\": \"Insert the Second Ranked URL's Title here.\",\n \"link\": \"Insert the Second Ranked URL here.\",\n \"description\": \"Insert the Second Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_3\": {\n \"title\": \"Insert the Third Ranked URL's Title here.\",\n \"link\": \"Insert the Third Ranked URL here.\",\n \"description\": \"Insert the Third Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_4\": {\n \"title\": \"Insert the Fourth Ranked URL's Title here.\",\n \"link\": \"Insert the Fourth Ranked URL here.\",\n \"description\": \"Insert the Fourth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_5\": {\n \"title\": \"Insert the Fifth Ranked URL's Title here.\",\n \"link\": \"Insert the Fifth Ranked URL here.\",\n \"description\": \"Insert the Fifth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_6\": {\n \"title\": \"Insert the Sixth Ranked URL's Title here.\",\n \"link\": \"Insert the Sixth Ranked URL here.\",\n \"description\": \"Insert the Sixth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_7\": {\n \"title\": \"Insert the Seventh Ranked URL's Title here.\",\n \"link\": \"Insert the Seventh Ranked URL here.\",\n \"description\": \"Insert the Seventh Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_8\": {\n \"title\": \"Insert the Eighth Ranked URL's Title here.\",\n \"link\": \"Insert the Eighth Ranked URL here.\",\n \"description\": \"Insert the Eighth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_9\": {\n \"title\": \"Insert the Ninth Ranked URL's Title here.\",\n \"link\": \"Insert the Ninth Ranked URL here.\",\n \"description\": \"Insert the Ninth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_10\": {\n \"title\": \"Insert the Tenth Ranked URL's Title here.\",\n \"link\": \"Insert the Tenth Ranked URL here.\",\n \"description\": \"Insert the Tenth Ranked URL's Description here.\"\n },\n \"Information_extracted\": \"Insert all extracted information relevant to the user's query or 'N/A' if none.\"\n}\n", "messages": {"messageValues": [{"message": "=\nYou are an expert information retrieval and critical evaluation assistant designed to process, rank, and extract high-relevance content from web search results for complex user queries. You must provide value-oriented insights while refining searches based on relevance and context sensitivity. \n\n**Your Process and Priorities:**\n\n#### 1. **Determine the User's Technical Intent**\n - Interpret the user's core question provided as `{{ $item(\"0\").$node[\"Webhook\"].json[\"query\"][\"Research Question\"] }}`, discerning underlying objectives and specialized needs.\n - Recognize that the search results may have been generated from a **secondary query**: `{{ $item(\"0\").$node[\"Semantic Search -Query Maker\"].json[\"output\"][\"final_search_query\"] }}`. \n - Judge the adequacy of this generated query. If it does not meet the user’s objectives, highlight the need for query refinement and prepare to adapt the approach.\n - Stay mindful of the date context, using `{{ $item(\"0\").$node[\"Date & Time\"].json[\"currentDate\"] }}` to assess the freshness of content or time-sensitive relevance.\n\n#### 2. **Rank Results Based on Analytical Relevance**\n - From the search results provided, **rank the top 3 URLs** that most closely align with the user’s intent and technical needs.\n - Use multi-dimensional analysis to assess how each link’s title, description, and source match the user’s objective.\n - Prioritize results based on credibility, relevance, and their potential to add depth to the user’s inquiry.\n - Your goal is to select the highest-value links, disregarding results that offer superficial, off-topic, or outdated information.\n\n#### 3. **Extract Key Information**\n - For each of the top 3 ranked results, extract insights and details from the description snippets that directly address the user’s query.\n - If no pertinent information is available in a description, record `\"N/A\"` to indicate its lack of relevance.\n\n#### 4. **Evaluate for Potential Query Improvement**\n - Evaluate the relevance and coverage of search results:\n - If fewer than 5 relevant results are present, consider that the initial query may be too narrow, specific, or otherwise misaligned.\n - Generate a **refined query** that is adjusted to better match the user’s likely needs and produce higher-quality results.\n - Use advanced language modifications, new keyword suggestions, or rephrasing to formulate a search query that enhances alignment with the user’s goals.\n"}]}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.4}, {"id": "a1ca671d-0b0c-4717-9def-93fdb965de8d", "name": "Query", "type": "n8n-nodes-base.httpRequest", "position": [-240, -240], "parameters": {"url": "https://api.search.brave.com/res/v1/web/search", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.output.final_search_query }}"}]}, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}, {"name": "X-Subscription-Token", "value": "<Insert Your API Key Here>"}]}}, "typeVersion": 4.2}, {"id": "d3cc4e7c-3ead-4d38-9b51-a11cd9d7faeb", "name": "Webhook Call", "type": "n8n-nodes-base.httpRequest", "position": [-180, 1040], "parameters": {"url": "https://primary-production-8aa4.up.railway.app/webhook-test/************************************", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "Research Question", "value": "what is the latest news in global world in politics and economy?"}]}}, "typeVersion": 4.2}, {"id": "6931404b-94d6-4b9d-9f0a-124012212eb5", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-640, 420], "parameters": {"color": 3, "width": 1180, "height": 840, "content": "## Step 2. Setup the Webhook Call Node\n\n**Instructions for Setting Up the Webhook Call and Using It in Your Workflow**\n\nThis node is designed to send a **web search query** to the workflow (partly built in this chart) and return the results. Follow these steps to correctly configure and use it:\n\n1. **Locate the \"Webhook\" Node in the Workflow**:\n - Navigate to the workflow above, the first item, the \"Webhook\" node.\n - In the \"Webhook\" node, change the **Webhook URL option** from \"Test URL\" to \"Production URL.\"\n - Copy the generated **Production URL**.\n\n2. **Paste the Webhook URL in the HTTP Node**:\n - In your target workflow, locate the **HTTP Request** node.\n - Paste the copied **Production URL** into the URL field of the HTTP Request node. This connects the two workflows.\n\n3. **Send the Research Request**:\n - When sending the request to this workflow, make sure to include your web search query in the **\"Research Question\" parameter** of the HTTP Request node.\n\n4. **Move the Webhook Call Node**:\n - Move this **Webhook Call Node** into the workflow where you need the research results. Ensure that it’s correctly connected and configured to send the data to the main workflow.\n"}, "typeVersion": 1}, {"id": "01d73f91-1dd6-4b80-951c-9f944ea9d992", "name": "Semantic Search -Query Maker", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-560, -240], "parameters": {"text": "=1. **Task:** `\"Your task is to develop a web search query that most effectively answers the research question given. Use meta-reasoning and multi-chain analysis to ensure a comprehensive approach.\"`\n\n2. **Structured Guidance for Chains of Thought:** \n a. **Chain 1:** Break down the research question, identifying keywords and relevant terms. \n b. **Chain 2:** Explore the context and potential sources, determining the types of results that would be most relevant. \n c. **Chain 3:** Refine the query for specificity and completeness, considering how to capture nuances of the question.\n\n3. **Final Query Generation:** Based on the insights from the three chains, generate a single, refined search query.\n\n\n4. Note, the queries must not be long tails , as they result in 0 websearch reutrns. We give you some examples of good web search queries:\nExamples:\n\nUser Question: \"What is the current state of the U.S. economy in 2024?\"\n\nEffective Search Query: \"U.S. Economy Analysis Report 2024\"\nUser Question: \"What are the recent advancements in artificial intelligence?\"\n\nEffective Search Query: \"2024 Artificial Intelligence Developments\"\nUser Question: \"How is climate change affecting agriculture globally?\"\n\nEffective Search Query: \"Global Impact of Climate Change on Agriculture 2024\"\nUser Question: \"What are the latest trends in cybersecurity threats?\"\n\nEffective Search Query: \"Cybersecurity Threats and Trends 2024\"\nUser Question: \"What is the outlook for renewable energy investments?\"\n\nEffective Search Query: \"Renewable Energy Investment Outlook 2024\"\n\n5. Data Input:\n - **Today's Date:** \"{{ $item(\"0\").$node[\"Date & Time\"].json[\"currentDate\"] }}\"\n **Search Inquiry:** \n - **Search Topic to create the query upon it:**{{ $item(\"0\").$node[\"Webhook\"].json[\"query\"][\"Research Question\"] }}\"\"\n\n6. Now develop the best fit web search query given the user request above under number 5\n---\n\n**Output Requirements:** \nThe Assistant’s output should be in JSON format, structured as follows:\n\n{\n \"reasoning_summary\": \"Detailed explanation of each analytical chain’s purpose and insights, including key terms and considerations for query formulation.\",\n \"final_search_query\": \"The single, best-fit search query derived from the meta-reasoning and multi-chain analysis, optimized to answer the research question.\"\n}\n```\n\n---\n", "messages": {"messageValues": [{"message": "You are an advanced data and research retrieval through smart search queires via Bing and Brave websearch APIs. "}, {"type": "HumanMessagePromptTemplate", "message": "1. **Task:** `\"Your task is to develop a web search query that most effectively answers the research question given. Use meta-reasoning and multi-chain analysis to ensure a comprehensive approach.\"`\n\n2. **Structured Guidance for Chains of Thought:** \n a. **Chain 1:** Break down the research question, identifying keywords and relevant terms. \n b. **Chain 2:** Explore the context and potential sources, determining the types of results that would be most relevant. \n c. **Chain 3:** Refine the query for specificity and completeness, considering how to capture nuances of the question.\n\n3. **Final Query Generation:** Based on the insights from the three chains, generate a single, refined search query.\n\n4. Data Input:\n - **Today's Date:** \"2024-11-12T10:21:33.764-05:00\"\n2. **Search Inquiry:** \n - **Search Topic to create the query upon it:** \"What is the latest stock Market Analysis in mid-term?\"\n\n\n---\n\n**Output Requirements:** \nThe Assistant’s output should be in JSON format, structured as follows:\n\n{\n \"reasoning_summary\": \"Detailed explanation of each analytical chain’s purpose and insights, including key terms and considerations for query formulation.\",\n \"final_search_query\": \"The single, best-fit search query derived from the meta-reasoning and multi-chain analysis, optimized to answer the research question.\"\n}\n```\n\n---\n"}, {"type": "AIMessagePromptTemplate", "message": "Assessing query functionality\n\nI’m evaluating the viability of the assistant's proposed search query. The user questions if it would yield meaningful results on different search engines.\n\nClarifying the query\n\nOK, let me see. The task involves clarifying the user's needs, ensuring the response aligns with OpenAI's policies, and possibly re-evaluating and correcting any errors in the previous response.\n\nRefining search specifics\n\nI'm working through refining the search query to focus on specific terms like 'S&P 500', integrating dates such as 'November 2024', and emphasizing phrases for precision.\n\nSelf-reflecting on search queries\n\nToday, I’m thinking about how to validate search queries for accuracy and realism, ensuring they are practical and likely to yield productive results.\n\nAcknowledging insights\n\nI’m recognizing and correcting a search query to ensure it’s optimal and effectively aligned with advanced user needs, leading to a more precise output.\n\nCrafting a search query\n\nI’m crafting a search query to pinpoint the most pertinent recent mid-term stock market analyses. Starting with relevant terms and then exploring potential sources for context and relevance.\n\nPinpointing sources and specifics\n\nI'm gathering info from financial news, investment reports, and economic research institutions. I’m refining search terms like \"mid-term stock market analysis\" and \"market forecast\" to keep results current and nuanced.\n\nCrafting the query\n\nI’m finalizing the query by merging terms like 'latest' and 'stock market analysis' with 'mid-term', and considering synonyms such as 'market forecast' to refine the search for 2024.\n\nTweaking the search\n\nI adjusted the search terms to 'latest stock market mid-term outlook 2024,' removing excessive quotes for better clarity and relevance.\n\n\n{\n \"reasoning_summary\": \"Chain 1: Broke down the research question to identify key terms such as 'latest', 'stock market analysis', and 'mid-term'. Considered synonyms like 'mid-term outlook', 'market forecast', and included the current year '2024' for recency.\\n\\nChain 2: Explored the context where the user seeks recent analyses of the stock market with a mid-term perspective. Relevant sources would be financial news outlets, investment banks, and economic research firms. The most useful results would be up-to-date articles or reports offering mid-term forecasts.\\n\\nChain 3: Refined the query by removing unnecessary quotation marks and simplifying it to reflect how an advanced user might search. Included terms like 'latest', 'mid-term outlook', and '2024' to enhance specificity without limiting the search results unnecessarily.\",\n \"final_search_query\": \"latest stock market mid-term outlook 2024\"\n}"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "696b4f97-ad29-406b-9157-44ad9d05c9cd", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [740, 180], "parameters": {"model": "claude-3-5-haiku-********", "options": {"topP": 0.8, "temperature": 0.4, "maxTokensToSample": 4096}}, "credentials": {"anthropicApi": {"id": "mVKB2CryW6bMm9Qo", "name": "Anthropic account"}}, "typeVersion": 1.2}, {"id": "82f25610-8b70-4aee-ad90-2616d3389f15", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [900, 180], "parameters": {"options": {"topP": 0.7, "maxTokens": 4096, "maxRetries": 1, "temperature": 0.5}}, "credentials": {"openAiApi": {"id": "wQQZLwJO9A5nFu8h", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "f230bdf0-4a22-4abf-96cd-47f309f0c514", "name": "Structured Output Parser2", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [260, -60], "parameters": {"jsonSchemaExample": "{\n \"chain_of_thought\": \"Insert your step-by-step reasoning here.\",\n \"Highest_RANKEDURL_1\": {\n \"title\": \"Insert the First Ranked URL's Title here.\",\n \"link\": \"Insert the First Ranked URL here.\",\n \"description\": \"Insert the First Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_2\": {\n \"title\": \"Insert the Second Ranked URL's Title here.\",\n \"link\": \"Insert the Second Ranked URL here.\",\n \"description\": \"Insert the Second Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_3\": {\n \"title\": \"Insert the Third Ranked URL's Title here.\",\n \"link\": \"Insert the Third Ranked URL here.\",\n \"description\": \"Insert the Third Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_4\": {\n \"title\": \"Insert the Fourth Ranked URL's Title here.\",\n \"link\": \"Insert the Fourth Ranked URL here.\",\n \"description\": \"Insert the Fourth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_5\": {\n \"title\": \"Insert the Fifth Ranked URL's Title here.\",\n \"link\": \"Insert the Fifth Ranked URL here.\",\n \"description\": \"Insert the Fifth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_6\": {\n \"title\": \"Insert the Sixth Ranked URL's Title here.\",\n \"link\": \"Insert the Sixth Ranked URL here.\",\n \"description\": \"Insert the Sixth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_7\": {\n \"title\": \"Insert the Seventh Ranked URL's Title here.\",\n \"link\": \"Insert the Seventh Ranked URL here.\",\n \"description\": \"Insert the Seventh Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_8\": {\n \"title\": \"Insert the Eighth Ranked URL's Title here.\",\n \"link\": \"Insert the Eighth Ranked URL here.\",\n \"description\": \"Insert the Eighth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_9\": {\n \"title\": \"Insert the Ninth Ranked URL's Title here.\",\n \"link\": \"Insert the Ninth Ranked URL here.\",\n \"description\": \"Insert the Ninth Ranked URL's Description here.\"\n },\n \"Highest_RANKEDURL_10\": {\n \"title\": \"Insert the Tenth Ranked URL's Title here.\",\n \"link\": \"Insert the Tenth Ranked URL here.\",\n \"description\": \"Insert the Tenth Ranked URL's Description here.\"\n },\n \"Information_extracted\": \"Insert all extracted information relevant to the user's query or 'N/A' if none.\"\n}\n"}, "typeVersion": 1.2}, {"id": "3421ebe5-6a86-435e-b9c6-e3dcf6dd7833", "name": "Parser Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-180, 180], "parameters": {"options": {"topP": 0.6, "temperature": 0.4, "maxOutputTokens": 4096}, "modelName": "models/gemini-1.5-flash-002"}, "credentials": {"googlePalmApi": {"id": "rTbWGMQGwWtjhNaA", "name": "Google Gemini(PaLM) Api account 4"}}, "typeVersion": 1}, {"id": "fd19e9f6-d8c7-45df-93d9-ecf7956b461f", "name": "Agent Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-180, -20], "parameters": {"options": {"topP": 0.6, "temperature": 0.4, "safetySettings": {"values": [{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}]}, "maxOutputTokens": 4086}, "modelName": "models/gemini-1.5-flash-002"}, "credentials": {"googlePalmApi": {"id": "rTbWGMQGwWtjhNaA", "name": "Google Gemini(PaLM) Api account 4"}}, "typeVersion": 1}, {"id": "156e83ff-928a-4aca-af8b-0c0fa51acd56", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [560, -20], "parameters": {"color": 6, "width": 712, "height": 370, "content": "\n## Customized Models to Replace\n\nIn case you rather to use another LLM Model to Perform the Semantic Search and Re-Ranking, These nodes below are Optimized based on the LLM Structure of OpenAI GPT4o & Anthropic Claude.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b824ca9d-5676-4ca5-b97d-e5113d955de2", "connections": {"Query": {"main": [[{"node": "Query-1 Combined", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Date & Time", "type": "main", "index": 0}]]}, "Agent Model": {"ai_languageModel": [[{"node": "Semantic Search - <PERSON><PERSON>t Re-Ranker", "type": "ai_languageModel", "index": 0}, {"node": "Semantic Search -Query Maker", "type": "ai_languageModel", "index": 0}]]}, "Date & Time": {"main": [[{"node": "Semantic Search -Query Maker", "type": "main", "index": 0}]]}, "Parser Model": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser6", "type": "ai_languageModel", "index": 0}, {"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Query-1 Combined": {"main": [[{"node": "Semantic Search - <PERSON><PERSON>t Re-Ranker", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[]]}, "Anthropic Chat Model": {"ai_languageModel": [[]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Semantic Search -Query Maker", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser6", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser6": {"ai_outputParser": [[{"node": "Semantic Search - <PERSON><PERSON>t Re-Ranker", "type": "ai_outputParser", "index": 0}]]}, "Semantic Search -Query Maker": {"main": [[{"node": "Query", "type": "main", "index": 0}]]}, "Semantic Search - Result Re-Ranker": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}