{"id": "LGpVLWPpNZSt9ISM", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Contact Form Text Classifier for eCommerce", "tags": [], "nodes": [{"id": "13175d48-c3a6-4ca6-afed-b70f40289f38", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-480, -320], "webhookId": "8e10c8ca-895c-4274-ba95-0d646b8bda4e", "parameters": {"options": {}, "formTitle": "Contacts", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "Name", "requiredField": true}, {"fieldLabel": "Email", "placeholder": "Email", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Message", "placeholder": "Message", "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "Basic Contact Form"}, "typeVersion": 2.2}, {"id": "7b352c9f-5d2e-46ca-9499-594063167e9a", "name": "Text Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-160, -320], "parameters": {"options": {"fallback": "other", "systemPromptTemplate": "=Please classify the text provided by the user into one of the following categories: {categories}, and use the provided formatting instructions below. Don't explain, and only output the json with the selected {categories}."}, "inputText": "={{ $json.Message }}", "categories": {"categories": [{"category": "Request Quote", "description": "Request for quote"}, {"category": "Product info", "description": "General information about a product"}, {"category": "General problem", "description": "General problems about a product"}, {"category": "Order", "description": "Information about an order placed"}]}}, "typeVersion": 1}, {"id": "efef4c71-5f56-44b0-a613-9fa888e495b8", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-220, -100], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "83f0d528-884c-4701-8fdd-dc07c05fafb5", "name": "Prod. Dep.", "type": "n8n-nodes-base.emailSend", "position": [320, -540], "parameters": {"html": "=Name: {{ $json.Name }}\nEmail: {{ $json.Email }}\n\nMessage:\n{{ $json.Message }}\n\nTipo prodotto: {{ $json[\"tipo prodotto\"] }}", "options": {"replyTo": "={{ $json.Email }}"}, "subject": "=[n8n Contacts] Product info", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "********-dcea-4db9-9ffd-f55193eaa83d", "name": "Quote Dep.", "type": "n8n-nodes-base.emailSend", "position": [320, -780], "parameters": {"html": "=Name: {{ $json.Name }}\nEmail: {{ $json.Email }}\n\nMessage:\n{{ $json.Message }}\n\nTipo prodotto: {{ $json[\"tipo prodotto\"] }}", "options": {"replyTo": "={{ $json.Email }}"}, "subject": "=[n8n Contacts] Quote", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "f6a63c4f-ee2e-42f1-a12c-b1fc6cf48f94", "name": "Gen. Dep.", "type": "n8n-nodes-base.emailSend", "position": [320, -320], "parameters": {"html": "=Name: {{ $json.Name }}\nEmail: {{ $json.Email }}\n\nMessage:\n{{ $json.Message }}\n\nTipo prodotto: {{ $json[\"tipo prodotto\"] }}", "options": {"replyTo": "={{ $json.Email }}"}, "subject": "=[n8n Contacts] General", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "04a3e144-af75-4a95-819f-d5f1d4591b67", "name": "Order Dep.", "type": "n8n-nodes-base.emailSend", "position": [320, -80], "parameters": {"html": "=Name: {{ $json.Name }}\nEmail: {{ $json.Email }}\n\nMessage:\n{{ $json.Message }}\n\nTipo prodotto: {{ $json[\"tipo prodotto\"] }}", "options": {"replyTo": "={{ $json.Email }}"}, "subject": "=[n8n Contacts] Order info", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "3767e3c7-b792-4b0d-a1f2-fc068310cb11", "name": "Other Dep.", "type": "n8n-nodes-base.emailSend", "position": [320, 140], "parameters": {"html": "=Name: {{ $json.Name }}\nEmail: {{ $json.Email }}\n\nMessage:\n{{ $json.Message }}\n\nTipo prodotto: {{ $json[\"tipo prodotto\"] }}", "options": {"replyTo": "={{ $json.Email }}"}, "subject": "=[n8n Contacts] Other", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "c411a82d-0b86-49da-a11f-47ec79f9f7ff", "name": "Quote DB", "type": "n8n-nodes-base.googleSheets", "position": [520, -780], "parameters": {"columns": {"value": {"TO": "={{ (JSON.stringify($json.envelope.to)) }}", "DATA": "={{ $('Text Classifier').item.json.submittedAt }}", "NOME": "={{ $('Text Classifier').item.json.Name }}", "EMAIL": "={{ $('Text Classifier').item.json.Email }}", "CATEGORIA": "info prodotti", "RICHIESTA": "={{ $('Text Classifier').item.json.Message }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOME", "type": "string", "display": true, "required": false, "displayName": "NOME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RICHIESTA", "type": "string", "display": true, "required": false, "displayName": "RICHIESTA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORIA", "type": "string", "display": true, "required": false, "displayName": "CATEGORIA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit?usp=drivesdk", "cachedResultName": "Classified Contact Form"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "c14008fb-8932-44ad-88ef-42f6d4029fb1", "name": "Prod DB", "type": "n8n-nodes-base.googleSheets", "position": [520, -540], "parameters": {"columns": {"value": {"TO": "={{ (JSON.stringify($json.envelope.to)) }}", "DATA": "={{ $('Text Classifier').item.json.submittedAt }}", "NOME": "={{ $('Text Classifier').item.json.Name }}", "EMAIL": "={{ $('Text Classifier').item.json.Email }}", "CATEGORIA": "info prodotti", "RICHIESTA": "={{ $('Text Classifier').item.json.Message }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOME", "type": "string", "display": true, "required": false, "displayName": "NOME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RICHIESTA", "type": "string", "display": true, "required": false, "displayName": "RICHIESTA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORIA", "type": "string", "display": true, "required": false, "displayName": "CATEGORIA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit?usp=drivesdk", "cachedResultName": "Classified Contact Form"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f2e02c07-7218-4d08-a816-1ce2de289312", "name": "General DB", "type": "n8n-nodes-base.googleSheets", "position": [520, -320], "parameters": {"columns": {"value": {"TO": "={{ (JSON.stringify($json.envelope.to)) }}", "DATA": "={{ $('Text Classifier').item.json.submittedAt }}", "NOME": "={{ $('Text Classifier').item.json.Name }}", "EMAIL": "={{ $('Text Classifier').item.json.Email }}", "CATEGORIA": "info prodotti", "RICHIESTA": "={{ $('Text Classifier').item.json.Message }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOME", "type": "string", "display": true, "required": false, "displayName": "NOME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RICHIESTA", "type": "string", "display": true, "required": false, "displayName": "RICHIESTA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORIA", "type": "string", "display": true, "required": false, "displayName": "CATEGORIA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit?usp=drivesdk", "cachedResultName": "Classified Contact Form"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d6ee5c05-d966-47c1-a7ec-df721f77c5d0", "name": "Order DB", "type": "n8n-nodes-base.googleSheets", "position": [520, -80], "parameters": {"columns": {"value": {"TO": "={{ (JSON.stringify($json.envelope.to)) }}", "DATA": "={{ $('Text Classifier').item.json.submittedAt }}", "NOME": "={{ $('Text Classifier').item.json.Name }}", "EMAIL": "={{ $('Text Classifier').item.json.Email }}", "CATEGORIA": "info prodotti", "RICHIESTA": "={{ $('Text Classifier').item.json.Message }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOME", "type": "string", "display": true, "required": false, "displayName": "NOME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RICHIESTA", "type": "string", "display": true, "required": false, "displayName": "RICHIESTA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORIA", "type": "string", "display": true, "required": false, "displayName": "CATEGORIA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit?usp=drivesdk", "cachedResultName": "Classified Contact Form"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "b4f344bd-a5c4-4977-af96-edbab85b49d0", "name": "Other DB", "type": "n8n-nodes-base.googleSheets", "position": [520, 140], "parameters": {"columns": {"value": {"TO": "={{ (JSON.stringify($json.envelope.to)) }}", "DATA": "={{ $('Text Classifier').item.json.submittedAt }}", "NOME": "={{ $('Text Classifier').item.json.Name }}", "EMAIL": "={{ $('Text Classifier').item.json.Email }}", "CATEGORIA": "info prodotti", "RICHIESTA": "={{ $('Text Classifier').item.json.Message }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NOME", "type": "string", "display": true, "required": false, "displayName": "NOME", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "EMAIL", "type": "string", "display": true, "required": false, "displayName": "EMAIL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "RICHIESTA", "type": "string", "display": true, "required": false, "displayName": "RICHIESTA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORIA", "type": "string", "display": true, "required": false, "displayName": "CATEGORIA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D6tfsAK81ZE6VA0-sd_syuyI_rloNYjgWOhwgszPIZw/edit?usp=drivesdk", "cachedResultName": "Classified Contact Form"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "99872f49-85c3-47a0-b0ea-10ebbdbb67f5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-480, -680], "parameters": {"width": 580, "height": 280, "content": "## Important notes\n\nThis very simple workflow is ideal for eCommerce businesses or customer support teams looking to automate and streamline the handling of contact form submissions.\n\n- It is possible to hook any external form such as CF7 for Wordpress through a webhook\n- It is possible to send the email through other providers by replacing them with the relative nodes (Gmail, Outlook....)\n- It is possible to change the collection database with other tools"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "649d6a6a-a2a1-49f6-b63a-6def1a8831f1", "connections": {"OpenAI": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Gen. Dep.": {"main": [[{"node": "General DB", "type": "main", "index": 0}]]}, "Order Dep.": {"main": [[{"node": "Order DB", "type": "main", "index": 0}]]}, "Other Dep.": {"main": [[{"node": "Other DB", "type": "main", "index": 0}]]}, "Prod. Dep.": {"main": [[{"node": "Prod DB", "type": "main", "index": 0}]]}, "Quote Dep.": {"main": [[{"node": "Quote DB", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Quote Dep.", "type": "main", "index": 0}, {"node": "Prod. Dep.", "type": "main", "index": 0}], [], [{"node": "Gen. Dep.", "type": "main", "index": 0}], [{"node": "Order Dep.", "type": "main", "index": 0}], [{"node": "Other Dep.", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}}}