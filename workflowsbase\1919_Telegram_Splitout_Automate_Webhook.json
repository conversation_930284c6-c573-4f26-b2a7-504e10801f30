{"id": "mOcaSIUAvpt3QjQ1", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🌐 Confluence Page AI Powered Chatbot", "tags": [], "nodes": [{"id": "f4761e1a-6430-4b3d-97f9-b91743e02ea6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, -340], "parameters": {"color": 7, "width": 633, "height": 974, "content": "## Confluence\nhttps://developer.atlassian.com/cloud/confluence/basic-auth-for-rest-apis/\nhttps://id.atlassian.com/manage-profile/security/api-tokens\nhttps://developer.atlassian.com/cloud/confluence/rest/v2/intro/#about\n\nSupplying basic auth headers\nYou can construct and send basic auth headers yourself, including a base64-encoded string that contains your Atlassian account email and API token.\n\nTo use basic auth headers, perform the following steps:\n\nGenerate an API Token for your Atlassian Account: https://id.atlassian.com/manage/api-tokens\nBuild a string of <NAME_EMAIL>:your_user_api_token.\nYou'll need to encode your authorization credentials to Base64 encoded. You can do this locally:\nLinux/Unix/MacOS:\n\nCopy\n```\necho -n <EMAIL>:your_user_api_token | base64\n```\nWindows 7 and later, using Microsoft Powershell:\n\nCopy\n```\n$Text = ‘<EMAIL>:your_user_api_token’\n$Bytes = [System.Text.Encoding]::UTF8.GetBytes($Text)\n$EncodedText = [Convert]::ToBase64String($Bytes)\n$EncodedText\n```\nSupply an Authorization header with content Basic followed by the encoded string. Example: Authorization: Basic ********************************************************\n\nCopy\n```\ncurl -D- \\\n   -X GET \\\n   -H \"Authorization: Basic <your_encoded_string>\" \\\n   -H \"Content-Type: application/json\" \\\n   \"https://<your-domain.atlassian.net>/wiki/rest/api/space\"\n```\n\nThe above cURL command will not work as shown. You need to replace <your_encoded_string> and <your-domain.atlassian.net> with your authorization credentials encoded string and instance information before running it in the terminal."}, "typeVersion": 1}, {"id": "b2865684-687e-45a9-bb0c-e78df4dda72e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [760, -340], "parameters": {"color": 5, "width": 768.3946456283678, "height": 381.59428876752247, "content": "## Using Rest API to GET Confluence Page Body\nhttps://developer.atlassian.com/cloud/confluence/rest/v2/api-group-page/#api-pages-id-get\n\nRequest\nhttps://<your-wiki>.atlassian.net/wiki/api/v2/pages/{id}\nPath parameters\nid\ninteger\n\nRequired\nQuery parameters\n\nThe content format types to be returned in the body field of the response. \nIf available, the representation will be available under a response field of the same name under the body field.\n\nValid values: storage, atlas_doc_format, view, export_view, anonymous_export_view, styled_view, editor\n\n"}, "typeVersion": 1}, {"id": "2fae2b02-b15f-4226-86c2-f4444f10965e", "name": "Confluence Page Storage View", "type": "n8n-nodes-base.httpRequest", "position": [900, 580], "parameters": {"url": "=https://example.atlassian.net/wiki/api/v2/pages/{{ $json.id }}", "options": {}, "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "body-format", "value": "storage"}]}, "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "KafuDlwiWOVNQcyC", "name": "Confluence API"}}, "typeVersion": 4.2}, {"id": "49c5c6f7-f879-4518-aeef-922154f47ea6", "name": "HTML to Markdown", "type": "n8n-nodes-base.markdown", "position": [1100, 580], "parameters": {"html": "={{ $json.body.storage.value }}", "options": {}}, "typeVersion": 1}, {"id": "6ef64460-1406-43c9-9c5b-9d8ae3f51d31", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1260, 760], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "b8f998da-34b2-40d4-9816-b7a3ca33a3d9", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [820, 180], "parameters": {}, "typeVersion": 1.1}, {"id": "8fcfb987-3ea1-43cd-804f-dc2d629e558e", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1400, 760], "parameters": {"sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "sessionIdType": "customKey"}, "typeVersion": 1.2}, {"id": "53fe680c-af07-4712-b3cd-ae853f19cf8a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [760, 420], "parameters": {"color": 6, "width": 1163, "height": 515, "content": "## Chatbot for Confluence Pages\n\n\n"}, "typeVersion": 1}, {"id": "f37546a9-1b33-4276-9ea3-e461b53fe70a", "name": "Chat Response", "type": "n8n-nodes-base.set", "position": [1700, 680], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "636ec5bb-141c-491b-b827-bf6c3753a531", "name": "output", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "c53f59bd-f0d9-4629-bf56-ca439ef9c7f5", "name": "Globals", "type": "n8n-nodes-base.set", "position": [1100, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "74683edb-6368-4673-95f3-2885e30595cf", "name": "page_id_tekla", "type": "string", "value": "688157"}, {"id": "3a8796d7-3426-4f4a-bddf-973720b59e9d", "name": "page_id_n8n", "type": "string", "value": "491546"}, {"id": "42b27698-8d11-4fb0-a308-e256e0752f4d", "name": "page_id_more_n8n", "type": "string", "value": "983041"}, {"id": "62572887-e17a-4957-9ab1-3546277f79ab", "name": "page_id_tekla_clash_checking", "type": "string", "value": "753691"}]}}, "typeVersion": 3.4}, {"id": "ee500c5b-9289-4636-8178-6235c0fe4080", "name": "Search By ID", "type": "n8n-nodes-base.httpRequest", "position": [1300, 180], "parameters": {"url": "=https://example.atlassian.net/wiki/rest/api/search?limit=1&cql=id={{ $json.page_id_n8n }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "KafuDlwiWOVNQcyC", "name": "Confluence API"}}, "typeVersion": 4.2}, {"id": "934f0c57-6184-4c85-a0dc-097b3c470be4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1020, 80], "parameters": {"width": 872, "height": 297, "content": "## Confluence Search By ID"}, "typeVersion": 1}, {"id": "c51b8421-962d-46a1-aaf5-1b170252b7da", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1700, 180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3e8b49af-f3c6-4441-842f-9ce9a42c34b6", "name": "content._links.webui", "type": "string", "value": "={{ $json.content._links.webui }}"}, {"id": "6fd53eb3-52b2-4f7b-92ca-89a26e05d52a", "name": "content._links.self", "type": "string", "value": "={{ $json.content._links.self }}"}, {"id": "dfc89cbb-2f63-41ca-acfb-27b4d36d0418", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "0e15af12-8ae2-4337-a174-f3c3592bd0c6", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "6bbfa6eb-d6db-42c4-9ef6-81611fda0365", "name": "excerpt", "type": "string", "value": "={{ $json.excerpt }}"}, {"id": "a5a26e42-af66-41a6-9406-7ccb86fb3386", "name": "id", "type": "string", "value": "={{ $json.content.id }}"}]}}, "typeVersion": 3.4}, {"id": "2c765cad-e488-44ad-98b6-6e0a2c575fd2", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1300, 580], "parameters": {"text": "=Answer questions from user with the context provided.  Only respond using the context.  If you do not know the answer simply respond with \"I don't know.\"\n\nUser question: {{ $('When chat message received').item.json.chatInput }}\n\nContext: {{ $json.data }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "a89508f9-fd88-4a9f-84da-a0ddef590e1b", "name": "Send Telegram Message", "type": "n8n-nodes-base.telegram", "position": [1700, 480], "webhookId": "3ba1ee6d-1648-4421-823b-e68ae14d769b", "parameters": {"text": "={{ $json.output}}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "dae8ae00-1552-4945-948e-2556dfdd8802", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1500, 180], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d57c434b-ed09-484a-bcc4-d81681001a36", "connections": {"Globals": {"main": [[{"node": "Search By ID", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Chat Response", "type": "main", "index": 0}, {"node": "Send Telegram Message", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Page Schema": {"main": [[{"node": "Confluence Page Storage View", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Search By ID": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "HTML to Markdown": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}, "Confluence Page Storage View": {"main": [[{"node": "HTML to Markdown", "type": "main", "index": 0}]]}}}