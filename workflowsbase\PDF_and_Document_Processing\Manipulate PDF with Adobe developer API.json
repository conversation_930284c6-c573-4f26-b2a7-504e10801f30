{"meta": {"instanceId": "cd478e616d2616186f4f92b70cfe0c2ed95b5b209f749f2b873b38bdc56c47c9"}, "nodes": [{"id": "f4b1bdd8-654d-4643-a004-ff1b2f32b5ae", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [580, 1100], "parameters": {}, "typeVersion": 1}, {"id": "d6b1c410-81c3-486d-bdcb-86a4c6f7bf9e", "name": "Create Asset", "type": "n8n-nodes-base.httpRequest", "position": [1940, 580], "parameters": {"url": "https://pdf-services.adobe.io/assets", "method": "POST", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "mediaType", "value": "application/pdf"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.access_token }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "PU8GmSwXswwM1Fzq", "name": "Adobe API calls"}}, "typeVersion": 4.1}, {"id": "9e900a45-d792-4dc5-938c-0d5cdfd2e647", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1140, 440], "parameters": {}, "typeVersion": 1}, {"id": "859f369d-f36f-4c3f-a50d-a17214fef2a3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, 140], "parameters": {"color": 5, "width": 667.6107231291055, "height": 715.2927406867177, "content": "# Adobe API Wrapper\n\nSee Adobe documentation:\n- https://developer.adobe.com/document-services/docs/overview/pdf-services-api/howtos/\n- https://developer.adobe.com/document-services/docs/overview/pdf-extract-api/gettingstarted/\n\nIn short, this workflow does the following steps :\n\n- Authentication\n- Upload an asset (pdf) to adobe\n- Wait for the asset to be processed by Adobe\n- Download the result\n\n## Credential\n\nCredentials are not \"predefined\" and you'll have to create 2 custom credentials, detailed in the workflow.\n\n## Result\n\nThe result will depend on the transformation requested. It could be 1 of various files (json, zip...) accessible via download URL returned by the workflow.\n\nWorkflow can be tested with a PDF filed fetched with Dorpbox for example or any storage provider. "}, "typeVersion": 1}, {"id": "450199c5-e588-486d-81cf-eb69cf729ab1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [560, 900], "parameters": {"width": 857.2064431277577, "height": 463.937514110429, "content": "## Testing for development"}, "typeVersion": 1}, {"id": "311a75d6-4fbe-4d8f-89b3-d4b0ee21f7ae", "name": "Adobe API Query", "type": "n8n-nodes-base.set", "position": [900, 1000], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "62bb6466-acf4-41e5-9444-c9ef608a6822", "name": "endpoint", "type": "string", "value": "extractpdf"}, {"id": "0352f585-1434-4ab7-a704-a1e187fffa96", "name": "json_payload", "type": "object", "value": "={{ \n{\n \"renditionsToExtract\": [\n \"tables\"\n ],\n \"elementsToExtract\": [\n \"text\",\n \"tables\"\n ]\n }\n}}"}]}}, "typeVersion": 3.4}, {"id": "abf20778-db50-4787-a5f4-7af5d5c76efe", "name": "Load a test pdf file", "type": "n8n-nodes-base.dropbox", "position": [900, 1180], "parameters": {"path": "/valerian/w/prod/_freelance/ADEZIF/AI/Source data/Brochures pour GPT/Brochure 3M/3M_doc_emballage VERSION FINALE.pdf", "operation": "download", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "9", "name": "Dropbox account"}}, "typeVersion": 1}, {"id": "8bb2ae0c-df61-4110-af44-b1040b4340a2", "name": "Query + File", "type": "n8n-nodes-base.merge", "position": [1180, 1080], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "92afa6d6-daf8-4358-8c95-36473b810dc2", "name": "Query + File + Asset information", "type": "n8n-nodes-base.merge", "position": [2180, 580], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "5d88b8e4-0b0a-463a-88db-c45d5e87e823", "name": "Process Query", "type": "n8n-nodes-base.httpRequest", "position": [2640, 580], "parameters": {"url": "=https://pdf-services.adobe.io/operation/{{ $('Query + File + Asset information').item.json.endpoint }}", "method": "POST", "options": {"redirect": {"redirect": {}}, "response": {"response": {"fullResponse": true}}}, "jsonBody": "={{ \n{\n...{ \"assetID\":$('Query + File + Asset information').first().json.assetID },\n...$('Query + File + Asset information').first().json.json_payload\n}\n}}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Authenticartion (get token)').first().json[\"access_token\"] }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "PU8GmSwXswwM1Fzq", "name": "Adobe API calls"}}, "typeVersion": 4.1}, {"id": "47278b2f-dd04-4609-90ab-52f34b9a0e72", "name": "Wait 5 second", "type": "n8n-nodes-base.wait", "position": [2860, 580], "webhookId": "ed00a9a8-d599-4a98-86f8-a15176352c0a", "parameters": {"unit": "seconds", "amount": 5}, "typeVersion": 1}, {"id": "691b52ae-132a-4105-b1e4-bb7d55d0e347", "name": "Try to download the result", "type": "n8n-nodes-base.httpRequest", "position": [3080, 580], "parameters": {"url": "={{ $('Process Query').item.json[\"headers\"][\"location\"] }}", "options": {}, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Authenticartion (get token)').first().json[\"access_token\"] }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "PU8GmSwXswwM1Fzq", "name": "Adobe API calls"}}, "typeVersion": 4.1}, {"id": "277dea14-de8d-4719-aff1-f4008d6d5c67", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [3260, 580], "parameters": {"rules": {"values": [{"outputKey": "in progress", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "in progress"}]}, "renameOutput": true}, {"outputKey": "failed", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6d6917f6-abb9-4175-a070-a2f500d9f34f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "failed"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}, {"id": "8f6f8273-43ed-4a44-bb27-6ce137000472", "name": "Forward response to origin workflow", "type": "n8n-nodes-base.set", "position": [3820, 600], "parameters": {"options": {}, "assignments": {"assignments": []}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "00e2d7e3-94cd-49e5-a975-2fdc1a7a95fd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2780, 480], "parameters": {"width": 741.3069226712129, "height": 336.57433650102917, "content": "## Wait for file do be processed"}, "typeVersion": 1}, {"id": "3667b1ba-b9a6-4e1a-94b1-61b37f1e7adc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1324.6733934850213, 147.59707015795897], "parameters": {"color": 5, "width": 402.63171535688423, "height": 700.9473619571734, "content": "### 1- Credential for token request\n\nCreate a \"Custom Auth\" credential like this :\n\n```\n{\n \"headers\": {\n \"Content-Type\":\"application/x-www-form-urlencoded\"\n }, \n \"body\" : {\n \"client_id\": \"****\", \n \"client_secret\":\"****\"\n }\n}\n```"}, "typeVersion": 1}, {"id": "718bb738-8ce4-4b38-94e4-6ccac1adf9ec", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1800, 152.6219700851708], "parameters": {"color": 5, "width": 1752.5923360342827, "height": 692.0175575715904, "content": "### 2- Credential for all other Queries\n\nCreate a \"Header Auth\" credential like this : \n\n```\nX-API-Key: **** (same value as client_id)\n```"}, "typeVersion": 1}, {"id": "d6bc8011-699d-4388-82f5-e5f90ba8672a", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [740, 140], "parameters": {"color": 5, "width": 529.7500231395039, "height": 718.8735380890446, "content": "## Workflow Input\n\n- endpoint: splitpdf, extractpdf, ...\n- json_payload : all endpoint payload except assetID which is handled in current workflow\n- **PDF Data as n8n Binary**\n\n\n### Example for **split** : \n\n```\n{\n \"endpoint\": \"splitpdf\",\n \"json_payload\": {\n \"splitoption\": \n { \"pageRanges\": [{\"start\": 1,\"end\": 2}]}\n }\n }\n}\n```\n\n### Example for **extractpdf**\n\n```\n{\n \"endpoint\": \"splitpdf\",\n \"json_payload\": {\n \"renditionsToExtract\": [\n \"tables\"\n ],\n \"elementsToExtract\": [\n \"text\",\n \"tables\"\n ]\n }\n}\n```"}, "typeVersion": 1}, {"id": "2bbf6d9d-8399-49ba-94ea-b90795ef44ba", "name": "Authenticartion (get token)", "type": "n8n-nodes-base.httpRequest", "position": [1500, 580], "parameters": {"url": "https://pdf-services.adobe.io/token", "method": "POST", "options": {}, "sendBody": true, "contentType": "form-urlencoded", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{}]}, "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "djeOoXpBafK4aiGX", "name": "Adobe API"}}, "typeVersion": 4.1}, {"id": "be4e87e8-6e56-408f-b932-320023382f98", "name": "Upload PDF File (asset)", "type": "n8n-nodes-base.httpRequest", "position": [2440, 580], "parameters": {"url": "={{ $json.uploadUri }}", "method": "PUT", "options": {"redirect": {"redirect": {}}}, "sendBody": true, "sendQuery": true, "contentType": "binaryData", "queryParameters": {"parameters": [{}]}, "inputDataFieldName": "data"}, "typeVersion": 4.1}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "Wait 5 second", "type": "main", "index": 0}], [{"node": "Forward response to origin workflow", "type": "main", "index": 0}], [{"node": "Forward response to origin workflow", "type": "main", "index": 0}]]}, "Create Asset": {"main": [[{"node": "Query + File + Asset information", "type": "main", "index": 1}]]}, "Query + File": {"main": [[{"node": "Authenticartion (get token)", "type": "main", "index": 0}, {"node": "Query + File + Asset information", "type": "main", "index": 0}]]}, "Process Query": {"main": [[{"node": "Wait 5 second", "type": "main", "index": 0}]]}, "Wait 5 second": {"main": [[{"node": "Try to download the result", "type": "main", "index": 0}]]}, "Adobe API Query": {"main": [[{"node": "Query + File", "type": "main", "index": 0}]]}, "Load a test pdf file": {"main": [[{"node": "Query + File", "type": "main", "index": 1}]]}, "Upload PDF File (asset)": {"main": [[{"node": "Process Query", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Authenticartion (get token)", "type": "main", "index": 0}, {"node": "Query + File + Asset information", "type": "main", "index": 0}]]}, "Try to download the result": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Authenticartion (get token)": {"main": [[{"node": "Create Asset", "type": "main", "index": 0}]]}, "Query + File + Asset information": {"main": [[{"node": "Upload PDF File (asset)", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Load a test pdf file", "type": "main", "index": 0}, {"node": "Adobe API Query", "type": "main", "index": 0}]]}}}