{"id": "Jy1RMuri0WJO5aO4", "meta": {"instanceId": "c4e0aa659a8ba8396fb6bfa469d1eafbfbfff96c330631376e31cb897259826e", "templateCredsSetupCompleted": true}, "name": "Summarize Google Drive Documents with Mistral AI and Send via Gmail", "tags": [{"id": "USkRpjRpntFcI8VH", "name": "working", "createdAt": "2025-03-09T00:24:01.723Z", "updatedAt": "2025-03-09T00:24:01.723Z"}], "nodes": [{"id": "680f9002-94fa-48c1-af5f-d2a5305b6291", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "3fa4ad1a-ce87-44db-b016-bd172c2318eb", "name": "Mistral Cloud Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "position": [500, 240], "parameters": {"options": {}}, "credentials": {"mistralCloudApi": {"id": "temjibUluGywOSoS", "name": "Mistral Cloud account"}}, "typeVersion": 1}, {"id": "124d62ae-3b46-4e75-a04e-155849fe280d", "name": "Download uploaded File from Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [220, 0], "parameters": {"fileId": {"__rl": true, "mode": "list", "value": "1d0njBA2ZM0zYyJOEbUeFwQmHSYIO7IM2", "cachedResultUrl": "https://drive.google.com/file/d/1d0njBA2ZM0zYyJOEbUeFwQmHSYIO7IM2/view?usp=drivesdk", "cachedResultName": "Goods and Services Receipt(WCC).pdf"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "7xFbFgdSc78zERPk", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "69b7621b-a273-4b0a-be61-4d45bf87618d", "name": "Summarization Chain to summarize a file", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [480, 0], "parameters": {"options": {"binaryDataKey": "data"}, "chunkSize": 800, "chunkOverlap": 0, "operationMode": "nodeInputBinary"}, "typeVersion": 2}, {"id": "573194cd-5f37-422f-b3fa-957187ac3538", "name": "Send Summarized text to Gmail", "type": "n8n-nodes-base.gmail", "position": [840, 0], "webhookId": "215c6c67-612c-4b8d-9849-0b796570003d", "parameters": {"sendTo": "<EMAIL>", "message": "=<h1 style=\"color: #4CAF50;\">📌 Quick Summary of Your Document! ✨</h1>\n<p>\n<h2>📝 Summary:</h2>\n<p>\n{{ $json['response']['text'].replace(\"\\n\", \"<br>\") }}\n<p>\n\n<h3>📅 Date Processed: </h3>\n{{ new Date().toLocaleString('en-GB', { timeZone: 'Africa/Lagos' }) }}\n\n\n\n\n\n    ", "options": {"senderName": "Swot.AI", "appendAttribution": false}, "subject": "Here is Your Summarized Response"}, "credentials": {"gmailOAuth2": {"id": "G3K9RkKiyLHtyVzi", "name": "Gmail account"}}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "8446e524-8468-4515-8778-be94db41d3e3", "connections": {"Mistral Cloud Chat Model": {"ai_languageModel": [[{"node": "Summarization Chain to summarize a file", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download uploaded File from Google Drive", "type": "main", "index": 0}]]}, "Summarization Chain to summarize a file": {"main": [[{"node": "Send Summarized text to Gmail", "type": "main", "index": 0}]]}, "Download uploaded File from Google Drive": {"main": [[{"node": "Summarization Chain to summarize a file", "type": "main", "index": 0}]]}}}