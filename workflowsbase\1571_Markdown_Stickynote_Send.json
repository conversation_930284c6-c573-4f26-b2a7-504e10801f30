{"id": "Nvn78tMRNnKji7Fg", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Very simple Human in the loop system email with AI e IMAP", "tags": [], "nodes": [{"id": "271bb16f-9b62-41d9-ab76-114cd7ba915a", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [-1300, 1340], "parameters": {"options": {}}, "credentials": {"imap": {"id": "k31W9oGddl9pMDy4", "name": "IMAP <EMAIL>"}}, "typeVersion": 2}, {"id": "42d150d8-d574-49f9-9c0e-71a2cdea3b79", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [-1040, 1340], "parameters": {"html": "={{ $json.textHtml }}", "options": {}}, "typeVersion": 1}, {"id": "e9498a60-0078-4581-b269-7ff552f4047a", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [920, 1320], "webhookId": "a79ae1b4-648c-4cb4-b6cd-04ea3c1d9314", "parameters": {"html": "={{ $('Set Email text').item.json.email }}", "options": {}, "subject": "=Re: {{ $('<PERSON><PERSON>gger (IMAP)').item.json.subject }}", "toEmail": "={{ $('<PERSON><PERSON> (IMAP)').item.json.from }}", "fromEmail": "={{ $('<PERSON><PERSON> (IMAP)').item.json.to }}"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "ab9f6ac3-2095-44df-aeba-2eab96ecf425", "name": "Email Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [-780, 1340], "parameters": {"options": {"binaryDataKey": "={{ $json.data }}", "summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following in max 100 words:\n\n\"{{ $json.data }}\"\n\nDo not enter the total number of words used.", "combineMapPrompt": "=Write a concise summary of the following in max 100 words:\n\n\"{{ $json.data }}\"\n\nDo not enter the total number of words used."}}}, "operationMode": "nodeInputBinary"}, "typeVersion": 2}, {"id": "86b7c3d0-e1f2-4e2f-b293-8042700d6816", "name": "Write email", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-340, 1340], "parameters": {"text": "=Write the text to reply to the following email:\n\n{{ $json.response.text }}", "options": {"systemMessage": "You are an expert at answering emails. You need to answer them professionally based on the information you have. This is a business email. Be concise and never exceed 100 words. Only the body of the email, not create the subject"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "5d5a397f-f9c3-4691-afd0-9a6102679eac", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-400, 1560], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5b36a295-fda6-4174-9078-0a8ec57620d2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-800, 1260], "parameters": {"width": 320, "height": 240, "content": "Chain that summarizes the received email"}, "typeVersion": 1}, {"id": "7110fe1f-0099-49aa-9095-96e733aa468f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-360, 1260], "parameters": {"width": 340, "height": 240, "content": "Agent that retrieves business information from a vector database and processes the response"}, "typeVersion": 1}, {"id": "e2bdbd64-3c37-4867-ae2c-0f6937d82b81", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1100, 1260], "parameters": {"height": 240, "content": "Convert email to Markdown format for better understanding of LLM models"}, "typeVersion": 1}, {"id": "8ae5d216-5897-4c33-800a-27ff939b174a", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [620, 1300], "parameters": {"height": 180, "content": "If the feedback is OK send email"}, "typeVersion": 1}, {"id": "4cfce63c-5931-45c5-99ca-eb85dca962b5", "name": "Approve Email", "type": "n8n-nodes-base.emailSend", "position": [380, 1340], "webhookId": "4f9f06e7-9b2b-4896-9b51-245972341d12", "parameters": {"message": "=<h3>MESSAGE</h3>\n{{ $('<PERSON><PERSON> (IMAP)').item.json.textHtml }}\n\n<h3>AI RESPONSE</h3>\n{{ $json.email }}", "options": {}, "subject": "=[Approval Required]  {{ $('<PERSON><PERSON> (IMAP)').item.json.subject }}", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>", "operation": "sendAndWait"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "d6c8acd2-ebc1-4aaa-bfcc-cdb18fcc8715", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-820, 1560], "parameters": {"model": {"__rl": true, "mode": "list", "value": "deepseek-chat", "cachedResultName": "deepseek-chat"}, "options": {}}, "credentials": {"openAiApi": {"id": "97Cz4cqyiy1RdcQL", "name": "DeepSeek"}}, "typeVersion": 1.2}, {"id": "33bbedeb-129a-4e99-ab5a-9e0ec4456156", "name": "Set Email text", "type": "n8n-nodes-base.set", "position": [100, 1340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "35d7c303-42f4-4dd1-b41e-6eb087c23c3d", "name": "email", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "2293e0e6-4f2a-4622-a610-64b65f34e1e5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [320, 1300], "parameters": {"height": 180, "content": "Human in the loop"}, "typeVersion": 1}, {"id": "510196ec-adaf-4e6c-aac0-8ca8b754438a", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1100, 940], "parameters": {"color": 3, "width": 540, "height": 260, "content": "# How it works\nThis workflow automates the handling of incoming emails, summarizes their content, generates appropriate responses and validate it through send IMAP email with \"Human in the loop\" system. \n\nYou can quickly integrate Gmail and Outlook via the appropriate nodes"}, "typeVersion": 1}, {"id": "c4c9157d-4d05-47a1-a5eb-63865e838d39", "name": "Approved?", "type": "n8n-nodes-base.if", "position": [680, 1340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "62e26bc5-1732-4699-a602-99490c7406fd", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.data.approved }}", "rightValue": ""}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "47e79286-00f4-48e8-a0d1-e0f56d9ba0d5", "connections": {"OpenAI": {"ai_languageModel": [[{"node": "Write email", "type": "ai_languageModel", "index": 0}]]}, "Markdown": {"main": [[{"node": "Email Summarization Chain", "type": "main", "index": 0}]]}, "Approved?": {"main": [[{"node": "Send Email", "type": "main", "index": 0}], []]}, "Write email": {"main": [[{"node": "Set Email text", "type": "main", "index": 0}]]}, "Approve Email": {"main": [[{"node": "Approved?", "type": "main", "index": 0}]]}, "Set Email text": {"main": [[{"node": "Approve Email", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Email Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "Email Trigger (IMAP)": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Email Summarization Chain": {"main": [[{"node": "Write email", "type": "main", "index": 0}]]}}}