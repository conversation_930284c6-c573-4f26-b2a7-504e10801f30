{"id": "Tygtx1aZi9pLdtUo", "meta": {"instanceId": "8418cffce8d48086ec0a73fd90aca708aa07591f2fefa6034d87fe12a09de26e", "templateCredsSetupCompleted": true}, "name": "Fully automated Video Captions generation with json2video", "tags": [], "nodes": [{"id": "38e862a1-dc25-4a41-b0e1-5ebba1032e0a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-980, -280], "parameters": {}, "typeVersion": 1}, {"id": "834ac32d-4bef-4087-87af-590cd200a858", "name": "json2video - Add Captions", "type": "n8n-nodes-base.httpRequest", "position": [-540, -280], "parameters": {"url": "https://api.json2video.com/v2/movies", "method": "POST", "options": {}, "jsonBody": "={\n  \"id\": \"qbaasr7s\",\n  \"resolution\": \"custom\",\n  \"quality\": \"high\",\n\"scenes\": [\n    {\n      \"id\": \"qyjh9lwj\",\n      \"comment\": \"Scene 1\",\n      \"elements\": []\n    }\n  ],\n  \"elements\": [\n    {\n      \"id\": \"q6dznzcv\",\n      \"type\": \"video\",\n      \"src\": \"{{ $json.video_url }}\"\n    },\n    {\n      \"id\": \"q41n9kxp\",\n      \"type\": \"subtitles\",\n      \"settings\": {\n        \"style\": \"classic-progressive\",\n        \"font-family\": \"Oswald\",\n        \"font-size\": 140,\n        \"word-color\": \"#FCF5C9\",\n        \"shadow-color\": \"#260B1B\",\n        \"line-color\": \"#F1E7F4\",\n        \"shadow-offset\": 2,\n        \"box-color\": \"#260B1B\"\n      },\n      \"language\": \"en\"\n    }\n  ],\n  \"width\": {{ $json.width }},\n  \"height\": {{ $json.height }}\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "FVrj0WeCT9IosZhh", "name": "json2video"}, "httpHeaderAuth": {"id": "TngzgS09J1YvLIXl", "name": "Perplexity"}}, "typeVersion": 4.2}, {"id": "93e98e02-a7e5-40d2-93a8-06c1ba3c4fb5", "name": "Config", "type": "n8n-nodes-base.set", "position": [-780, -280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "408b70d1-30ea-4f88-847d-97c59e467168", "name": "video_url", "type": "string", "value": "https://aiatelier.s3.eu-west-1.amazonaws.com/workflows-material/json2video/captions-sample.mp4"}, {"id": "e54d0b14-3261-4d8c-83ac-b63a37981257", "name": "width", "type": "string", "value": "1080"}, {"id": "70a87f6b-8cf1-48b0-96bf-b7a8aa5bc6da", "name": "height", "type": "string", "value": "1920"}]}}, "typeVersion": 3.4}, {"id": "d3b6d3f3-d3ca-455d-929c-ffb869bd23d8", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [-180, -220], "webhookId": "f50b5765-4a91-415d-ba27-cfda281dc941", "parameters": {"amount": 10}, "typeVersion": 1.1}, {"id": "07099d4c-6012-4447-8720-af8e75521e24", "name": "<PERSON> Error", "type": "n8n-nodes-base.if", "position": [180, -240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a9813eb6-0dbf-41ac-837f-8f2760cbc5e3", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.movie.status }}", "rightValue": "error"}]}}, "typeVersion": 2.2}, {"id": "a94a6b24-4674-42ac-8db4-6e9298b44b7d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.noOp", "position": [420, -380], "parameters": {}, "typeVersion": 1}, {"id": "cd6bba4e-b329-4476-b983-248bb8e4423a", "name": "Output", "type": "n8n-nodes-base.set", "position": [460, 20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c7ce3d37-6455-407a-bf57-286d91c16f97", "name": "url", "type": "string", "value": "={{ $json.movie.url }}"}, {"id": "e969f3bd-2c36-43f6-9fc3-a66a0424ec20", "name": "duration", "type": "number", "value": "={{ $json.movie.duration }}"}, {"id": "a5f9b903-40c0-432e-b030-5a1fdea844db", "name": "size", "type": "number", "value": "={{ $json.movie.size }}"}, {"id": "660565f1-8da7-4c2f-a5e0-b62130aef7cb", "name": "width", "type": "number", "value": "={{ $json.movie.width }}"}, {"id": "5e2a9144-45e5-40f2-b71e-d74b25890ab6", "name": "height", "type": "number", "value": "={{ $json.movie.height }}"}, {"id": "601f8514-61f5-4cea-9b64-373881e3c879", "name": "rendering_time", "type": "number", "value": "={{ $json.movie.rendering_time }}"}, {"id": "2b7812f9-1e44-4843-b2ca-051b54153051", "name": "project", "type": "string", "value": "={{ $json.movie.project }}"}, {"id": "1b562ac3-e62b-4d67-adab-2af0d15fd11e", "name": "remaining_quota", "type": "number", "value": "={{ $json.remaining_quota.time }}"}]}}, "typeVersion": 3.4}, {"id": "378e027a-b033-4490-93e6-666d3d7def86", "name": "json2video - Get Status", "type": "n8n-nodes-base.httpRequest", "position": [0, -180], "parameters": {"url": "=https://api.json2video.com/v2/movies?id={{ $('json2video - Add Captions').first().json.project }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth"}, "credentials": {"httpCustomAuth": {"id": "FVrj0WeCT9IosZhh", "name": "json2video"}, "httpHeaderAuth": {"id": "TngzgS09J1YvLIXl", "name": "Perplexity"}}, "typeVersion": 4.2}, {"id": "a818a3a6-4cef-4043-ac3e-96fa3f54373d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, -300], "parameters": {"color": 7, "width": 640, "height": 580, "content": "## Check video status"}, "typeVersion": 1}, {"id": "7258a9ec-591f-4b07-840c-3171c36f193e", "name": "is Completed", "type": "n8n-nodes-base.if", "position": [200, 40], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2643b070-cbb2-4562-9269-a61389e0c242", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.movie.status }}", "rightValue": "done"}]}}, "typeVersion": 2.2}, {"id": "cbce69e0-730c-46ea-bd0a-b8694bd7780d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1700, -480], "parameters": {"width": 640, "height": 820, "content": "# Automatically Generate Captions for Your Videos with json2video\n\nThis workflow automatically adds captions to your videos using [json2video](https://json2video.com/?afco=manu), a powerful service for video automation, that integrates seamlessly with n8n.\n\n# [👉🏻 Try json2video for free 👈🏻](https://json2video.com/?afco=manu)\n\n## Setup\n\n### Step 1: Create a json2video Account & API Key\n1. Sign up for a [json2video account](https://json2video.com/?afco=manu).\n2. Once registered, you will receive your API key via email.\n\n### Step 2: Create n8n Credentials\n1. In n8n, create new credentials and select **\"Custom Auth\"** as the type.\n2. Paste the following JSON code into the credentials configuration, replacing `\"your-json2video-api-key\"` with your actual API key:\n\n    ```json\n    {\n      \"headers\": {\n        \"x-api-key\": \"your-json2video-api-key\"\n      }\n    }\n    ```\n\n### Step 3: Connect Your Credentials\n1. In your n8n workflow, locate the two HTTP nodes that interact with json2video.\n2. Select the credentials you created in Step 2 for both nodes.\n"}, "typeVersion": 1}, {"id": "4ce3a85f-3abc-48e9-8840-f37f32490b62", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-760, -120], "parameters": {"width": 440, "height": 200, "content": "# ☝️ Provide Video Details\n\nFor the workflow to add captions, please provide:\n\n- **URL:** The link to your video.\n- **Width & Height:** The dimensions of your video"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5d8108e2-3f44-4585-9c25-f31f95f06424", "connections": {"Wait": {"main": [[{"node": "json2video - Get Status", "type": "main", "index": 0}]]}, "Config": {"main": [[{"node": "json2video - Add Captions", "type": "main", "index": 0}]]}, "Is Error": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "is Completed", "type": "main", "index": 0}]]}, "is Completed": {"main": [[{"node": "Output", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "json2video - Get Status": {"main": [[{"node": "<PERSON> Error", "type": "main", "index": 0}]]}, "json2video - Add Captions": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}}}