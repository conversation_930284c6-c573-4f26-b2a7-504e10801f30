{"nodes": [{"id": "4dd52c72-9a9b-4db4-8de5-5b12b1e5c4be", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [180, 1480], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "9226181c-b84c-4ea1-a5b4-eedb6c62037b", "name": "Search daily", "type": "n8n-nodes-base.airtable", "position": [440, 1480], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "filterByFormula": "AND({Status} = 'active', {Interval} = 'daily')"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "1a3b6224-2f66-41c6-8b3d-be286cf16370", "name": "Search weekly", "type": "n8n-nodes-base.airtable", "position": [440, 1660], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "filterByFormula": "=AND(\n {Status} = 'active', \n {Interval} = 'weekly', \n {Last Sent} <= DATEADD(TODAY(), -7, 'days')\n)"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "1ea47e14-0a28-4780-95c7-31e24eb724d5", "name": "confirmation email1", "type": "n8n-nodes-base.gmail", "position": [620, 820], "webhookId": "dd8bd6df-2013-4f8d-a2cc-cd9b3913e3d2", "parameters": {"sendTo": "={{ $('Subscribe Form').item.json.email }}", "message": "=This is to confirm your request to subscribe to \"Learn something every day!\" - a free service to send you facts about your favourite topics.\n\nTopic: {{ $('Subscribe Form').item.json.topic }}\nSchedule: {{ $('Subscribe Form').item.json.frequency }}", "options": {"appendAttribution": false}, "subject": "Learn something every day confirmation"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "d95262af-1b52-4f9c-8346-183b4eee8544", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [1140, 1480], "parameters": {"mode": "each", "options": {"waitForSubWorkflow": false}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}}, "typeVersion": 1.1}, {"id": "075292af-7a66-4275-ac2d-3c392189a10c", "name": "Create Event", "type": "n8n-nodes-base.set", "position": [980, 1480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b28a0142-a028-471a-8180-9883e930feea", "name": "email", "type": "string", "value": "={{ $json.Email }}"}, {"id": "970f5495-05df-42b6-a422-b2ac27f8eb95", "name": "topic", "type": "string", "value": "={{ $json.Topic }}"}, {"id": "e871c431-948f-4b80-aa17-1e4266674663", "name": "interval", "type": "string", "value": "={{ $json.Interval }}"}, {"id": "9b72597d-1446-4ef3-86e5-0a071c69155b", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "b17039c2-14a2-4811-9528-88ae963e44f7", "name": "created_at", "type": "string", "value": "={{ $json.Created }}"}]}}, "typeVersion": 3.4}, {"id": "28776aaf-6bd9-4f9f-bcf0-3d4401a74219", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1360, 1480], "parameters": {}, "typeVersion": 1}, {"id": "0eb62e75-228b-452b-80ab-f9ef3ad33204", "name": "Unsubscribe Form", "type": "n8n-nodes-base.formTrigger", "position": [180, 1160], "webhookId": "e64db96d-5e61-40d5-88fb-761621a829ab", "parameters": {"options": {"path": "free-factoids-unsubscribe"}, "formTitle": "Unsubscribe from Learn Something Every Day", "formFields": {"values": [{"fieldLabel": "ID", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Reason For Unsubscribe", "multiselect": true, "fieldOptions": {"values": [{"option": "Emails not relevant"}, {"option": "Too many Emails"}, {"option": "I did not sign up to this service"}]}}]}, "formDescription": "We're sorry to see you go! Please take a moment to help us improve the service."}, "typeVersion": 2.2}, {"id": "f889efe9-dc3c-428b-ad8e-4f7d17f23e75", "name": "Set Email <PERSON>ars", "type": "n8n-nodes-base.set", "position": [2500, 1480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "62a684fb-16f9-4326-8eeb-777d604b305a", "name": "to", "type": "string", "value": "={{ $('Execute Workflow Trigger').first().json.email }},<EMAIL>"}, {"id": "4270849e-c805-4580-9088-e8d1c3ef2fb4", "name": "subject", "type": "string", "value": "=Your {{ $('Execute Workflow Trigger').first().json.interval }} factoid"}, {"id": "81d0e897-2496-4a3c-b16c-9319338f899f", "name": "message", "type": "string", "value": "=<p>\n<strong>You asked about \"{{ $('Execution Data').first().json.topic.replace('\"','') }}\"</strong>\n</p>\n<p>\n<i>{{ $('Content Generation Agent').first().json.output }}</i>\n</p>"}, {"id": "ee05de7b-5342-4deb-8118-edaf235d92cc", "name": "unsubscribe_link", "type": "string", "value": "=https://<MY_HOST>/form/inspiration-unsubscribe?ID={{ $('Execute Workflow Trigger').first().json.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "84741e6d-f5be-440d-8633-4eb30ccce170", "name": "Log Last Sent", "type": "n8n-nodes-base.airtable", "position": [2860, 1480], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "columns": {"value": {"id": "={{ $('Execute Workflow Trigger').first().json.id }}", "Last Sent": "2024-11-29T13:34:11"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "inactive", "value": "inactive"}, {"name": "active", "value": "active"}], "removed": true, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Interval", "type": "options", "display": true, "options": [{"name": "daily", "value": "daily"}, {"name": "weekly", "value": "weekly"}, {"name": "surprise", "value": "surprise"}], "removed": true, "readOnly": false, "required": false, "displayName": "Interval", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Start Day", "type": "options", "display": true, "options": [{"name": "Mon", "value": "Mon"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Wed", "value": "Wed"}, {"name": "<PERSON>hu", "value": "<PERSON>hu"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Sat", "value": "Sat"}, {"name": "Sun", "value": "Sun"}], "removed": true, "readOnly": false, "required": false, "displayName": "Start Day", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Topic", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Topic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Sent", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Last Sent", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "88f864d6-13fb-4f09-b22d-030d016678e1", "name": "Search surprise", "type": "n8n-nodes-base.airtable", "position": [440, 1840], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "options": {}, "operation": "search", "filterByFormula": "=AND(\n {Status} = 'active', \n {Interval} = 'surprise'\n)"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "28238d9a-7bc0-4a22-bb4e-a7a2827e4da3", "name": "Should Send = True", "type": "n8n-nodes-base.filter", "position": [800, 1840], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9aaf9ae2-8f96-443a-8294-c04270296b22", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.should_send }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "3a46dd3d-48a6-40ca-8823-0516aa9f73a4", "name": "Should Send?", "type": "n8n-nodes-base.code", "position": [620, 1840], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const luckyPick = Math.floor(Math.random() * 10) + 1;\n$input.item.json.should_send = luckyPick == 8;\nreturn $input.item;"}, "typeVersion": 2}, {"id": "3611da19-920b-48e6-84a4-f7be0b3a78fc", "name": "Create Subscriber", "type": "n8n-nodes-base.airtable", "position": [440, 820], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "columns": {"value": {"Email": "={{ $json.email }}", "Topic": "={{ $json.topic }}", "Status": "active", "Interval": "={{ $json.frequency }}", "Start Day": "={{ $json.submittedAt.toDateTime().format('EEE') }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "inactive", "value": "inactive"}, {"name": "active", "value": "active"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Interval", "type": "options", "display": true, "options": [{"name": "daily", "value": "daily"}, {"name": "weekly", "value": "weekly"}, {"name": "surprise", "value": "surprise"}], "removed": false, "readOnly": false, "required": false, "displayName": "Interval", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Start Day", "type": "options", "display": true, "options": [{"name": "Mon", "value": "Mon"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Wed", "value": "Wed"}, {"name": "<PERSON>hu", "value": "<PERSON>hu"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Sat", "value": "Sat"}, {"name": "Sun", "value": "Sun"}], "removed": false, "readOnly": false, "required": false, "displayName": "Start Day", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Topic", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Topic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Sent", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Last Sent", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "2213a81f-53a9-4142-9586-e87b88710eec", "name": "Update Subscriber", "type": "n8n-nodes-base.airtable", "position": [440, 1160], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appL3dptT6ZTSzY9v", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v", "cachedResultName": "Scheduled Emails"}, "table": {"__rl": true, "mode": "list", "value": "tblzR9vSuFUzlQNMI", "cachedResultUrl": "https://airtable.com/appL3dptT6ZTSzY9v/tblzR9vSuFUzlQNMI", "cachedResultName": "Table 1"}, "columns": {"value": {"id": "={{ $json.ID }}", "Status": "inactive"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "inactive", "value": "inactive"}, {"name": "active", "value": "active"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Interval", "type": "options", "display": true, "options": [{"name": "daily", "value": "daily"}, {"name": "weekly", "value": "weekly"}], "removed": true, "readOnly": false, "required": false, "displayName": "Interval", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Start Day", "type": "options", "display": true, "options": [{"name": "Mon", "value": "Mon"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Wed", "value": "Wed"}, {"name": "<PERSON>hu", "value": "<PERSON>hu"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Sat", "value": "Sat"}, {"name": "Sun", "value": "Sun"}], "removed": true, "readOnly": false, "required": false, "displayName": "Start Day", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Topic", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Topic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "c94ec18b-e0cf-4859-8b89-23abdd63739c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, 1280], "parameters": {"color": 7, "width": 335, "height": 173, "content": "### 4. Using Subworkflows to run executions concurrently\nThis configuration is desired when sequential execution is slow and unnecessary. Also if one email fails, it doesn't fail the execution for everyone else."}, "typeVersion": 1}, {"id": "c14cab28-13eb-4d91-8578-8187a95a8909", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, 700], "parameters": {"color": 7, "width": 380, "height": 80, "content": "### 1. Subscribe flow\nUse a form to allow users to subscribe to the service."}, "typeVersion": 1}, {"id": "0e44ada0-f8a7-440e-aded-33b446190a08", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, 1020], "parameters": {"color": 7, "width": 355, "height": 115, "content": "### 2. Unsubscribe flow\n* Uses Form's pre-fill field feature to identify user\n* Doesn't use \"email\" as identifier so you can't unsubscribe others"}, "typeVersion": 1}, {"id": "e67bdffe-ccfc-4818-990d-b2a5ab613035", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [180, 1340], "parameters": {"color": 7, "width": 347, "height": 114, "content": "### 3. Scheduled Trigger\n* Runs every day at 9am\n* Handles all 3 frequency types\n* Send emails concurrently"}, "typeVersion": 1}, {"id": "ce7d5310-7170-46d3-b8d8-3f97407f9dfd", "name": "Subscribe Form", "type": "n8n-nodes-base.formTrigger", "position": [180, 820], "webhookId": "c6abe3e3-ba87-4124-a227-84e253581b58", "parameters": {"options": {"path": "free-factoids-subscribe", "appendAttribution": false, "respondWithOptions": {"values": {"formSubmittedText": "Thanks! Your factoid is on its way!"}}}, "formTitle": "Learn something every day!", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "topic", "placeholder": "What topic(s) would you like to learn about?", "requiredField": true}, {"fieldType": "email", "fieldLabel": "email", "placeholder": "eg. <EMAIL>", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "frequency", "fieldOptions": {"values": [{"option": "daily"}, {"option": "weekly"}, {"option": "surprise me"}]}, "requiredField": true}]}, "formDescription": "Get a fact a day (or week) about any subject sent to your inbox."}, "typeVersion": 2.2}, {"id": "a5d50886-7d6b-4bf8-b376-b23c12a60608", "name": "Execution Data", "type": "n8n-nodes-base.executionData", "position": [1560, 1480], "parameters": {"dataToSave": {"values": [{"key": "email", "value": "={{ $json.email }}"}]}}, "typeVersion": 1}, {"id": "69b40d8d-7734-47f1-89fe-9ea0378424b7", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1860, 1680], "parameters": {"sessionKey": "=scheduled_send_{{ $json.email }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "f83cff18-f41f-4a63-9d43-7e3947aae386", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [2020, 1680], "parameters": {}, "typeVersion": 1}, {"id": "77457037-e3ab-42f1-948b-b994d42f2f6e", "name": "Content Generation Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1780, 1460], "parameters": {"text": "=Generate an new factoid on the following topic: \"{{ $json.topic.replace('\"','') }}\"\nEnsure it is unique and not one generated previously.", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "cdfdd870-48b6-4c7d-a7d1-a22d70423e37", "name": "<PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "position": [1720, 1680], "parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "credentials": {"groqApi": {"id": "02xZ4o87lUMUFmbT", "name": "Groq account"}}, "typeVersion": 1}, {"id": "87df322d-a544-476f-b2ff-83feb619fe7f", "name": "Generate Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2120, 1460], "parameters": {"prompt": "=Generate a child-friendly illustration which compliments the following paragraph:\n{{ $json.output }}", "options": {}, "resource": "image"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "5c8d9e72-4015-44da-b5d5-829864d33672", "name": "Resize Image", "type": "n8n-nodes-base.editImage", "position": [2280, 1460], "parameters": {"width": 480, "height": 360, "options": {}, "operation": "resize"}, "typeVersion": 1}, {"id": "a9939fad-98b3-4894-aae0-c11fa40d09da", "name": "Send Message", "type": "n8n-nodes-base.gmail", "position": [2680, 1480], "webhookId": "dd8bd6df-2013-4f8d-a2cc-cd9b3913e3d2", "parameters": {"sendTo": "={{ $json.to }}", "message": "=<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n <meta charset=\"UTF-8\">\n <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <title>{{ $json.subject }}</title>\n</head>\n<body>\n {{ $json.message }}\n<p>\n<a href=\"{{ $json.unsubscribe_link }}\">Unsubscribe</a>\n</p>\n</body>\n</html>\n", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}, "appendAttribution": false}, "subject": "={{ $json.subject }}"}, "credentials": {"gmailOAuth2": {"id": "Sf5Gfl9NiFTNXFWb", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "10b6ad35-fc1c-47a2-b234-5de3557d1164", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1320, 1660], "parameters": {"color": 7, "width": 335, "height": 113, "content": "### 5. Use Execution Data to Filter Logs\nIf you've registered for community+ or are on n8n cloud, best practice is to use execution node to allow filtering of execution logs."}, "typeVersion": 1}, {"id": "e3563fae-ff35-457b-9fb1-784eda637518", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1780, 1280], "parameters": {"color": 7, "width": 340, "height": 140, "content": "### 6. Use AI to Generate Factoid and Image\nUse an AI agent to automate the generation of factoids as requested by the user. This is a simple example but we recommend a adding a unique touch to stand out from the crowd!"}, "typeVersion": 1}, {"id": "d1016c5d-c855-44c5-8ad3-a534bedaa8cf", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2500, 1040], "parameters": {"color": 7, "width": 460, "height": 400, "content": "### 7. Send Email to User\nFinally, send a message to the user with both text and image.\nLog the event in the Airtable for later analysis if required.\n\n![Screenshot of email result](https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/dbpctdhohj3vlewy6oyc)"}, "typeVersion": 1}, {"id": "773075fa-e5a2-4d4f-8527-eb07c7038b00", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-420, 680], "parameters": {"width": 480, "height": 900, "content": "## Try It Out!\n\n### This n8n templates demonstrates how to build a simple subscriber service entirely in n8n using n8n forms as a frontend, n8n generally as the backend and Airtable as the storage layer.\n\nThis template in particular shows a fully automated service to send automated messages containing facts about a topic the user requested for.\n\n### How it works\n* An n8n form is setup up to allow users to subscribe with a desired topic and interval of which to recieve messages via n8n forms which is then added to the Airtable.\n* A scheduled trigger is executed every morning and searches for subscribers to send messages for based on their desired intervals.\n* Once found, Subscribers are sent to a subworkflow which performs the text content generation via an AI agent and also uses a vision model to generate an image.\n* Both are attached to an email which is sent to the subscriber. This email also includes an unsubscribe link.\n* The unsubscribe flow works similarly via n8n form interface which when submitted disables further scheduled emails to the user.\n\n## How to use\n* Make a copy of sample Airtable here: https://airtable.com/appL3dptT6ZTSzY9v/shrLukHafy5bwDRfD\n* Make sure the workflow is \"activated\" and the forms are available and reachable by your audience.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Wikipedia": {"ai_tool": [[{"node": "Content Generation Agent", "type": "ai_tool", "index": 0}]]}, "Create Event": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Resize Image": {"main": [[{"node": "Set Email <PERSON>ars", "type": "main", "index": 0}]]}, "Search daily": {"main": [[{"node": "Create Event", "type": "main", "index": 0}]]}, "Send Message": {"main": [[{"node": "Log Last Sent", "type": "main", "index": 0}]]}, "Should Send?": {"main": [[{"node": "Should Send = True", "type": "main", "index": 0}]]}, "Search weekly": {"main": [[{"node": "Create Event", "type": "main", "index": 0}]]}, "Execution Data": {"main": [[{"node": "Content Generation Agent", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Resize Image", "type": "main", "index": 0}]]}, "Set Email Vars": {"main": [[{"node": "Send Message", "type": "main", "index": 0}]]}, "Subscribe Form": {"main": [[{"node": "Create Subscriber", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "Content Generation Agent", "type": "ai_languageModel", "index": 0}]]}, "Search surprise": {"main": [[{"node": "Should Send?", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Search surprise", "type": "main", "index": 0}, {"node": "Search daily", "type": "main", "index": 0}, {"node": "Search weekly", "type": "main", "index": 0}]]}, "Unsubscribe Form": {"main": [[{"node": "Update Subscriber", "type": "main", "index": 0}]]}, "Create Subscriber": {"main": [[{"node": "confirmation email1", "type": "main", "index": 0}]]}, "Should Send = True": {"main": [[{"node": "Create Event", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Content Generation Agent", "type": "ai_memory", "index": 0}]]}, "Content Generation Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Execution Data", "type": "main", "index": 0}]]}}}