{"id": "ra8MrqshnzXPy55O", "meta": {"instanceId": "3378b0d68c3b7ebfc71b79896d94e1a044dec38e99a1160aed4e9c323910fbe2"}, "name": "upload-post images", "tags": [], "nodes": [{"id": "7d899b35-ae00-418a-b890-e318f6d61f7a", "name": "POST TO INSTAGRAM1", "type": "n8n-nodes-base.httpRequest", "position": [820, -220], "parameters": {"url": "https://api.upload-post.com/api/upload_photos", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "title", "value": "title-ig"}, {"name": "user", "value": "user_name"}, {"name": "platform[]", "value": "instagram"}, {"name": "photos[]", "parameterType": "formBinaryData", "inputDataFieldName": "=photo1"}, {"name": "photos[]", "parameterType": "formBinaryData", "inputDataFieldName": "photo2"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Apikey api"}]}}, "typeVersion": 4.2}, {"id": "025c1aa3-acf2-4211-93e1-9df2182bbf07", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-840, -360], "parameters": {"color": 6, "width": 1880, "height": 660, "content": "# POST : to Instagram"}, "typeVersion": 1}, {"id": "7a98a200-3c96-45f8-a4d2-860c74d81c1f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [220, -120], "parameters": {}, "typeVersion": 3}, {"id": "d7bd532e-b07a-43f8-9ceb-c4dad685734d", "name": "Change name to photo1", "type": "n8n-nodes-base.code", "position": [-100, -220], "parameters": {"jsCode": "return items.map((item, index) => {\n  // Grab the existing binary buffer under \"data\"\n  const buffer = item.binary.data;\n  // Build a new item with the renamed binary key\n  return {\n    json: item.json,\n    binary: {\n      // Rename to photo1, photo2, ...\n      [`photo${index + 1}`]: buffer\n    }\n  };\n});\n"}, "typeVersion": 2}, {"id": "f5efe3ce-c8b9-445a-8667-fefc3dc36545", "name": "Change name to photo2", "type": "n8n-nodes-base.code", "position": [-100, -20], "parameters": {"jsCode": "return items.map((item, index) => {\n  // Grab the existing binary buffer under \"data\"\n  const buffer = item.binary.data;\n  // Build a new item with the renamed binary key\n  return {\n    json: item.json,\n    binary: {\n      // Rename to photo1, photo2, ...\n      [`photo${index + 2}`]: buffer\n    }\n  };\n});\n"}, "typeVersion": 2}, {"id": "4901b1f3-12e7-4f7d-b87a-5582d2319237", "name": "Send as 1 merged file", "type": "n8n-nodes-base.code", "position": [520, -120], "parameters": {"jsCode": "// Merge all incoming items (each with one binary photoX) into one item\nconst mergedItem = {\n  json: {},\n  binary: {}\n};\n\nfor (const item of items) {\n  // Copy every binary field from each item into mergedItem.binary\n  for (const [key, bin] of Object.entries(item.binary || {})) {\n    mergedItem.binary[key] = bin;\n  }\n}\n\n// Return a single-item array\nreturn [mergedItem];\n"}, "typeVersion": 2}, {"id": "34a88bd7-6302-4f22-aec0-d4318<PERSON><PERSON><PERSON>a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-760, -120], "parameters": {}, "typeVersion": 1}, {"id": "e710233a-e408-4718-9d1d-3a373fad33b8", "name": "POST TO TIKTOK", "type": "n8n-nodes-base.httpRequest", "position": [820, -20], "parameters": {"url": "https://api.upload-post.com/api/upload_photos", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "title", "value": "title-ig"}, {"name": "user", "value": "user_name"}, {"name": "platform[]", "value": "tiktok"}, {"name": "photos[]", "parameterType": "formBinaryData", "inputDataFieldName": "=photo1"}, {"name": "photos[]", "parameterType": "formBinaryData", "inputDataFieldName": "photo2"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Apikey api"}]}}, "typeVersion": 4.2}, {"id": "000f92e8-64df-4ebd-a608-d5b0d2e1a5c4", "name": "Get Image 1", "type": "n8n-nodes-base.httpRequest", "position": [-420, -220], "parameters": {"url": "https://upload.wikimedia.org/wikipedia/commons/7/70/Example.png", "options": {}}, "typeVersion": 4.2}, {"id": "f15f5cd5-9ca5-4ab7-bc66-32f7a3ec1e0c", "name": "Get Image 2", "type": "n8n-nodes-base.httpRequest", "position": [-420, -20], "parameters": {"url": "https://upload.wikimedia.org/wikipedia/commons/7/70/Example.png", "options": {}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d79c90a0-bb65-45b1-9d1b-c6af98f8480b", "connections": {"Merge": {"main": [[{"node": "Send as 1 merged file", "type": "main", "index": 0}]]}, "Get Image 1": {"main": [[{"node": "Change name to photo1", "type": "main", "index": 0}]]}, "Get Image 2": {"main": [[{"node": "Change name to photo2", "type": "main", "index": 0}]]}, "Change name to photo1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Change name to photo2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Send as 1 merged file": {"main": [[{"node": "POST TO INSTAGRAM1", "type": "main", "index": 0}, {"node": "POST TO TIKTOK", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Image 1", "type": "main", "index": 0}, {"node": "Get Image 2", "type": "main", "index": 0}]]}}}