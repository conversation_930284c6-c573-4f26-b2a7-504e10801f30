{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "d61d8ff3-532a-4b0d-a5a7-e02d2e79ddce", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2660, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "b6d5c1cf-b4a1-4901-b001-0c375747ee63", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [1660, 520], "parameters": {}, "typeVersion": 1}, {"id": "f4e08e32-bb96-4b5d-852e-26ad6fec3c8c", "name": "Add to Messages Stack", "type": "n8n-nodes-base.redis", "position": [1340, 200], "parameters": {"list": "=chat-buffer:{{ $json.From }}", "tail": true, "operation": "push", "messageData": "={{ $json.Body }}"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account"}}, "typeVersion": 1}, {"id": "181ae99e-ebe7-4e99-b5a5-999acc249621", "name": "Should Continue?", "type": "n8n-nodes-base.if", "position": [1660, 360], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ec39573f-f92a-4fe4-a832-0a137de8e7d0", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Get Latest Message Stack').item.json.messages.last() }}", "rightValue": "={{ $('<PERSON><PERSON><PERSON>').item.json.Body }}"}]}}, "typeVersion": 2}, {"id": "640c63ca-2798-48a9-8484-b834c1a36301", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [2780, 480], "parameters": {"sessionKey": "=chat-debouncer:{{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "sessionIdType": "customKey"}, "typeVersion": 1.2}, {"id": "123c35c5-f7b2-4b4d-b220-0e5273e25115", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilioTrigger", "position": [940, 360], "webhookId": "0ca3da0e-e4e1-4e94-8380-06207bf9b429", "parameters": {"updates": ["com.twilio.messaging.inbound-message.received"]}, "credentials": {"twilioApi": {"id": "TJv4H4lXxPCLZT50", "name": "Twilio account"}}, "typeVersion": 1}, {"id": "f4e86455-7f4d-4401-8f61-a859be1433a9", "name": "Get Latest Message Stack", "type": "n8n-nodes-base.redis", "position": [1500, 360], "parameters": {"key": "=chat-buffer:{{ $json.From }}", "keyType": "list", "options": {}, "operation": "get", "propertyName": "messages"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "02f8e7f5-12b4-4a5a-9ce9-5f0558e447aa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1232.************, -50.***************], "parameters": {"color": 7, "width": 632.*************, "height": 766.*************, "content": "## Step 2. Buffer Incoming Messages\n[Learn more about using Redis](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.redis)\n\n* New messages are captured into a list.\n* After X seconds, we get a fresh copy of this list\n* If the last message on the list is the same as the incoming message, then we know no new follow-on messages were sent within the last 5 seconds. Hence the user should be waiting and it is safe to reply.\n* But if the reverse is true, then we will abort the execution here."}, "typeVersion": 1}, {"id": "311c0d69-a735-4435-91b6-e80bf7d4c012", "name": "Send Reply", "type": "n8n-nodes-base.twilio", "position": [3000, 320], "parameters": {"to": "={{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "from": "={{ $('<PERSON><PERSON><PERSON>').item.json.To }}", "message": "={{ $json.output }}", "options": {}}, "credentials": {"twilioApi": {"id": "TJv4H4lXxPCLZT50", "name": "Twilio account"}}, "typeVersion": 1}, {"id": "c0e0cd08-66e3-4ca3-9441-8436c0d9e664", "name": "Wait 5 seconds", "type": "n8n-nodes-base.wait", "position": [1340, 360], "webhookId": "d486979c-8074-4ecb-958e-fcb24455086b", "parameters": {}, "typeVersion": 1.1}, {"id": "c7959fa2-69a5-46b4-8e67-1ef824860f4e", "name": "Get Chat History", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [2000, 280], "parameters": {"options": {"groupMessages": true}}, "typeVersion": 1.1}, {"id": "55933c54-5546-4770-8b36-a31496163528", "name": "Window Buffer Memory1", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [2000, 420], "parameters": {"sessionKey": "=chat-debouncer:{{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "sessionIdType": "customKey"}, "typeVersion": 1.2}, {"id": "459c0181-d239-4eec-88b6-c9603868d518", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [774.*************, 198.**************], "parameters": {"color": 7, "width": 431.*************, "height": 357.49804533541777, "content": "## Step 1. Listen for Twilio Messages\n[Read more about <PERSON><PERSON><PERSON> Trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.twiliotrigger)\n\nIn this example, we'll use the sender's phone number as the session ID. This will be important in retrieving chat history."}, "typeVersion": 1}, {"id": "e06313a9-066a-4387-a36c-a6c6ff57d6f9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1900, 80], "parameters": {"color": 7, "width": 618.970917763344, "height": 501.77420646931444, "content": "## Step 3. Get Messages Since Last Reply\n[Read more about using Chat Memory](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.memorymanager)\n\nOnce conditions are met and we allow the agent to reply, we'll need to find the bot's last reply and work out the buffer of user messages since then. We can do this by looking using chat memory and comparing this to the latest message in our redis messages stack."}, "typeVersion": 1}, {"id": "601a71f6-c6f8-4b73-98c7-cfa11b1facaa", "name": "Get Messages Buffer", "type": "n8n-nodes-base.set", "position": [2320, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "01434acb-c224-46d2-99b0-7a81a2bb50c5", "name": "messages", "type": "string", "value": "={{\n$('Get Latest Message Stack').item.json.messages\n .slice(\n $('Get Latest Message Stack').item.json.messages.lastIndexOf(\n $('Get Chat History').item.json.messages.last().human\n || $('Twi<PERSON> Trigger').item.json.chatInput\n ),\n $('Get Latest Message Stack').item.json.messages.length\n )\n .join('\\n')\n}}"}]}}, "typeVersion": 3.4}, {"id": "9e49f2de-89e6-4152-8e9c-ed47c5fc4654", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2549, 120], "parameters": {"color": 7, "width": 670.2274698011594, "height": 522.5993538768389, "content": "## Step 4. Send Single Agent Reply For Many Messages\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nFinally, our buffered messages are sent to the AI Agent that can formulate a single response for all. This could potentially improve the conversation experience if the chat interaction is naturally more rapid and spontaneous. A drawback however is that responses could be feel much slower - tweak the wait threshold to suit your needs!"}, "typeVersion": 1}, {"id": "be13c74a-467c-4ab1-acca-44878c68dba4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 80], "parameters": {"width": 375.55385425077225, "height": 486.69228315530853, "content": "## Try It Out!\n### This workflow demonstrates a simple approach to stagger an AI Agent's reply if users often send in a sequence of partial messages and in short bursts.\n\n* Twilio webhook receives user's messages which are recorded in a message stack powered by Redis.\n* The execution is immediately paused for 5 seconds and then another check is done against the message stack for the latest message.\n* The purpose of this check lets use know if the user is sending more messages or if they are waiting for a reply.\n* The execution is aborted if the latest message on the stack differs from the incoming message and continues if they are the same.\n* For the latter, the agent receives buffered messages and is able to respond to all in a single reply."}, "typeVersion": 1}, {"id": "334d38e1-ec16-46f2-a57d-bf531adb8d3d", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2660, 320], "parameters": {"text": "={{ $json.messages }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.6}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "Send Reply", "type": "main", "index": 0}]]}, "Twilio Trigger": {"main": [[{"node": "Add to Messages Stack", "type": "main", "index": 0}, {"node": "Wait 5 seconds", "type": "main", "index": 0}]]}, "Wait 5 seconds": {"main": [[{"node": "Get Latest Message Stack", "type": "main", "index": 0}]]}, "Get Chat History": {"main": [[{"node": "Get Messages Buffer", "type": "main", "index": 0}]]}, "Should Continue?": {"main": [[{"node": "Get Chat History", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Messages Buffer": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Window Buffer Memory1": {"ai_memory": [[{"node": "Get Chat History", "type": "ai_memory", "index": 0}]]}, "Get Latest Message Stack": {"main": [[{"node": "Should Continue?", "type": "main", "index": 0}]]}}}