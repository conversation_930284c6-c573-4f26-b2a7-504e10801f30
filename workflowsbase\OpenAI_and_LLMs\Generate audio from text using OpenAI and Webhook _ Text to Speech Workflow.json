{"id": "OVSyGmI6YFviPu8Q", "meta": {"instanceId": "fb261afc5089eae952e09babdadd9983000b3d863639802f6ded8c5be2e40067", "templateCredsSetupCompleted": true}, "name": "Generate audio from text using OpenAI - text-to-speech Workflow", "tags": [], "nodes": [{"id": "c40966a4-1709-4998-ae95-b067ce3496c9", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1320, 200], "parameters": {"options": {}, "respondWith": "binary"}, "typeVersion": 1.1}, {"id": "c4e57bb6-79a4-4b26-a179-73e30d681521", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, -140], "parameters": {"width": 501.55, "height": 493.060000000001, "content": "This `Webhook` node triggers the workflow when it receives a POST request.\n\n### 1. Test Mode:\n* Use the test webhook URL\n* Click the `Test workflow` button on the canvas. (In test mode, the webhook only works for one call after you click this button)\n\n### 1. Production Mode:\n* The workflow must be active for a **Production URL** to run successfully.\n* You can activate the workflow using the toggle in the top-right of the editor.\n* Note that unlike test URL calls, production URL calls aren't shown on the canvas (only in the executions list)."}, "typeVersion": 1}, {"id": "1364a4b6-2651-4b38-b335-c36783a25f12", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [825, 60], "parameters": {"color": 4, "width": 388.**************, "height": 292.**************, "content": "### Configure the OpenAI node with your API key:\nIf you haven't connected your OpenAI credentials in n8n yet, log in to your OpenAI account to get your API Key. Then, open the OpenAI node, click `Create New Credentials` and connect with the **OpenAI API**.\n"}, "typeVersion": 1}, {"id": "ba755814-75e6-4e16-b3a6-50cf4fc06350", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [480, 200], "webhookId": "28feeb38-ef2d-4a2e-bd7c-25a524068e25", "parameters": {"path": "generate_audio", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "ac46df50-cb1f-484c-8edf-8131192ba464", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [960, 200], "parameters": {"input": "={{ $json.body.text_to_convert }}", "voice": "fable", "options": {}, "resource": "audio"}, "credentials": {"openAiApi": {"id": "2Cije3KX7OIVwn9B", "name": "n8n OpenAI"}}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "84f1b893-e1a3-40c3-83b0-7cd637b353c4", "connections": {"OpenAI": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}