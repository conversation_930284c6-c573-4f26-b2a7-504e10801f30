{"meta": {"instanceId": "84ba6d895254e080ac2b4916d987aa66b000f88d4d919a6b9c76848f9b8a7616"}, "nodes": [{"id": "ecb4bbc8-939a-4c6c-80b6-6f053d1d7745", "name": "Get the Image", "type": "n8n-nodes-base.telegramTrigger", "position": [1640, 880], "webhookId": "8404b32c-14bd-428e-88a6-560755f0f7ba", "parameters": {"updates": ["message"], "additionalFields": {"download": true}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "2fd523b7-5f89-4e53-9445-4336b51cad51", "name": "Send Content for the Analyzed image", "type": "n8n-nodes-base.telegram", "position": [2380, 760], "parameters": {"text": "={{ $json.content }}", "chatId": "={{ $('Get the Image').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "b77fe84f-7651-42aa-aa40-f903b10c8fb1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 360], "parameters": {"width": 1235.*************, "height": 1361.*************, "content": "# Automated Image Analysis and Response via Telegram\n\n## Example: @SubAlertMe_Bot\n\n## Summary:\nThe automated image analysis and response workflow using n8n is a sophisticated solution designed to streamline the process of analyzing images sent via Telegram and delivering insightful responses based on the analysis outcomes. This cutting-edge workflow employs a series of meticulously orchestrated nodes to ensure seamless automation and efficiency in image processing tasks.\n\n## Use Cases:\nThis advanced workflow caters to a myriad of scenarios where real-time image analysis and response mechanisms are paramount. The use cases include:\n- Providing immediate feedback on images shared within Telegram groups.\n- Enabling automated content moderation based on the analysis of image content.\n- Facilitating rapid categorization and tagging of images based on the results of the analysis.\n\n## Detailed Workflow Setup:\nTo effectively implement this workflow, users must adhere to a meticulous setup process, which includes:\n- Access to the versatile n8n platform, ensuring seamless workflow orchestration.\n- Integration of a Telegram account to facilitate image reception and communication.\n- Utilization of an OpenAI account for sophisticated image analysis capabilities.\n- Configuration of Telegram and OpenAI credentials within the n8n environment for seamless integration.\n- Proficiency in creating and interconnecting nodes within the n8n workflow for optimal functionality.\n\n## Detailed Node Description:\n1. **Get the Image (Telegram Trigger):**\n - Actively triggers upon receipt of an image via Telegram, ensuring prompt processing.\n - Extracts essential information from the received image message to initiate further actions.\n\n2. **Merge all fields To get data from trigger:**\n - Seamlessly amalgamates all relevant data fields extracted from the trigger node for comprehensive data consolidation.\n\n3. **Analyze Image (OpenAI):**\n - Harnesses the powerful capabilities of OpenAI services to conduct in-depth analysis of the received image.\n - Processes the image data in base64 format to derive meaningful insights from the visual content.\n\n4. **Aggregate all fields:**\n - Compiles and consolidates all data items for subsequent processing and analysis, ensuring comprehensive data aggregation.\n\n5. **Send Content for the Analyzed Image (Telegram):**\n - Transmits the analyzed content back to the Telegram chat interface for seamless communication.\n - Delivers the analyzed information in textual format, enhancing user understanding and interaction.\n\n6. **Switch Node:**\n - The Switch node is pivotal for decision-making based on predefined conditions within the workflow.\n - It evaluates incoming data to determine the existence or absence of specific elements, such as images in this context.\n - Utilizes a set of rules to assess the presence of image data in the message payload and distinguishes between cases where images are detected and when they are not.\n - This crucial node plays a pivotal role in directing the flow of the workflow based on the outcomes of its evaluations.\n\n\n\n## Conclusion:\nThe automation of image analysis processes through this sophisticated workflow not only enhances operational efficiency but also revolutionizes communication dynamics within Telegram interactions. By incorporating this advanced workflow solution, users can optimize their image analysis workflows, bolster communication efficacy, and unlock new levels of automation in image processing tasks.\n"}, "typeVersion": 1}, {"id": "7a588ccb-7a97-4776-82fd-c4f42640e8f7", "name": "Update Telegram Error Message", "type": "n8n-nodes-base.telegram", "position": [2380, 1000], "parameters": {"text": "Please Upload an Image ....", "chatId": "={{ $json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "k3RE6o9brmFRFE9p", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "0cd83b82-0a20-4bf6-82bc-24827a368b89", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [2180, 1000], "webhookId": "d4d6fc13-d8ad-42b6-b4dd-e922b5534282", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "a6d52335-72e7-4ce4-92e9-861b2806e9ae", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1620, 360], "parameters": {"color": 4, "width": 1139.*************, "height": 1359.*************, "content": ""}, "typeVersion": 1}, {"id": "0222b4f6-a7c1-4183-8df8-b47b9e0cd685", "name": "Analyze image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2180, 760], "parameters": {"options": {}, "resource": "image", "inputType": "base64", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "kDo5LhPmHS2WQE0b", "name": "OpenAi account"}}, "typeVersion": 1.3}, {"id": "f83c7dc2-a986-40e7-831c-b7968866ef4e", "name": "Switch ( image or not )", "type": "n8n-nodes-base.switch", "position": [1820, 880], "parameters": {"rules": {"values": [{"outputKey": "Image", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "array", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.photo }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "Empty", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3fe3a96d-6ee9-4f12-a32c-f5f5b729e257", "operator": {"type": "array", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.message.photo }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "Update Telegram Error Message", "type": "main", "index": 0}]]}, "Analyze image": {"main": [[{"node": "Send Content for the Analyzed image", "type": "main", "index": 0}]]}, "Get the Image": {"main": [[{"node": "Switch ( image or not )", "type": "main", "index": 0}]]}, "Switch ( image or not )": {"main": [[{"node": "Analyze image", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}}}