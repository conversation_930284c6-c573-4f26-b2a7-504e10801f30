{"meta": {"instanceId": "f4f5d195bb2162a0972f737368404b18be694648d365d6c6771d7b4909d28167"}, "nodes": [{"id": "b165115d-5505-4e03-bf41-c21320cb8b09", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [80, 40], "parameters": {"color": 7, "width": 681.8337349708484, "height": 843.1482165886073, "content": "## Workflow: Text automations using Apple Shortcuts\n\n**Overview**\n- This workflow answers user requests sent via Apple Shortcuts\n- Several Shortcuts call the same webhook, with a query and a type of query\n- Types of query are:\n - translate to english\n - translate to spanish\n - correct grammar (without changing the actual content)\n - make content shorter\n - make content longer\n\n\n**How it works**\n- Select a text you are writing\n- Launch the shortcut\n- The text is sent to the webhook\n- Depending on the type of request, a different prompt is used\n- Each request is sent to an OpenAI node\n- The workflow responds to the request with the response from GPT\n- Shortcut replace the selected text with the new one\n\n**How to use it**\n- Activate the workflow\n- Download [this Shortcut template](https://drive.usercontent.google.com/u/0/uc?id=16zs5iJX7KeX_4e0SoV49_KfbU7-EF0NE&export=download)\n- Install the shortcut\n- In step 2 of the shortcut, change the url of the Webhook\n- In Shortcut details, \"add Keyboard Shortcut\" with the key you want to use to launch the shortcut\n- Go to settings, advanced, check \"Allow running scripts\"\n- You are ready to use the shortcut. Select a text and hit the keyboard shortcut you just defined\n\n\n**Notes**\n- If you use rich formatting, you'll have to test multiple ways to replace characters in the output. For example, you might use `{{ $json.message.content.output.replaceAll('\\n', \"<br/>\") }}` in the \"Respond to Shortcut\" node depending on the app you use most.\n- This is a basic example that you can extend and modify at your will\n- You can duplicate and modify the example shortcut based on your need, as well as making new automations in this workflow."}, "typeVersion": 1}, {"id": "c45400b8-d3b8-47f7-81c6-d791bce4c266", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1020, 380], "parameters": {"rules": {"values": [{"outputKey": "spanish", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "spanish"}]}, "renameOutput": true}, {"outputKey": "english", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bedb302f-646c-4dcd-8246-1fcfecfe3f2e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "english"}]}, "renameOutput": true}, {"outputKey": "grammar", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "94e6cf7d-576d-4ad9-85b0-c6b945eb41b7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "grammar"}]}, "renameOutput": true}, {"outputKey": "shorter", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1ed0d1e1-2df0-4f8d-b102-4004a25919ed", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "shorter"}]}, "renameOutput": true}, {"outputKey": "longer", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4756df03-7e7c-4e28-9b37-14684326b083", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.type }}", "rightValue": "longer"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "48e0e58e-6293-4e11-a488-ca9943b53484", "name": "Respond to Shortcut", "type": "n8n-nodes-base.respondToWebhook", "position": [1840, 400], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.message.content.output.replaceAll('\\n', '<br/>') }}"}, "typeVersion": 1.1}, {"id": "2655b782-9538-416c-ae65-35f8c77889c7", "name": "<PERSON>hook from Shortcut", "type": "n8n-nodes-base.webhook", "position": [840, 400], "webhookId": "e4ddadd2-a127-4690-98ca-e9ee75c1bdd6", "parameters": {"path": "shortcut-global-as", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "880ed4a2-0756-4943-a51f-368678e22273", "name": "OpenAI - <PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 540], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Summarize this content a little bit (5% shorter)\nOutput a JSON with a single field: output"}, {"content": "={{ $json.body.content }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "WqzqjezKh8VtxdqA", "name": "OpenAi account - Baptiste"}}, "typeVersion": 1.4}, {"id": "c6c6d988-7aab-4677-af1f-880d05691ec3", "name": "OpenAI - <PERSON>", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 680], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Make this content a little longer (5% longer)\nOutput a JSON with a single field: output"}, {"content": "={{ $json.body.content }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "WqzqjezKh8VtxdqA", "name": "OpenAi account - Baptiste"}}, "typeVersion": 1.4}, {"id": "8e6de4b7-22c3-45c9-a8d7-d498cf829b6f", "name": "OpenAI - Correct Grammar", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 400], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Correct grammar only, don't change the actual contents.\nOutput a JSON with a single field: output"}, {"content": "={{ $json.body.content }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "WqzqjezKh8VtxdqA", "name": "OpenAi account - Baptiste"}}, "typeVersion": 1.4}, {"id": "bc006b36-5a96-4c3a-9a28-2778a6c49f10", "name": "OpenAI - To Spanish", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 120], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Translate this message to Spanish.\nOutput a JSON with a single field: output"}, {"content": "={{ $json.body.content }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "WqzqjezKh8VtxdqA", "name": "OpenAi account - Baptiste"}}, "typeVersion": 1.4}, {"id": "330d2e40-1e52-4517-94e0-ce96226697fa", "name": "OpenAI - To English", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 260], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"role": "system", "content": "Translate this message to English.\nOutput a JSON with a single field: output"}, {"content": "={{ $json.body.content }}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "WqzqjezKh8VtxdqA", "name": "OpenAi account - Baptiste"}}, "typeVersion": 1.4}, {"id": "925e4b55-ac26-4c16-941f-66d17b6794ab", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [80, 900], "parameters": {"color": 7, "width": 469.**************, "height": 341.**************, "content": "### Check these explanations [< 3 min]\n\n[![Check the explanations](https://cdn.loom.com/sessions/thumbnails/c5b657568af64bb1b50fa8e8a91c45d1-1db3990a618986c9-full-play.gif)](https://www.loom.com/share/c5b657568af64bb1b50fa8e8a91c45d1?sid=a406be73-55eb-4754-9f51-9ddf49b22d69)"}, "typeVersion": 1}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "OpenAI - To Spanish", "type": "main", "index": 0}], [{"node": "OpenAI - To English", "type": "main", "index": 0}], [{"node": "OpenAI - Correct Grammar", "type": "main", "index": 0}], [{"node": "OpenAI - <PERSON>", "type": "main", "index": 0}], [{"node": "OpenAI - <PERSON>", "type": "main", "index": 0}]]}, "OpenAI - To English": {"main": [[{"node": "Respond to Shortcut", "type": "main", "index": 0}]]}, "OpenAI - To Spanish": {"main": [[{"node": "Respond to Shortcut", "type": "main", "index": 0}]]}, "OpenAI - Make Longer": {"main": [[{"node": "Respond to Shortcut", "type": "main", "index": 0}]]}, "OpenAI - Make Shorter": {"main": [[{"node": "Respond to Shortcut", "type": "main", "index": 0}]]}, "Webhook from Shortcut": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenAI - Correct Grammar": {"main": [[{"node": "Respond to Shortcut", "type": "main", "index": 0}]]}}}