{"id": "XiwLd0JwGmDoY0mr", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Image-to-3D", "tags": [], "nodes": [{"id": "8cc77575-854f-4359-8faa-fc78b8c23b65", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-220, 400], "parameters": {}, "typeVersion": 1}, {"id": "0dc7e6b8-43b8-4b9a-aa7a-4a100598162f", "name": "Get status", "type": "n8n-nodes-base.httpRequest", "position": [840, 400], "parameters": {"url": "=https://queue.fal.run/fal-ai/trellis/requests/{{ $('Create 3D Image').item.json.request_id }}/status ", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "daOZafXpRXLtoLUV", "name": "Fal.run API"}}, "typeVersion": 4.2}, {"id": "7540df1c-35e2-4ac5-871d-4d8410217979", "name": "Wait 60 sec.", "type": "n8n-nodes-base.wait", "position": [660, 400], "webhookId": "e10e9912-38e7-4e1f-ad7e-52b1e6a65d79", "parameters": {"amount": 60}, "typeVersion": 1.1}, {"id": "44c4b506-2a14-40ca-a75f-7af86ef5a9af", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-220, 260], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "ca8b3bcd-3eb6-4723-b2ea-a973582d46af", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-220, -860], "parameters": {"color": 3, "width": 740, "height": 520, "content": "# Image-to-3D\n\n\nThis workflow allows users to convert a 2D image into a 3D model by integrating multiple AI and web services. The process begins with a user uploading or providing an image URL, which is then sent to a generative AI model capable of interpreting the content and generating a 3D representation in .glb format. The model is then stored and a download link is returned to the user.\n\n![image](https://i.postimg.cc/1Xd20z4R/3d.png)"}, "typeVersion": 1}, {"id": "2230e7a5-225d-4538-b091-a9fbeedb1323", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-220, -300], "parameters": {"width": 740, "height": 200, "content": "## STEP 1 - <PERSON><PERSON><PERSON><PERSON> SHEET\nCreate a [Google Sheet like this](https://docs.google.com/spreadsheets/d/1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY/edit?usp=sharing).\n\nPlease insert:\n- in the \"IMAGE MODEL\" column the basic image of the model to dress\n\nLeave the \"3D RESULT\" column unfilled. It will be inserted by the system once the image has been created"}, "typeVersion": 1}, {"id": "3aad3211-e6fc-4e4b-9c59-7dd82827a43b", "name": "Completed?", "type": "n8n-nodes-base.if", "position": [1020, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "383d112e-2cc6-4dd4-8985-f09ce0bd1781", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED"}]}}, "typeVersion": 2.2}, {"id": "6ad70838-dbf4-4cb1-9b61-4cf6e1fcdf6a", "name": "Update result", "type": "n8n-nodes-base.googleSheets", "position": [440, 780], "parameters": {"columns": {"value": {"row_number": "={{ $('Get new image').item.json.row_number }}", "IMAGE RESULT": "={{ $('Get Url 3D image').item.json.model_mesh.url }}"}, "schema": [{"id": "IMAGE MODEL", "type": "string", "display": true, "required": false, "displayName": "IMAGE MODEL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMAGE PRODUCT", "type": "string", "display": true, "required": false, "displayName": "IMAGE PRODUCT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PRODUCT ID", "type": "string", "display": true, "required": false, "displayName": "PRODUCT ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMAGE RESULT", "type": "string", "display": true, "required": false, "displayName": "IMAGE RESULT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11ebWJvwwXHgvQld9kxywKQUvIoBw6xMa0g0BuIqHDxE/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY/edit?usp=drivesdk", "cachedResultName": "Image to 3D"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "239b45b3-94cc-43a9-aa2e-2c85725f4cc0", "name": "Set data", "type": "n8n-nodes-base.set", "position": [220, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c713d31f-9abd-496a-ac79-e8e2efe60aa0", "name": "image", "type": "string", "value": "={{ $json['IMAGE'] }}"}]}}, "typeVersion": 3.4}, {"id": "70908a7d-72a5-4131-a82b-ed455a453fd5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-220, 120], "parameters": {"width": 740, "height": 100, "content": "## STEP 3 - <PERSON>IN FLOW\nStart the workflow manually or periodically by hooking the \"Schedule Trigger\" node. It is recommended to set it at 5 minute intervals."}, "typeVersion": 1}, {"id": "d81f8aa0-3302-4a26-9425-aeb2a87674e7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-220, -60], "parameters": {"width": 740, "height": 140, "content": "## STEP 2 - GET API KEY (YOURAPIKEY)\nCreate an account [here](https://fal.ai/) and obtain API KEY.\nIn the node \"Create Image\" set \"Header Auth\" and set:\n- Name: \"Authorization\"\n- Value: \"Key YOURAPIKEY\""}, "typeVersion": 1}, {"id": "484d029d-b88f-48bb-b487-e7a50b47eb7d", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [400, 340], "parameters": {"width": 180, "height": 200, "content": "Set API Key created in Step 2"}, "typeVersion": 1}, {"id": "7061d7c1-7da8-473c-98a3-57dc15def557", "name": "Get new image", "type": "n8n-nodes-base.googleSheets", "position": [0, 400], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupColumn": "3D RESULT"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C0Et6X3Zwr_6CxeNjhLpDwjAfIGeUvLGFawckKb0utY/edit?usp=drivesdk", "cachedResultName": "Image to 3D"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "edcdc4f0-4e7f-4fec-af9c-bbe8bf6bd8e6", "name": "Create 3D Image", "type": "n8n-nodes-base.httpRequest", "position": [440, 400], "parameters": {"url": "https://queue.fal.run/fal-ai/trellis", "method": "POST", "options": {}, "jsonBody": "={\n  \"image_url\": \"{{ $json.image }}\",\n  \"ss_guidance_strength\": 7.5,\n  \"ss_sampling_steps\": 12,\n  \"slat_guidance_strength\": 3,\n  \"slat_sampling_steps\": 12,\n  \"mesh_simplify\": 0.95,\n  \"texture_size\": 1024\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "daOZafXpRXLtoLUV", "name": "Fal.run API"}}, "typeVersion": 4.2}, {"id": "9ac6f843-090f-4c15-88e7-46ee494ed1b9", "name": "Get Url 3D image", "type": "n8n-nodes-base.httpRequest", "position": [-220, 780], "parameters": {"url": "=https://queue.fal.run/fal-ai/trellis/requests/{{ $json.request_id }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "daOZafXpRXLtoLUV", "name": "Fal.run API"}}, "typeVersion": 4.2}, {"id": "a02ac260-c88a-4c5a-9fc6-7230b95c462b", "name": "Get File 3D image", "type": "n8n-nodes-base.httpRequest", "position": [0, 780], "parameters": {"url": "={{ $json.model_mesh.url }}", "options": {}}, "typeVersion": 4.2}, {"id": "311be624-4707-4361-a58a-ee90ff42490c", "name": "Upload 3D Image", "type": "n8n-nodes-base.googleDrive", "position": [220, 780], "parameters": {"name": "={{ $now.format('yyyyLLddHHmmss') }}-{{ $('Get Url 3D image').item.json.model_mesh.file_name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1aHRwLWyrqfzoVC8HoB-YMrBvQ4tLC-NZ", "cachedResultUrl": "https://drive.google.com/drive/folders/1aHRwLWyrqfzoVC8HoB-YMrBvQ4tLC-NZ", "cachedResultName": "Fal.run"}}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2f0d3488-25ac-4332-a8e3-62d7b34b96ae", "connections": {"Set data": {"main": [[{"node": "Create 3D Image", "type": "main", "index": 0}]]}, "Completed?": {"main": [[{"node": "Get Url 3D image", "type": "main", "index": 0}], [{"node": "Wait 60 sec.", "type": "main", "index": 0}]]}, "Get status": {"main": [[{"node": "Completed?", "type": "main", "index": 0}]]}, "Wait 60 sec.": {"main": [[{"node": "Get status", "type": "main", "index": 0}]]}, "Get new image": {"main": [[{"node": "Set data", "type": "main", "index": 0}]]}, "Update result": {"main": [[]]}, "Create 3D Image": {"main": [[{"node": "Wait 60 sec.", "type": "main", "index": 0}]]}, "Upload 3D Image": {"main": [[{"node": "Update result", "type": "main", "index": 0}]]}, "Get Url 3D image": {"main": [[{"node": "Get File 3D image", "type": "main", "index": 0}]]}, "Get File 3D image": {"main": [[{"node": "Upload 3D Image", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get new image", "type": "main", "index": 0}]]}}}