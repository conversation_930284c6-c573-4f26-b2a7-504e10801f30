{"id": "aOP0D1cAqzGv7Xa8", "meta": {"instanceId": "0a5638e14e0c728ef975d18d109cfb41edae575e3d911724f4f1eccde06a729f"}, "name": "spy tool", "tags": [], "nodes": [{"id": "5690844d-5322-4c62-8c83-eb4d4dc9c481", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1400, 340], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "ZOKbogCxHnP2W0H5", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "3b1c034f-501b-423c-844f-9cb607fa91e6", "name": "Gmail", "type": "n8n-nodes-base.gmailTool", "position": [1580, 340], "webhookId": "6a510528-22e0-4140-b987-770bb7a138de", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $fromAI(\"change\", \"What relevant part has changed on the website?\") }}", "options": {"appendAttribution": false}, "subject": "=Relevant changes on {{ $('parse results').item.json.website_url }}", "emailType": "text", "descriptionType": "manual", "toolDescription": "=Use this tool if you need to send an email, but only if the terms in the instructions mentioned explicitly state so\n"}, "credentials": {"gmailOAuth2": {"id": "jtANm6k92Kl6ent1", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "4d448a02-4569-451e-8be5-59bfc48f36d8", "name": "parse results", "type": "n8n-nodes-base.code", "position": [1180, -160], "parameters": {"jsCode": "const parsedObject = JSON.parse($('convert message to website url & instruction').first().json.choices[0].message.content);\n\nreturn parsedObject"}, "typeVersion": 2}, {"id": "238298c4-5bba-4ac1-b3cc-ab5a28888560", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1420, -120], "parameters": {"width": 260, "height": 180, "content": "## Note: almost never works right away\nAdjust the prompts in the 'Tools agent' and 'Gmail' node as desired to steer the agent's behavior in the right direction"}, "typeVersion": 1}, {"id": "0d519c06-aa30-4a33-895f-9185936d27cf", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [480, 100], "parameters": {"width": 150, "height": 80, "content": "Connect your Firecrawl account"}, "typeVersion": 1}, {"id": "9e327bbe-0096-4a4d-aec2-2e4cae7d91bd", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1740, 80], "parameters": {"width": 150, "height": 80, "content": "Connect your own OpenAI account\n"}, "typeVersion": 1}, {"id": "30ce0e22-f536-462f-8f94-f3fd92ae036f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1660, 300], "parameters": {"width": 150, "height": 80, "content": "Connect your own Gmail account\n"}, "typeVersion": 1}, {"id": "bc003781-3d91-49b6-b6bb-b2970b39256a", "name": "convert message to website url & instruction", "type": "n8n-nodes-base.httpRequest", "position": [940, -160], "parameters": {"url": "https://api.openai.com/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"gpt-4o-2024-08-06\",\n  \"messages\": [\n              {\n        \"role\": \"user\",\n        \"content\": \"convert the following message to a website url (just the plain text url, NOT formatted or in markdown) and prompt to AI. Make the prompt as verbose as possible. Message: {{ $('New espionage assignment').first().json.assignment_instructions }}\"\n    }\n  ],\n  \"response_format\": {\n    \"type\": \"json_schema\",\n    \"json_schema\": {\n      \"name\": \"variable_extraction\",\n      \"schema\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"website_url\": { \"type\": \"string\" },\n          \"prompt\": { \"type\": \"string\" }\n        },\n        \"required\": [\"website_url\", \"prompt\"],\n        \"additionalProperties\": false\n      },\n      \"strict\": true\n    }\n  }\n}\n", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "ZOKbogCxHnP2W0H5", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "6a8c172d-ac39-4cb0-b601-39fc770695ed", "name": "New espionage assignment", "type": "n8n-nodes-base.formTrigger", "position": [700, -160], "webhookId": "7470334f-93e1-47af-9521-d3a232c38b13", "parameters": {"options": {}, "formTitle": "New espionage assignment", "formFields": {"values": [{"fieldLabel": "assignment_instructions"}]}}, "notesInFlow": false, "typeVersion": 2.2}, {"id": "c5c64e5c-88de-45e3-bb9b-4096e74a6e83", "name": "wait 1 day", "type": "n8n-nodes-base.wait", "position": [940, 80], "webhookId": "22e689e4-b93d-4c59-81e5-43c070833454", "parameters": {"unit": "days", "amount": 1}, "typeVersion": 1.1}, {"id": "62a278ff-ed00-4e54-a608-************", "name": "scrape page - 1", "type": "n8n-nodes-base.httpRequest", "position": [700, 80], "parameters": {"url": "https://api.firecrawl.dev/v1/scrape", "method": "POST", "options": {}, "jsonBody": "={\n  \"url\": \"{{ $('parse results').item.json.website_url }}\",\n  \"formats\": [\n    \"markdown\"\n  ],\n  \"onlyMainContent\": true,\n  \"waitFor\": 5000\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBasicAuth": {"id": "h2XRcXzLcEfvDVKb", "name": "Unnamed credential"}, "httpHeaderAuth": {"id": "FoyIka0WgFG4FPxA", "name": "Head<PERSON> Au<PERSON> account 2"}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "89c15d8f-7f8e-4391-b24a-07579964ca5c", "name": "scrape page - 2", "type": "n8n-nodes-base.httpRequest", "position": [1180, 80], "parameters": {"url": "https://api.firecrawl.dev/v1/scrape", "method": "POST", "options": {}, "jsonBody": "={\n  \"url\": \"{{ $('parse results').item.json.website_url }}\",\n  \"formats\": [\n    \"markdown\"\n  ],\n  \"onlyMainContent\": true,\n  \"waitFor\": 5000\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "FoyIka0WgFG4FPxA", "name": "Head<PERSON> Au<PERSON> account 2"}}, "retryOnFail": true, "typeVersion": 4.2}, {"id": "7b148c5b-d4ae-498a-b7ef-2ed4ecc0a665", "name": "send e-mail?", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1420, 80], "parameters": {"text": "={{ $('parse results').item.json.prompt }}\n\nNOTE: ONLY send an email if the situation meets the above condition. Otherwise, do NOT use the tool\n\nNOTE: this concerns differences between the \"old version page\" (scrape from yesterday) and \"new version page\" (scrape from now)", "options": {"systemMessage": "=old version page: \\n\\n {{ JSON.stringify($('scrape page - 1').item.json[\"data\"][\"markdown\"]) }} \\n\\n /// \\n\\n new version page: \\n\\n {{ JSON.stringify($('scrape page - 1').item.json[\"data\"][\"markdown\"]) }}"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "7897d707-2c27-43bf-9ea0-90ab7996bf4a", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [920, -260], "parameters": {"width": 150, "height": 80, "content": "Connect your own OpenAI account\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "dec23eea-1590-4418-ab2b-1cb4a6ccfdc6", "connections": {"Gmail": {"ai_tool": [[{"node": "send e-mail?", "type": "ai_tool", "index": 0}]]}, "wait 1 day": {"main": [[{"node": "scrape page - 2", "type": "main", "index": 0}]]}, "send e-mail?": {"main": [[{"node": "scrape page - 1", "type": "main", "index": 0}]]}, "parse results": {"main": [[{"node": "scrape page - 1", "type": "main", "index": 0}]]}, "scrape page - 1": {"main": [[{"node": "wait 1 day", "type": "main", "index": 0}]]}, "scrape page - 2": {"main": [[{"node": "send e-mail?", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "send e-mail?", "type": "ai_languageModel", "index": 0}]]}, "New espionage assignment": {"main": [[{"node": "convert message to website url & instruction", "type": "main", "index": 0}]]}, "convert message to website url & instruction": {"main": [[{"node": "parse results", "type": "main", "index": 0}]]}}}