{"id": "PVBUCGQUOiOrIfli", "meta": {"instanceId": "740d0a59ff901341d9247a8b17beaace585edc6342f8d716c9cf18ea3ac6313a", "templateCredsSetupCompleted": true}, "name": "n8n update", "tags": [{"id": "AW45ve4sa5vbdnkZ", "name": "#n8n", "createdAt": "2025-03-30T00:22:43.140Z", "updatedAt": "2025-03-30T00:22:43.140Z"}], "nodes": [{"id": "445aa127-ac55-4e01-ab07-90a45cf0fab1", "name": "Pull n8n Image", "type": "n8n-nodes-base.ssh", "position": [300, -240], "parameters": {"cwd": "={{ $('Set Default variable').item.json['working-directory'] }}", "command": "sudo docker pull docker.n8n.io/n8nio/n8n"}, "credentials": {"sshPassword": {"id": "tqNyOVbQTikb35Tk", "name": "SSH Password account"}}, "typeVersion": 1}, {"id": "e437f006-cdee-4ab3-bfaa-f323b072c380", "name": "docker compose pull", "type": "n8n-nodes-base.ssh", "position": [560, -240], "parameters": {"cwd": "={{ $('Set Default variable').item.json['working-directory'] }}", "command": "sudo docker compose pull"}, "credentials": {"sshPassword": {"id": "tqNyOVbQTikb35Tk", "name": "SSH Password account"}}, "typeVersion": 1}, {"id": "79e7a23e-1bd6-45c8-b9b0-ee959c11aa01", "name": "check n8n installed version", "type": "n8n-nodes-base.ssh", "position": [-1100, -660], "parameters": {"cwd": "={{ $json['working-directory'] }}", "command": "=sudo docker inspect \"{{ $json['n8n-container-name'] }}\" | jq -r '.[0].Config.Labels[\"org.opencontainers.image.version\"]'"}, "credentials": {"sshPassword": {"id": "tqNyOVbQTikb35Tk", "name": "SSH Password account"}}, "executeOnce": false, "typeVersion": 1, "alwaysOutputData": false}, {"id": "a84ab5f7-da59-4d7a-aeac-ec1651115924", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1800, -200], "parameters": {}, "typeVersion": 1}, {"id": "9306fb33-0780-49db-bf22-13e264f0c2bf", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1800, -460], "parameters": {"rule": {"interval": [{"daysInterval": 3, "triggerAtHour": 13}]}}, "typeVersion": 1.2}, {"id": "a47fe6aa-f12b-4974-ac55-214b2fd76eff", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1540, -660], "parameters": {"height": 500, "content": "## Default Variables  \nBefore starting, please set the following variables:  \n\n- **working-directory**: The directory where your `docker-compose.yml` file is located.  \n- **n8n-container-name**: The name of your n8n Docker container.  \n- **telegram-id**: Your Telegram chat ID. You can find it by messaging `@get_id_bot` on Telegram.  \n"}, "typeVersion": 1}, {"id": "bd9cba21-5ce6-4ce1-803b-ac77b9e58f45", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1300, -240], "parameters": {"height": 300, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nGet information from the n8n GitHub repository to find the latest released version of n8n.  \n"}, "typeVersion": 1}, {"id": "4db11a08-3feb-4f91-9671-a675a2e7ae76", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1040, -240], "parameters": {"height": 300, "content": "\n\n\n\n\n\n\n\n\n\n\n\nThis node removes \"n8n@\" from the version string.  \nFor example:  \n- Before: `n8n@1.84.3`  \n- After: `1.84.3`  \n\n"}, "typeVersion": 1}, {"id": "d479524f-143e-4894-909d-e9970590e0e2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1160, -880], "parameters": {"height": 400, "content": "## Check Installed Image Version  \nExecute a command on the server to determine which version of n8n is currently running.  \n"}, "typeVersion": 1}, {"id": "f896acec-2dc5-477b-88f2-9b8e36550006", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-820, -880], "parameters": {"width": 420, "height": 520, "content": "## SQL Query: Merging Inputs  \nThis query retrieves the `stdout` field from `input1` and `tag_name` from `input2`.  \nIt uses a `LEFT JOIN` to merge the data based on matching `id` fields.  \n\nQuery:  \n```sql\nSELECT input1.stdout AS stdout, \ninput2.tag_name AS tag_name\nFROM input1\nLEFT JOIN input2 ON input1.id = input2.id;\n"}, "typeVersion": 1}, {"id": "7913800b-b46f-40e2-851d-cc3cc2104843", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-60, -1080], "parameters": {"height": 380, "content": "## Telegram Notificaton [OPTIONAL]\nSend a Telegram message and inform that there is nothing to do and n8n is already on the latest version.\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "63ef2c3f-d0d2-438e-9427-90592397df22", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-380, -680], "parameters": {"height": 340, "content": "## Compare Versions  \nThis node compares two versions: one from Docker and another from the n8n GitHub repository.  \nIf a new version is available, it will be detected.  \n\n\n"}, "typeVersion": 1}, {"id": "77ebd46f-09af-4e68-a1a7-a6d97c3f9021", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-40, -420], "parameters": {"height": 340, "content": "## Telegram Approve\nSend a Telegram notification and inform that a new version is available. Ask if the user wants to update.\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "7365971c-1e77-4eae-a23f-9f7843b40889", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [220, -420], "parameters": {"height": 340, "content": "## Pull Docker Image\nPull the latest n8n image. You can modify the command as needed.\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "1e7b547c-e9b1-4b91-8241-6cdd6a746100", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [500, -420], "parameters": {"height": 340, "content": "## Docker Compose Pull\nRuns in the directory you defined in the De<PERSON>ult variable.\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "f2817779-b2e9-4205-baf7-1f0481bae0e4", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [780, -420], "parameters": {"height": 340, "content": "## Docker Compose Up \nIn this step, the container will be started with the new image.\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "4823d32f-9237-444f-812f-1bc494d0e087", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1060, -420], "parameters": {"height": 340, "content": "## Telegram Notificaton\nSend a Telegram notification and inform that n8n has been updated.\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "8bcf0308-7904-4635-8f0e-7335e360dabc", "name": "docker compose up", "type": "n8n-nodes-base.ssh", "position": [840, -240], "parameters": {"cwd": "={{ $('Set Default variable').item.json['working-directory'] }}", "command": "sudo docker compose up -d"}, "credentials": {"sshPassword": {"id": "tqNyOVbQTikb35Tk", "name": "SSH Password account"}}, "typeVersion": 1}, {"id": "add506c6-88b7-480c-be13-2de86f6094ca", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1840, -660], "parameters": {"height": 340, "content": "## Schedule Trigger  \nYou can schedule this workflow, for example, every three days to check n8n images.\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "045ebd0a-9d54-4cae-8307-f23ed26103f9", "name": "Set Default variable", "type": "n8n-nodes-base.set", "position": [-1480, -320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c06b2d24-1fd7-40f0-aee5-b5d6553e289e", "name": "working-directory", "type": "string", "value": ""}, {"id": "451aad67-5190-4eab-a982-56092734bb07", "name": "n8n-container-name", "type": "string", "value": ""}, {"id": "8a294900-f367-47a2-b260-344b133dc2ff", "name": "telegram-id", "type": "string", "value": "*********"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "6bc2b28c-0f3c-44aa-a536-766f972f9e22", "name": "Github HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-1240, -220], "parameters": {"url": "https://api.github.com/repos/n8n-io/n8n/releases/latest", "options": {}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "8ba3f574-9f2b-48d2-95fc-b57d57ecf6c1", "name": "Merge Results", "type": "n8n-nodes-base.merge", "position": [-660, -560], "parameters": {"mode": "combineBySql", "query": "SELECT input1.stdout, input2.tag_name \nFROM input1 \nLEFT JOIN input2 ON true;"}, "typeVersion": 3.1}, {"id": "c97723c3-1733-4481-add2-7ecae02ea144", "name": "Edit Version String", "type": "n8n-nodes-base.set", "position": [-960, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f6e5cc51-aa49-46e5-aa4c-73baec811afa", "name": "tag_name", "type": "string", "value": "={{ $json[\"tag_name\"].replace(\"n8n@\", \"\") }}"}]}}, "typeVersion": 3.4}, {"id": "22bec435-84ba-44bb-8127-c5b099fda7f2", "name": "Comapre Two Input", "type": "n8n-nodes-base.if", "position": [-320, -500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e88d2c77-5ee1-4296-a612-d9b2290f6e03", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.stdout }}", "rightValue": "={{ $json.tag_name }}"}]}, "looseTypeValidation": "="}, "typeVersion": 2.2}, {"id": "be9bdef5-88f4-4f12-8938-82f8206b8655", "name": "Telegram Notif", "type": "n8n-nodes-base.telegram", "position": [0, -860], "webhookId": "38d19f3d-0ef4-40df-b831-701ea242bb8f", "parameters": {"text": "n8n is up to date.", "chatId": "={{ $('Set Default variable').item.json['telegram-id'] }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Zx3ibmlSzRKZQsFa", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "59ece477-dcf2-4898-a362-7fcd35a49315", "name": "Telegram Approve", "type": "n8n-nodes-base.telegram", "position": [20, -240], "webhookId": "e816696f-cb7a-4036-92bf-eafb5f06778c", "parameters": {"chatId": "={{ $('Set Default variable').item.json['telegram-id'] }}", "message": "Hi, \na new n8n version is available. \nI'm ready to update. \nCan I start now?", "options": {}, "operation": "sendAndWait"}, "credentials": {"telegramApi": {"id": "Zx3ibmlSzRKZQsFa", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "292efcfd-bbd7-4170-9e9f-020f10483c5e", "name": "Telegram Notif1", "type": "n8n-nodes-base.telegram", "position": [1120, -240], "webhookId": "254019a6-a298-4a9e-b100-8b92f22469c3", "parameters": {"text": "We are updating n8n to the latest version.", "chatId": "={{ $('Set Default variable').item.json['telegram-id'] }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Zx3ibmlSzRKZQsFa", "name": "Telegram account"}}, "typeVersion": 1.2}], "active": true, "pinData": {}, "settings": {"timezone": "Asia/Tehran", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveExecutionProgress": false}, "versionId": "e7c52f1d-e131-4911-bf04-5d3ebefc8271", "connections": {"Merge Results": {"main": [[{"node": "Comapre Two Input", "type": "main", "index": 0}]]}, "Pull n8n Image": {"main": [[{"node": "docker compose pull", "type": "main", "index": 0}]]}, "Telegram Notif1": {"main": [[{"node": "Comapre Two Input", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Set Default variable", "type": "main", "index": 0}]]}, "Telegram Approve": {"main": [[{"node": "Pull n8n Image", "type": "main", "index": 0}]]}, "Comapre Two Input": {"main": [[{"node": "Telegram Notif", "type": "main", "index": 0}], [{"node": "Telegram Approve", "type": "main", "index": 0}]]}, "docker compose up": {"main": [[{"node": "Telegram Notif1", "type": "main", "index": 0}]]}, "Edit Version String": {"main": [[{"node": "Merge Results", "type": "main", "index": 1}]]}, "Github HTTP Request": {"main": [[{"node": "Edit Version String", "type": "main", "index": 0}]]}, "docker compose pull": {"main": [[{"node": "docker compose up", "type": "main", "index": 0}]]}, "Set Default variable": {"main": [[{"node": "check n8n installed version", "type": "main", "index": 0}, {"node": "Github HTTP Request", "type": "main", "index": 0}]]}, "check n8n installed version": {"main": [[{"node": "Merge Results", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Default variable", "type": "main", "index": 0}]]}}}