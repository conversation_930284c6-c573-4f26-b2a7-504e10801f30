{"id": "QJZLBn9L6NbmjmLK", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "🤖🧠 AI Agent Chatbot + LONG TERM Memory + Note Storage + Telegram", "tags": [], "nodes": [{"id": "20a2d959-5412-447b-a2c4-7736b6b758b3", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-320, 1600], "webhookId": "8ba8fa53-2c24-47a8-b4dd-67b88c106e3d", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "de79c268-bac5-48ff-be4d-18f522861c22", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-100, 1280], "parameters": {"color": 4, "width": 340, "height": 380, "content": "## Retrieve Long Term Memories\nGoogle Docs"}, "typeVersion": 1}, {"id": "000a94d1-57ce-4eec-a021-9123685d22bf", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1040, 1840], "parameters": {"width": 280, "height": 380, "content": "## Save To Current Chat Memory (Optional)"}, "typeVersion": 1}, {"id": "1bf1cade-bb3e-450a-a531-9add259069df", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1360, 1840], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Save Long Term Memories\nGoogle Docs"}, "typeVersion": 1}, {"id": "8b30f207-8204-4548-8f51-38c387d98ae9", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [820, 1900], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "50271e59-6dd2-4f54-9b28-dd4a9f33ddc5", "name": "Chat Response", "type": "n8n-nodes-base.set", "position": [1440, 1600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d6f68b1c-a6a6-44d4-8686-dc4dcdde4767", "name": "output", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "1064a2bf-bf74-44cd-ba8a-48f93700e887", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1140, 2000], "parameters": {"sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "sessionIdType": "customKey", "contextWindowLength": 50}, "typeVersion": 1.3}, {"id": "280fe3b1-faca-41b6-be0e-2ab906cd1662", "name": "Save Long Term Memories", "type": "n8n-nodes-base.googleDocsTool", "position": [1460, 2000], "parameters": {"actionsUi": {"actionFields": [{"text": "={ \n \"memory\": \"{{ $fromAI('memory') }}\",\n \"date\": \"{{ $now }}\"\n}", "action": "insert"}]}, "operation": "update", "documentURL": "[Google Doc ID]", "descriptionType": "manual", "toolDescription": "Save Memory"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "37baa147-120a-40a8-b92f-df319fc4bc46", "name": "Retrieve Long Term Memories", "type": "n8n-nodes-base.googleDocs", "position": [20, 1420], "parameters": {"operation": "get", "documentURL": "[Google Doc ID]"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "b047a271-d2aa-4a26-b663-6a76d249824a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [720, 1840], "parameters": {"color": 3, "width": 280, "height": 380, "content": "## LLM"}, "typeVersion": 1}, {"id": "15bb5fd5-7dfe-4da9-830c-e1d905831640", "name": "Telegram Response", "type": "n8n-nodes-base.telegram", "position": [1440, 1260], "parameters": {"text": "={{ $json.output }}", "chatId": "=**********", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "8cc38a87-e214-4193-9fe6-ba4adc3d5530", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1360, 1160], "parameters": {"width": 260, "height": 300, "content": "## Telegram \n(Optional)"}, "typeVersion": 1}, {"id": "38121a81-d768-4bb0-a9e6-39de0906e026", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [680, 1500], "parameters": {"color": 5, "width": 1320, "height": 780, "content": "## AI AGENT with Long Term Memory & Note Storage"}, "typeVersion": 1}, {"id": "7d5d1466-b4c9-4055-a634-ea7025dc370a", "name": "DeepSeek-V3 Chat", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [820, 2060], "parameters": {"model": "=deepseek-chat", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}, {"id": "68303b67-2203-41e8-b370-220d884d2945", "name": "AI Tools Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1060, 1600], "parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "=## ROLE \nYou are a friendly, attentive, and helpful AI assistant. Your primary goal is to assist the user while maintaining a personalized and engaging interaction. \n\n---\n\n## RULES \n\n1. **Memory Management**: \n - When the user sends a new message, evaluate whether it contains noteworthy or personal information (e.g., preferences, habits, goals, or important events). \n - If such information is identified, use the **Save Memory** tool to store this data in memory. \n - Always send a meaningful response back to the user, even if your primary action was saving information. This response should not reveal that information was stored but should acknowledge or engage with the user’s input naturally. \n\n2. **Note Management**: \n - If the user provides information that is intended to be stored as a note (e.g., specific instructions, reminders, or standalone pieces of information), use the **Save Note** tool. \n - Notes should not be stored in memory using the **Save Memory** tool. \n - Ensure that notes are clear, concise, and accurately reflect the user’s input. \n\n3. **Context Awareness**: \n - Use stored memories and notes to provide contextually relevant and personalized responses. \n - Always consider the **date and time** when a memory or note was collected to ensure your responses are up-to-date and accurate.\n\n4. **User-Centric Responses**: \n - Tailor your responses based on the user's preferences and past interactions. \n - Be proactive in recalling relevant details from memory or notes when appropriate but avoid overwhelming the user with unnecessary information.\n\n5. **Privacy and Sensitivity**: \n - Handle all user data with care and sensitivity. Avoid making assumptions or sharing stored information unless it directly enhances the conversation or task at hand.\n - Never store passwords or usernames.\n\n6. **Fallback Responses**: \n - **IMPORTANT** If no specific task or question arises from the user’s message (e.g., when only saving information), respond in a way that keeps the conversation flowing naturally. For example: \n - Acknowledge their input: “Thanks for sharing that!” \n - Provide a friendly follow-up: “Is there anything else I can help you with today?” \n - DO NOT tell jokes as a fallback response.\n\n---\n\n## TOOLS \n\n### Save Memory \n- Use this tool to store summarized, concise, and meaningful information about the user. \n- Extract key details from user messages that could enhance future interactions (e.g., likes/dislikes, important dates, hobbies). \n- Ensure that the summary is clear and devoid of unnecessary details.\n\n### Save Note \n- Use this tool to store specific instructions, reminders, or standalone pieces of information provided by the user. \n- Notes should not include general personal preferences or habits meant for long-term memory storage. \n- Ensure that notes are concise and accurately reflect what the user wants to store.\n\n---\n\n## MEMORIES \n\n### Recent Noteworthy Memories \nHere are the most recent memories collected from the user, including their date and time of collection: \n\n**{{ $json.data[0].content }}**\n\n### Guidelines for Using Memories: \n- Prioritize recent memories but do not disregard older ones if they remain relevant. \n- Cross-reference memories to maintain consistency in your responses. For example, if a user shares conflicting preferences over time, clarify or adapt accordingly.\n\n---\n\n## NOTES \n\n### Recent Notes Collected from User: \nHere are the most recent notes collected from the user: \n\n**{{ $json.data[1].content }}**\n\n### Guidelines for Using Notes: \n- Use notes for tasks requiring specific instructions or reminders.\n- Do not mix note content with general memory content; keep them distinct.\n\n---\n\n## ADDITIONAL INSTRUCTIONS \n\n- Think critically before responding to ensure your answers are thoughtful and accurate. \n- Strive to build trust with the user by being consistent, reliable, and personable in your interactions. \n- Avoid robotic or overly formal language; aim for a conversational tone that aligns with being \"friendly and helpful.\" \n"}, "promptType": "define"}, "typeVersion": 1.7, "alwaysOutputData": false}, {"id": "a6741133-93a1-42f8-83b4-bc29b9f49ae2", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1680, 1840], "parameters": {"color": 4, "width": 280, "height": 380, "content": "## Save Notes\nGoogle Docs"}, "typeVersion": 1}, {"id": "87c88d31-811d-4265-b44e-ab30a45ff88b", "name": "Save Notes", "type": "n8n-nodes-base.googleDocsTool", "position": [1780, 2000], "parameters": {"actionsUi": {"actionFields": [{"text": "={ \n \"note\": \"{{ $fromAI('memory') }}\",\n \"date\": \"{{ $now }}\"\n}", "action": "insert"}]}, "operation": "update", "documentURL": "[Google Doc ID]", "descriptionType": "manual", "toolDescription": "Save Notes"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "b9b97837-d6f2-4cef-89c4-9301973015df", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-100, 1680], "parameters": {"color": 4, "width": 340, "height": 380, "content": "## Retrieve Notes\nGoogle Docs"}, "typeVersion": 1}, {"id": "0002a227-4240-4d3c-9a45-fc6e23fdc7f5", "name": "Retrieve Notes", "type": "n8n-nodes-base.googleDocs", "onError": "continueRegularOutput", "position": [20, 1820], "parameters": {"operation": "get", "documentURL": "[Google Doc ID]"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "88f7024c-87d4-48b4-b6bb-f68c88202f56", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [520, 1600], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "48d576fc-870a-441e-a7be-3056ef7e1d7a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [340, 1600], "parameters": {}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "8130e77c-ecbd-470e-afec-ec8728643e00", "connections": {"Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Tools Agent", "type": "main", "index": 0}]]}, "Save Notes": {"ai_tool": [[{"node": "AI Tools Agent", "type": "ai_tool", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "AI Tools Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Tools Agent": {"main": [[{"node": "Telegram Response", "type": "main", "index": 0}, {"node": "Chat Response", "type": "main", "index": 0}], []]}, "Retrieve Notes": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "DeepSeek-V3 Chat": {"ai_languageModel": [[]]}, "Telegram Response": {"main": [[]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Tools Agent", "type": "ai_memory", "index": 0}]]}, "Save Long Term Memories": {"ai_tool": [[{"node": "AI Tools Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Retrieve Long Term Memories", "type": "main", "index": 0}, {"node": "Retrieve Notes", "type": "main", "index": 0}]]}, "Retrieve Long Term Memories": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}