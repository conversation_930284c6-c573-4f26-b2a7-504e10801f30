{"meta": {"instanceId": "ff412ab2a6cd55af5dedbbab9b8e43f0f3a0cb16fb794fa8d3837f957b771ad2"}, "nodes": [{"id": "9c3c06eb-8b48-4229-9b16-7fe7c4f886c3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [78.44447107090468, 520], "parameters": {}, "typeVersion": 1}, {"id": "2a8d8297-18de-4e1f-b44b-93842f7c1709", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1678.*************, 2020], "parameters": {"model": "gpt-4o-mini", "options": {}}, "typeVersion": 1}, {"id": "a6c24857-ad3b-4561-b40b-8520064e861b", "name": "Format QA Pair1", "type": "n8n-nodes-base.set", "position": [2018.*************, 1880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2c1bd408-29f0-487b-9a33-7513d5bbfe23", "name": "question", "type": "string", "value": "={{ $('Needs AI Completion?1').item.json.question }}"}, {"id": "02ffc3b7-3d77-4dfe-ba3f-2052f5cc9e83", "name": "answer", "type": "string", "value": "={{\n[\n $('Needs AI Completion?1').item.json.answer,\n $json.text\n ? $json.text[0].toLowerCase() + $json.text.substring(1, $json.text.length)\n : '',\n $('Needs AI Completion?1').item.json.append || '',\n].join(' ').trim()\n}}"}]}}, "typeVersion": 3.4}, {"id": "2b4712cb-371c-45bc-a024-363ae951b0ac", "name": "For Each Question...1", "type": "n8n-nodes-base.splitInBatches", "position": [1238.*************, 1400], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "8f7cefc1-9fc0-474b-a81e-bf573068258b", "name": "Question to List1", "type": "n8n-nodes-base.splitOut", "position": [1038.*************, 1400], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "9aeb5858-d6d4-4541-8a0d-851740d948ae", "name": "Questions to Object...1", "type": "n8n-nodes-base.aggregate", "position": [1978.*************, 1380], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "2c1d56c5-20f2-4691-ab89-87edf9902a5f", "name": "Format DisplayName + Questions1", "type": "n8n-nodes-base.set", "position": [2198.************, 1380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "66318f17-a3bd-4bcf-b326-50208b503143", "name": "name", "type": "string", "value": "={{ $('Execute Workflow Trigger').first().json.data.displayName || $('Execute Workflow Trigger').first().json.data['Category name'] }}"}, {"id": "a83abac5-ddc6-4316-a916-7eab338f97cf", "name": "questions", "type": "array", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "5147d5ef-f56d-49b0-9be8-0af7ccb8cdae", "name": "Create From Text", "type": "n8n-nodes-base.googleDrive", "position": [2380, 1380], "parameters": {"name": "={{ $json.name + '-' + $now.format('yyyyMMdd') }}", "content": "={{ JSON.stringify($json, null, 4) }}", "driveId": {"__rl": true, "mode": "list", "value": ""}, "options": {}, "folderId": {"__rl": true, "mode": "id", "value": "={{ $('Execute Workflow Trigger').first().json.outdir }}"}, "operation": "createFromText"}, "typeVersion": 3}, {"id": "9abc3871-8103-4659-9afa-93142dabec01", "name": "Define Sheets", "type": "n8n-nodes-base.set", "position": [518.*************, 520], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n \"data\": [\n \"Single Integration Native\",\n \"Single Integration Cred-only\",\n \"Single Integration Non-native\",\n \"Categories\"\n ]\n}\n"}, "typeVersion": 3.4}, {"id": "417b1c53-ec19-4f59-9580-b6080d3bc103", "name": "Sheets To List...", "type": "n8n-nodes-base.splitOut", "position": [698.*************, 520], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "d8495ac2-7f45-4dd5-8eb5-d95c9e572dd3", "name": "Get Services", "type": "n8n-nodes-base.googleSheets", "position": [1098.*************, 660], "parameters": {"options": {"returnAllMatches": "returnAllMatches"}, "filtersUI": {"values": [{"lookupColumn": "=status"}]}, "sheetName": {"__rl": true, "mode": "name", "value": "={{ $json.data }}"}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 4.3, "alwaysOutputData": true}, {"id": "e5b7ebe7-0e0f-4f61-8a14-afc51eb37270", "name": "Single Integration Cred-only", "type": "n8n-nodes-base.set", "position": [778.*************, 1400], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n \"data\": [\n {\n \"question\": \"How can I set up {{ $json.data.displayName }} integration in n8n?\",\n \"answer\": \"To use {{ $json.data.displayName }} integration in n8n, start by adding the HTTP Request node to your workflow canvas and authenticate it using a predefined credential type. This allows you to perform custom operations, without additional authentication setup. Once connected, you can make custom API calls to {{ $json.data.displayName }} to query the data you need using the URLs you provide, for example:\",\n \"ai_example\": \"Assume useris advanced in n8n integration and sending HTTP requests, focus instead on examples operations and/or use-cases such as creating records, updating records, or retrieving data.\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"Do I need any special permissions or API keys to integrate {{ $json.data.displayName }} with n8n?\",\n \"answer\": \"Yes, you need an API key with the necessary permissions to integrate {{ $json.data.displayName }} with n8n. You will typically need to use the {{ $json.data.displayName }} API docs to construct your request via the HTTP Request node. Ensure the API key has the appropriate access rights for the data and actions you want to automate within your workflows.\",\n \"ai_completion\": {{ false }}\n },\n {\n \"question\": \"Can I combine {{ $json.data.displayName }} with other apps in n8n workflows?\",\n \"answer\": \"Definitely! n8n enables you to create workflows that combine {{ $json.data.displayName }} with other apps and services. For instance,\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"What are some common use cases for {{ $json.data.displayName }} integrations with n8n?\",\n \"answer\": \"Common use cases for {{ $json.data.displayName }} automation include\",\n \"append\": \"With n8n, you can customize these workflows to fit your specific needs and extend them by adding other 400+ integrations or incorporating advanced AI logic.\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"How does n8n’s pricing model benefit me when integrating {{ $json.data.displayName }}?\",\n \"answer\": \"n8n’s pricing model is designed to be both affordable and scalable, which is particularly beneficial when integrating with {{ $json.data.displayName }}. Unlike other platforms that charge per operation or task, n8n charges only for full workflow executions. This means you can create complex workflows with {{ $json.data.displayName }}, involving thousands of tasks or steps, without worrying about escalating costs. For example, if your {{ $json.data.displayName }} workflows perform around 100k tasks, you could be paying $500+/month on other platforms, but with n8n's pro plan, you start at around $50. This approach allows you to scale your {{ $json.data.displayName }} integrations efficiently while maintaining predictable costs.\",\n \"ai_completion\": {{ false }}\n }\n ]\n}"}, "typeVersion": 3.4}, {"id": "e2cc607b-8502-4beb-ace5-8670af845134", "name": "Single Integration Native", "type": "n8n-nodes-base.set", "position": [778.*************, 1240], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={\n \"data\": [\n {\n \"question\": \"How can I set up {{ $json.data.displayName }} integration in n8n?\",\n \"answer\": \"To use {{ $json.data.displayName }} integration in n8n, start by adding the {{ $json.data.displayName }} node to your workflow. You'll need to authenticate your {{ $json.data.displayName }} account using supported authentication methods. Once connected, you can choose from the list of supported actions or make custom API calls via the HTTP Request node, for example:\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"Do I need any special permissions or API keys to integrate {{ $json.data.displayName }} with n8n?\",\n \"answer\": \"Yes, you will typically need an API key, token, or similar credentials to add {{ $json.data.displayName }} integration to n8n. These can usually be found in your account settings for the service. Ensure that your credentials have the necessary permissions to access and manage the data or actions you want to automate within your workflows.\",\n \"ai_completion\": {{ false }}\n },\n {\n \"question\": \"Can I combine {{ $json.data.displayName }} with other apps in n8n workflows?\",\n \"answer\": \"Definitely! n8n enables you to create workflows that combine {{ $json.data.displayName }} with other apps and services. For instance,\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"What are some common use cases for {{ $json.data.displayName }} integrations with n8n?\",\n \"answer\": \"Common use cases for {{ $json.data.displayName }} automation include\",\n \"append\": \"With n8n, you can customize these workflows to fit your specific needs and extend them by adding other 400+ integrations or incorporating advanced AI logic.\",\n \"ai_completion\": {{ true }}\n },\n {\n \"question\": \"How does n8n’s pricing model benefit me when integrating {{ $json.data.displayName }}?\",\n \"answer\": \"n8n’s pricing model is designed to be both affordable and scalable, which is particularly beneficial when integrating with {{ $json.data.displayName }}. Unlike other platforms that charge per operation or task, n8n charges only for full workflow executions. This means you can create complex workflows with {{ $json.data.displayName }}, involving thousands of tasks or steps, without worrying about escalating costs. For example, if your {{ $json.data.displayName }} workflows perform around 100k tasks, you could be paying $500+/month on other platforms, but with n8n's pro plan, you start at around $50. This approach allows you to scale your {{ $json.data.displayName }} integrations efficiently while maintaining predictable costs.\",\n \"ai_completion\": {{ false }}\n }\n ]\n}"}, "typeVersion": 3.4}, {"id": "ce1905c2-f41a-4dea-bd03-a9ae1e893326", "name": "Categories", "type": "n8n-nodes-base.set", "position": [778.*************, 1760], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n \"data\": [\n {\n \"question\": `What types of ${$json.data['Category name']} tools can I integrate with n8n?`,\n \"answer\": `n8n offers integrations with a wide range of ${$json.data['Category name']} tools, including`,\n \"append\": `These integrations allow you to streamline your ${$json.data['Category name']} workflows, automate repetitive tasks, and improve collaboration across your team.`,\n \"ai_completion\": true\n },\n {\n \"question\": `Are there any specific requirements or limitations for using ${$json.data['Category name']} integrations?`,\n \"answer\": `Yes, each ${$json.data['Category name']} integration may have specific requirements. For example,`,\n \"append\": `n8n offers a significant number of pre-built ${$json.data['Category name']} integrations (called nodes). If n8n doesn't support the integration you need, use the HTTP Request node or custom code to connect to the service's API. Be sure to review the integration documentation for any app-specific prerequisites. Additionally, consider any API rate limits or usage constraints that might affect your workflows.`,\n \"ai_completion\": true\n },\n {\n \"question\": `What are some popular use cases for ${$json.data['Category name']} integrations in n8n?`,\n \"answer\": `${$json.data['Category name']} integrations with n8n offer a variety of practical use cases. For example:`,\n \"ai_completion\": true,\n \"ai_completion_format\": \"list\"\n },\n {\n \"question\": `How does n8n’s pricing model benefit ${$json.data['Category name']} workflows?`,\n \"answer\": `n8n's pricing model, which charges only for full workflow executions rather than individual tasks or steps, is particularly advantageous for ${$json.data['Category name']} workflows. This means you can build complex, multi-step workflows involving various ${$json.data['Category name']} tools without worrying about cost increases due to the number of operations. For example, if your ${$json.data['Category name']} workflows perform around 100k tasks, you could be paying $500+/month on other platforms, but with n8n's pro plan, you start at around $50. This approach allows you to scale your ${$json.data['Category name']} integrations efficiently while maintaining predictable costs.`,\n \"ai_completion\": false\n },\n {\n \"question\": `How can I leverage n8n's AI capabilities in my ${$json.data['Category name']} workflows?`,\n \"answer\": `n8n offers powerful AI capabilities that can enhance your ${$json.data['Category name']} workflows. For example, you can integrate AI tools like OpenAI with n8n to`,\n \"append\": `To add AI capabilities, navigate to the AI category in n8n's integrations directory and set up the integration with your chosen AI service. This combination of AI and ${$json.data['Category name']} integrations can significantly boost your development efficiency and innovation.`,\n \"ai_completion\": true\n }\n ]\n}\n}}"}, "typeVersion": 3.4}, {"id": "344c93e6-3ed9-4dd0-8a38-c2f853ef3cc1", "name": "For Each Sheet...", "type": "n8n-nodes-base.splitInBatches", "position": [918.*************, 520], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "e5776c79-51e4-4469-8cf7-dff009ee0ffd", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [298.*************, 1400], "parameters": {}, "typeVersion": 1}, {"id": "76aca3a6-c3ff-41fa-9fdf-30839df85669", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [1898.*************, 660], "parameters": {"mode": "each", "options": {}, "workflowId": "={{ $workflow.id }}"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "663b1ce2-ccb5-43d1-8871-c5fa7412151c", "name": "Prepare Job", "type": "n8n-nodes-base.set", "position": [1278.*************, 660], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2755153b-d38c-4aba-be8f-f72c3bf91cf2", "name": "sheet", "type": "string", "value": "={{ $('For Each Sheet...').item.json.data }}"}, {"id": "eed4a03a-451b-4b74-b591-ce970d84f990", "name": "data", "type": "object", "value": "={{ $json }}"}, {"id": "ee73316c-0316-4389-aa13-4bb145637262", "name": "outdir", "type": "string", "value": "={{\n{\n \"Single Integration Native\": \"Insert the corresponding Google Drive folder ID here\",\n \"Single Integration Cred-only\": \"Insert the corresponding Google Drive folder ID here\",\n \"Single Integration Non-native\": \"Insert the corresponding Google Drive folder ID here\",\n \"Categories\": \"Insert the corresponding Google Drive folder ID here\",\n}[$('For Each Sheet...').item.json.data]\n}}"}]}}, "typeVersion": 3.4}, {"id": "087249d0-d001-49c3-8695-e0e3f02b66e2", "name": "For Each Service...", "type": "n8n-nodes-base.splitInBatches", "position": [1498.*************, 520], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "edd9e2c7-9477-4145-bb1f-1424ccb2080f", "name": "Update Row Status", "type": "n8n-nodes-base.googleSheets", "position": [2558.************, 1380], "parameters": {"columns": {"value": {"status": "done", "row_number": "={{ $('Execute Workflow Trigger').first().json.data.row_number }}"}, "schema": [{"id": "displayName", "type": "string", "display": true, "required": false, "displayName": "displayName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "name", "value": "={{ $('Execute Workflow Trigger').first().json.sheet }}"}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 4.4}, {"id": "454ccacd-104c-4cad-b52e-72447a49fb04", "name": "Single Integration Non-native", "type": "n8n-nodes-base.set", "position": [778.*************, 1580], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{\n{\n \"data\": [\n {\n \"question\": `How can I set up ${$json.data.displayName} integration in n8n?`,\n \"answer\": `To use ${$json.data.displayName} integration in n8n, start by adding the HTTP Request node to your workflow canvas and authenticate it using a generic authentication method. Once connected, you can make custom API calls to ${$json.data.displayName} to query the data you need using the URLs you provide, for example:`,\n \"ai_example\": \"Assume useris advanced in n8n integration and sending HTTP requests, focus instead on examples operations and/or use-cases such as creating records, updating records, or retrieving data.\",\n \"ai_completion\": true\n },\n{\n \"question\": `Do I need any special permissions or API keys to integrate ${$json.data.displayName} with n8n?`,\n \"answer\": `Yes, with generic authentication, you'll typically need to provide endpoint URLs, headers, parameters, and any other authentication details specific to **${$json.data.displayName}**: - Find the&nbsp;**${$json.data.displayName}** API documentation and see if the API supports HTTP requests; - Most APIs require some form of authentication and you can configure this in the HTTP Request mode (Basic Auth, <PERSON>th, <PERSON>, <PERSON><PERSON>, OAuth1 API, OAuth2 API, Query Auth).`,\n \"ai_completion\": false\n },\n{\n \"question\": `Can I combine ${$json.data.displayName} with other apps in n8n workflows?`,\n \"answer\": `Definitely! n8n enables you to create workflows that combine ${$json.data.displayName} with other apps and services. For instance,`,\n \"ai_completion\": true\n },\n {\n \"question\": `What are some common use cases for ${$json.data.displayName} integrations with n8n?`,\n \"answer\": `Common use cases for ${$json.data.displayName} automation include`,\n \"append\": `With n8n, you can customize these workflows to fit your specific needs and extend them by adding other 400+ integrations or incorporating advanced AI logic.`,\n \"ai_completion\": true\n },\n {\n \"question\": `How does n8n’s pricing model benefit me when integrating ${$json.data.displayName}?`,\n \"answer\": `n8n's pricing model is designed to be both affordable and scalable, which is particularly beneficial when integrating with ${ $json.data.displayName}. Unlike other platforms that charge per operation or task, n8n charges only for full workflow executions. This means you can create complex workflows with ${ $json.data.displayName}, involving thousands of tasks or steps, without worrying about escalating costs. For example, if your ${ $json.data.displayName} workflows perform around 100k tasks, you could be paying $500+/month on other platforms, but with n8n's pro plan, you start at around $50. This approach allows you to scale your ${ $json.data.displayName} integrations efficiently while maintaining predictable costs.`,\n \"ai_completion\": false\n }\n ]\n}\n}}"}, "typeVersion": 3.4}, {"id": "660fda59-4222-489a-a19a-b3ae0ed7c66f", "name": "If has Data", "type": "n8n-nodes-base.if", "position": [1678.*************, 640], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "aea0bac0-4d4a-4359-8df0-1309c3126376", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.data }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "911aece8-1137-48d4-85f6-ee15ebfdc299", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1238.*************, 620], "parameters": {"width": 193.4545454545455, "height": 317.09090909090907, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Set Destination Folders Here"}, "typeVersion": 1}, {"id": "44d206a7-049c-4721-8934-2308a4b67821", "name": "Needs AI Completion?1", "type": "n8n-nodes-base.switch", "position": [1458.*************, 1780], "parameters": {"rules": {"values": [{"outputKey": "TEXT_REPLACE", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.ai_completion }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "AI_COMPLETE", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f3fcd8ea-6cfa-4658-86c3-3ace9b81d3f2", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.ai_completion }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "14999c7a-2497-46db-b3b5-ede6a9c89dcb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-20, 320], "parameters": {"color": 7, "width": 322.9750655002858, "height": 374.7055783044638, "content": "## Trigger event\nThis could be changed to whatever trigger event you need: an app event, a schedule, a webhook call, another workflow or an AI chat. Sometimes, the HTTP Request node might already serve as your starting point."}, "typeVersion": 1}, {"id": "99a4ca3b-3ad0-48a7-84d7-eb83b61e938b", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [538.*************, 1400], "parameters": {"rules": {"values": [{"outputKey": "Single - Native", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.sheet }}", "rightValue": "Single Integration Native"}]}, "renameOutput": true}, {"outputKey": "Single - C<PERSON>", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6dcb9e09-5eb6-4527-9c22-7eb8867643f4", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.sheet }}", "rightValue": "Single Integration Cred-only"}]}, "renameOutput": true}, {"outputKey": "Single - Non Native", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "04ee4ccd-9efc-46a9-9521-fe50fb0c3087", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.sheet }}", "rightValue": "Single Integration Non-native"}]}, "renameOutput": true}, {"outputKey": "Categories", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "21579253-15c5-4cb4-869b-5760322ae5b5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.sheet }}", "rightValue": "Categories"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "7fe047c7-716c-4ac3-8b7c-c07949c579a4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [459.1561069271204, 320], "parameters": {"color": 7, "width": 1627.0681704544622, "height": 636.4009080766225, "content": "## Prepare data in Google Sheets\nThis part of the workflow prepares the data for reading from a Google Sheets document containing information about different services or categories. Here's an example of Google Sheet: https://docs.google.com/spreadsheets/d/1DCf-phfLWvuTwu02bumx-qykVQeFANnacTTAkRj5tZk/edit?usp=sharing"}, "typeVersion": 1}, {"id": "cb3dc532-40db-437d-97ec-f522e6087b7c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [498.*************, 1080], "parameters": {"color": 7, "width": 513.3200522929088, "height": 840.0651105548446, "content": "## Create your Q&A templates\nFor each service or category, this part of the workflow generates a set of standard questions and answers covering setup, permissions, integrations, use cases, and pricing benefits. You can modify here the input that you will feed to AI."}, "typeVersion": 1}, {"id": "b4095a1b-91aa-4abc-8ed5-d6ca7271ee6c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1238.*************, 1640], "parameters": {"color": 7, "width": 989.1782467385665, "height": 523.7514972875132, "content": "## Complete your Q&A templates with AI\n* An AI model (OpenAI's GPT) is used to enhance or complete some of the answers, making the content more comprehensive and natural-sounding.\n* The workflow formats the Q&A pairs, combining AI-generated content with predefined answers where applicable."}, "typeVersion": 1}, {"id": "d944dfd9-4bfc-4fb0-8655-3269f6caa8ef", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1858.*************, 1200], "parameters": {"color": 7, "width": 907.1258470912726, "height": 396.4865508957922, "content": "## Generate JSON schemas and upload to Google Drive\n* The generated files are saved to specific folders in Google Drive, organized by the type of integration (native, credential-only, non-native) or category.\n* After processing each service or category, it updates the status in the original Google Sheets document to mark it as completed."}, "typeVersion": 1}, {"id": "e21d2a42-021f-4f8e-889d-68a851e9e688", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.strapi", "position": [2978.************, 1380], "parameters": {"operation": "create"}, "typeVersion": 1}, {"id": "92ba57a7-a37a-4d67-9db9-7fa2fe72eec5", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2918.************, 1100], "parameters": {"color": 7, "width": 437.8755022115163, "height": 1073.2774375197612, "content": "## Send the JSON schemas to your CMS\nThis step is up to you to finish: you can choose either pre-built n8n nodes to connect with your CMS or use the HTTP Request node if you CMS is not supported directly in n8n."}, "typeVersion": 1}, {"id": "a42de52f-292b-4b60-ba6d-ff1a672a9758", "name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [2978.************, 1580], "parameters": {"additionalFields": {}}, "credentials": {"wordpressApi": {"id": "dk1CzqTOkihXrjym", "name": "Wordpress account"}}, "typeVersion": 1}, {"id": "abcad9f3-9f05-40e7-8925-32c59b1a6355", "name": "Webflow", "type": "n8n-nodes-base.webflow", "position": [2978.************, 1780], "parameters": {"operation": "create"}, "typeVersion": 2}, {"id": "********-646f-43df-8c0c-c78975ea38c4", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [2978.************, 1980], "parameters": {"options": {}}, "typeVersion": 4.2}, {"id": "d0a97b0c-1271-48e7-8587-5aae565b9d95", "name": "AI Completion1", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1678.*************, 1880], "parameters": {"text": "=### The question\n{{ $json.question }}\n### Prefered answer format\n{{ $json.ai_completion_format ? 'markdown bullet list' : 'markdown' }}\n### User's answer\n{{ $json.answer }}\n{{\n$json.ai_example\n ? `### Guidance\\nWhen giving answer, follow this blueprint: ${$json.ai_example}`\n : ''\n}}", "messages": {"messageValues": [{"message": "=You are assisting with writing a FAQ for the service, {{ $('Execute Workflow Trigger').first().json.data.displayName || $('Execute Workflow Trigger').first().json.data['Category name'] }}. Complete the user's answer in regards to the given question. Ensure the answer is consistent by assuming the tone and style of the user's answer. Give your answer as succinctly as you can with no more than 3 sentences. Do not mention the user or use markdown, return plain text only as this output will be directly appended."}]}, "promptType": "define"}, "executeOnce": false, "typeVersion": 1.4}], "pinData": {}, "connections": {"Switch": {"main": [[{"node": "Single Integration Native", "type": "main", "index": 0}], [{"node": "Single Integration Cred-only", "type": "main", "index": 0}], [{"node": "Single Integration Non-native", "type": "main", "index": 0}], [{"node": "Categories", "type": "main", "index": 0}]]}, "Categories": {"main": [[{"node": "Question to List1", "type": "main", "index": 0}]]}, "If has Data": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}], [{"node": "For Each Service...", "type": "main", "index": 0}]]}, "Prepare Job": {"main": [[{"node": "For Each Sheet...", "type": "main", "index": 0}]]}, "Get Services": {"main": [[{"node": "Prepare Job", "type": "main", "index": 0}]]}, "Define Sheets": {"main": [[{"node": "Sheets To List...", "type": "main", "index": 0}]]}, "AI Completion1": {"main": [[{"node": "Format QA Pair1", "type": "main", "index": 0}]]}, "Format QA Pair1": {"main": [[{"node": "For Each Question...1", "type": "main", "index": 0}]]}, "Create From Text": {"main": [[{"node": "Update Row Status", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "For Each Service...", "type": "main", "index": 0}]]}, "For Each Sheet...": {"main": [[{"node": "For Each Service...", "type": "main", "index": 0}], [{"node": "Get Services", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Completion1", "type": "ai_languageModel", "index": 0}]]}, "Question to List1": {"main": [[{"node": "For Each Question...1", "type": "main", "index": 0}]]}, "Sheets To List...": {"main": [[{"node": "For Each Sheet...", "type": "main", "index": 0}]]}, "Update Row Status": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "For Each Service...": {"main": [null, [{"node": "If has Data", "type": "main", "index": 0}]]}, "For Each Question...1": {"main": [[{"node": "Questions to Object...1", "type": "main", "index": 0}], [{"node": "Needs AI Completion?1", "type": "main", "index": 0}]]}, "Needs AI Completion?1": {"main": [[{"node": "Format QA Pair1", "type": "main", "index": 0}], [{"node": "AI Completion1", "type": "main", "index": 0}]]}, "Questions to Object...1": {"main": [[{"node": "Format DisplayName + Questions1", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Single Integration Native": {"main": [[{"node": "Question to List1", "type": "main", "index": 0}]]}, "Single Integration Cred-only": {"main": [[{"node": "Question to List1", "type": "main", "index": 0}]]}, "Single Integration Non-native": {"main": [[{"node": "Question to List1", "type": "main", "index": 0}]]}, "Format DisplayName + Questions1": {"main": [[{"node": "Create From Text", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Define Sheets", "type": "main", "index": 0}]]}}}