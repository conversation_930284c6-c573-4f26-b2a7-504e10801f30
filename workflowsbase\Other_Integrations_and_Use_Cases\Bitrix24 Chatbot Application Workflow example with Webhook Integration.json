{"id": "cmGsNvW9bEORABdo", "meta": {"instanceId": "15c09ee9508dd818e298e675375571ba4b871bbb8c420fd01ac9ed7c58622669"}, "name": "Bitrix24 Chatbot Application Workflow example with Webhook Integration", "tags": [{"id": "5YZ9E6AmGZn6WTMa", "name": "Tech demo", "createdAt": "2024-12-28T09:13:02.965Z", "updatedAt": "2024-12-28T09:13:02.965Z"}, {"id": "hEvnK1kMYTPrL3vs", "name": "Bitrix24", "createdAt": "2025-01-04T16:12:36.741Z", "updatedAt": "2025-01-04T16:12:36.741Z"}, {"id": "yKS9RGKLuFUhYFIE", "name": "<PERSON><PERSON><PERSON>", "createdAt": "2025-01-04T16:12:36.757Z", "updatedAt": "2025-01-04T16:12:36.757Z"}], "nodes": [{"id": "ddd802bb-0da0-474d-b1e9-74f247e603e0", "name": "Bitrix24 Handler", "type": "n8n-nodes-base.webhook", "position": [0, 0], "webhookId": "c3ae607d-41f0-42bc-b669-c2c77936d443", "parameters": {"path": "bitrix24/handler.php", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1}, {"id": "5676a53e-6758-4ad5-ace6-e494fa10b6c3", "name": "Credentials", "type": "n8n-nodes-base.set", "position": [200, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "030f8f90-2669-4c20-9eab-c572c4b7c70c", "name": "CLIENT_ID", "type": "string", "value": "local.6779636e712043.37129431"}, {"id": "de9bbb7a-b782-4540-b259-527625db8490", "name": "CLIENT_SECRET", "type": "string", "value": "dTzUfBoTFLxNhuzc1zsnDbCeii98ZaE5By4aQPQEOxLJAS9y6i"}, {"id": "86b7aff7-1e25-4b12-a366-23cf34e5a405", "name": "application_token", "type": "string", "value": "={{ $json.body['auth[application_token]'] }}"}, {"id": "69bbcb1f-ba6e-42eb-be8a-ee0707ce997d", "name": "domain", "type": "string", "value": "={{ $json.body['auth[domain]'] }}\n"}, {"id": "dc1b0515-f06a-4731-b0dc-912a8d04e56b", "name": "access_token", "type": "string", "value": "={{ $json.body['auth[access_token]'] }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "b72c00cf-9f8c-4c2a-9093-b80d82bab85b", "name": "Validate Token", "type": "n8n-nodes-base.if", "position": [400, 0], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "da73d0ba-6eeb-405e-89fe-9d041fd2e0cd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.CLIENT_ID }}", "rightValue": "={{ $json.application_token }}"}, {"id": "4ba90f7b-0299-4097-9ae7-6e4dee428a74", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "1", "rightValue": "1"}]}}, "typeVersion": 2.2}, {"id": "f0feb392-873a-4643-b7ad-0e6d9f877e82", "name": "Route Event", "type": "n8n-nodes-base.switch", "position": [600, 0], "parameters": {"rules": {"values": [{"outputKey": "ONIMBOTMESSAGEADD", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTMESSAGEADD"}]}, "renameOutput": true}, {"outputKey": "ONIMBOTJOINCHAT", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e9125f57-129e-4026-86ff-746d40b92b04", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTJOINCHAT"}]}, "renameOutput": true}, {"outputKey": "ONAPPINSTALL", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2db7bed5-fd88-4900-b8d2-e27b49c2fcca", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONAPPINSTALL"}]}, "renameOutput": true}, {"outputKey": "ONIMBOTDELETE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b708d339-fd46-470d-b0d5-ff2eb405f5ce", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.body.event }}", "rightValue": "ONIMBOTDELETE"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "56fcdc5f-d509-4c9f-a437-79c53add49f8", "name": "Process Message", "type": "n8n-nodes-base.function", "position": [800, 0], "parameters": {"functionCode": "// Process Message Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get message data from the correct path\nconst message = item.json.body['data[PARAMS][MESSAGE]'];\nconst dialogId = item.json.body['data[PARAMS][DIALOG_ID]'];\n\n// Get auth data\nconst auth = {\n access_token: item.json.access_token,\n domain: item.json.domain\n};\n\nif (message.toLowerCase() === \"what's hot\") {\n return {\n json: {\n DIALOG_ID: dialogId,\n MESSAGE: \"Hi! I am an example-bot.\\nI repeat what you say\",\n AUTH: auth.access_token,\n DOMAIN: auth.domain\n }\n };\n} else {\n return {\n json: {\n DIALOG_ID: dialogId,\n MESSAGE: `You said:\\n${message}`,\n AUTH: auth.access_token,\n DOMAIN: auth.domain\n }\n };\n}"}, "typeVersion": 1}, {"id": "a647ed67-c812-4416-8c85-55a681bc7f80", "name": "Process Join", "type": "n8n-nodes-base.function", "position": [800, 160], "parameters": {"functionCode": "// Process Join Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get dialog ID from the correct path\nconst dialogId = item.json.body['data[PARAMS][DIALOG_ID]'];\n\n// Get auth data\nconst auth = {\n access_token: item.json.access_token,\n domain: item.json.domain\n};\n\nreturn {\n json: {\n DIALOG_ID: dialogId,\n MESSAGE: 'Hi! I am an example-bot. I repeat what you say',\n AUTH: auth.access_token,\n DOMAIN: auth.domain\n }\n};"}, "typeVersion": 1}, {"id": "4aac8853-d80e-4201-9f31-7838d18afe71", "name": "Process Install", "type": "n8n-nodes-base.function", "position": [800, 320], "parameters": {"functionCode": "// Process Install Node\nconst items = $input.all();\nconst item = items[0];\n\n// Get the webhook URL from input\nconst handlerBackUrl = item.json.webhookUrl;\n\n// Get auth data directly from item.json\nconst auth = {\n access_token: item.json.access_token,\n application_token: item.json.application_token,\n domain: item.json.domain\n};\n\nreturn {\n json: {\n handler_back_url: handlerBackUrl,\n CODE: 'LocalExampleBot',\n TYPE: 'B',\n EVENT_MESSAGE_ADD: handlerBackUrl,\n EVENT_WELCOME_MESSAGE: handlerBackUrl,\n EVENT_BOT_DELETE: handlerBackUrl,\n PROPERTIES: {\n NAME: 'Bot',\n LAST_NAME: 'Example',\n COLOR: 'AQUA',\n EMAIL: '<EMAIL>',\n PERSONAL_BIRTHDAY: '2020-07-18',\n WORK_POSITION: 'Report on affairs',\n PERSONAL_GENDER: 'M'\n },\n // Use the auth data from item.json\n AUTH: auth.access_token,\n CLIENT_ID: auth.application_token,\n DOMAIN: auth.domain\n }\n};"}, "typeVersion": 1}, {"id": "30922462-255b-4ba6-8167-88aec244fdb1", "name": "Register Bot", "type": "n8n-nodes-base.httpRequest", "position": [1000, 320], "parameters": {"url": "=https://{{ $json.DOMAIN }}/rest/imbot.register?auth={{$json.AUTH}}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "CODE", "value": "LocalExampleBot"}, {"name": "TYPE", "value": "B"}, {"name": "EVENT_MESSAGE_ADD", "value": "={{$json.handler_back_url}}"}, {"name": "EVENT_WELCOME_MESSAGE", "value": "={{$json.handler_back_url}}"}, {"name": "EVENT_BOT_DELETE", "value": "={{$json.handler_back_url}}"}, {"name": "PROPERTIES", "value": "={{ {\n NAME: 'Bot',\n LAST_NAME: 'Example',\n COLOR: 'AQUA',\n EMAIL: '<EMAIL>',\n PERSONAL_BIRTHDAY: '2020-07-18',\n WORK_POSITION: 'Report on affairs',\n PERSONAL_GENDER: 'M'\n} }}"}, {"name": "CLIENT_ID", "value": "={{ $json.CLIENT_ID }}"}, {"name": "CLIENT_SECRET", "value": "={{ $json.AUTH }}"}]}}, "typeVersion": 4.2}, {"id": "8c1c7ebf-d5b3-472e-9d98-34cc65ba86ba", "name": "Send Message", "type": "n8n-nodes-base.httpRequest", "position": [1000, 0], "parameters": {"url": "=https://{{$json.DOMAIN}}/rest/imbot.message.add?auth={{$json.AUTH}}", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "DIALOG_ID", "value": "={{ $json.DIALOG_ID }}"}, {"name": "MESSAGE", "value": "={{ $json.MESSAGE }}"}, {"name": "AUTH", "value": "={{ $json.AUTH }}"}]}}, "typeVersion": 4.2}, {"id": "af0d2b44-53f7-4c4c-9428-d54ebcf41bff", "name": "Send Join Message", "type": "n8n-nodes-base.httpRequest", "position": [1000, 160], "parameters": {"url": "=https://{{$json.DOMAIN}}/rest/imbot.message.add", "method": "POST", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "DIALOG_ID", "value": "={{ $json.DIALOG_ID }}"}, {"name": "MESSAGE", "value": "={{ $json.MESSAGE }}"}, {"name": "AUTH", "value": "={{ $json.AUTH }}"}]}}, "typeVersion": 4.2}, {"id": "9110f66d-1c35-44b4-bc73-18f821b50b71", "name": "Process Delete", "type": "n8n-nodes-base.noOp", "position": [800, 480], "parameters": {}, "typeVersion": 1}, {"id": "81a5fc23-47a4-4ef8-bfb4-31593aed12fd", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "position": [1200, 0], "parameters": {"options": {"responseCode": 200}, "respondWith": "json", "responseBody": "={\n \"result\": true\n}"}, "typeVersion": 1.1}, {"id": "a19f3b0b-496f-4f3d-a9c2-044356070e32", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "position": [400, 160], "parameters": {"options": {"responseCode": 401}, "respondWith": "json", "responseBody": "={{\n \"result\": false,\n \"error\": \"Invalid application token\"\n}}"}, "typeVersion": 1.1}], "active": true, "pinData": {"Bitrix24 Handler": [{"json": {"body": {"ts": "1737037713", "event": "ONIMBOTMESSAGEADD", "auth[scope]": "imbot,im", "auth[domain]": "hgap.bitrix24.eu", "auth[status]": "L", "auth[expires]": "1737041313", "auth[user_id]": "256", "data[USER][ID]": "256", "auth[member_id]": "19acdffbcfadf692f61b677d3d824490", "auth[expires_in]": "3600", "data[USER][NAME]": "Java Tech User", "event_handler_id": "126", "auth[access_token]": "a12589670074bb250066880900000100000007f6bf4682415a014425fed22a6b37af33", "data[USER][GENDER]": "M", "data[USER][IS_BOT]": "N", "auth[refresh_token]": "91a4b0670074bb2500668809000001000000075047004f6b25a0b76236e66bb7316e97", "auth[client_endpoint]": "https://hgap.bitrix24.eu/rest/", "auth[server_endpoint]": "https://oauth.bitrix.info/rest/", "data[BOT][302][scope]": "imbot,im", "data[PARAMS][CHAT_ID]": "6196", "data[PARAMS][MESSAGE]": "Szia!", "data[USER][LAST_NAME]": "Java", "data[BOT][302][BOT_ID]": "302", "data[BOT][302][domain]": "hgap.bitrix24.eu", "data[BOT][302][status]": "L", "data[PARAMS][LANGUAGE]": "en", "data[USER][FIRST_NAME]": "Tech User", "data[USER][IS_NETWORK]": "N", "auth[application_token]": "0d83800efe3a5b2977650e025e0754d5", "data[BOT][302][expires]": "1737041313", "data[BOT][302][user_id]": "302", "data[PARAMS][AUTHOR_ID]": "256", "data[PARAMS][CHAT_TYPE]": "P", "data[PARAMS][DIALOG_ID]": "256", "data[USER][IS_EXTRANET]": "N", "data[BOT][302][BOT_CODE]": "LocalExampleBot", "data[PARAMS][MESSAGE_ID]": "314686", "data[PARAMS][TO_USER_ID]": "302", "data[USER][IS_CONNECTOR]": "N", "data[BOT][302][client_id]": "local.6779636e712043.37129431", "data[BOT][302][member_id]": "19acdffbcfadf692f61b677d3d824490", "data[PARAMS][TEMPLATE_ID]": "09c62e39-23c2-4281-a53f-4a3a76d2cf4a", "data[USER][WORK_POSITION]": "Technical User", "data[BOT][302][expires_in]": "3600", "data[PARAMS][FROM_USER_ID]": "256", "data[PARAMS][MESSAGE_TYPE]": "P", "data[PARAMS][SKIP_COMMAND]": "N", "data[BOT][302][AUTH][scope]": "imbot,im", "data[BOT][302][AUTH][domain]": "hgap.bitrix24.eu", "data[BOT][302][AUTH][status]": "L", "data[BOT][302][access_token]": "a12589670074bb25006688090000012ee0e30782de43659ca7cc172d61e7a91b24b241", "data[PARAMS][SKIP_CONNECTOR]": "N", "data[PARAMS][SKIP_URL_INDEX]": "N", "data[BOT][302][AUTH][expires]": "1737041313", "data[BOT][302][AUTH][user_id]": "302", "data[BOT][302][refresh_token]": "91a4b0670074bb25006688090000012ee0e307bbd7e4e8b80e4c5ba61e3c99f0283f40", "data[PARAMS][COMMAND_CONTEXT]": "TEXTAREA", "data[PARAMS][SILENT_CONNECTOR]": "N", "data[BOT][302][AUTH][client_id]": "local.6779636e712043.37129431", "data[BOT][302][AUTH][member_id]": "19acdffbcfadf692f61b677d3d824490", "data[BOT][302][client_endpoint]": "https://hgap.bitrix24.eu/rest/", "data[BOT][302][server_endpoint]": "https://oauth.bitrix.info/rest/", "data[BOT][302][AUTH][expires_in]": "3600", "data[BOT][302][application_token]": "0d83800efe3a5b2977650e025e0754d5", "data[PARAMS][IMPORTANT_CONNECTOR]": "N", "data[BOT][302][AUTH][access_token]": "a12589670074bb25006688090000012ee0e30782de43659ca7cc172d61e7a91b24b241", "data[BOT][302][AUTH][refresh_token]": "91a4b0670074bb25006688090000012ee0e307bbd7e4e8b80e4c5ba61e3c99f0283f40", "data[BOT][302][AUTH][client_endpoint]": "https://hgap.bitrix24.eu/rest/", "data[BOT][302][AUTH][server_endpoint]": "https://oauth.bitrix.info/rest/", "data[PARAMS][SKIP_COUNTER_INCREMENTS]": "N", "data[BOT][302][AUTH][application_token]": "0d83800efe3a5b2977650e025e0754d5"}, "query": {}, "params": {}, "headers": {"host": "orpheus-dev.h-gap.hu", "x-real-ip": "***********", "user-agent": "Bitrix24 Webhook Engine", "content-type": "application/x-www-form-urlencoded", "content-length": "3711", "accept-encoding": "gzip", "x-forwarded-for": "***********", "x-forwarded-proto": "https", "x-forwarded-scheme": "https"}, "webhookUrl": "REDACTED_WEBHOOK_URL", "executionMode": "production"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "401b00c7-dc0c-4067-9b27-27dc171cc52e", "connections": {"Credentials": {"main": [[{"node": "Validate Token", "type": "main", "index": 0}]]}, "Route Event": {"main": [[{"node": "Process Message", "type": "main", "index": 0}], [{"node": "Process Join", "type": "main", "index": 0}], [{"node": "Process Install", "type": "main", "index": 0}], [{"node": "Process Delete", "type": "main", "index": 0}], [], [], [], [], []]}, "Process Join": {"main": [[{"node": "Send Join Message", "type": "main", "index": 0}]]}, "Register Bot": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Send Message": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Process Delete": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Validate Token": {"main": [[{"node": "Route Event", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Process Install": {"main": [[{"node": "Register Bot", "type": "main", "index": 0}]]}, "Process Message": {"main": [[{"node": "Send Message", "type": "main", "index": 0}]]}, "Bitrix24 Handler": {"main": [[{"node": "Credentials", "type": "main", "index": 0}]]}, "Send Join Message": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}}