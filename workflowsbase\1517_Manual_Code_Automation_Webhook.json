{"id": "IwOOVikQC7cn9VTv", "meta": {"instanceId": "a897062ac3223eacd9c7736276b653c446bc776a63cde2a42a2949ad984f7092"}, "name": "Perform a domain search (single) with Icypeas", "tags": [], "nodes": [{"id": "a8be94cc-c695-4a24-b045-d6716fe6f043", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [4360, 1720], "parameters": {}, "typeVersion": 1}, {"id": "c636d4ed-6310-4f2a-876e-c24d54dc9349", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [4200, 1460], "parameters": {"height": 243.*************, "content": "## Perform a domain search (single) with Icypeas\n\nThis workflow demonstrates how to perform a domain scan using Icypeas. Visit https://icypeas.com to create your account.\n\n\n"}, "typeVersion": 1}, {"id": "a6f4dbe2-478d-426c-b544-60cd97c84901", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [4507, 1536], "parameters": {"width": 506, "height": 1041.*************, "content": "## Authenticates to your Icypeas account\n\nThis code node utilizes your API key, API secret, and User ID to establish a connection with your Icypeas account.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nOpen this node and insert your API Key, API secret, and User ID within the quotation marks. You can locate these credentials on your Icypeas profile at https://app.icypeas.com/bo/profile. Here is the extract of what you have to change :\n\nconst API_KEY = \"**PUT_API_KEY_HERE**\";\nconst API_SECRET = \"**PUT_API_SECRET_HERE**\";\nconst USER_ID = \"**PUT_USER_ID_HERE**\";\n\nDo not change any other line of the code.\n\nIf you are a self-hosted user, follow these steps to activate the crypto module :\n\n1.Access your n8n instance:\nLog in to your n8n instance using your web browser by navigating to the URL of your instance, for example: http://your-n8n-instance.com.\n\n2.Go to Settings:\nIn the top-right corner, click on your username, then select \"Settings.\"\n\n3.Select General Settings:\nIn the left menu, click on \"General.\"\n\n4.Enable the Crypto module:\nScroll down to the \"Additional Node Packages\" section. You will see an option called \"crypto\" with a checkbox next to it. Check this box to enable the Crypto module.\n\n5.Save the changes:\nAt the bottom of the page, click \"Save\" to apply the changes.\n\nOnce you've followed these steps, the Crypto module should be activated for your self-hosted n8n instance. Make sure to save your changes and optionally restart your n8n instance for the changes to take effect.\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "c42e5f50-93dd-48c6-8cfc-c37aefc609a5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [5013, 1540], "parameters": {"width": 492, "height": 748, "content": "## Performs a domain/company scan on your Icypeas account\n\n\nThis node executes an HTTP request (POST) to scan the domain/company you have provided in the body section, using Icypeas.\n\n\n\n\n\n\n\n\n\n\n\n\n\n### You need to create credentials in the HTTP Request node :\n\n➔ In the Credential for <PERSON><PERSON>, click on - Create new Credential -.\n➔ In the Name section, write “Authorization”\n➔ In the Value section, select expression (located just above the field on the right when you hover on top of it) and write {{ $json.api.key + ':' + $json.api.signature }} .\n➔ Then click on “Save” to save the changes.\n\n### To scan the domain/company :\n\n➔ go to the Body Parameters section,\n➔ create a new parameter,\n➔ enter \"domainOrCompany\" in the Name field.\n➔ put the domain/company you want to scan in the Value field.\n\nYou will find the result here : https://app.icypeas.com/bo/singlesearch?task=domain-search\n"}, "typeVersion": 1}, {"id": "cdee270f-b6c0-4f60-ba41-f2ee9e091eaa", "name": "Run domain scan (single)", "type": "n8n-nodes-base.httpRequest", "position": [5080, 1720], "parameters": {"url": "={{ $json.api.url }}", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "domainOrCompany", "value": "=google"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "X-ROCK-TIMESTAMP", "value": "={{ $json.api.timestamp }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "KGXtUrqC6lNLwW2w", "name": "Header Auth account"}}, "typeVersion": 4.1}, {"id": "b066f965-a3a7-45cb-96c2-ca3bdf2bb231", "name": "Authenticates to your Icypeas account", "type": "n8n-nodes-base.code", "position": [4700, 1720], "parameters": {"jsCode": "const BASE_URL = \"https://app.icypeas.com\";\nconst PATH = \"/api/domain-search\";\nconst METHOD = \"POST\";\n\n// Change here\nconst API_KEY = \"PUT_API_KEY_HERE\";\nconst API_SECRET = \"PUT_API_SECRET_HERE\";\nconst USER_ID = \"PUT_USER_ID_HERE\";\n////////////////\n\nconst genSignature = (\n    path,\n    method,\n    secret,\n    timestamp = new Date().toISOString()\n) => {\n    const Crypto = require('crypto');\n    const payload = `${method}${path}${timestamp}`.toLowerCase();\n    const sign = Crypto.createHmac(\"sha1\", secret).update(payload).digest(\"hex\");\n\n    return sign;\n};\n\nconst fullPath = `${BASE_URL}${PATH}`;\n$input.first().json.api = {\n  timestamp: new Date().toISOString(),\n  secret: API_SECRET,\n  key: API_KEY,\n  userId: USER_ID,\n  url: fullPath,\n};\n$input.first().json.api.signature = genSignature(PATH, METHOD, API_SECRET, $input.first().json.api.timestamp);\nreturn $input.first();"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "499f7092-5891-46cb-9756-0a11f75242f4", "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Authenticates to your Icypeas account", "type": "main", "index": 0}]]}, "Authenticates to your Icypeas account": {"main": [[{"node": "Run domain scan (single)", "type": "main", "index": 0}]]}}}