{"nodes": [{"id": "9cc26a42-eb43-40c4-b507-cbaf187a5e15", "name": "Get New Message", "type": "n8n-nodes-base.telegramTrigger", "position": [1120, 500], "webhookId": "464f0a75-56d1-402f-8b12-b358452e9736", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "rI0zyfIYVIyXt2fL", "name": "Telegram Club"}}, "typeVersion": 1.1}, {"id": "098b6fcf-7cb6-4730-8892-949fedc946b3", "name": "OPENAI - Create thread", "type": "n8n-nodes-base.httpRequest", "position": [1740, 640], "parameters": {"url": "https://api.openai.com/v1/threads", "method": "POST", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "OpenAI-Beta", "value": "assistants=v2"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "zJhr5piyEwVnWtaI", "name": "OpenAi club"}}, "typeVersion": 4.2}, {"id": "fa157f8c-b776-4b20-bfaf-c17460383505", "name": "Create User", "type": "n8n-nodes-base.supabase", "position": [1900, 640], "parameters": {"tableId": "telegram_users", "fieldsUi": {"fieldValues": [{"fieldId": "telegram_id", "fieldValue": "={{ $('Get New Message').item.json.message.chat.id }}"}, {"fieldId": "openai_thread_id", "fieldValue": "={{ $('OPENAI - Create thread').item.json.id }}"}]}}, "credentials": {"supabaseApi": {"id": "QBhcokohbJHfQZ9A", "name": "Supabase club"}}, "typeVersion": 1}, {"id": "115e417f-5962-409b-8adf-ff236eb9ce2e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2080, 500], "parameters": {}, "typeVersion": 3}, {"id": "ba5c7385-8c80-43c8-9de2-430175bda70b", "name": "OPENAI - Send message", "type": "n8n-nodes-base.httpRequest", "position": [2240, 500], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Merge').item.json.openai_thread_id }}/messages ", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "role", "value": "user"}, {"name": "content", "value": "={{ $('Get New Message').item.json.message.text }}"}]}, "headerParameters": {"parameters": [{"name": "OpenAI-Beta", "value": "assistants=v2"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "fLfRtaXbR0EVD0pl", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "024832bc-3d42-4879-a57f-b23e962b4c69", "name": "OPENAI - Run assistant", "type": "n8n-nodes-base.httpRequest", "position": [2440, 500], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Merge').item.json.openai_thread_id }}/runs", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "assistant_id", "value": "asst_b0QhuzySG6jofHFdzPZD7WEz"}, {"name": "stream", "value": "={{true}}"}]}, "headerParameters": {"parameters": [{"name": "OpenAI-Beta", "value": "assistants=v2"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "fLfRtaXbR0EVD0pl", "name": "OpenAi account"}}, "typeVersion": 4.2}, {"id": "bc191e2b-15f4-45b7-af2e-19ed1639b7f5", "name": "OPENAI - Get messages", "type": "n8n-nodes-base.httpRequest", "position": [2640, 500], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Merge').item.json.openai_thread_id }}/messages", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "OpenAI-Beta", "value": "assistants=v2"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "zJhr5piyEwVnWtaI", "name": "OpenAi club"}}, "typeVersion": 4.2}, {"id": "c22e05e5-f0a7-4a09-a864-acfc58469b30", "name": "Send Message to User", "type": "n8n-nodes-base.telegram", "position": [2840, 500], "parameters": {"text": "={{ $('OPENAI - Get messages').item.json.data[0].content[0].text.value }}", "chatId": "={{ $('Get New Message').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "rI0zyfIYVIyXt2fL", "name": "Telegram Club"}}, "typeVersion": 1.2}, {"id": "0673be1f-3cae-42a0-9c62-1ed570859043", "name": "If User exists", "type": "n8n-nodes-base.if", "position": [1560, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b6e69a1f-eb42-4ef6-b80c-3167f1b8c830", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.id }}", "rightValue": ""}]}}, "typeVersion": 2.1}, {"id": "a4916f54-ae6b-495d-979b-92dca965e3bb", "name": "Find User", "type": "n8n-nodes-base.supabase", "position": [1360, 500], "parameters": {"filters": {"conditions": [{"keyName": "telegram_id", "keyValue": "={{ $json.message.chat.id }}", "condition": "eq"}]}, "tableId": "telegram_users", "operation": "getAll"}, "credentials": {"supabaseApi": {"id": "QBhcokohbJHfQZ9A", "name": "Supabase club"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "6d01d7ed-e96b-47cf-9a5f-46608031baa2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1300, 800], "parameters": {"color": 7, "width": 600.************, "height": 213.15921994594194, "content": "SQL query to create table in Supabase:\n\n```\ncreate table\n public.telegram_users (\n id uuid not null default gen_random_uuid (),\n date_created timestamp with time zone not null default (now() at time zone 'utc'::text),\n telegram_id bigint null,\n openai_thread_id text null,\n constraint telegram_users_pkey primary key (id)\n ) tablespace pg_default;\n```"}, "typeVersion": 1}, {"id": "1a996da0-6022-48d7-ba40-1d137547a3d7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2340, 360], "parameters": {"color": 3, "width": 282.075050779723, "height": 80, "content": "Create assistant in [OpenAI](https://platform.openai.com/assistants).\n\n**Specify own assistant id here**\n"}, "typeVersion": 1}, {"id": "b24d2008-7950-41f0-a7fa-50360c0c6854", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1040, 380], "parameters": {"color": 3, "width": 235.09282368774151, "height": 80, "content": "Create own Telegram bot in [<PERSON><PERSON><PERSON> bot](https://t.me/botfather)"}, "typeVersion": 1}, {"id": "9eb2491e-5ad9-4015-8ed9-611e72924503", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1300, 680], "parameters": {"color": 3, "height": 80, "content": "Create table in [Supabase](https://supabase.com) with SQL query"}, "typeVersion": 1}, {"id": "884b5a1b-007c-4752-becc-46c8fc58db92", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [200, 120], "parameters": {"color": 7, "width": 280.2462120317618, "height": 438.5821431288714, "content": "### Set up steps\n1. **Create a Telegram Bot** using the [<PERSON><PERSON><PERSON>](https://t.me/botfather) and obtain the bot token.\n2. **Set up Supabase:**\n\t1. Create a new project and generate a ```SUPABASE_URL``` and ```SUPABASE_KEY```.\n\t2. Create a new table named ```telegram_users``` with the following SQL query:\n```\ncreate table\n public.telegram_users (\n id uuid not null default gen_random_uuid (),\n date_created timestamp with time zone not null default (now() at time zone 'utc'::text),\n telegram_id bigint null,\n openai_thread_id text null,\n constraint telegram_users_pkey primary key (id)\n ) tablespace pg_default;\n```\n3. **OpenAI Setup:**\n\t1. Create an OpenAI assistant and obtain the ```OPENAI_API_KEY```.\n\t2. Customize your assistant’s personality or use cases according to your requirements.\n4. **Environment Configuration in n8n:**\n\t1. Configure the Telegram, Supabase, and OpenAI nodes with the appropriate credentials.\n\t2. Set up triggers for receiving messages and handling conversation logic.\n\t3. Set up OpenAI assistant ID in \"++OPENAI - Run assistant++\" node."}, "typeVersion": 1}, {"id": "02db77ac-4909-4a56-a558-03c86d8b8552", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [200, -400], "parameters": {"color": 7, "width": 636.2128494576581, "height": 494.9629292914819, "content": "![5min Logo](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Untitled%20(1500%20x%20300%20px).png)\n## AI Telegram Bot with Supabase memory\n**Made by [<PERSON>](https://www.linkedin.com/in/marklowcoding/) from community [5minAI](https://www.skool.com/5minai-2861)**\n\nMany simple chatbots lack context awareness and user memory. This workflow solves that by integrating Supabase to keep track of user sessions (via ```telegram_id``` and ```openai_thread_id```), allowing the bot to maintain continuity and context in conversations, leading to a more human-like and engaging experience.\n\nThis Telegram bot template connects with OpenAI to answer user queries while storing and retrieving user information from a Supabase database. The memory component ensures that the bot can reference past interactions, making it suitable for use cases such as customer support, virtual assistants, or any application where context retention is crucial.\n\n"}, "typeVersion": 1}, {"id": "a991a7c9-ea5f-4a25-aa92-6dc2fce11b05", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [500, 120], "parameters": {"color": 7, "width": 330.5152611046425, "height": 240.6839895136402, "content": "### ... or watch set up video [5 min]\n[![Youtube Thumbnail](https://cflobdhpqwnoisuctsoc.supabase.co/storage/v1/object/public/my_storage/Youtube%20thumb%20(3).png)](https://www.youtube.com/watch?v=kS41gut8l0g)\n"}, "typeVersion": 1}], "pinData": {"Merge": [{"id": "4a5d71a4-a2f7-43e2-936f-37ee5bf5cc9e", "telegram_id": 1468754364, "date_created": "2024-10-04T08:29:07.458869+00:00", "openai_thread_id": null}], "Find User": [{"id": "4a5d71a4-a2f7-43e2-936f-37ee5bf5cc9e", "telegram_id": 1468754364, "date_created": "2024-10-04T08:29:07.458869+00:00", "openai_thread_id": null}], "Get New Message": [{"message": {"chat": {"id": 1468754364, "type": "private", "username": "low_code", "first_name": "<PERSON>"}, "date": 1727961249, "from": {"id": 1468754364, "is_bot": false, "username": "low_code", "first_name": "<PERSON>", "language_code": "en"}, "text": "Hello, how are you?", "entities": [{"type": "bot_command", "length": 6, "offset": 0}], "message_id": 3}, "update_id": 412281353}], "Send Message to User": [{"ok": true, "result": {"chat": {"id": 1468754364, "type": "private", "username": "low_code", "first_name": "<PERSON>"}, "date": 1727971919, "from": {"id": 7999029315, "is_bot": true, "username": "test241234_bot", "first_name": "Test bot"}, "text": "Hello! I'm just a program, but I'm here and ready to help you. How can I assist you today?", "message_id": 7}}], "OPENAI - Get messages": [{"data": [{"id": "msg_C7aXbSotAl6xCxjR9avi4wUz", "role": "assistant", "object": "thread.message", "run_id": "run_9avgP4lZ1FRSsL3y9UO8HPa1", "content": [{"text": {"value": "Hello! I'm just a program, but I'm here and ready to help you. How can I assist you today?", "annotations": []}, "type": "text"}], "metadata": {}, "thread_id": "thread_laO8JLPW6L1upYHW6fSRj8Bt", "created_at": 1727971739, "attachments": [], "assistant_id": "asst_b0QhuzySG6jofHFdzPZD7WEz"}, {"id": "msg_fVGPVHR03QKheHXh54SFpmpm", "role": "user", "object": "thread.message", "run_id": null, "content": [{"text": {"value": "Hello, how are you?", "annotations": []}, "type": "text"}], "metadata": {}, "thread_id": "thread_laO8JLPW6L1upYHW6fSRj8Bt", "created_at": 1727971467, "attachments": [], "assistant_id": null}], "object": "list", "last_id": "msg_fVGPVHR03QKheHXh54SFpmpm", "first_id": "msg_C7aXbSotAl6xCxjR9avi4wUz", "has_more": false}], "OPENAI - Send message": [{"id": "msg_fVGPVHR03QKheHXh54SFpmpm", "role": "user", "object": "thread.message", "run_id": null, "content": [{"text": {"value": "Hello, how are you?", "annotations": []}, "type": "text"}], "metadata": {}, "thread_id": "thread_laO8JLPW6L1upYHW6fSRj8Bt", "created_at": 1727971467, "attachments": [], "assistant_id": null}], "OPENAI - Create thread": [{"id": "thread_laO8JLPW6L1upYHW6fSRj8Bt", "object": "thread", "metadata": {}, "created_at": 1727971362, "tool_resources": {}}], "OPENAI - Run assistant": [{"data": "event: thread.run.created\ndata: {\"id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"object\":\"thread.run\",\"created_at\":1727971737,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"status\":\"queued\",\"started_at\":null,\"expires_at\":1727972337,\"cancelled_at\":null,\"failed_at\":null,\"completed_at\":null,\"required_action\":null,\"last_error\":null,\"model\":\"gpt-4o-mini\",\"instructions\":\"You are ChatGPT\",\"tools\":[],\"tool_resources\":{\"code_interpreter\":{\"file_ids\":[]}},\"metadata\":{},\"temperature\":1.0,\"top_p\":1.0,\"max_completion_tokens\":null,\"max_prompt_tokens\":null,\"truncation_strategy\":{\"type\":\"auto\",\"last_messages\":null},\"incomplete_details\":null,\"usage\":null,\"response_format\":\"auto\",\"tool_choice\":\"auto\",\"parallel_tool_calls\":true}\n\nevent: thread.run.queued\ndata: {\"id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"object\":\"thread.run\",\"created_at\":1727971737,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"status\":\"queued\",\"started_at\":null,\"expires_at\":1727972337,\"cancelled_at\":null,\"failed_at\":null,\"completed_at\":null,\"required_action\":null,\"last_error\":null,\"model\":\"gpt-4o-mini\",\"instructions\":\"You are ChatGPT\",\"tools\":[],\"tool_resources\":{\"code_interpreter\":{\"file_ids\":[]}},\"metadata\":{},\"temperature\":1.0,\"top_p\":1.0,\"max_completion_tokens\":null,\"max_prompt_tokens\":null,\"truncation_strategy\":{\"type\":\"auto\",\"last_messages\":null},\"incomplete_details\":null,\"usage\":null,\"response_format\":\"auto\",\"tool_choice\":\"auto\",\"parallel_tool_calls\":true}\n\nevent: thread.run.in_progress\ndata: {\"id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"object\":\"thread.run\",\"created_at\":1727971737,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"status\":\"in_progress\",\"started_at\":1727971738,\"expires_at\":1727972337,\"cancelled_at\":null,\"failed_at\":null,\"completed_at\":null,\"required_action\":null,\"last_error\":null,\"model\":\"gpt-4o-mini\",\"instructions\":\"You are ChatGPT\",\"tools\":[],\"tool_resources\":{\"code_interpreter\":{\"file_ids\":[]}},\"metadata\":{},\"temperature\":1.0,\"top_p\":1.0,\"max_completion_tokens\":null,\"max_prompt_tokens\":null,\"truncation_strategy\":{\"type\":\"auto\",\"last_messages\":null},\"incomplete_details\":null,\"usage\":null,\"response_format\":\"auto\",\"tool_choice\":\"auto\",\"parallel_tool_calls\":true}\n\nevent: thread.run.step.created\ndata: {\"id\":\"step_b0iFvL1q1UEZDfBRbbNTiulO\",\"object\":\"thread.run.step\",\"created_at\":1727971739,\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"type\":\"message_creation\",\"status\":\"in_progress\",\"cancelled_at\":null,\"completed_at\":null,\"expires_at\":1727972337,\"failed_at\":null,\"last_error\":null,\"step_details\":{\"type\":\"message_creation\",\"message_creation\":{\"message_id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\"}},\"usage\":null}\n\nevent: thread.run.step.in_progress\ndata: {\"id\":\"step_b0iFvL1q1UEZDfBRbbNTiulO\",\"object\":\"thread.run.step\",\"created_at\":1727971739,\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"type\":\"message_creation\",\"status\":\"in_progress\",\"cancelled_at\":null,\"completed_at\":null,\"expires_at\":1727972337,\"failed_at\":null,\"last_error\":null,\"step_details\":{\"type\":\"message_creation\",\"message_creation\":{\"message_id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\"}},\"usage\":null}\n\nevent: thread.message.created\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message\",\"created_at\":1727971739,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"status\":\"in_progress\",\"incomplete_details\":null,\"incomplete_at\":null,\"completed_at\":null,\"role\":\"assistant\",\"content\":[],\"attachments\":[],\"metadata\":{}}\n\nevent: thread.message.in_progress\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message\",\"created_at\":1727971739,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"status\":\"in_progress\",\"incomplete_details\":null,\"incomplete_at\":null,\"completed_at\":null,\"role\":\"assistant\",\"content\":[],\"attachments\":[],\"metadata\":{}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\"Hello\",\"annotations\":[]}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\"!\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" I'm\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" just\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" a\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" program\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\",\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" but\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" I'm\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" here\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" and\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" ready\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" to\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" help\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" you\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\".\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" How\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" can\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" I\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" assist\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" you\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\" today\"}}]}}\n\nevent: thread.message.delta\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message.delta\",\"delta\":{\"content\":[{\"index\":0,\"type\":\"text\",\"text\":{\"value\":\"?\"}}]}}\n\nevent: thread.message.completed\ndata: {\"id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\",\"object\":\"thread.message\",\"created_at\":1727971739,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"status\":\"completed\",\"incomplete_details\":null,\"incomplete_at\":null,\"completed_at\":1727971740,\"role\":\"assistant\",\"content\":[{\"type\":\"text\",\"text\":{\"value\":\"Hello! I'm just a program, but I'm here and ready to help you. How can I assist you today?\",\"annotations\":[]}}],\"attachments\":[],\"metadata\":{}}\n\nevent: thread.run.step.completed\ndata: {\"id\":\"step_b0iFvL1q1UEZDfBRbbNTiulO\",\"object\":\"thread.run.step\",\"created_at\":1727971739,\"run_id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"type\":\"message_creation\",\"status\":\"completed\",\"cancelled_at\":null,\"completed_at\":1727971740,\"expires_at\":1727972337,\"failed_at\":null,\"last_error\":null,\"step_details\":{\"type\":\"message_creation\",\"message_creation\":{\"message_id\":\"msg_C7aXbSotAl6xCxjR9avi4wUz\"}},\"usage\":{\"prompt_tokens\":39,\"completion_tokens\":25,\"total_tokens\":64}}\n\nevent: thread.run.completed\ndata: {\"id\":\"run_9avgP4lZ1FRSsL3y9UO8HPa1\",\"object\":\"thread.run\",\"created_at\":1727971737,\"assistant_id\":\"asst_b0QhuzySG6jofHFdzPZD7WEz\",\"thread_id\":\"thread_laO8JLPW6L1upYHW6fSRj8Bt\",\"status\":\"completed\",\"started_at\":1727971738,\"expires_at\":null,\"cancelled_at\":null,\"failed_at\":null,\"completed_at\":1727971740,\"required_action\":null,\"last_error\":null,\"model\":\"gpt-4o-mini\",\"instructions\":\"You are ChatGPT\",\"tools\":[],\"tool_resources\":{\"code_interpreter\":{\"file_ids\":[]}},\"metadata\":{},\"temperature\":1.0,\"top_p\":1.0,\"max_completion_tokens\":null,\"max_prompt_tokens\":null,\"truncation_strategy\":{\"type\":\"auto\",\"last_messages\":null},\"incomplete_details\":null,\"usage\":{\"prompt_tokens\":39,\"completion_tokens\":25,\"total_tokens\":64},\"response_format\":\"auto\",\"tool_choice\":\"auto\",\"parallel_tool_calls\":true}\n\nevent: done\ndata: [DONE]\n\n"}]}, "connections": {"Merge": {"main": [[{"node": "OPENAI - Send message", "type": "main", "index": 0}]]}, "Find User": {"main": [[{"node": "If User exists", "type": "main", "index": 0}]]}, "Create User": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "If User exists": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "OPENAI - Create thread", "type": "main", "index": 0}]]}, "Get New Message": {"main": [[{"node": "Find User", "type": "main", "index": 0}]]}, "OPENAI - Get messages": {"main": [[{"node": "Send Message to User", "type": "main", "index": 0}]]}, "OPENAI - Send message": {"main": [[{"node": "OPENAI - Run assistant", "type": "main", "index": 0}]]}, "OPENAI - Create thread": {"main": [[{"node": "Create User", "type": "main", "index": 0}]]}, "OPENAI - Run assistant": {"main": [[{"node": "OPENAI - Get messages", "type": "main", "index": 0}]]}}}