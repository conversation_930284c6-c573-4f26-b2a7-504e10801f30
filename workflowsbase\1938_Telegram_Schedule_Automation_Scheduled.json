{"id": "02GdRzvsuHmSSgBw", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "#️⃣Nostr #damus AI Powered Reporting + Gmail + Telegram", "tags": [], "nodes": [{"id": "e9c4c7bf-0cce-456e-9b95-726669e4b260", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-500, -60], "parameters": {}, "typeVersion": 1}, {"id": "b8f57e15-8a6e-4a29-a6e8-745bebbd1f44", "name": "Get HTML", "type": "n8n-nodes-base.markdown", "position": [880, -840], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.text }}"}, "typeVersion": 1}, {"id": "8b212119-9b69-449c-8a3b-4fdc5b085f30", "name": "Gmail Themes", "type": "n8n-nodes-base.gmail", "position": [1080, -840], "webhookId": "e07f9378-bfa5-48ac-88fd-0ef88a725ede", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {"appendAttribution": false}, "subject": "#damus"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "b7fc214b-72cb-4caf-8563-7b2f13a1110d", "name": "Get HTML Report", "type": "n8n-nodes-base.markdown", "position": [880, 80], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.text }}"}, "typeVersion": 1}, {"id": "dd7580bc-f97c-4ad1-8556-2329f88bea75", "name": "#damus Themes List", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, -400], "parameters": {"text": "=Extract a list of themes from this: {{ $json.text }}\n\nDo not include any preamble or further explanation.", "promptType": "define"}, "typeVersion": 1.5}, {"id": "60a9d8fe-4ba0-4450-8073-4108b832981e", "name": "#damus Thread Themes", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, -840], "parameters": {"text": "=Tell me the theme and highlight some common threads associated with these Nostr threads that are all #damus.  Specifically mention the main reason #damus is hashtagged.  These are the threads: {{ $json.content.toJsonString() }}", "promptType": "define"}, "typeVersion": 1.5}, {"id": "72ab08a7-f729-46e3-8a4d-56005cabaf17", "name": "#damus Themes & Threads Report", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, 80], "parameters": {"text": "=**Task:** Analyze the attached file containing Nostr threads using the hashtag #damus. Provide a detailed report with examples thread based on the following themes.  Got deep and seek out the underlying motivation of the users who posted the threads: \n\n## Themes\n{{ $json.text }}\n\n1. **Overall Theme:** Summarize the central topic(s) discussed across the threads.\n2. **Common Threads:** Identify recurring topics or ideas that unify the posts.\n3. **Key Highlights:** Extract specific examples or quotes that illustrate prominent themes.\n4. **Insights and Observations:** Offer insights on how the #damus community engages with the app and its ecosystem.\n5. **Suggestions for Improvement:** If applicable, suggest ways to enhance user experience or community engagement based on the analysis.\n\n**Requirements:**\n- Expand on each theme with comprehensive details and analysis.\n- Use bullet points or numbered lists for clarity.\n- Include relevant quotes or examples from the text to support your analysis.\n- Ensure your response is detailed, well-structured, and easy to read.\n\n**Context:** The analysis should focus on understanding how users interact with Damus, their appreciation for its features, challenges they face, and how it fits into the broader Nostr ecosystem.\n\n## Nostr thread with hashtag #damus: \n{{ $json.content.toJsonString() }}\n\n", "promptType": "define"}, "typeVersion": 1.5}, {"id": "55362e03-ca0b-4f5e-a7ff-02828522fc7d", "name": "gemini-2.0-flash-lite-preview", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [600, -680], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/gemini-2.0-flash-lite-preview"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "7f457b3f-d39b-4062-ada0-5e81f3768857", "name": "gemini-2.0-flash-lite-preview1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [600, -240], "parameters": {"options": {"temperature": 0.4}, "modelName": "models/gemini-2.0-flash-lite-preview"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "bd68e36a-2fa7-4b78-96d8-9c4f97388249", "name": "gemini-2.0-flash-lite-preview2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [600, 240], "parameters": {"options": {"temperature": 0.4}, "modelName": "models/gemini-2.0-flash-lite-preview"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "24f378ca-8a10-441f-886d-136314fa30de", "name": "Gmail Report", "type": "n8n-nodes-base.gmail", "position": [1080, 80], "webhookId": "e07f9378-bfa5-48ac-88fd-0ef88a725ede", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {"appendAttribution": false}, "subject": "#damus"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "f4814872-577a-4243-ac1b-e152e147dca0", "name": "Aggregate #damus Content", "type": "n8n-nodes-base.aggregate", "position": [120, -140], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}]}}, "typeVersion": 1}, {"id": "d2079c9e-b743-4353-bda9-e269168f5461", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [360, -940], "parameters": {"color": 6, "width": 960, "height": 420, "content": "## #dam<PERSON>hreads Themes"}, "typeVersion": 1}, {"id": "5f69afb5-6e3c-4f65-84bb-8c1f4544b2c5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [360, -480], "parameters": {"color": 5, "width": 520, "height": 420, "content": "## #dam<PERSON>hreads Themes"}, "typeVersion": 1}, {"id": "6de3d9d2-98be-4102-9ed5-cda48b37eee7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [360, -20], "parameters": {"color": 4, "width": 960, "height": 420, "content": "## #damus Threads & Threads Report"}, "typeVersion": 1}, {"id": "42f333ce-bdd7-4950-9ef1-ae797a671f5d", "name": "Merge Themes and Content", "type": "n8n-nodes-base.merge", "position": [1000, -160], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "7ff77e60-03ed-4937-b923-74a7f588fd2a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-500, -260], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "d1939a96-1e68-4d90-a456-55852c941e28", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-280, -580], "parameters": {"color": 6, "width": 340, "height": 700, "content": "## Get Nostr Threads with Hashtag #damus\n\nThe social network you control\nYour very own social network for your friends or business.\nAvailable Now on iOS, iPad and macOS (M1/M2)\n\nhttps://nostr.com/\nhttps://damus.io/\nhttps://damus.io/notedeck/\n\n### n8n Community Node https://github.com/ocknamo/n8n-nodes-nostrobots\n"}, "typeVersion": 1}, {"id": "89905442-bf8d-40d2-a9b1-fb3cf3a2ac44", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [940, -640], "parameters": {"width": 320, "height": 280, "content": "## Telegram \n"}, "typeVersion": 1}, {"id": "aee0f3eb-7b0e-4df1-968d-5abe1c22e26a", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [940, 280], "parameters": {"width": 320, "height": 280, "content": "## Telegram \n"}, "typeVersion": 1}, {"id": "f6b00109-74ef-4522-b568-6426b054bea3", "name": "Telegram Themes", "type": "n8n-nodes-base.telegram", "position": [1040, -560], "webhookId": "8406b3d2-5ac6-452d-847f-c0886c8cd058", "parameters": {"text": "={{ $json.text.slice(0, 4000) }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "3e7e9c70-43c6-4074-be9a-2f5ed6c4fb0e", "name": "Telegram Themes & Threads", "type": "n8n-nodes-base.telegram", "position": [1040, 360], "webhookId": "8406b3d2-5ac6-452d-847f-c0886c8cd058", "parameters": {"text": "={{ $json.text.slice(0, 4000) }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "5bc52456-7bbc-445a-8ffd-f47403a4b978", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-580, -340], "parameters": {"color": 4, "width": 260, "height": 460, "content": "## Try Me!"}, "typeVersion": 1}, {"id": "3b61555e-4e20-41d2-8fb7-490a2488f5f2", "name": "Nostr Read #damus", "type": "n8n-nodes-nostrobots.nostrobotsread", "position": [-160, -140], "parameters": {"from": 180, "hashtag": "#damus", "strategy": "hashtag"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "06d6edc0-ed5c-48d1-abe6-22b04368d19b", "connections": {"Get HTML": {"main": [[{"node": "Gmail Themes", "type": "main", "index": 0}]]}, "Get HTML Report": {"main": [[{"node": "Gmail Report", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Nostr Read #damus", "type": "main", "index": 0}]]}, "Nostr Read #damus": {"main": [[{"node": "Aggregate #damus Content", "type": "main", "index": 0}]]}, "#damus Themes List": {"main": [[{"node": "Merge Themes and Content", "type": "main", "index": 0}]]}, "#damus Thread Themes": {"main": [[{"node": "Get HTML", "type": "main", "index": 0}, {"node": "#damus Themes List", "type": "main", "index": 0}, {"node": "Telegram Themes", "type": "main", "index": 0}]]}, "Aggregate #damus Content": {"main": [[{"node": "#damus Thread Themes", "type": "main", "index": 0}, {"node": "Merge Themes and Content", "type": "main", "index": 1}]]}, "Merge Themes and Content": {"main": [[{"node": "#damus Themes & Threads Report", "type": "main", "index": 0}]]}, "gemini-2.0-flash-lite-preview": {"ai_languageModel": [[{"node": "#damus Thread Themes", "type": "ai_languageModel", "index": 0}]]}, "#damus Themes & Threads Report": {"main": [[{"node": "Get HTML Report", "type": "main", "index": 0}, {"node": "Telegram Themes & Threads", "type": "main", "index": 0}]]}, "gemini-2.0-flash-lite-preview1": {"ai_languageModel": [[{"node": "#damus Themes List", "type": "ai_languageModel", "index": 0}]]}, "gemini-2.0-flash-lite-preview2": {"ai_languageModel": [[{"node": "#damus Themes & Threads Report", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Nostr Read #damus", "type": "main", "index": 0}]]}}}