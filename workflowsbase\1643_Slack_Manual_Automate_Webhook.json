{"id": "SHpLY12UobbcWRnl", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Slack AI Chatbot with RAG for company staff", "tags": [], "nodes": [{"id": "df994f64-af5b-49f5-ad83-5c4b69983d41", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-780, 340], "parameters": {"text": "={{ $json.blocks[0].elements[0].elements[1].text }}", "options": {"systemMessage": "=You are an AI assistant  connected to the company's internal knowledge base through a RAG (Retrieval Augmented Generation) system. Your purpose is to help team members quickly find and understand information from company documents.\n\nCORE RESPONSIBILITIES:\n- Respond to queries about company policies, procedures, documentation, and internal knowledge\n- Provide concise, accurate information retrieved from the company's document repository\n- Format responses appropriately for Slack (use markdown for clarity)\n- Cite the specific document source when providing information\n\nINTERACTION GUIDELINES:\n- Keep responses brief and to the point (aim for 3-5 sentences when possible)\n- Use bullet points for lists or step-by-step instructions\n- Include direct quotes from documents when relevant, using > for blockquotes\n- When unable to find information, clearly state this and suggest alternative resources\n\nTECHNICAL CONTEXT:\n- You receive queries through Slack messages\n- You use the RAG tool in n8n to search and retrieve relevant document sections\n- All responses should be crafted for readability on <PERSON><PERSON><PERSON>'s interface\n\nRESPONSE STRUCTURE:\n1. Direct answer to the question (1-2 sentences)\n2. Supporting details from retrieved documents (2-3 sentences or bullet points)\n3. Source citation (document name and date if available)\n4. Follow-up suggestion if applicable (1 sentence)\n\nAlways prioritize accuracy over speed. If multiple documents contain relevant information, synthesize the most important points rather than providing all details. If the query is ambiguous, ask a clarifying question before searching.\n\nRemember that you are a tool to empower employees, not replace human judgment. When questions involve complex decision-making, provide the relevant information and encourage the user to consult with appropriate team members.\n\nDate; {{ $now }}"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "047141fc-a7a0-4532-ae45-da0f2cc27b69", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-720, 600], "parameters": {"sessionKey": "={{ $('Get message').item.json.channel }}_{{ $('Get message').item.json.blocks[0].elements[0].elements[0].user_id }}", "sessionIdType": "customKey", "contextWindowLength": 10}, "typeVersion": 1.3}, {"id": "f7da4458-3dc5-43b8-a97d-dac3e599543c", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-460, 800], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "4zwP0MSr8zkNvvV9", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "14a6052f-e619-4d19-99aa-42253c45a913", "name": "RAG", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-420, 620], "parameters": {"mode": "retrieve-as-tool", "topK": 10, "options": {}, "toolName": "company_info", "toolDescription": "Get business documents", "qdrantCollection": {"__rl": true, "mode": "id", "value": "COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1.1}, {"id": "c6334fd2-0d54-4980-857e-079be08959a5", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [-560, 600], "parameters": {}, "typeVersion": 1}, {"id": "87a629b9-980f-4d0d-9fee-5efa560770d2", "name": "Get message", "type": "n8n-nodes-base.slackTrigger", "position": [-1040, 340], "webhookId": "3146b3e9-4cfc-493f-882c-57c865380115", "parameters": {"options": {}, "trigger": ["app_mention"], "channelId": {"__rl": true, "mode": "list", "value": "C08L6SEPWMB", "cachedResultName": "n8n-test"}}, "credentials": {"slackApi": {"id": "QjSyGP8ykppazXDW", "name": "Slack account (Token)"}}, "typeVersion": 1}, {"id": "939b309d-1828-4159-b1dc-4a1629069c37", "name": "Send message", "type": "n8n-nodes-base.slack", "position": [-420, 340], "webhookId": "946ab278-f815-4bd3-a20d-49ba59d76659", "parameters": {"text": "={{ $json.output }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08L6SEPWMB", "cachedResultName": "n8n-test"}, "otherOptions": {"mrkdwn": true, "thread_ts": {"replyValues": {"thread_ts": "={{ $('Get message').item.json.event_ts }}", "reply_broadcast": true}}, "unfurl_links": true, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "QjSyGP8ykppazXDW", "name": "Slack account (Token)"}}, "typeVersion": 2.3}, {"id": "50be03ea-ab0c-48cb-b95a-b096e51c3d16", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1120, -1020], "parameters": {}, "typeVersion": 1}, {"id": "2a765d76-59c6-49c3-95b4-429e5439da37", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-820, -1160], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "66eb2691-4316-4470-aa6d-9696beff6cf2", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-820, -900], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n  \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "c0e16404-d82c-418e-b384-d9cc5dceeab6", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [-600, -900], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "ed9768aa-e381-4d53-b0b4-702833e388b9", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [-380, -900], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "0da72902-4338-4610-a48c-ad2762690623", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [20, -700], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "8783e0bc-df82-4bee-9340-5c788e7f7d3c", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [0, -520], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "d3872217-ff7e-4ed7-9992-ab2b6f5af9e1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-620, -1220], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "887598e8-5ac2-4433-9bd6-779a028eab14", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-140, -900], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "d0ab0fb8-e4b8-49e2-9d40-74c9855af7b0", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-840, -960], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "f3311b6f-1130-41c7-ab3a-447bb617be1b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1140, -1500], "parameters": {"color": 3, "width": 1400, "height": 200, "content": "# Slack AI Chatbot Workflow with RAG\n\nImagine having an AI chatbot on Slack that seamlessly integrates with your company’s workflow, automating repetitive requests. No more digging through emails or documents to find answers about IT requests, company policies, or vacation days—just ask the bot, and it will instantly provide the right information.\n\nWith its 24/7 availability, the chatbot ensures that team members get immediate support without waiting for a colleague to be online, making assistance faster and more efficient."}, "typeVersion": 1}, {"id": "b81155d1-6382-4bd8-96a1-09b063f95c43", "name": "Embeddings OpenAI2", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-140, -680], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "7754f8bd-56c2-46c9-85da-d9a49ccf5c81", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -340], "parameters": {"width": 900, "height": 640, "content": "# STEP 3\nCreate a Slack Bot [here](https://api.slack.com) and add it to your Slack (Private o Public) channel.\n\nSet \"Scope Subscribe to Bot Event\":\n- app_mention \n- message.channels\n\nSet Bot Token Scopes:\n- app_mentions:read\n- channels:history\n- channels:manage\n- channels:read\n- chat:write\n- files:read\n- groups:history\n- groups:read\n- im:history\n- im:read\n- mpim:history\n- mpim:read\n- reactions:read\n- reactions:write\n- usergroups:read\n- users:read\n\nIn RAG Qdrant node change: \n- COLLECTION"}, "typeVersion": 1}, {"id": "9933da43-8797-40ed-b399-49ddeb369e42", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [-900, 600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-********", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {}}, "credentials": {"anthropicApi": {"id": "NNTZAD0Gmf7lcniq", "name": "Anthropic account"}}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9ed2f0d0-c463-4942-be0c-e5b606973048", "connections": {"RAG": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send message", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "Get message": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store1", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "RAG", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store1", "type": "ai_document", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}