{"id": "8FLJK1NsduFL0Y5P", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "Qualify new leads in Google Sheets via OpenAI's GPT-4", "tags": [{"id": "y9tvM3hISJKT2jeo", "name": "Ted's Tech Talks", "createdAt": "2023-08-15T22:12:34.260Z", "updatedAt": "2023-08-15T22:12:34.260Z"}], "nodes": [{"id": "1f179325-0bec-4e5c-8ebd-0a2bb3ebefaa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1440, 340], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "7b548661-2b32-451f-ba52-91ca86728f1e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [358, 136.3642172523962], "parameters": {"width": 442, "height": 360.6357827476038, "content": "### 1. Create a Google Sheet document\n* This template uses Google Sheet document connected to Google Forms, but a standalone Sheet document will work too\n* Adapt initial trigger to your needs: check for new entries periodically or add a manual trigger\n\n[Link to the Google Sheet template](https://docs.google.com/spreadsheets/d/1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs)"}, "typeVersion": 1}, {"id": "308b4dce-4656-47bd-b217-69565b1c34f6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [820, 420], "parameters": {"width": 471, "height": 322, "content": "### 2. Provide lead qualification instructions\n* Create a __system message__ with overall instructions\n* Add a __user message__ with the JSON variables\n* Set node parses the resulting JSON object, but you can also request a plain string response in the system message"}, "typeVersion": 1}, {"id": "c00442ca-98cf-4296-b084-f0881ce4fd39", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1320, 222.18785942492013], "parameters": {"width": 355, "height": 269.81214057507987, "content": "### 3. Co<PERSON>ine the initial data with GPT response\n* This Merge node puts together original records from the google sheet and responses from the OpenAI"}, "typeVersion": 1}, {"id": "62643a4c-a69c-4351-9960-20413285ff33", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1700, 220], "parameters": {"width": 398, "height": 265, "content": "### 4. Update the Google Sheet document\n* Provide __Column to Match On__ (usually a timestamp in case of Google Forms)\n* Enter the result from GPT into a separate column"}, "typeVersion": 1}, {"id": "4cd58340-81c4-46c7-b346-25a9b6ef2910", "name": "Update lead status", "type": "n8n-nodes-base.googleSheets", "position": [1860, 340], "parameters": {"columns": {"value": {"Rating": "={{ $json.reply.rating }}", "Timestamp": "={{ $json.Timestamp }}"}, "schema": [{"id": "Timestamp", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email Address", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Email Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Your name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Your name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Your business area", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Your business area", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Your team size", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Your team size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Rating", "type": "string", "display": true, "required": false, "displayName": "Rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Timestamp"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": ********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs/edit#gid=********", "cachedResultName": "Form Responses 1"}, "documentId": {"__rl": true, "mode": "list", "value": "1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs/edit?usp=drivesdk", "cachedResultName": "Join Community (Responses)"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "RtRiRezoxiWkzZQt", "name": "<PERSON>'s Tech Talks Google account"}}, "typeVersion": 4.2}, {"id": "fea0acee-13b6-441a-8cf9-c8fedbc4617d", "name": "Extract JSON reply", "type": "n8n-nodes-base.set", "position": [1120, 580], "parameters": {"fields": {"values": [{"name": "reply", "type": "objectValue", "objectValue": "={{ JSON.parse($json.message.content) }}"}]}, "include": "selected", "options": {}}, "typeVersion": 3.2}, {"id": "0a0608fe-894f-4eb5-b690-233c6dfc0428", "name": "Qualify leads with GPT", "type": "n8n-nodes-base.openAi", "position": [900, 580], "parameters": {"prompt": {"messages": [{"role": "system", "content": "Your task is to qualify incoming leads. Leads are form submissions to a closed community group. Use the following criteria for a quality lead:\n\n1. We are looking for decision makers who run companies or who have some teams. The bigger the team - the better. Basically, everyone with some level of responsibility should be accepted. This is the main criterion.\n2. Email from a non-standard domain. Ideally this should be a corporate domain, but this is a secondary criterion.\n\nPlease thing step by step whether a lead is quality or not?\n\nIf at least one of the criteria satisfy, reply with \"qualified\" in response. Otherwise reply \"not qualified\". Reply with a JSON of the following structure: {\"rating\":\"string\",\"explanation\":\"string\"}. Reply only with with the JSON and nothing more!"}, {"content": "=Here's a lead info:\nName: {{ $json['Your name'] }}\nEmail: {{ $json['Email Address'] }}\nBusiness area: {{ $json['Your business area'] }}\nSize of the team: {{ $json['Your team size'] }}"}]}, "options": {"temperature": 0.3}, "resource": "chat", "chatModel": "gpt-4-turbo-preview"}, "credentials": {"openAiApi": {"id": "rveqdSfp7pCRON1T", "name": "Ted's Tech Talks OpenAi"}}, "typeVersion": 1.1}, {"id": "22fdec69-a4a9-430d-9950-79195799ae7a", "name": "Check for new entries", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [520, 340], "parameters": {"event": "rowAdded", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": ********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs/edit#gid=********", "cachedResultName": "Form Responses 1"}, "documentId": {"__rl": true, "mode": "list", "value": "1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jk8ZbfOMObvHGGImc0sBJTZB_hracO4jRqfbryMgzEs/edit?usp=drivesdk", "cachedResultName": "Join Community (Responses)"}}, "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "m33qCYf9eEvSgo0x", "name": "<PERSON>'s Tech Talks Google Sheets Trigger"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true, "saveExecutionProgress": true, "saveDataSuccessExecution": "all"}, "versionId": "ffad0998-1a6b-469d-9297-6d7fd88387b9", "connections": {"Merge": {"main": [[{"node": "Update lead status", "type": "main", "index": 0}]]}, "Extract JSON reply": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Check for new entries": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Qualify leads with GPT", "type": "main", "index": 0}]]}, "Qualify leads with GPT": {"main": [[{"node": "Extract JSON reply", "type": "main", "index": 0}]]}}}