{
"meta": {
"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"
},
"nodes": [
{
"id": "1eb82902-a1d6-4eff-82a2-26908a82cea2",
"name": "When clicking \"Test workflow\"",
"type": "n8n-nodes-base.manualTrigger",
"position": [
720,
320
],
"parameters": {},
"typeVersion": 1
},
{
"id": "e0031fc3-27f1-45d9-910b-4c07dd322115",
"name": "Get This Week's Menu",
"type": "n8n-nodes-base.httpRequest",
"position": [
992,
370
],
"parameters": {
"url": "=https://www.hellofresh.co.uk/menus/{{ $now.year }}-W{{ $now.weekNumber }}",
"options": {}
},
"typeVersion": 4.2
},
{
"id": "2c556cc7-7d4e-4d80-902f-9686e756ed8c",
"name": "Extract Available Courses",
"type": "n8n-nodes-base.code",
"position": [
992,
650
],
"parameters": {
"jsCode": "const pageData = JSON.parse($input.first().json.data)\nreturn pageData.props.pageProps.ssrPayload.courses.slice(0, 10);"
},
"typeVersion": 2
},
{
"id": "90c39db6-6116-4c37-8d48-a6d5e8f8c777",
"name": "Extract Server Data",
"type": "n8n-nodes-base.html",
"position": [
992,
510
],
"parameters": {
"options": {
"trimValues": false,
"cleanUpText": true
},
"operation": "extractHtmlContent",
"extractionValues": {
"values": [
{
"key": "data",
"cssSelector": "script#__NEXT_DATA__"
}
]
}
},
"typeVersion": 1.2
},
{
"id": "fbd4ed97-0154-4991-bf16-d9c4cb3f4776",
"name": "Get Course Metadata",
"type": "n8n-nodes-base.set",
"position": [
1172,
370
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "3c90fd1e-e9ac-49c1-a459-7cff8c87fe8d",
"name": "name",
"type": "string",
"value": "={{ $json.recipe.name }}"
},
{
"id": "c4f3a5df-346c-4e8d-90ba-a49ed6afdedf",
"name": "cuisines",
"type": "array",
"value": "={{ $json.recipe.cuisines.map(item => item.name) }}"
},
{
"id": "97917928-0956-497b-bb68-507df1783240",
"name": "category",
"type": "string",
"value": "={{ $json.recipe.category.name }}"
},
{
"id": "1e84cf1e-7ad7-4888-9606-d3f7a310ce5f",
"name": "tags",
"type": "array",
"value": "={{ $json.recipe.tags.flatMap(tag => tag.preferences) }}"
},
{
"id": "cf6e2174-e8cb-4935-8303-2f8ed067f510",
"name": "nutrition",
"type": "object",
"value": "={{ $json.recipe.nutrition.reduce((acc,item) => ({ ...acc, [item.name]: item.amount + item.unit }), {}) }}"
},
{
"id": "25ba3fe6-c2fa-4315-a2cb-112ec7e3620f",
"name": "url",
"type": "string",
"value": "={{ $json.recipe.websiteUrl }}"
},
{
"id": "8f444fb3-c2ee-4254-b505-440cca3c7b8b",
"name": "id",
"type": "string",
"value": "={{ $json.recipe.id }}"
}
]
}
},
"typeVersion": 3.3
},
{
"id": "5ab1a5fa-adc3-41e0-be6d-f680af301aca",
"name": "Get Recipe",
"type": "n8n-nodes-base.httpRequest",
"position": [
1172,
510
],
"parameters": {
"url": "={{ $json.recipe.websiteUrl }}",
"options": {}
},
"typeVersion": 4.2
},
{
"id": "5014dc62-8320-4968-b9bd-396a517a2b5c",
"name": "Embeddings Mistral Cloud",
"type": "@n8n/n8n-nodes-langchain.embeddingsMistralCloud",
"position": [
1960,
420
],
"parameters": {
"options": {}
},
"credentials": {
"mistralCloudApi": {
"id": "EIl2QxhXAS9Hkg37",
"name": "Mistral Cloud account"
}
},
"typeVersion": 1
},
{
"id": "2a8fad89-f74b-4808-8cb6-97c6b46a53ee",
"name": "Default Data Loader",
"type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader",
"position": [
2080,
420
],
"parameters": {
"options": {
"metadata": {
"metadataValues": [
{
"name": "week",
"value": "={{ $json.week }}"
},
{
"name": "cuisine",
"value": "={{ $json.cuisines }}"
},
{
"name": "category",
"value": "={{ $json.category }}"
},
{
"name": "tag",
"value": "={{ $json.tags }}"
},
{
"name": "recipe_id",
"value": "={{ $json.id }}"
}
]
}
},
"jsonData": "={{ $json.data }}",
"jsonMode": "expressionData"
},
"typeVersion": 1
},
{
"id": "44ceef5c-1d08-40d2-8ab4-227b551f72f5",
"name": "Merge Course & Recipe",
"type": "n8n-nodes-base.merge",
"position": [
1480,
500
],
"parameters": {
"mode": "combine",
"options": {},
"combinationMode": "mergeByPosition"
},
"typeVersion": 2.1
},
{
"id": "b56bd85e-f182-49d1-aeb1-062e905c316a",
"name": "Prepare Documents",
"type": "n8n-nodes-base.set",
"position": [
1660,
500
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "462567fe-02ec-4747-ae33-407d2bc6d776",
"name": "data",
"type": "string",
"value": "=# {{ $json.name }}\n{{ $json.description.replaceAll('\\n\\n','\\n') }}\n\n# Website\n{{ $json.url }}\n\n## Ingredients\n{{ $json.ingredients.replaceAll('\\n\\n','\\n') }}\n\n## Utensils\n{{ $json.utensils }}\n\n## Nutrition\n{{ Object.keys($json.nutrition).map(key => `* ${key}: ${$json.nutrition[key]}`).join('\\n') }}\n\n## Instructions\n{{ $json.instructions.replaceAll('\\n\\n','\\n') }}"
},
{
"id": "5738e420-abfe-4a85-b7ad-541cfc181563",
"name": "cuisine",
"type": "array",
"value": "={{ $json.cuisines }}"
},
{
"id": "349f46d4-e230-4da8-a118-50227ceb7233",
"name": "category",
"type": "string",
"value": "={{ $json.category }}"
},
{
"id": "9588b347-4469-4aa5-93a2-e7bf41b4c468",
"name": "tag",
"type": "array",
"value": "={{ $json.tags }}"
},
{
"id": "7ddab229-fa52-4d27-84e1-83ed47280d29",
"name": "week",
"type": "string",
"value": "={{ $now.year }}-W{{ $now.weekNumber }}"
},
{
"id": "13163e45-5699-4d25-af3d-4c7910dd2926",
"name": "id",
"type": "string",
"value": "={{ $json.id }}"
},
{
"id": "a0c5d599-ff2b-420d-9173-2baf9218abc5",
"name": "name",
"type": "string",
"value": "={{ $json.name }}"
}
]
}
},
"typeVersion": 3.3
},
{
"id": "6b800632-f320-4fc3-bd2a-6a062834343d",
"name": "Recursive Character Text Splitter",
"type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter",
"position": [
2080,
560
],
"parameters": {
"options": {}
},
"typeVersion": 1
},
{
"id": "df7f17a2-8b27-4203-a2ff-091aaf6609b8",
"name": "Chat Trigger",
"type": "@n8n/n8n-nodes-langchain.chatTrigger",
"position": [
2440,
360
],
"webhookId": "745056ec-2d36-4ac3-9c70-6ff0b1055d0a",
"parameters": {},
"typeVersion": 1
},
{
"id": "ee38effe-5929-421e-a3c5-b1055a755242",
"name": "Extract Recipe Details",
"type": "n8n-nodes-base.html",
"position": [
1172,
650
],
"parameters": {
"options": {},
"operation": "extractHtmlContent",
"extractionValues": {
"values": [
{
"key": "description",
"cssSelector": "[data-test-id=\"recipe-description\"]"
},
{
"key": "ingredients",
"cssSelector": "[data-test-id=\"ingredients-list\"]"
},
{
"key": "utensils",
"cssSelector": "[data-test-id=\"utensils\"]"
},
{
"key": "instructions",
"cssSelector": "[data-test-id=\"instructions\"]",
"skipSelectors": "img,a"
}
]
}
},
"typeVersion": 1.2
},
{
"id": "dede108f-2fde-49cb-8a0e-fa5786c59d4b",
"name": "Qdrant Recommend API",
"type": "@n8n/n8n-nodes-langchain.toolWorkflow",
"position": [
2840,
540
],
"parameters": {
"name": "get_recipe_recommendation",
"fields": {
"values": [
{
"name": "week",
"stringValue": "={{ $now.year }}-W{{ $now.weekNumber }}"
}
]
},
"schemaType": "manual",
"workflowId": "={{ $workflow.id }}",
"description": "Call this tool to get a recipe recommendation. Pass in the following params as a json object:\n* positives - a description of what the user wants to cook. This could be ingredients, flavours, utensils available, number of diners, type of meal etc.\n* negatives - a description of what the user wants to avoid in the recipe. This could be flavours to avoid, allergen considerations, conflicts with theme of meal etc.",
"inputSchema": "{\n\"type\": \"object\",\n\"properties\": {\n\t\"positive\": {\n\t\t\"type\": \"string\",\n\t\t\"description\": \"a description of what the user wants to cook. This could be ingredients, flavours, utensils available, number of diners, type of meal etc.\"\n\t},\n \"negative\": {\n \"type\": \"string\",\n \"description\": \"a description of what the user wants to avoid in the recipe. This could be flavours to avoid, allergen considerations, conflicts with theme of meal etc.\"\n }\n}\n}",
"specifyInputSchema": true
},
"typeVersion": 1.1
},
{
"id": "5e703134-4dd9-464b-9ec9-dc6103907a1e",
"name": "Execute Workflow Trigger",
"type": "n8n-nodes-base.executeWorkflowTrigger",
"position": [
2420,
940
],
"parameters": {},
"typeVersion": 1
},
{
"id": "9fb5f4fd-3b38-4a35-8986-d3955754c8d1",
"name": "Mistral Cloud Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud",
"position": [
2660,
540
],
"parameters": {
"model": "mistral-large-2402",
"options": {}
},
"credentials": {
"mistralCloudApi": {
"id": "EIl2QxhXAS9Hkg37",
"name": "Mistral Cloud account"
}
},
"typeVersion": 1
},
{
"id": "d38275e6-aede-4f1c-9b05-018f3cf4faab",
"name": "Get Tool Response",
"type": "n8n-nodes-base.set",
"position": [
3160,
940
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "10b55200-4610-4e9b-8be7-d487c6b56a78",
"name": "response",
"type": "string",
"value": "={{ JSON.stringify($json.result) }}"
}
]
}
},
"typeVersion": 3.3
},
{
"id": "dc3ceb2f-3c64-4b42-aeca-ddcdb84abf12",
"name": "Wait for Rate Limits",
"type": "n8n-nodes-base.wait",
"position": [
2420,
1080
],
"webhookId": "e86d8ae4-3b0d-4c40-9d12-a11d6501a043",
"parameters": {
"amount": 1.1
},
"typeVersion": 1.1
},
{
"id": "ec36d6f8-c3da-4732-8d56-a092a3358864",
"name": "Get Mistral Embeddings",
"type": "n8n-nodes-base.httpRequest",
"position": [
2620,
940
],
"parameters": {
"url": "https://api.mistral.ai/v1/embeddings",
"method": "POST",
"options": {},
"sendBody": true,
"authentication": "predefinedCredentialType",
"bodyParameters": {
"parameters": [
{
"name": "model",
"value": "mistral-embed"
},
{
"name": "encoding_format",
"value": "float"
},
{
"name": "input",
"value": "={{ [$json.query.positive, $json.query.negative].compact() }}"
}
]
},
"nodeCredentialType": "mistralCloudApi"
},
"credentials": {
"mistralCloudApi": {
"id": "EIl2QxhXAS9Hkg37",
"name": "Mistral Cloud account"
}
},
"typeVersion": 4.2
},
{
"id": "aebcb860-d25c-4833-9e9d-0297101259c7",
"name": "Use Qdrant Recommend API",
"type": "n8n-nodes-base.httpRequest",
"position": [
2800,
940
],
"parameters": {
"url": "=http://qdrant:6333/collections/hello_fresh/points/recommend/groups",
"method": "POST",
"options": {},
"sendBody": true,
"authentication": "predefinedCredentialType",
"bodyParameters": {
"parameters": [
{
"name": "strategy",
"value": "average_vector"
},
{
"name": "limit",
"value": "={{ 3 }}"
},
{
"name": "positive",
"value": "={{ [$json.data[0].embedding] }}"
},
{
"name": "negative",
"value": "={{ [$json.data[1].embedding] }}"
},
{
"name": "filter",
"value": "={{ { \"must\": {\"key\": \"metadata.week\", \"match\": { \"value\": $('Execute Workflow Trigger').item.json.week } } } }}"
},
{
"name": "with_payload",
"value": "={{ true }}"
},
{
"name": "group_by",
"value": "metadata.recipe_id"
},
{
"name": "group_size",
"value": "={{ 3 }}"
}
]
},
"nodeCredentialType": "qdrantApi"
},
"credentials": {
"qdrantApi": {
"id": "NyinAS3Pgfik66w5",
"name": "QdrantApi account"
}
},
"typeVersion": 4.2
},
{
"id": "2474c97d-0d85-4acc-a95e-2eb6494786dc",
"name": "Get Recipes From DB",
"type": "n8n-nodes-base.code",
"position": [
2980,
940
],
"parameters": {
"language": "python",
"pythonCode": "import sqlite3\ncon = sqlite3.connect(\"hello_fresh_1.db\")\n\nrecipe_ids = list(set([group.id for group in _input.all()[0].json.result.groups if group.hits[0].score > 0.5]))\nplaceholders = ','.join(['?' for i in range(0,len(recipe_ids))])\n\ncur = con.cursor()\nres = cur.execute(f'SELECT * FROM recipes WHERE id IN ({placeholders})', recipe_ids)\nrows = res.fetchall()\n\ncon.close()\n\nreturn [{ \"result\": [row[2] for row in rows] }]"
},
"typeVersion": 2
},
{
"id": "54229c2a-6e26-4350-8a94-57f415ef2340",
"name": "Save Recipes to DB",
"type": "n8n-nodes-base.code",
"position": [
1960,
940
],
"parameters": {
"language": "python",
"pythonCode": "import sqlite3\ncon = sqlite3.connect(\"hello_fresh_1.db\")\n\ncur = con.cursor()\ncur.execute(\"CREATE TABLE IF NOT EXISTS recipes (id TEXT PRIMARY KEY, name TEXT, data TEXT, cuisine TEXT, category TEXT, tag TEXT, week TEXT);\")\n\nfor item in _input.all():\n cur.execute('INSERT OR REPLACE INTO recipes VALUES(?,?,?,?,?,?,?)', (\n item.json.id,\n item.json.name,\n item.json.data,\n ','.join(item.json.cuisine),\n item.json.category,\n ','.join(item.json.tag),\n item.json.week\n ))\n\ncon.commit()\ncon.close()\n\nreturn [{ \"affected_rows\": len(_input.all()) }]"
},
"typeVersion": 2
},
{
"id": "725c1f56-5373-4891-92b9-3f32dd28892b",
"name": "Sticky Note",
"type": "n8n-nodes-base.stickyNote",
"position": [
901.1666225087287,
180.99920515712074
],
"parameters": {
"color": 7,
"width": 484.12381677448207,
"height": 674.1153489831718,
"content": "## Step 1. Fetch Available Courses For the Current Week\n\nTo populate our vectorstore, we'll scrape the weekly menu off the HelloFresh Website. The pages are quite large so may take a while so please be patient."
},
"typeVersion": 1
},
{
"id": "f4e882b8-3762-4e6b-9e95-b0d708d0c284",
"name": "Sticky Note1",
"type": "n8n-nodes-base.stickyNote",
"position": [
1420,
300
],
"parameters": {
"color": 7,
"width": 409.1756468632768,
"height": 398.81415970574335,
"content": "## Step 2. Create Recipe Documents For VectorStore\n\nTo populate our vectorstore, we'll scrape the weekly menu off the HelloFresh Website. The pages are quite large so may take a while so please be patient."
},
"typeVersion": 1
},
{
"id": "fc3c2221-b67c-451c-9096-d6acd2a297fa",
"name": "Sticky Note2",
"type": "n8n-nodes-base.stickyNote",
"position": [
1860,
19.326425127730317
],
"parameters": {
"color": 7,
"width": 486.02284096214964,
"height": 690.7816167755491,
"content": "## Step 3. Vectorise Recipes For Recommendation Engine\n[Read more about Qdrant node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreqdrant)\n\nWe'll store our documents in our Qdrant vectorstore by converting to vectors using Mistral Embed. Our goal is to a build a recommendation engine for meals of the week which Qdrant is a perfect solution."
},
"typeVersion": 1
},
{
"id": "43296173-b929-46cc-b6ea-58007837b8df",
"name": "Sticky Note3",
"type": "n8n-nodes-base.stickyNote",
"position": [
1740,
740
],
"parameters": {
"color": 7,
"width": 547.0098868353456,
"height": 347.6002738958705,
"content": "## Step 4. Save Original Document to Database\n[Read more about Code Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code)\n\nFinally, let's have the original document stored in a more traditional datastore. USually our vectorsearch will return partial docs and those are enough for many use-cases, however in this instance we'll pull the full docs for the Agent get the info required to make the recommendation. "
},
"typeVersion": 1
},
{
"id": "6e2e58d2-e0ad-4503-8ed6-891124c8035b",
"name": "Sticky Note4",
"type": "n8n-nodes-base.stickyNote",
"position": [
2380,
160
],
"parameters": {
"color": 7,
"width": 673.6008766895472,
"height": 552.9202706743265,
"content": "## 5. Chat with Our HelloFresh Recommendation AI Agent\n[Read more about AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent is designed to recommend HelloFresh recipes based on your current taste preferences. Need something hot and spicy, warm and comforting or fast and chilled? This agent will capture what you would like and not like and queries our Recipe Recommendation engine powered by Qdrant Vectorstore."
},
"typeVersion": 1
},
{
"id": "ba692c21-38bc-48a1-8b40-bad298be8b9e",
"name": "AI Agent",
"type": "@n8n/n8n-nodes-langchain.agent",
"position": [
2660,
360
],
"parameters": {
"options": {
"systemMessage": "=You are a recipe bot for the company, \"Hello fresh\". You will help the user choose which Hello Fresh recipe to choose from this week's menu. The current week is {{ $now.year }}-W{{ $now.weekNumber }}.\nDo not recommend any recipes other from the current week's menu. If there are no recipes to recommend, please ask the user to visit the website instead https://hellofresh.com."
}
},
"typeVersion": 1.6
},
{
"id": "d7ca0f97-72dc-4f4c-8b46-3ff57b9068a4",
"name": "Sticky Note5",
"type": "n8n-nodes-base.stickyNote",
"position": [
2320,
740
],
"parameters": {
"color": 7,
"width": 987.4785537889618,
"height": 531.9173034334732,
"content": "## 5. Using Qdrant's Recommend API & Grouping Functionality\n[Read more about Qdrant's Recommend API](https://qdrant.tech/documentation/concepts/explore/?q=recommend)\n\nUnlike basic similarity search, Qdrant's Recommend API takes a positive query to match against (eg. Roast Dinner) and a negative query to avoid (eg. Roast Chicken). This feature significantly improves results for a recommendation engine. Additionally, by utilising Qdrant's Grouping feature, we're able to group similar matches from the same recipe - meaning we can ensure unique recipes everytime."
},
"typeVersion": 1
},
{
"id": "96a294e2-1437-4ded-9973-0999b444c999",
"name": "Sticky Note6",
"type": "n8n-nodes-base.stickyNote",
"position": [
440,
-40
],
"parameters": {
"width": 432.916474478624,
"height": 542.9295980774649,
"content": "## Try it out!\n### This workflow does the following:\n* Fetches and stores this week's HelloFresh's menu\n* Builds the foundation of a recommendation engine by storing the recipes in a Qdrant Vectorstore and SQLite database.\n* Builds an AI Agent that allows for a chat interface to query for a the week's recipe recommendations.\n* AI agent uses the Qdrant Recommend API, providing what the user likes/dislikes as the query.\n* Qdrant returns the results which enable the AI Agent to make the recommendation to the user.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"
},
"typeVersion": 1
},
{
"id": "72c98600-f21a-42d4-97be-836b8ef6dc77",
"name": "Qdrant Vector Store",
"type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant",
"position": [
1960,
240
],
"parameters": {
"mode": "insert",
"options": {},
"qdrantCollection": {
"__rl": true,
"mode": "list",
"value": "hello_fresh",
"cachedResultName": "hello_fresh"
}
},
"credentials": {
"qdrantApi": {
"id": "NyinAS3Pgfik66w5",
"name": "QdrantApi account"
}
},
"typeVersion": 1
},
{
"id": "b7c4b597-ac2b-41d7-8f0f-1cbba25085de",
"name": "Sticky Note7",
"type": "n8n-nodes-base.stickyNote",
"position": [
1860,
-195.*************
],
"parameters": {
"width": 382.**************,
"height": 195.*************,
"content": "### 🚨Ensure Qdrant collection exists!\nYou'll need to run the following command in Qdrant:\n```\nPUT collections/hello_fresh\n{\n \"vectors\": {\n \"distance\": \"Cosine\",\n \"size\": 1024\n }\n}\n```"
},
"typeVersion": 1
},
{
"id": "********-ecc2-46f0-a31a-0a7e9c47ac5d",
"name": "Sticky Note8",
"type": "n8n-nodes-base.stickyNote",
"position": [
2740,
920
],
"parameters": {
"width": 213.**************,
"height": 332.**************,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨Configure Your Qdrant Connection\n* Be sure to enter your endpoint address"
},
"typeVersion": 1
}
],
"pinData": {},
"connections": {
"Get Recipe": {
"main": [
[
{
"node": "Extract Recipe Details",
"type": "main",
"index": 0
}
]
]
},
"Chat Trigger": {
"main": [
[
{
"node": "AI Agent",
"type": "main",
"index": 0
}
]
]
},
"Prepare Documents": {
"main": [
[
{
"node": "Qdrant Vector Store",
"type": "main",
"index": 0
},
{
"node": "Save Recipes to DB",
"type": "main",
"index": 0
}
]
]
},
"Default Data Loader": {
"ai_document": [
[
{
"node": "Qdrant Vector Store",
"type": "ai_document",
"index": 0
}
]
]
},
"Extract Server Data": {
"main": [
[
{
"node": "Extract Available Courses",
"type": "main",
"index": 0
}
]
]
},
"Get Course Metadata": {
"main": [
[
{
"node": "Merge Course & Recipe",
"type": "main",
"index": 0
}
]
]
},
"Get Recipes From DB": {
"main": [
[
{
"node": "Get Tool Response",
"type": "main",
"index": 0
}
]
]
},
"Get This Week's Menu": {
"main": [
[
{
"node": "Extract Server Data",
"type": "main",
"index": 0
}
]
]
},
"Qdrant Recommend API": {
"ai_tool": [
[
{
"node": "AI Agent",
"type": "ai_tool",
"index": 0
}
]
]
},
"Wait for Rate Limits": {
"main": [
[
{
"node": "Get Mistral Embeddings",
"type": "main",
"index": 0
}
]
]
},
"Merge Course & Recipe": {
"main": [
[
{
"node": "Prepare Documents",
"type": "main",
"index": 0
}
]
]
},
"Extract Recipe Details": {
"main": [
[
{
"node": "Merge Course & Recipe",
"type": "main",
"index": 1
}
]
]
},
"Get Mistral Embeddings": {
"main": [
[
{
"node": "Use Qdrant Recommend API",
"type": "main",
"index": 0
}
]
]
},
"Embeddings Mistral Cloud": {
"ai_embedding": [
[
{
"node": "Qdrant Vector Store",
"type": "ai_embedding",
"index": 0
}
]
]
},
"Execute Workflow Trigger": {
"main": [
[
{
"node": "Wait for Rate Limits",
"type": "main",
"index": 0
}
]
]
},
"Mistral Cloud Chat Model": {
"ai_languageModel": [
[
{
"node": "AI Agent",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Use Qdrant Recommend API": {
"main": [
[
{
"node": "Get Recipes From DB",
"type": "main",
"index": 0
}
]
]
},
"Extract Available Courses": {
"main": [
[
{
"node": "Get Course Metadata",
"type": "main",
"index": 0
},
{
"node": "Get Recipe",
"type": "main",
"index": 0
}
]
]
},
"When clicking \"Test workflow\"": {
"main": [
[
{
"node": "Get This Week's Menu",
"type": "main",
"index": 0
}
]
]
},
"Recursive Character Text Splitter": {
"ai_textSplitter": [
[
{
"node": "Default Data Loader",
"type": "ai_textSplitter",
"index": 0
}
]
]
}
}
}sRecipe Recommendations with Qdrant and Mistral