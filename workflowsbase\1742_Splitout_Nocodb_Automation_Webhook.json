{"id": "W5cevjhP3xIQdMhT", "meta": {"instanceId": "b8ef33547995f2a520f12118ac1f7819ea58faa7a1096148cac519fa08be8e99", "templateCredsSetupCompleted": true}, "name": "Simple LinkedIn profile collector", "tags": [{"id": "DDb2eQi5fXOMcVD6", "name": "LinkedIn", "createdAt": "2025-04-27T16:44:17.404Z", "updatedAt": "2025-04-27T16:44:17.404Z"}, {"id": "WvVrZMOsmCMjmf8G", "name": "leads", "createdAt": "2025-05-05T13:14:14.918Z", "updatedAt": "2025-05-05T13:14:14.918Z"}, {"id": "hIooJnHTaPcNsX7s", "name": "SERP", "createdAt": "2025-05-05T13:14:29.068Z", "updatedAt": "2025-05-05T13:14:29.068Z"}], "nodes": [{"id": "6a120c5d-3405-467e-8073-80bf30f2f0fc", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "position": [-580, 160], "parameters": {}, "typeVersion": 1}, {"id": "5a4cb9af-faff-4fba-a5ce-d2c9bc25a070", "name": "Google search w/ SerpAPI", "type": "n8n-nodes-base.httpRequest", "position": [-100, 160], "parameters": {"url": "https://serpapi.com/search", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "q", "value": "=site:{{ $json.site }} {{ $json.Keyword }} {{ $json.Location }}"}, {"name": "hl", "value": "={{ $json['Host langauge'] }}"}, {"name": "gl", "value": "={{ $json.Geolocation }}"}, {"name": "num", "value": "={{ $json['Number of search results to be returned'] }}"}, {"name": "engine", "value": "={{ $json['Search engine'] }}"}]}, "nodeCredentialType": "serp<PERSON><PERSON>"}, "credentials": {"serpApi": {"id": "mL117f55z8IG4i1V", "name": "SerpAPI account"}}, "typeVersion": 4.2}, {"id": "300e3483-0f7b-427d-9f95-bf631dbda3d3", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [340, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ab7399a3-8fe8-447b-b9c6-33240e07e2b6", "name": "NameInLinkedinProfile", "type": "string", "value": "={{ $json.title }}"}, {"id": "6f9a2bd6-e46d-4294-adbf-29aec0b8b2eb", "name": "linkedinUrl", "type": "string", "value": "={{ $json.link }}"}, {"id": "e1e87eb4-ecc8-4b50-ab74-4c0a0016f84d", "name": "Snippet", "type": "string", "value": "={{ $json.snippet }}"}, {"id": "632ee133-06be-4730-9178-6edde40e087a", "name": "linkedinUrl", "type": "string", "value": "={{ $json.link }}"}, {"id": "9ce26329-eedf-47ae-815b-f19fc34b2e83", "name": "Followers", "type": "string", "value": "={{ $json.displayed_link }}"}, {"id": "39b81062-afd1-468d-95aa-e158bd34b773", "name": "Keyword", "type": "string", "value": "={{ $('Search parameter').item.json.Keyword }}"}, {"id": "9e1ab1fc-86eb-44c0-bdcb-bc5dc63f069c", "name": "Location", "type": "string", "value": "={{ $('Search parameter').item.json.Location }}"}, {"id": "f9e0eb5e-e81d-4cd3-8b47-d301ae7920e8", "name": "Rich snippet", "type": "string", "value": "={{ $json.rich_snippet.top.extensions }}"}, {"id": "fca0eaa4-70e0-4c1e-99a9-bf66477aad0f", "name": "snippet_highlighted_words", "type": "string", "value": "={{ $json.snippet_highlighted_words }}"}]}}, "typeVersion": 3.4}, {"id": "ca824e0a-dddd-401a-a48a-debe4821d24e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-160, -200], "parameters": {"width": 220, "height": 520, "content": "### Adaptation required\nGet a free tier for serpAPI (Google Search) at serpapi.com\n\nSet up the credentials for serpAPI\n\nExplanations in the [n8n docs](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolserpapi/)"}, "typeVersion": 1}, {"id": "b8feccbd-6d14-4838-afc3-7fb9a1cd4f04", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [80, -200], "parameters": {"width": 180, "height": 520, "content": "### NO adaptation required\nThe search metadata is being discarded and only the \"organic results\" being preserved as individual list items as they are containing the relevant data\n"}, "typeVersion": 1}, {"id": "a5eb2f30-37e1-43b9-8e2c-dde0227908c5", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [300, -200], "parameters": {"width": 180, "height": 520, "content": "### NO adaptation required\nDiscard irrelevant search result (meta)data\n"}, "typeVersion": 1}, {"id": "94232837-e5b8-484e-b453-17952b3d8fbe", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [500, -200], "parameters": {"width": 520, "height": 520, "content": "### Adaptation required\n\n**This node does the following**:\n- Identify where possible the company name the LinkedIn profile is working in.\n- Turn the number of followers into a real number, e.g. \"3.3k+\" &rarr; 3300\n\n\n\n**Set up**\n- Get API credentials from openai.com\n- Set up credentials in n8n\n- Select the OpenAI model you want to use, e.g. GPT-4o\n- The prompt is already included but can be improved\n\n\n[n8n documentation](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatopenai/) for more explanations"}, "typeVersion": 1}, {"id": "3e3214b0-ace5-47e2-bb17-2db3c3db1de3", "name": "Discard meta data", "type": "n8n-nodes-base.set", "position": [1080, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a821b4a3-d4e2-4f37-a154-8606426078ef", "name": "followers_number", "type": "number", "value": "={{ $json.message.content.followers }}"}, {"id": "e1ac8cc3-4a51-4c01-9e75-8d92dff3b70d", "name": "NameOfCompany", "type": "string", "value": "={{ $json.message.content.company_name }}"}]}}, "typeVersion": 3.4}, {"id": "2b1a66c3-be8a-4b00-86ee-3438022ad775", "name": "LinkedIn profiles in Excel for download", "type": "n8n-nodes-base.convertToFile", "position": [1600, 160], "parameters": {"options": {}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "b1b982f2-eeb7-4816-be25-aee5568d2283", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1220, -200], "parameters": {"color": 4, "width": 540, "height": 260, "content": "## What problem does this solve? \n\nIt fetches **LinkedIn profiles** based on a keyword and location via Google search and stores them in an Excel file for download and in a NocoDB database.\nIt tries to avoid using costly services and should be n8n **beginner friendly**.\nIt uses the SerpAPI.com to avoid being blocked by Google Search and to process the data in an easier way.\n"}, "typeVersion": 1}, {"id": "15340d73-272d-45a1-b96f-b75569bae0b5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1040, -200], "parameters": {"width": 180, "height": 520, "content": "### NO adaption required\nThis node discards irrelevant OpenAI metadata"}, "typeVersion": 1}, {"id": "da183064-0eb2-4e7d-ad83-7aca8f9b9e36", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-420, 120], "parameters": {"height": 760, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Setting the parameters for Google search via SerpAPI\n\nSearching **LinkedIn profiles** by setting the following **parameters** for the Google query in the next node\n\n- Keyword on what to look for \n- Location or region to look into\n- Number of search results\n- Host language\n- Geolocation\n- Search engine\n\n\nMore on search parameters: https://serpapi.com/blog/google-search-parameters/ or in the [n8n docs](https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolserpapi/)"}, "typeVersion": 1}, {"id": "1fc2f6f8-df39-47c5-92a1-c1a14cbe0d65", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-1220, 100], "parameters": {"color": 4, "width": 540, "height": 200, "content": "## What does it do?\n\n- Based on criteria input, it searches LinkedIn profiles\n- It discards unnecessary data and turns the follower count into a real number\n- The output is provided as an Excel table for download and in a NocoDB database"}, "typeVersion": 1}, {"id": "a522ed81-9d50-464e-b872-42a4c66a8584", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-1220, 640], "parameters": {"color": 4, "width": 540, "height": 500, "content": "## Step-by-step instruction\n\n\n1. Import the Workflow:\nCopy the workflow JSON from the \"Template Code\" section below.\nImport it into n8n via \"Import from File\" or \"Import from URL\".\n\n2. Set up a free account at serpapi.com and get API credentials to enable good Google search results\n\n3. Set up an API account at openai.com and get API key\n\n4. Set up a nocodb.com account (or self-host) and get the API credentials\n\n4. Create the credentials for serpapi.com, opemnai.com and nocodb.com in n8n.\n\n5. Set up a table in NocoDB with the fields indicated in the note above the NocoDB node\n\n5. Follow the instructions as detailed in the notes above individual nodes \n\n6. When the workflow is finished, open the Excel node and click download if you need the Excel file\n\n"}, "typeVersion": 1}, {"id": "********-5ed2-4891-8cf3-1bcf9fc83ebd", "name": "Search parameter", "type": "n8n-nodes-base.set", "position": [-360, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d4c0a5dc-c656-45e7-bcd1-2cee3fbc9aa5", "name": "Keyword", "type": "string", "value": "nocode"}, {"id": "f5365eff-7e79-411c-8ebb-a7d244e9e1fa", "name": "Location", "type": "string", "value": "Germany"}, {"id": "24b4046f-7083-416d-8ae9-bc72c5323b14", "name": "Number of search results to be returned", "type": "string", "value": 20}, {"id": "25c114e6-7628-4eb9-9b3e-a6bb5fbae1dc", "name": "Host langauge", "type": "string", "value": "en"}, {"id": "ac29cb67-89ec-41ae-870c-196a4bf524a6", "name": "Geolocation", "type": "string", "value": "de"}, {"id": "d1e78115-f788-4ffd-9374-60b83e7e2b8a", "name": "Search engine", "type": "string", "value": "google"}, {"id": "7af59bb4-548b-4061-8095-3261b2ce8227", "name": "site", "type": "string", "value": "linkedin.com/in"}]}}, "typeVersion": 3.4}, {"id": "0b588ebc-eddf-4c4c-a0c2-81cc0e8ae9d1", "name": "Turn search results into individual items", "type": "n8n-nodes-base.splitOut", "position": [120, 160], "parameters": {"options": {}, "fieldToSplitOut": "organic_results"}, "typeVersion": 1}, {"id": "daef5714-3e40-4ac1-a02e-f3dacddeb5e8", "name": "Company name & followers", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 160], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Transform  {{ $json.Followers }} into a number and extract where possible the name of the company in {{ $json.NameInLinkedinProfile }} or in {{ $json.Snippet }} Do not output things like location or name, only followers and company_name"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "0Vdk5RlVe7AoUdAM", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "2f204f01-836c-41ab-97c1-38fee34adffc", "name": "Generate final data via merge", "type": "n8n-nodes-base.merge", "position": [1300, 280], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "f52e65b5-1369-4410-99fe-0cb0c11f5da5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1260, -60], "parameters": {"width": 180, "height": 520, "content": "### NO adaption required\nThis node creates the final data output "}, "typeVersion": 1}, {"id": "de7ace7e-ba9b-4abb-a54b-8996fc9b88a6", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1540, -60], "parameters": {"width": 220, "height": 520, "content": "### NO adaption required\nThis node creates stores all the data in an Excel file which can be downloaded. \n- Open the node\n- Click on download button"}, "typeVersion": 1}, {"id": "17a32318-e1bc-4c07-b6a2-59f47a68a595", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1540, 480], "parameters": {"width": 780, "height": 920, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Adaption required\n\n- This node creates stores all the data in an NocoDB database for further utilization.\n- In case the database is not needed, just delete this node.\n\n\n\n**Set up part 1**\n\n- Create an NocoDB account, either via nocodb.com or self-hosted\n- Create the credentials in n8n along the [n8n documentation](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.nocodb/)\n- Set up a table with a name of your choice\n\n\n**Set up part 2**\n\nCreate the following fields in this table: \n- NameInLinkedinProfile (type: Single line text): name of the person\n- NameOfCompany: the name of the company as generated by OpenAI\n- linkedinUrl (type: url): the link to the LinkedIn profile\n- Followers: the number of followers as text and indicated by LinkedIn \n- followers_number (type: Number): the number of followers as a number\n- Keyword: the keyword used for searching LinkedIn profiles\n- Location: the location used for searching LinkedIn profiles \n- Rich snippet (type: Long text): \n- snippet_highlighted_words (type: Long text): \n\n\n**Adaptations in the node itself**\n- Make sure that the right table is selected\n- Select \"row\" in the \"Resources\" field\n- Select \"create\" in the field \"Operation\"\n- Select \"Auto map ....\" in the field \"Data to Send\""}, "typeVersion": 1}, {"id": "d41f26fe-9068-4202-9677-a355c5276999", "name": "Store data in a NocoDB table", "type": "n8n-nodes-base.nocoDb", "position": [1600, 520], "parameters": {"table": "mttbkp3hxy9rnwx", "operation": "create", "projectId": "puqzjel7f0swv1t", "dataToSend": "autoMapInputData", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "gjNns0VJMS3P2RQ3", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "98212dd7-5449-4fc1-b96f-3f1b94931c32", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-1220, 320], "parameters": {"color": 4, "width": 540, "height": 280, "content": "## How does it do it?\n\n- Based on criteria input, it uses serpAPI.com to conduct Google search of the respective LinkedI profiles\n- With OpenAI.com the name of the respective company is being added\n- With OpenAI.com the follower number e.g., 300+ is turned into a real number: 300\n- All unnecessary metadata is being discarded\n- As an output an Excel file is being created\n- The output is stored in a nocodb.com table"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ba732d3f-968b-445d-83cc-e58a47b97e30", "connections": {"Edit Fields": {"main": [[{"node": "Company name & followers", "type": "main", "index": 0}, {"node": "Generate final data via merge", "type": "main", "index": 1}]]}, "Manual Trigger": {"main": [[{"node": "Search parameter", "type": "main", "index": 0}]]}, "Search parameter": {"main": [[{"node": "Google search w/ SerpAPI", "type": "main", "index": 0}]]}, "Discard meta data": {"main": [[{"node": "Generate final data via merge", "type": "main", "index": 0}]]}, "Company name & followers": {"main": [[{"node": "Discard meta data", "type": "main", "index": 0}]]}, "Google search w/ SerpAPI": {"main": [[{"node": "Turn search results into individual items", "type": "main", "index": 0}]]}, "Generate final data via merge": {"main": [[{"node": "LinkedIn profiles in Excel for download", "type": "main", "index": 0}, {"node": "Store data in a NocoDB table", "type": "main", "index": 0}]]}, "Turn search results into individual items": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}