{"meta": {"instanceId": "n8n.syncbricks.com"}, "nodes": [{"id": "e6d85380-7cfa-4c6e-9b0f-d390ad0cbc67", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [1400, -180], "parameters": {"url": "=https://proxmox.syncbricks.com/api2/json{{ $json.output.url }}", "method": "=POST", "options": {"allowUnauthorizedCerts": true}, "jsonBody": "={{ $json.output.details }}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 4.2}, {"id": "9b497de8-0f01-40b1-8f8e-28fad1f758c4", "name": "Proxmox API Documentation", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-300, 40], "parameters": {"url": "https://pve.proxmox.com/pve-docs/api-viewer/index.html", "toolDescription": "This is Proxmox API Documentation ensure to read the details from here"}, "typeVersion": 1.1}, {"id": "e7ac54a9-37be-44b5-b58e-8b631892367e", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [40, 60], "parameters": {"options": {"prompt": "Instructions:\n--------------\n{instructions}\n--------------\nCompletion:\n--------------\n{completion}\n--------------\n\nAbove, the Completion did not satisfy the constraints given in the Instructions.\nError:\n--------------\n{error}\n--------------\n\nPlease try again. Please only respond with an answer that satisfies the constraints laid out in the Instructions:"}}, "typeVersion": 1}, {"id": "5d8c8c6d-d5de-4c87-9950-46f1f5757314", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-40, 360], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "pKFvSpPWSRFpnBoB", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "8565ac2f-0cdd-4e7f-a1e9-6f273869e068", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [180, 360], "parameters": {"jsonSchemaExample": "{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/psb1/qemu\",\n \"details\": {\n \"vmid\": 105,\n \"cores\": 4,\n \"memory\": 8192,\n \"net0\": \"virtio,bridge=vmbr0\",\n \"disk0\": \"local:10,format=qcow2\",\n \"sockets\": 1,\n \"ostype\": \"l26\"\n },\n \"message\": \"The VM with ID 105 has been successfully configured to be created on node psb1.\"\n}"}, "typeVersion": 1.2}, {"id": "80b1ef4d-b4c7-40b4-969f-f53d0068cac7", "name": "Proxmox", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-80, 40], "parameters": {"url": "https://************:8006/api2/json/cluster/status", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "=This is Proxmox which will help you to get the details of existing Proxmox installations, ensure to append to existing url : https://************:8006/api2/ to get response from existing proxmox \n\nMy prommox nodes are named as psb1, psb2 and psb3\npsb1 : https://************:8006/api2/\npsb2 : https://************:8006/api2/\npsb3 : https://************:8006/api2/"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 1.1}, {"id": "09444fa1-3b5e-4411-b70c-cf777db971bb", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [1080, -320], "parameters": {"url": "=https://************:8006/api2/json{{ $json.output.properties.url.pattern }}", "method": "=GET", "options": {"allowUnauthorizedCerts": true}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 4.2}, {"id": "d148b395-01e9-48a6-b98c-cb515fa3446d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, -660], "parameters": {"width": 736.2768017274677, "height": 1221.0199187779397, "content": "## API Key for Proxmox\n** Create Credentails *** ensure to create credentials in Proxmox Data Center as API Key and then create credentails. \n** Add Credentials to n8n ** Click on Credentails, add new Credentails and Chose Header Auth\n** In Header Auth Below will be used \nName : Authorization\nValue : PVEAPIToken=<user>@<realm>!<token-id>=<token-value>\n\nSuppose my token id is n8n and key is 1234 so value will be as below\n\nValue : PVEAPIToken=root@pam!n8n=1234\n"}, "typeVersion": 1}, {"id": "d356bb83-c567-44b6-ba23-3e330abf835e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -120], "parameters": {"color": 6, "width": 492.************, "height": 702.*************, "content": "## Trigger\nYou can use any trigger as input, a chat, telegram, email etc"}, "typeVersion": 1}, {"id": "d2829180-9c14-4437-9ae1-1bb822d8d925", "name": "Google Gemini Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1880, -320], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "pKFvSpPWSRFpnBoB", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "0e8a617b-8b95-4bed-8bff-876266fc4151", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-440, -690], "parameters": {"color": 5, "width": 789.*************, "height": 1260.************, "content": "## Porxmox Custom AI Agent \nIt uses the intelligence provided to it including the Proxmox API Wiki, Proxmox Cluster Linked and Proxmox API Documentation.\n\nThe AI Model connected with this is Gemini, you can connect any AI Model by Ollama, OpenAI, Claude etc.\n\nOutput Parser is used to ensure the fixed output structure that can be used for API URL"}, "typeVersion": 1}, {"id": "4cbf39ae-7b81-44b1-858c-10c21af9d558", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-680, -300], "webhookId": "63de8c82-04fc-4126-8bbf-b0eb62794d74", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "f91a1d2d-ce33-4469-b4da-e9ef1dd070e0", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [-1080, 320], "webhookId": "c86fa48b-ae66-46f2-b438-f156225a5c74", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "uwpC7pPg6WJYh8Ad", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "aec3c1f4-058e-4321-99dd-772dcc04e206", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-1080, -20], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "pccYQxL0liStKP66", "name": "Gmail account INFO"}}, "typeVersion": 1.2}, {"id": "1afea4f3-adea-42ac-bc48-fa863b26e5a0", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-1080, 160], "webhookId": "459d848d-72ed-490f-bc48-e5dc60242896", "parameters": {"path": "459d848d-72ed-490f-bc48-e5dc60242896", "options": {}, "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 2}, {"id": "de4af096-7b23-41ba-b390-8c52f58b09c6", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, -680], "parameters": {"color": 3, "width": 486.*************, "height": 1245.*************, "content": "## HTTP methods\nGET\tRetrieve resources\tFetch VM status, list nodes, get logs.\n\nPOST\tCreate or trigger actions\tStart/stop VMs, create backups.\n\nPUT\tUpdate/replace entire resource configuration\tModify VM configurations.\n\nDELETE\tDelete resources\tRemove VMs, delete users, remove files.\n\nOPTIONS\tFetch supported methods for an endpoint\tCheck available operations for an API.\n\nPATCH\tApply partial updates\tUpdate specific fields in VM settings."}, "typeVersion": 1}, {"id": "2c4ef73b-281f-4a24-81a2-cae72e446955", "name": "Proxmox API Wiki", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-180, 40], "parameters": {"url": "https://pve.proxmox.com/wiki/Proxmox_VE_API", "toolDescription": "Get the proxmox API details from Proxmox Wiki"}, "typeVersion": 1.1}, {"id": "f11ac59e-6031-4435-a417-200cdd559bd2", "name": "Structure Response", "type": "n8n-nodes-base.code", "position": [1480, -520], "parameters": {"jsCode": "// Access all items from the incoming node\nconst items = $input.all();\n\n// Combine all fields of each item into a single string\nconst combinedData = items.map(item => {\n const inputData = item.json; // Access the JSON data of the current item\n \n // Combine all fields into a single string\n const combinedField = Object.entries(inputData)\n .map(([key, value]) => {\n // Handle objects or arrays by converting them to JSON strings\n const formattedValue = typeof value === 'object' ? JSON.stringify(value) : value;\n return `${key}: ${formattedValue}`;\n })\n .join(' | '); // Combine key-value pairs as a single string with a delimiter\n\n // Return the new structure\n return {\n json: {\n combinedField // Only keep the combined field for table representation\n },\n };\n});\n\n// Output the combined data\nreturn combinedData;\n"}, "typeVersion": 2}, {"id": "7752281b-226b-4c19-bcd4-33804ea2abe7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1680, -660], "parameters": {"color": 5, "width": 895.2529822972874, "height": 517.5348441931358, "content": "## Porxmox Custom AI Agent (Get)\nThis agent will convert the response from proxmox to meaningful explanation"}, "typeVersion": 1}, {"id": "fd65db23-0d36-42b1-a012-2ddcdd2ca914", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1680, -122.8638048233953], "parameters": {"color": 5, "width": 900.3261837471116, "height": 712.4591709572671, "content": "## Created or triggered an action on the server.\nResponse will come back here"}, "typeVersion": 1}, {"id": "60234199-d28c-4fb8-8ad7-1d24693599ed", "name": "Structgure Response from Proxmox", "type": "n8n-nodes-base.code", "position": [2120, 140], "parameters": {"jsCode": "// Access the 'data' field from the input\nlet rawData = $json[\"data\"];\n\n// Split the string by colon (:) to extract parts\nlet parts = rawData.split(\":\");\n\n// Create an object with the extracted parts\nreturn {\n upid: parts[0], // UPID\n node: parts[1], // Node (e.g., psb1)\n processID: parts[2], // Process ID\n taskID: parts[3], // Task ID\n timestamp: parts[4], // Timestamp\n operation: parts[5], // Operation (e.g., aptupdate)\n user: parts[7] // User (e.g., root@pam!n8n)\n};\n"}, "typeVersion": 2}, {"id": "57ab92f3-6f65-459d-8f41-8a391108457b", "name": "Format Response and Hide Sensitive Data", "type": "n8n-nodes-base.code", "position": [2380, 140], "parameters": {"jsCode": "// Extract required fields from the input\nlet node = $json[\"node\"] || \"unknown node\";\nlet operation = $json[\"operation\"] || \"unknown operation\";\nlet user = $json[\"user\"] || \"unknown user\";\nlet rawTimestamp = $json[\"timestamp\"] || \"unknown timestamp\";\n\n// Convert timestamp to a readable format\nlet readableTimestamp = \"Invalid timestamp\";\ntry {\n let timestamp = parseInt(rawTimestamp, 16) * 1000; // Convert hex to milliseconds\n readableTimestamp = new Date(timestamp).toLocaleString();\n} catch (error) {\n readableTimestamp = \"Unable to parse timestamp\";\n}\n\n// Construct the simple message\nlet message = `The operation '${operation}' was executed successfully on node '${node}' by user '${user}' at '${readableTimestamp}'.`;\n\nreturn {\n message: message\n};\n"}, "typeVersion": 2}, {"id": "aca671cb-4bb7-4f9e-847a-34d89151d2e2", "name": "If", "type": "n8n-nodes-base.if", "position": [1060, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "da8ce97e-70bf-42a4-981c-e2133bcee24a", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.output.details }}", "rightValue": ""}, {"id": "d7052c40-9a43-452e-901c-6c8fd0122e5f", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.output.details }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "15562980-019c-4d91-8f80-f85420efc8b0", "name": "HTTP Request2", "type": "n8n-nodes-base.httpRequest", "position": [1400, 20], "parameters": {"url": "=https://************:8006/api2/json{{ $json.output.url }}", "method": "=POST", "options": {"allowUnauthorizedCerts": true}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 4.2}, {"id": "fd974862-4e06-4874-8477-c2c3b559669a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1820, -20], "parameters": {}, "typeVersion": 3}, {"id": "5c0d9814-3c9e-4ef4-8f12-9495785c1c06", "name": "HTTP Request3", "type": "n8n-nodes-base.httpRequest", "position": [1400, 200], "parameters": {"url": "=https://************:8006/api2/json{{ $json.output.url }}", "method": "DELETE", "options": {"allowUnauthorizedCerts": true}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 4.2}, {"id": "097c10ac-577e-44ce-8aa2-446137973b18", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-420, 40], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "pKFvSpPWSRFpnBoB", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b26ce08e-9eeb-4fbe-8283-7197d2595021", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1860, -520], "parameters": {"text": "=You are a are a Proxmox Information Output Expert who will provide the summary of the information generated about proxmox. Here is the information about proxmox : from url{{ $('AI Agent').item.json.output.properties.url.pattern }} {{ $json.combinedField }}", "agent": "conversationalAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "942305fd-38b9-4636-8713-35a43fb5879f", "name": "If1", "type": "n8n-nodes-base.if", "position": [1080, 120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "da8ce97e-70bf-42a4-981c-e2133bcee24a", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.output.details }}", "rightValue": ""}, {"id": "d7052c40-9a43-452e-901c-6c8fd0122e5f", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.output.details }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "09bfbbf3-72aa-472f-8e91-2552798263a2", "name": "HTTP Request4", "type": "n8n-nodes-base.httpRequest", "position": [1400, 380], "parameters": {"url": "=https://************:8006/api2/json{{ $json.output.url }}", "method": "DELETE", "options": {"allowUnauthorizedCerts": true}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "pJcVQegRQ5mpraoQ", "name": "Proxmox"}}, "typeVersion": 4.2}, {"id": "18e68174-872a-4bd9-b54f-b7ab97db1b0b", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [1860, 260], "parameters": {}, "typeVersion": 3}, {"id": "1492e53e-66b5-485b-b7e5-a42b76ebccb6", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-260, -300], "parameters": {"text": "=You are a Proxmox AI Agent expert designed to generate API commands based on user input. \nThis is Proxmox which will help you to get the details of existing Proxmox installations, ensure to append to existing url : https://************:8006/api2/ to get response from existing proxmox \n\nMy prommox nodes are named as psb1, psb2 and psb3\npsb1 : https://************:8006/api2/\npsb2 : https://************:8006/api2/\npsb3 : https://************:8006/api2/\n\nYour objectives are:\n\n### **1. Understand User Intent**\n- Parse user requests related to Proxmox operations.\n- Accurately interpret intent to generate valid Proxmox API commands.\n\n### **2. Refer to tools**\n- **Proxmox API Documentation**\n= ** Proxmox API Wiki**\n- **Proxmox**\n- Ensure every generated command meets the API's specifications, including required fields.\n\n### **3. Structure Responses**\nEvery response must include:\n- `response_type`: The HTTP method (e.g., POST, GET, DELETE).\n- `url`: The API endpoint, complete with placeholders (e.g., `/nodes/{node}/qemu/{vmid}`).\n- `details`: The payload for the request. Exclude optional fields if not explicitly defined by the user to allow default handling by Proxmox.\n\n### **4. Validate Inputs**\n- **Mandatory Fields**:\n - Validate user input for required parameters.\n - If missing fields are detected, respond with:\n {\n \"message\": \"Missing required parameters: [list of missing parameters].\"\n }\n\n- **Optional Fields**:\n - Omit fields not provided by the user to leverage Proxmox's defaults.\n\n### **5. Default Behavior**\n- If the user omits the `node`, default to `psb1`.\n- Automatically generate the next available VM ID (`vmid`) by querying Proxmox for the highest existing ID.\n\n### **6. Rules for Outputs**\n- Always respond in strict JSON format:\n - Start with `{` and end with `}`.\n - Avoid additional information or comments.\n - Do not include sensitive data such as passwords, fingerprints, or keys.\n- If input is unrelated to Proxmox, respond with:\n\n {\n \"response_type\": \"Invalid\"\n }\n\n### **7. Examples**\n\n1. Create a VM\nInput: \"Create a VM with ID 201, 2 cores, 4GB RAM, and 32GB disk on node1 using virtio network and SCSI storage.\"\nOutput:\n{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/node1/qemu\",\n \"details\": {\n \"vmid\": 201,\n \"cores\": 2,\n \"memory\": 1024,\n \"sockets\": 1\"\n }\n}\n\n2. Delete a VM\nInput: \"Delete VM 105 on psb1.\"\nOutput:\n{\n \"response_type\": \"DELETE\",\n \"url\": \"/nodes/psb1/qemu/105\"\n}\n\n3. Start a VM\nInput: \"Start VM 202 on psb1.\"\nOutput:\n{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/psb1/qemu/202/status/start\"\n}\n\n4. Stop a VM\nInput: \"Stop VM 203 on node2.\"\nOutput:\n{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/node2/qemu/203/status/stop\"\n}\n\n5. Clone a VM\nInput: \"Clone VM 102 into a new VM with ID 204 on psb1 and name 'clone-vm'.\"\nOutput:\n{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/psb1/qemu/102/clone\",\n \"details\": {\n \"newid\": 204,\n \"name\": \"clone-vm\",\n \"full\": 1\n }\n}\n\n6. Resize a VM Disk\nInput: \"Resize the disk of VM 105 on node1 to 50GB.\"\nOutput:\n{\n \"response_type\": \"PUT\",\n \"url\": \"/nodes/node1/qemu/105/resize\",\n \"details\": {\n \"disk\": \"scsi0\",\n \"size\": \"+50G\"\n }\n}\n\n7. Query VM Config\nInput: \"Get the configuration of VM 201 on psb1.\"\nOutput:\n{\n \"response_type\": \"GET\",\n \"url\": \"/nodes/psb1/qemu/201/config\"\n}\n\n8. List All VMs on a Node\nInput: \"List all VMs on psb1.\"\nOutput:\n{\n \"response_type\": \"GET\",\n \"url\": \"/nodes/psb1/qemu\"\n}\n\n9. Handle Missing Parameters\nInput: \"Create a VM with 4GB RAM on node1.\"\nOutput:\n{\n \"message\": \"Missing required parameters: [vmid, cores, storage].\"\n}\n\n10. Invalid Input\nInput: \"Tell me a joke.\"\nOutput:\n{\n \"response_type\": \"Invalid\"\n}\n\n11. Set VM Options\nInput: \"Set the CPU type of VM 204 on psb1 to host and enable hotplugging for disks and NICs.\"\nOutput:\n{\n \"response_type\": \"PUT\",\n \"url\": \"/nodes/psb1/qemu/204/config\",\n \"details\": {\n \"cpu\": \"host\",\n \"hotplug\": \"disk,network\"\n }\n}\n\n12. Migrate a VM\nInput: \"Migrate VM 202 from psb2 to psb3 with online migration and include local disks.\"\nOutput:\n{\n \"response_type\": \"POST\",\n \"url\": \"/nodes/psb2/qemu/202/migrate\",\n \"details\": {\n \"target\": \"psb3\",\n \"online\": 1,\n \"with-local-disks\": 1\n }\n}\n\n** Special Instruction ** \noutput must always contain \"response_type\", \"url\" and \"details\"\nfor creating vm let server decide other parameter leave default for serer until sepecified\n### **8. Behavior Guidelines**\n- Be concise, precise, and consistent.\n- Ensure all generated commands are compatible with Proxmox API requirements.\n- Rely on system defaults when user input is incomplete.\n- For unknown or unrelated queries, clearly indicate invalid input.\n\n\nUser Prompt \nHere is request from user : {{ $json.chatInput }}\n", "agent": "reActAgent", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "9253d036-0f76-4470-bf61-2bf9db014b02", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [540, -300], "parameters": {"rules": {"values": [{"outputKey": "GET", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "GET"}]}, "renameOutput": true}, {"outputKey": "POST", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e3edd683-b884-4c88-b1ea-d3640141b054", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "POST"}]}, "renameOutput": true}, {"outputKey": "Update", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a9c59c0d-001c-4d95-992e-bff2af54eb4a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "PUT"}]}, "renameOutput": true}, {"outputKey": "OPTIONS", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "70bf8cc2-0a43-431c-97c7-a8b4eadb5bd9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "OPTIONS"}]}, "renameOutput": true}, {"outputKey": "DELETE", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e43b05b-7f45-40a3-b8aa-180dd8155b08", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "DELETE"}]}, "renameOutput": true}, {"outputKey": "INVALID", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bd03a24c-a233-4302-a576-1bfe0060c367", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.response_type }}", "rightValue": "Invalid"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "c410a832-dafc-479a-93d6-b96ae4f6d3fb", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-720, -680], "parameters": {"color": 7, "width": 261.5261328042567, "height": 1262.1316376259997, "content": "## Trigger\nYou can use any trigger as input, a chat, telegram, email etc\n\nYou can think of any input, even it could be from your cloud platform, your own Web Applicaiton, etc. \n\nPossibilities are limitless.\n\nCha<PERSON> is shown just as example."}, "typeVersion": 1}, {"id": "a4962963-ce33-4398-ad9d-75df3a85c64f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -680], "parameters": {"color": 4, "width": 475.27306699862953, "height": 515.4734551650874, "content": "## Developed by <PERSON><PERSON>\n\nThank you for using this workflow template. It has taken me countless hours of hard work, research, and dedication to develop, and I sincerely hope it adds value to your work.\n\nIf you find this template helpful, I kindly ask you to consider supporting my efforts. Your support will help me continue improving and creating more valuable resources.\n\nYou can contribute via PayPal here:\n\nhttp://paypal.me/pmptraining\n\nAdditionally, when sharing this template, I would greatly appreciate it if you include my original information to ensure proper credit is given.\n\nThank you for your generosity and support!\nEmail : <EMAIL>\nhttps://linkedin.com/in/amjidali\nhttps://syncbricks.com\nhttps://youtube.com/@syncbricks"}, "typeVersion": 1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}], [{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}], [{"node": "HTTP Request4", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Structgure Response from Proxmox", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Structgure Response from Proxmox", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "If", "type": "main", "index": 0}], null, null, [{"node": "If1", "type": "main", "index": 0}]]}, "Proxmox": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Structure Response", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "HTTP Request3": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "HTTP Request4": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Proxmox API Wiki": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Structure Response": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Proxmox API Documentation": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Structgure Response from Proxmox": {"main": [[{"node": "Format Response and Hide Sensitive Data", "type": "main", "index": 0}]]}}}