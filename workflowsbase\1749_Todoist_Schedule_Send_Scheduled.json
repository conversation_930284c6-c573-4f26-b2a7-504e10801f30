{"id": "WUX0BsRA1dbzTKnl", "meta": {"instanceId": "bdce9ec27bbe2b742054f01d034b8b468d2e7758edd716403ad5bd4583a8f649", "templateCredsSetupCompleted": true}, "name": "Email mailbox as Todoist tasks", "tags": [], "nodes": [{"id": "5b711a67-3d03-4687-a550-0514e2a5d251", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-220, 100], "parameters": {}, "typeVersion": 1}, {"id": "5862ae85-d48b-49a6-9c9f-4a682d42af78", "name": "<PERSON>", "type": "n8n-nodes-base.gmail", "position": [580, -240], "webhookId": "b1551d40-6d53-47ec-ad90-0386c04de860", "parameters": {"messageId": "={{ $json.id }}", "operation": "mark<PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "UmjYQEDj616cX3UR", "name": "Gmail lukp12"}}, "typeVersion": 2.1}, {"id": "19655c59-dc43-4d3d-aaa1-a23e0b5c64e2", "name": "Star", "type": "n8n-nodes-base.gmail", "position": [780, -240], "webhookId": "b1551d40-6d53-47ec-ad90-0386c04de860", "parameters": {"labelIds": ["STARRED"], "messageId": "={{ $json.id }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "UmjYQEDj616cX3UR", "name": "Gmail lukp12"}}, "typeVersion": 2.1}, {"id": "62eeda04-39b2-4423-90e4-7d6518210b00", "name": "Get Starred From Inbox", "type": "n8n-nodes-base.gmail", "position": [300, 20], "webhookId": "848b9f06-db3c-4c74-aac3-fb40feb1187b", "parameters": {"filters": {"labelIds": ["STARRED", "INBOX"]}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "UmjYQEDj616cX3UR", "name": "Gmail lukp12"}}, "typeVersion": 2.1}, {"id": "dfd857e6-7728-4d7e-88ad-c20ffab1a90b", "name": "Get Unread From Inbox", "type": "n8n-nodes-base.gmail", "position": [300, -160], "webhookId": "848b9f06-db3c-4c74-aac3-fb40feb1187b", "parameters": {"filters": {"labelIds": ["INBOX"], "readStatus": "unread"}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "UmjYQEDj616cX3UR", "name": "Gmail lukp12"}}, "typeVersion": 2.1}, {"id": "a864282e-c50c-4719-a813-dc407f28043b", "name": "If Task Not Exist", "type": "n8n-nodes-base.if", "position": [-140, 800], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "443f2e6e-5145-4c09-af8e-193f24b6536f", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.content }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "7f0ce1d6-14de-4087-b3f1-542cf38e5df3", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [220, 820], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "zjIZQuuuZMJpiUny", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "7e314e74-1f3e-460e-99f2-7338e3f4627e", "name": "Structure Output Todoist Ready", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [600, 820], "parameters": {"jsonSchemaExample": "{\n\t\"content\": \"Task name\",\n\t\"description\": \"Description of the thread\",\n    \"actions\": \"What actions should you take before closing this task\",\n    \"answer\": \"Proposed answer to the thread or propositions of possible actions\"\n}"}, "typeVersion": 1.2}, {"id": "a36464d3-917c-4a8c-bd4b-f5ae21e3d758", "name": "If AI responded properly", "type": "n8n-nodes-base.if", "position": [740, 580], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1109c955-21b6-4342-8851-1926ae4216b7", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.output.content }}", "rightValue": ""}, {"id": "cc5ac32f-564f-47be-8782-600d278995fb", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.output.description }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "4d2de14b-719d-44f2-a207-94cc4394738a", "name": "Create Todoist Task", "type": "n8n-nodes-base.todoist", "position": [980, 560], "parameters": {"content": "={{ $json.output.content }}", "options": {"description": "={{ $json.output.description }}\n\n**Proposed actions**\n\n{{ $json.output.actions }}\n\n**Proposed answer**\n\n{{ $json.output.answer }}"}, "project": {"__rl": true, "mode": "list", "value": "2351998202", "cachedResultName": "Test Project"}}, "credentials": {"todoistApi": {"id": "3MjbugUWx4uLv97e", "name": "Todoist <PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "25fddc8e-6781-473a-9978-aae3c900e39a", "name": "Get Full Message", "type": "n8n-nodes-base.gmail", "position": [140, 580], "webhookId": "d20c5a51-4078-47e2-bec4-54af54ddf4dd", "parameters": {"simple": false, "options": {}, "messageId": "={{ $json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "UmjYQEDj616cX3UR", "name": "Gmail lukp12"}}, "typeVersion": 2.1}, {"id": "7531e446-4d17-460f-b3c4-6830c01060c0", "name": "Summarize Message", "type": "@n8n/n8n-nodes-langchain.agent", "position": [340, 580], "parameters": {"text": "=You are a professional email assistant. Your task is to analyze provided email content and transform email into task. Your output will be JSON and will have:\n\n- \"content\", which is exactly \"{{ $json.subject }}\"\n- \"description\", which is short summary what this email is about and what is whole thread point\n- \"actions\", which is your proposition on what actions should be done before responding or closing this email thread\n- \"answer\", which is your proposition on how to respond to this email, when all actions are done\n\nAnswer should be detailed and assuming that content of the email is not known to reader.\n\nEmail content is:\n\n{{ $json.html }}", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8, "alwaysOutputData": true}, {"id": "5034b666-de2f-42f4-8b3a-3a4e36ca52a7", "name": "<PERSON>rich Emails With Tasks", "type": "n8n-nodes-base.merge", "position": [760, 380], "parameters": {"mode": "combine", "options": {"multipleMatches": "all"}, "advanced": true, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "Subject", "field2": "content"}]}}, "typeVersion": 3.1}, {"id": "ec352eb7-7670-47ab-b16e-6407b9a69455", "name": "Enrich Tasks with Emails", "type": "n8n-nodes-base.merge", "position": [900, 180], "parameters": {"mode": "combine", "options": {"multipleMatches": "all"}, "advanced": true, "joinMode": "enrichInput2", "mergeByFields": {"values": [{"field1": "Subject", "field2": "content"}]}}, "typeVersion": 3.1}, {"id": "e3a513f0-8113-4bcc-990f-c113a1086bc6", "name": "If <PERSON><PERSON>red (Not Exist)", "type": "n8n-nodes-base.if", "position": [-200, 1160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4ed052b1-1938-4537-9099-f8e2475a63b3", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.Subject }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "5acaa018-6592-4449-8ff4-9225f8ed28c5", "name": "Close Task", "type": "n8n-nodes-base.todoist", "position": [80, 1100], "parameters": {"taskId": "={{ $json.id }}", "operation": "close"}, "credentials": {"todoistApi": {"id": "3MjbugUWx4uLv97e", "name": "Todoist <PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "4004e68a-4dcf-4216-bd01-2f2da0eeba77", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "disabled": true, "position": [-220, -80], "parameters": {"options": {}}, "credentials": {"imap": {"id": "qdjU2dd5Fy7RqGPx", "name": "IMAP support@sailingbyte"}}, "typeVersion": 2}, {"id": "0e986c57-a016-4053-acb3-08e3b3a7af08", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "disabled": true, "position": [-220, -260], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "fe1012a0-638f-469f-af09-f30a890a4937", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [0, 100], "parameters": {}, "typeVersion": 1}, {"id": "5e09f039-53cc-4473-ab60-5c1337ab75af", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-280, -360], "parameters": {"width": 500, "height": 660, "content": "## Select Trigger\n**This workflow will work with many triggers**"}, "typeVersion": 1}, {"id": "1874a3c5-7467-4642-9ce4-7171b6e76af7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [500, -380], "parameters": {"width": 560, "height": 320, "content": "## Read and Star\n**Mark messages as Read and add <PERSON> to them**\nYou can delete this section if you don't want to make this happen automatically to your emails"}, "typeVersion": 1}, {"id": "c8f68aac-971c-427d-8b74-167d78b37037", "name": "<PERSON><PERSON> Starred and Unread Messages", "type": "n8n-nodes-base.merge", "position": [600, 40], "parameters": {}, "typeVersion": 3.1}, {"id": "7eab417d-6332-45d9-be0d-ba7b8888dd91", "name": "Get Open Tasks", "type": "n8n-nodes-base.todoist", "position": [300, 200], "parameters": {"filters": {"projectId": "2351998202"}, "operation": "getAll"}, "credentials": {"todoistApi": {"id": "3MjbugUWx4uLv97e", "name": "Todoist <PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "60071d08-d333-475e-a48b-cdb9c19eb7a2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-240, 1000], "parameters": {"width": 720, "height": 340, "content": "## Close Task\n**If <PERSON><PERSON> was Unstarred manually by you, then we can probably close task, so we are closing it**"}, "typeVersion": 1}, {"id": "780d6400-226b-4da6-8d33-6f7977fcbb2d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-200, 380], "parameters": {"width": 1420, "height": 600, "content": "## Make New Task in Todoist\nIf there is no task on Todoist with same subject as your either starred or unread message, then it's time to create one.\nBut to make it easier for you to close tasks and emails, let AI help you out.\nAI not only summarizes message. It also proposes actions you should take or answers how you should reply to this email."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "66e4624f-4d02-4895-aeda-270e9277acdf", "connections": {"Close Task": {"main": [[]]}, "Mark As Read": {"main": [[{"node": "Star", "type": "main", "index": 0}]]}, "Get Open Tasks": {"main": [[{"node": "<PERSON>rich Emails With Tasks", "type": "main", "index": 1}, {"node": "Enrich Tasks with Emails", "type": "main", "index": 1}]]}, "Get Full Message": {"main": [[{"node": "Summarize Message", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "If Task Not Exist": {"main": [[{"node": "Get Full Message", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Summarize Message", "type": "ai_languageModel", "index": 0}]]}, "Summarize Message": {"main": [[{"node": "If AI responded properly", "type": "main", "index": 0}]]}, "Email Trigger (IMAP)": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Get Unread From Inbox": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON> Starred and Unread Messages", "type": "main", "index": 0}]]}, "Get Starred From Inbox": {"main": [[{"node": "<PERSON><PERSON> Starred and Unread Messages", "type": "main", "index": 1}]]}, "Enrich Emails With Tasks": {"main": [[{"node": "If Task Not Exist", "type": "main", "index": 0}]]}, "Enrich Tasks with Emails": {"main": [[{"node": "If <PERSON><PERSON>red (Not Exist)", "type": "main", "index": 0}]]}, "If AI responded properly": {"main": [[{"node": "Create Todoist Task", "type": "main", "index": 0}], []]}, "No Operation, do nothing": {"main": [[{"node": "Get Unread From Inbox", "type": "main", "index": 0}, {"node": "Get Starred From Inbox", "type": "main", "index": 0}, {"node": "Get Open Tasks", "type": "main", "index": 0}]]}, "If Email Unstarred (Not Exist)": {"main": [[{"node": "Close Task", "type": "main", "index": 0}]]}, "Structure Output Todoist Ready": {"ai_outputParser": [[{"node": "Summarize Message", "type": "ai_outputParser", "index": 0}]]}, "Merge Starred and Unread Messages": {"main": [[{"node": "<PERSON>rich Emails With Tasks", "type": "main", "index": 0}, {"node": "Enrich Tasks with Emails", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}}