{"id": "ThLx9WKLEujJHrvW", "meta": {"instanceId": "f047839feca8ac8518cd22514bc718f9790615975a10271981c34822b5cd9b5b", "templateCredsSetupCompleted": true}, "name": "Github Releases", "tags": [], "nodes": [{"id": "597d4aa3-e56a-4831-a0a8-6414e6e56de3", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [600, 380], "parameters": {}, "typeVersion": 1}, {"id": "731ac3c8-9c24-4f73-aad1-f96f359cf0f7", "name": "Loop", "type": "n8n-nodes-base.splitInBatches", "position": [40, 255], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "150d10c1-97ee-48b2-8d78-0bcce9776f7c", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1440, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7cbf2c0f-f242-4106-95c3-684d1b0959b1", "name": "name", "type": "string", "value": "={{ $('Loop').item.json.name }}"}, {"id": "cdd6bd5d-d4b8-4656-8b01-1521f870b3d9", "name": "title", "type": "string", "value": "={{ $('Limit').item.json.title }}"}, {"id": "61164f4d-d97c-4588-a54a-81b230b2cf3b", "name": "link", "type": "string", "value": "={{ $('Limit').item.json.link }}"}, {"id": "f1b1717b-4689-4356-8deb-f103a69af0e1", "name": "pubDate", "type": "string", "value": "={{ $('Limit').item.json.pubDate }}"}, {"id": "ec9394a9-5adb-4a00-92ca-b4a52f742ac0", "name": "contentSnippet", "type": "string", "value": "={{ $('Limit').item.json.contentSnippet }}"}, {"id": "678d9b68-f5a5-4968-a5dc-827c3dd0fcfb", "name": "id", "type": "string", "value": "={{ $('Limit').item.json.id }}"}, {"id": "d57a1455-b5d6-4caa-870c-0a4fac317932", "name": "github", "type": "string", "value": "={{ $('Loop').item.json.github }}"}]}}, "typeVersion": 3.4}, {"id": "c65ab032-a35a-4a00-89ed-de897d45b62f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.scheduleTrigger", "position": [-840, 260], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */10 9-23 * * *"}]}}, "typeVersion": 1.2}, {"id": "ebe92d40-a4a3-49fa-ae49-c1d0b87fcc0d", "name": "GitHub Config", "type": "n8n-nodes-base.code", "position": [-400, 260], "parameters": {"jsCode": "return [\n  {\n    \"name\": \"n8n\",\n    \"github\": \"n8n-io/n8n\"\n  },\n  {\n    \"name\": \"Roo-Code\",\n    \"github\": \"RooVetGit/Roo-Code\"\n  },\n  {\n    \"name\": \"LobeChat\",\n    \"github\": \"lobehub/lobe-chat\"\n  },\n  {\n    \"name\": \"New API\",\n    \"github\": \"Calcium-Ion/new-api\"\n  },\n  {\n    \"name\": \"ChatWise\",\n    \"github\": \"egoist/chatwise-releases\"\n  },\n  {\n    \"name\": \"Folo\",\n    \"github\": \"RSSNext/Folo\"\n  },\n  {\n    \"name\": \"Clash Verge Rev\",\n    \"github\": \"clash-verge-rev/clash-verge-rev\"\n  },\n  {\n    \"name\": \"Cherry Studio\",\n    \"github\": \"CherryHQ/cherry-studio\"\n  }\n];"}, "notesInFlow": false, "typeVersion": 2}, {"id": "4e659c3f-3fa4-42c8-aceb-9ea18dfcff0f", "name": "If No Error", "type": "n8n-nodes-base.if", "position": [420, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "56f4a7f3-7823-4794-ad74-bac41ef85d83", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.error }}", "rightValue": 0}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "f9ccdc63-06ae-47d0-8429-7a2b63d8c38a", "name": "If New", "type": "n8n-nodes-base.if", "position": [940, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "ed896551-f486-4a1f-8585-8660f3a4a9bd", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.cache }}", "rightValue": "={{ $('Limit').item.json.id }}"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "caf31152-18f0-4bf7-b09c-f76ba05dec5b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [1200, 560], "parameters": {"options": {}}, "typeVersion": 3.4}, {"id": "1fe3264a-2db3-4d5c-b800-182e15a8a355", "name": "Send Error", "type": "n8n-nodes-base.slack", "position": [620, 560], "webhookId": "eaf921a6-4cc9-472f-bdf3-dd24db51c769", "parameters": {"text": "=:x: *`{{ $('Loop').item.json.name }}`* Error\n\n> {{ $json.error }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08ME7TDZ3J", "cachedResultName": "github-release"}, "otherOptions": {"mrkdwn": true, "sendAsUser": "Release Bot", "unfurl_links": false, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "NG6LWZ4Leh25N3VZ", "name": "波特科技"}}, "typeVersion": 2.2}, {"id": "970c3556-abf9-402f-85bc-b80da949ce0b", "name": "If Not Empty", "type": "n8n-nodes-base.if", "position": [220, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4fdc7d1e-68f6-45ea-af6e-59a1eddbf214", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "0425dbee-461f-4fdb-a9d2-4f78beb61826", "name": "Date Format", "type": "n8n-nodes-base.dateTime", "position": [780, 240], "parameters": {"date": "={{ $('Loop').item.json.pubDate }}", "format": "custom", "options": {"timezone": true}, "operation": "formatDate", "customFormat": "yyyy-MM-dd HH:mm"}, "typeVersion": 2}, {"id": "a06e7050-1f84-4083-9cd3-a6c4f2dd25f3", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [440, 240], "parameters": {"text": "={{ $json.contentSnippet }}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nYou need to analyze GitHub Release:\n\n1. Parse input content and identify all change items\n2. Filter out:\n   - Contributor handles (@username)\n   - Version numbers\n   - Appreciation/congratulatory statements\n3. Categorize into:\n   - features: New functionalities\n   - fixes: Bug fixes\n   - others: Documentation, configurations, etc.\n4. Language conversion:\n   - Translate English descriptions to Chinese\n   - Technical terms can remain in English but must use Chinese syntax\n5. Maintain original meaning with necessary simplification:\n   - Remove redundancies\n   - Merge similar entries\n   - Simplify technical jargon\n\nProhibited elements:\n1. Explanatory text\n2. Markdown formatting\n3. Uncategorized content\n4. Untranslated English items\n5. Empty category headers"}, "schemaType": "fromJson", "jsonSchemaExample": "{\n  \"features\": [\n    \"新增首页功能，默认启动页面改为首页\",\n    \"新增 DNS 覆写功能，默认启用 DNS 覆写\"\n  ],\n  \"fixes\": [\n    \"修复弹黑框的问题\",\n    \"修复系统代理地址错误的问题\"\n  ],\n  \"others\": [\n    \"重构后端，巨幅性能优化\",\n    \"优化定时器管理\"\n  ]\n}"}, "typeVersion": 1}, {"id": "42ed9553-ed63-4554-b0c5-8b4d9a1e9ae9", "name": "Send Message", "type": "n8n-nodes-base.slack", "position": [1200, 240], "webhookId": "eaf921a6-4cc9-472f-bdf3-dd24db51c769", "parameters": {"text": "=Release - {{ $('If Not Empty').item.json.name }}", "select": "channel", "blocksUi": "={{ $json }}", "channelId": {"__rl": true, "mode": "id", "value": "C08ME7TDZ3J"}, "messageType": "block", "otherOptions": {"mrkdwn": true, "sendAsUser": "GitHub Release", "unfurl_links": false, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "NG6LWZ4Leh25N3VZ", "name": "波特科技"}}, "typeVersion": 2.2}, {"id": "c4b89e3f-0c61-493d-8950-e77b56f38ca3", "name": "Gemini", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [440, 100], "parameters": {"options": {"temperature": 0.3}, "modelName": "models/gemini-2.0-flash-001"}, "credentials": {"googlePalmApi": {"id": "wN3fB5ELQ7iJt3b8", "name": "Gemini"}}, "typeVersion": 1}, {"id": "b3979529-5445-4d44-bd9e-69079b222b8d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, -140], "parameters": {"width": 420, "height": 540, "content": "## GitHub Releases Config\n- Edit the JavaScript array within this node's code area.\n- Modify or add the repositories you want to follow. Each repository object needs a `name` (custom display name) and `github` (format: `owner/repo`).\n- Example:\n   ```javascript\n   {\n    \"name\": \"n8n\", // Custom display name\n    \"github\": \"n8n-io/n8n\" // GitHub path\n   },\n   {\n    \"name\": \"LobeChat\",\n    \"github\": \"lobehub/lobe-chat\"\n   }\n   // ... add more repositories\n   ```"}, "typeVersion": 1}, {"id": "ed1b69c4-cb95-424a-85e8-7de827b20e22", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-900, 80], "parameters": {"width": 260, "height": 340, "content": "## <PERSON><PERSON>\nAdjust the `Rule` setting to change the update check frequency (default is `0 */10 9-23 * * *`, checking every 10 minutes between 9 AM and 11 PM daily)."}, "typeVersion": 1}, {"id": "0ff16ac1-755d-4a83-a631-e6a8df4d14a6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, -220], "parameters": {"width": 380, "height": 580, "content": "## Gemini (AI Model)\n- Select your configured Google Gemini credentials.\n- (Optional) Replace with a different supported AI model node and select its credentials.\n## Information Extractor \nAI Processing & Translation\n- **Main Configuration**: Review the `System Prompt`. By default, it asks the AI to extract information and translate it into **Chinese**. Modify this prompt if you need a different language or summary style."}, "typeVersion": 1}, {"id": "6a985f02-105c-4f6e-a924-2289538dfdc0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1140, 20], "parameters": {"height": 380, "content": "## Send Message\nSlack Notifications\n- Select your configured Slack credentials in both Slack nodes.\n- Set the target `Channel ID` for notifications."}, "typeVersion": 1}, {"id": "80300633-feba-4f12-9ee6-2abba300a153", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [560, 540], "parameters": {"height": 340, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Send Error\n- Select your configured Slack credentials in both Slack nodes.\n- Set the target `Channel ID` for notifications."}, "typeVersion": 1}, {"id": "9f671e1d-0b72-4e2c-ae80-f65a5aa56c1d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1440, -220], "parameters": {"width": 460, "height": 900, "content": "## Prerequisites\n\n* **Redis**: Have an available Redis service and configure its credentials in n8n.\n* **AI Provider (Gemini)**: Configure credentials for Google Gemini (or your chosen AI model) in n8n.\n* **Slack**: Configure your Slack app credentials in n8n.\n\n## Slack Permissions Config\n- In the `Bot Token Scopes` section of the `OAuth & Permissions` menu, add the following permissions:\n   - `chat:write`\n   - `chat:write.customize`\n- Perform the `Install` (or Reinstall) operation in the `Install App` menu.\n- Obtain the `Bot User OAuth Token` and configure it in the credentials of n8n."}, "typeVersion": 1}, {"id": "1b4274ec-0364-4c8d-b040-8882e48ab192", "name": "Redis Set Id", "type": "n8n-nodes-base.redis", "position": [1440, 240], "parameters": {"key": "=github_release:{{ $('If Not Empty').item.json.github }}", "value": "={{ $('If Not Empty').item.json.id }}", "keyType": "string", "operation": "set"}, "credentials": {"redis": {"id": "qrUBdRWlD3Zuri46", "name": "Redis account"}}, "typeVersion": 1}, {"id": "3a809420-5bee-4976-a57e-ca161677de76", "name": "Code for Slack Tpl", "type": "n8n-nodes-base.code", "position": [980, 240], "parameters": {"jsCode": "function generateRichTextBlock(title, items) {\n  return {\n    type: \"rich_text\",\n    elements: [\n      {\n        type: \"rich_text_section\",\n        elements: [{ type: \"text\", text: `${title}:` }]\n      },\n      {\n        type: \"rich_text_list\",\n        style: \"bullet\",\n        indent: 0,\n        border: 0,\n        elements: items.map(item => ({\n          type: \"rich_text_section\",\n          elements: [{ type: \"text\", text: item }]\n        }))\n      }\n    ]\n  };\n}\n\nfunction generateRichText(value, metadata) {\n  if (!value || typeof value !== 'object') return [];\n\n  const { name, link, title, formattedDate } = metadata;\n  \n  const baseBlocks = [\n    {\n      type: \"header\",\n      text: {\n        type: \"plain_text\",\n        text: name\n      }\n    },\n    {\n      type: \"context\",\n      elements: [{\n        text: `${formattedDate}  |  <${link}|${title}>`,\n        type: \"mrkdwn\"\n      }]\n    },\n    { type: \"divider\" }\n  ];\n\n  const sections = [\n    { key: \"features\", title: \"Features\" },\n    { key: \"fixes\", title: \"Fixes\" },\n    { key: \"others\", title: \"Others\" }\n  ];\n\n  const contentBlocks = sections\n    .filter(({ key }) => Array.isArray(value[key]) && value[key].length > 0)\n    .map(({ key, title }) => generateRichTextBlock(title, value[key]));\n\n  return {\n    blocks: [...baseBlocks, ...contentBlocks]\n  };\n}\n\nfunction processAllItems(infoExtractor, ifNotEmpty, dateFormat) {\n  return infoExtractor.all().map((item, index) => {\n    const metadata = {\n      name: ifNotEmpty.all()[index].json.name,\n      link: ifNotEmpty.all()[index].json.link,\n      title: ifNotEmpty.all()[index].json.title,\n      formattedDate: dateFormat.all()[index].json.formattedDate\n    };\n    return generateRichText(item.json.output, metadata);\n  });\n}\n\nreturn processAllItems(\n  $('Information Extractor'),\n  $('If Not Empty'), \n  $('Date Format')\n);"}, "typeVersion": 2}, {"id": "d11a10fc-c68b-4e2b-a00e-5d63ec38abf6", "name": "RSS for Release", "type": "n8n-nodes-base.rssFeedRead", "onError": "continueRegularOutput", "position": [220, 380], "parameters": {"url": "=https://github.com/{{ $json.github }}/releases.atom ", "options": {}}, "typeVersion": 1.1}, {"id": "e9691400-a3de-4267-93d8-f99469399e21", "name": "Redis Get", "type": "n8n-nodes-base.redis", "position": [780, 380], "parameters": {"key": "=github_release:{{ $('Loop').item.json.github }}", "keyType": "string", "options": {"dotNotation": false}, "operation": "get", "propertyName": "cache"}, "credentials": {"redis": {"id": "qrUBdRWlD3Zuri46", "name": "Redis account"}}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "19314a54-e5b4-49ef-a550-1cabb23c8104", "connections": {"Loop": {"main": [[{"node": "If Not Empty", "type": "main", "index": 0}], [{"node": "RSS for Release", "type": "main", "index": 0}]]}, "Null": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Redis Get", "type": "main", "index": 0}]]}, "Gemini": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "If New": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Redis Get": {"main": [[{"node": "If New", "type": "main", "index": 0}]]}, "Date Format": {"main": [[{"node": "Code for Slack Tpl", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "If No Error": {"main": [[{"node": "Limit", "type": "main", "index": 0}], [{"node": "Send Error", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Cron Trigger": {"main": [[{"node": "GitHub Config", "type": "main", "index": 0}]]}, "If Not Empty": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Redis Set Id": {"main": [[]]}, "Send Message": {"main": [[{"node": "Redis Set Id", "type": "main", "index": 0}]]}, "GitHub Config": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "RSS for Release": {"main": [[{"node": "If No Error", "type": "main", "index": 0}]]}, "Code for Slack Tpl": {"main": [[{"node": "Send Message", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Date Format", "type": "main", "index": 0}]]}}}