{"id": "IvgAFAUOSI3biT4L", "meta": {"instanceId": "2723a3a635131edfcb16103f3d4dbaadf3658e386b4762989cbf49528dccbdbd"}, "name": "Translate Telegram audio messages with AI (55 supported languages) v1", "tags": [], "nodes": [{"id": "f91fa0cf-ea01-4fc0-9ef2-754da399b7fb", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [440, 220], "webhookId": "c537cfcc-6c4a-436a-8871-d32f8ce016cb", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "057ae05f-2c7d-48c5-a057-a6917a88971c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1240, 0], "parameters": {"width": 556.*************, "height": 586.*************, "content": "## Translation\n\n- Converts from speech to text.\n\n- Translates the language from the native language to translated language (as specified in settings node)\n\n"}, "typeVersion": 1}, {"id": "c6947668-118e-4e23-bc55-1cdbce554a20", "name": "Text reply", "type": "n8n-nodes-base.telegram", "position": [2240, 220], "parameters": {"text": "={{ $json.text }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "93551aea-0213-420d-bf82-7669ab291dae", "name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [1060, 220], "parameters": {"fileId": "={{ $('Telegram Trigger').item.json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "972177e4-b0a4-424f-9ca6-6555ff3271d7", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1520, 400], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0e8f610f-03a7-4943-bd19-b3fb10c89519", "name": "Input Error Handling", "type": "n8n-nodes-base.set", "position": [860, 220], "parameters": {"fields": {"values": [{"name": "message.text", "stringValue": "={{ $json?.message?.text || \"\" }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "c8ab9e01-c9b5-4647-8008-9157ed97c4c3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1920, 0], "parameters": {"width": 585.*************, "height": 583.*************, "content": "## Telegram output\n\n- Provide the output in both text as well as speech. \n\n- Many languages are supported including English,French, German, Spanish, Chinese, Japanese.\n\nFull list here:\nhttps://platform.openai.com/docs/guides/speech-to-text/supported-languages\n"}, "typeVersion": 1}, {"id": "0898dc4d-c3ad-43df-871f-1896f673f631", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-140, 0], "parameters": {"color": 4, "width": 489.00549958607303, "height": 573.4892086330929, "content": "## Multi-lingual AI Powered Universal Translator with Speech ⭐\n\n### Key capabilities\nThis flow enables a Telegram bot that can \n- accept speech in one of 55 languages \n- translates to another language and returns result in speech\n\n### Use case:\n- Learning a new language\n- Communicate with others while traveling to another country\n\n### Setup\n- Open the Settings node and specify the languages you would like to work with"}, "typeVersion": 1}, {"id": "ae0595d2-7e40-4c1e-a643-4b232220d19a", "name": "Settings", "type": "n8n-nodes-base.set", "position": [660, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "501ac5cc-73e8-4e9c-bf91-df312aa9ff88", "name": "language_native", "type": "string", "value": "english"}, {"id": "efb9a7b2-5baa-44cc-b94d-c8030f17e890", "name": "language_translate", "type": "string", "value": "french"}]}}, "typeVersion": 3.3}, {"id": "2d3654cf-a182-4916-a50c-a501828c2f6e", "name": "Auto-detect and translate", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1500, 220], "parameters": {"text": "=Detect the language of the text that follows. \n- If it is {{ $('Settings').item.json.language_native }} translate to {{ $('Settings').item.json.language_translate }}. \n- If it is in {{ $('Settings').item.json.language_translate }} translate to {{ $('Settings').item.json.language_native }} . \n- In the output just provide the translation and do not explain it. Just provide the translation without anything else.\n\nText:\n {{ $json.text }}\n", "promptType": "define"}, "typeVersion": 1.4}, {"id": "a6e63516-4967-4e81-ba5b-58ad0ab21ee3", "name": "Audio reply", "type": "n8n-nodes-base.telegram", "position": [2240, 400], "parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "operation": "sendAudio", "binaryData": true, "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "e4782117-03de-41d2-9208-390edc87fc08", "name": "OpenAI2", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 220], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1.3}, {"id": "b29355f5-122c-4557-8215-28fdb523d221", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2020, 400], "parameters": {"input": "={{ $json.text }}", "options": {}, "resource": "audio"}, "credentials": {"openAiApi": {"id": "fOF5kro9BJ6KMQ7n", "name": "OpenAi account"}}, "typeVersion": 1.3}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ac9c6f40-10c8-4b60-9215-8d4e253bf318", "connections": {"OpenAI": {"main": [[{"node": "Audio reply", "type": "main", "index": 0}]]}, "OpenAI2": {"main": [[{"node": "Auto-detect and translate", "type": "main", "index": 0}]]}, "Settings": {"main": [[{"node": "Input Error Handling", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "OpenAI2", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Auto-detect and translate", "type": "ai_languageModel", "index": 0}]]}, "Input Error Handling": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "Auto-detect and translate": {"main": [[{"node": "Text reply", "type": "main", "index": 0}, {"node": "OpenAI", "type": "main", "index": 0}]]}}}