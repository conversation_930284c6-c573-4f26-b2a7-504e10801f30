{"id": "EJHT9UmGXNOyynV0", "meta": {"instanceId": "a67174bc280416abad7fd5fdbb66d968f3f284b847009b8f7b28adae86c50c98", "templateCredsSetupCompleted": true}, "name": "Scans von PDF zu Nextcloud", "tags": [], "nodes": [{"id": "574d02f2-54c9-4f24-9c8b-4618ccdf2c7c", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-80, -80], "parameters": {"url": "http://192.168.1.100:8080/api/v1/files", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "7a1b5ef3-750f-45c5-b60e-34d463978abf", "name": "Nextcloud", "type": "n8n-nodes-base.nextCloud", "position": [340, -80], "parameters": {"path": "=/Scans/{{ $json.name }}", "binaryDataUpload": true}, "credentials": {"nextCloudApi": {"id": "P2d7981fwo6hiE8n", "name": "NextCloud account"}}, "typeVersion": 1}, {"id": "93a27a7e-d709-4ceb-b062-4136fcaa7c0a", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [140, -80], "parameters": {"url": "=http://192.168.1.100:8080/api/v1/files/{{ $json.name }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "*/*"}]}}, "typeVersion": 4.2}, {"id": "********-b1b3-4a75-8190-628cb10c6734", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-280, -80], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.2}, {"id": "c49a991e-0faf-4326-9238-d3cf4a661ea5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -220], "parameters": {"width": 900, "height": 380, "content": "## Copy Scanner Documents to Nextcloud\n** Needed USB-Scanner and Program ScanServJS with an API"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1c982aa5-fffb-469b-8b2c-8f5b974f9f44", "connections": {"HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Nextcloud", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}