{"id": "R4EuB1gx1IpMXCJM", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "CoinMarketCap_Crypto_Agent_Tool", "tags": [], "nodes": [{"id": "c055762a-8fe7-4141-a639-df2372f30060", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-240, 260], "parameters": {"workflowInputs": {"values": [{"name": "message"}, {"name": "sessionId"}]}}, "typeVersion": 1.1}, {"id": "3638379c-fad2-4d3b-bb90-b32242da4cc7", "name": "CoinMarketCap Crypto Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [260, 260], "parameters": {"text": "={{ $json.message }}", "options": {"systemMessage": "You are an AI cryptocurrency analyst. You have access to six live CoinMarketCap tools, each linked to a real API endpoint. These tools allow you to retrieve price data, metadata, market rankings, conversions, and global market stats.\n\nUse the most relevant tool based on the user’s intent. Below is a list of your currently connected tools, their functions, and accepted input parameters.\n\n---\n\n### 🔧 **Connected Tools & Supported Inputs**\n\n---\n\n#### 1. **Crypto Map**\n- **Endpoint**: `/v1/cryptocurrency/map`\n- **Purpose**: Get CoinMarketCap IDs, symbols, and names.\n- **Supported Inputs**:\n  - `symbol` – (Optional) Comma-separated crypto symbols (e.g., BTC,ETH)\n  - `listing_status` – `active`, `inactive`, or `untracked`\n  - `start` – (Pagination start)\n  - `limit` – (Number of results)\n- **Use Cases**:\n  - “What is the CoinMarketCap ID for SOL?”\n  - “List all active cryptocurrencies.”\n\n---\n\n#### 2. **Crypto Info**\n- **Endpoint**: `/v2/cryptocurrency/info`\n- **Purpose**: Get metadata like description, whitepaper, and social links.\n- **Supported Inputs**:\n  - `symbol` – (Required) Comma-separated symbols\n- **Use Cases**:\n  - “Show me the whitepaper for ETH.”\n  - “What’s the website and Twitter handle of DOGE?”\n\n---\n\n#### 3. **Crypto Listings**\n- **Endpoint**: `/v1/cryptocurrency/listings/latest`\n- **Purpose**: Ranked list of coins sorted by market cap.\n- **Supported Inputs**:\n  - `start` – (e.g., 1 for top coin, 101 for rank 101+)\n  - `limit` – (e.g., 10 for top 10)\n  - `convert` – Currency to convert values into (e.g., USD, EUR)\n- **Use Cases**:\n  - “Show me the top 20 coins.”\n  - “What are the top 5 coins in EUR?”\n\n---\n\n#### 4. **CoinMarketCap Price**\n- **Endpoint**: `/v2/cryptocurrency/quotes/latest`\n- **Purpose**: Real-time price, volume, and market cap.\n- **Supported Inputs**:\n  - `symbol` – (Required) Single or multiple symbols\n  - `convert` – Currency to display results in (e.g., USD)\n- **Use Cases**:\n  - “What’s the current price of ADA?”\n  - “How much volume has BTC traded in the last 24h?”\n\n---\n\n#### 5. **Global Metrics**\n- **Endpoint**: `/v1/global-metrics/quotes/latest`\n- **Purpose**: Global crypto market stats.\n- **Supported Inputs**:\n  - *(None required)*\n- **Use Cases**:\n  - “What’s the total crypto market cap?”\n  - “How dominant is Bitcoin?”\n\n---\n\n#### 6. **Price Conversion**\n- **Endpoint**: `/v1/tools/price-conversion`\n- **Purpose**: Convert one crypto/fiat into another.\n- **Supported Inputs**:\n  - `amount` – (Required) Numerical amount to convert\n  - `symbol` – (Required) The crypto to convert from\n  - `convert` – (Required) The target currency (e.g., BTC, USD)\n- **Use Cases**:\n  - “Convert 5 ETH to USD.”\n  - “What’s 1000 DOGE in BTC?”\n\n"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "52e42df6-6b67-45d6-80a0-5361259a9d8f", "name": "Crypto Agent Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-300, 520], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "8387d236-2e94-48de-b5b9-0838762440f9", "name": "Crypto Agent Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-120, 520], "parameters": {}, "typeVersion": 1.3}, {"id": "a48f47a0-9bef-412c-91b8-df57ce3dba12", "name": "CoinMarketCap Price", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [600, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v2/cryptocurrency/quotes/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "symbol"}, {"name": "convert"}]}, "toolDescription": "The tool going to recieve input of cryptocurrency name and then request the price from CoinMarketCap and send the price back in a message.", "parametersHeaders": {"values": [{"name": "Accept", "value": "application/json", "valueProvider": "fieldValue"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "d5d5e847-efbc-41cd-b581-095eb3825bfd", "name": "Crypto Map", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [60, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v1/cryptocurrency/map", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "symbol", "valueProvider": "modelOptional"}, {"name": "listing_status", "valueProvider": "modelOptional"}, {"name": "start", "valueProvider": "modelOptional"}, {"name": "limit", "valueProvider": "modelOptional"}]}, "toolDescription": "Get a map of all cryptocurrencies with CoinMarketCap ID, name, and symbol.", "parametersHeaders": {"values": [{"name": "Accept"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "ac224086-1243-4dcb-85eb-dbf59fc927ac", "name": "Crypto Info", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [240, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v2/cryptocurrency/info", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "symbol"}]}, "toolDescription": "Get metadata for one or more cryptocurrencies including logo, description, and links.\n\n", "parametersHeaders": {"values": [{"name": "Accept"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "b261f3ed-a1dc-4dd0-bc63-31e77041bb01", "name": "Crypto Listings", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [420, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "start"}, {"name": "limit"}, {"name": "convert"}]}, "toolDescription": "Retrieve a ranked list of cryptocurrencies sorted by market cap. Supports pagination and conversion currency.", "parametersHeaders": {"values": [{"name": "Accept"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "cfa6badf-0eed-4b37-bb1d-2ffcd39a23fc", "name": "Global Metrics", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [800, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v1/global-metrics/quotes/latest", "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "toolDescription": "Returns global crypto market metrics including market cap, 24h volume, BTC dominance, and total active cryptocurrencies.", "parametersHeaders": {"values": [{"name": "Accept"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "ca40fc60-8cdd-48ec-98ba-63259582a16e", "name": "Price Conversion", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1000, 520], "parameters": {"url": "https://pro-api.coinmarketcap.com/v1/tools/price-conversion", "sendQuery": true, "sendHeaders": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "parametersQuery": {"values": [{"name": "amount"}, {"name": "symbol"}, {"name": "convert"}]}, "toolDescription": "Convert cryptocurrency or fiat value from one currency to another.", "parametersHeaders": {"values": [{"name": "Accept"}]}}, "credentials": {"httpHeaderAuth": {"id": "OKXROn8aWkgAOvvV", "name": "CoinMarketCap Standard"}}, "typeVersion": 1.1}, {"id": "360bb74c-0ca6-4cd7-95ab-7f14a2c89e6c", "name": "Crypto Agent Guide", "type": "n8n-nodes-base.stickyNote", "position": [-1140, -760], "parameters": {"width": 840, "height": 840, "content": "# 🧠 CoinMarketCap_Crypto_Agent_Tool Guide\n\nThis agent is part of the modular **CoinMarketCap AI Analyst** system in **n8n**, focused on **cryptocurrency-level queries** such as price, supply, metadata, rankings, and conversions.\n\n## 🔌 Endpoints Supported:\n1. `/v1/cryptocurrency/map` – Get IDs, symbols, names\n2. `/v2/cryptocurrency/info` – Get metadata, logos, whitepapers\n3. `/v1/cryptocurrency/listings/latest` – Market rankings by cap\n4. `/v2/cryptocurrency/quotes/latest` – Price, volume, and supply\n5. `/v1/global-metrics/quotes/latest` – Total market cap, BTC dominance\n6. `/v1/tools/price-conversion` – Fiat and crypto conversions\n\n## 🧠 Node Overview:\n- **🧠 Brain**: `GPT-4o Mini`\n- **💾 Memory**: Session context buffer\n- **⚙️ Tools**: 6 live API endpoints\n\n## ⚙️ Required Inputs:\n- `message` – User query\n- `sessionId` – Used to preserve memory between calls\n\n## 📝 Tip:\nUse descriptive prompts like:\n- “What is the CoinMarketCap ID for ETH?”\n- “Convert 1000 DOGE to BTC.”\n- “Show top 10 tokens by market cap.”"}, "typeVersion": 1}, {"id": "f2f24886-4157-40f5-9731-dea431fb6cb8", "name": "Usage & Examples", "type": "n8n-nodes-base.stickyNote", "position": [-120, -760], "parameters": {"color": 5, "width": 720, "height": 900, "content": "## 📌 Usage Instructions\n\n### ✅ Step 1: Provide Inputs\nUse `symbol`, `amount`, `convert`, `start`, `limit` where needed.\n\n### ✅ Step 2: Trigger from Supervisor\nSupervisor AI sends the message and sessionId to this agent.\n\n### ✅ Step 3: Review Output\nReturns raw JSON or formatted insights.\n\n---\n\n## 🔍 Sample Prompts\n\n### 1️⃣ Convert 5 ETH to USD\n```plaintext\nGET /v1/tools/price-conversion?amount=5&symbol=ETH&convert=USD\n```\n\n### 2️⃣ Get CoinMarketCap ID of SHIB\n```plaintext\nGET /v1/cryptocurrency/map?symbol=SHIB\n```\n\n### 3️⃣ View total market cap\n```plaintext\nGET /v1/global-metrics/quotes/latest\n```\n\n### 4️⃣ Top 5 coins in EUR\n```plaintext\nGET /v1/cryptocurrency/listings/latest?limit=5&convert=EUR\n```"}, "typeVersion": 1}, {"id": "06d501a6-8730-4093-a145-53fd9378fa8e", "name": "Errors & Licensing", "type": "n8n-nodes-base.stickyNote", "position": [780, -760], "parameters": {"color": 3, "width": 600, "height": 560, "content": "## ⚠️ API Errors & Troubleshooting\n\n| Code | Message |\n|------|---------|\n| 200  | OK ✅ |\n| 400  | Bad Request – Check inputs |\n| 401  | Unauthorized – Invalid/missing API key |\n| 429  | Rate limit exceeded – Slow down |\n| 500  | CoinMarketCap server issue |\n\n### ✅ Tips:\n- Double check symbols and convert params\n- Use `start`, `limit`, `convert` for pagination\n- Add delay to avoid 429 rate limits\n\n---\n\n## 🛠️ Need Help?\n🔗 [<PERSON> LinkedIn](https://linkedin.com/in/donjayamahajr)\n\n© 2025 Treasurium Capital Limited Company. All rights reserved.\nThis AI workflow architecture, including logic, design, and prompt structures, is the intellectual property of Treasurium Capital Limited Company. Unauthorized reproduction, redistribution, or resale is prohibited under U.S. copyright law. Licensed use only."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a6a08338-6720-4a3a-bf3b-ed9559257b10", "connections": {"Crypto Map": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Crypto Info": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Global Metrics": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Crypto Listings": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Price Conversion": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Crypto Agent Brain": {"ai_languageModel": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_languageModel", "index": 0}]]}, "CoinMarketCap Price": {"ai_tool": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_tool", "index": 0}]]}, "Crypto Agent Memory": {"ai_memory": [[{"node": "CoinMarketCap Crypto Agent", "type": "ai_memory", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "CoinMarketCap Crypto Agent", "type": "main", "index": 0}]]}}}