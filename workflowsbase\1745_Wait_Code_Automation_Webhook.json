{"id": "WETMyIJCbD3et6Rh", "meta": {"instanceId": "ddfdf733df99a65c801a91865dba5b7c087c95cc22a459ff3647e6deddf2aee6"}, "name": "High-Level Service Page SEO Blueprint Report", "tags": [], "nodes": [{"id": "49aa0dd2-1d64-4047-9988-8e4f386d557a", "name": "Convert URLs to Items", "type": "n8n-nodes-base.code", "position": [600, 500], "parameters": {"jsCode": "// Get the raw input string from the \"Start\" node\nconst input = $('Start').item.json.Competitors;\n\n// Split the string by line breaks and filter out any empty lines\nconst urls = input\n  .split('\\n')\n  .map(url => url.trim())\n  .filter(url => url.length > 0);\n\n// Return the array as output with \"competitor_url\" field\nreturn urls.map(url => ({ json: { competitor_url: url } }));"}, "typeVersion": 2}, {"id": "ec7b74db-43fc-4041-b63e-02ff21b9442e", "name": "Start", "type": "n8n-nodes-base.formTrigger", "position": [240, 500], "webhookId": "dafbc2ba-7397-4f83-b84d-630294e636b0", "parameters": {"options": {}, "formTitle": "Competitors Analysis for Service-Based Queries", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Competitors", "placeholder": "competitor1.com\ncompetitor2.com", "requiredField": true}, {"fieldLabel": "Target Keyword", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Services Offered", "requiredField": true}, {"fieldLabel": "Brand Name", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Is Homepage?", "fieldOptions": {"values": [{"option": "Yes"}, {"option": "No"}]}}]}, "formDescription": "Generate a high-level service page content blueprint report to follow to beat the competition. \n\nNote: Do not add more than 5 competitors otherwise this could dilute the context and quality of the final report."}, "typeVersion": 2.2}, {"id": "c517d397-4962-4281-962e-a57e3a12bea0", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [880, 500], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "ac532215-626d-4690-9c99-d11f09fa86dc", "name": "Get URL HTML", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "maxTries": 5, "position": [1100, 340], "parameters": {"url": "=https://r.jina.ai/{{ $json.competitor_url }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Edit Fields').first().json['JINA Reader API Key'] }}"}, {"name": "X-Return-Format", "value": "html"}]}}, "executeOnce": false, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 5000}, {"id": "ed03887c-9996-4dfb-b46a-a745bc64864a", "name": "Extract HTML Elements", "type": "n8n-nodes-base.code", "position": [1300, 340], "parameters": {"jsCode": "// Function to remove inner HTML tags and decode common HTML entities\nfunction cleanText(text) {\n  // Remove any HTML tags inside the text\n  let cleaned = text.replace(/<\\/?[^>]+(>|$)/g, '');\n\n  // Decode common HTML entities\n  cleaned = cleaned\n    .replace(/&nbsp;/g, ' ')  // Non-breaking space\n    .replace(/&amp;/g, '&')   // Ampersand\n    .replace(/&quot;/g, '\"')  // Double quote\n    .replace(/&lt;/g, '<')    // Less-than\n    .replace(/&gt;/g, '>')    // Greater-than\n    .replace(/&#8217;/g, \"'\")  // Right single quotation mark\n    .replace(/&#8220;/g, '\"')  // Left double quotation mark\n    .replace(/&#8221;/g, '\"')  // Right double quotation mark\n    .replace(/&rsquo;/g, \"'\")  // Right single quotation mark\n    .replace(/&lsquo;/g, \"'\")  // Left single quotation mark\n    .replace(/&rdquo;/g, '\"')  // Right double quotation mark\n    .replace(/&ldquo;/g, '\"')  // Left double quotation mark\n    .replace(/&mdash;/g, '—')  // Em dash\n    .replace(/&ndash;/g, '–')  // En dash\n    .replace(/&hellip;/g, '…') // Ellipsis\n    .replace(/&#(\\d+);/g, (match, dec) => String.fromCharCode(dec)); // Handle numeric entities\n\n  return cleaned.trim();  // Remove extra whitespace\n}\n\n// Function to generate n-grams from text\nfunction generateNgrams(text, n) {\n  // Convert text to lowercase and split into words\n  const words = text.toLowerCase()\n    .replace(/[^\\w\\s]|_/g, ' ')  // Replace punctuation and underscores with spaces\n    .replace(/\\s+/g, ' ')        // Replace multiple spaces with a single space\n    .trim()                       // Remove leading/trailing spaces\n    .split(' ');                  // Split into words\n  \n  // Filter out stop words and very short words (optional)\n  const filteredWords = words.filter(word => word.length > 1);\n  \n  // Generate n-grams\n  const ngrams = [];\n  for (let i = 0; i <= filteredWords.length - n; i++) {\n    ngrams.push(filteredWords.slice(i, i + n).join(' '));\n  }\n  \n  return ngrams;\n}\n\n// Function to count n-grams\nfunction countNgrams(textArray, n) {\n  const ngramCounts = {};\n  \n  textArray.forEach(text => {\n    const ngrams = generateNgrams(text, n);\n    \n    ngrams.forEach(ngram => {\n      ngramCounts[ngram] = (ngramCounts[ngram] || 0) + 1;\n    });\n  });\n  \n  // Convert to array of objects and sort by count (descending)\n  return Object.entries(ngramCounts)\n    .map(([phrase, count]) => ({ phrase, count }))\n    .sort((a, b) => b.count - a.count);\n}\n\n// Initialize an array to store the results for each item\nlet results = [];\n\n// Iterate through all items (each item corresponds to one URL in the loop)\nitems.forEach(item => {\n  // Get the raw HTML content for the current item\n  const html = item.json.data || '';  // Ensure you're getting the correct field where HTML is stored\n\n  // Initialize arrays to store the extracted outline\n  let outline = [];\n  let meta = {};\n  let schemas = [];\n  let headingTexts = []; // Store all heading texts for n-gram analysis\n\n  // Store all headings with their positions to maintain original order\n  let headingsWithPositions = [];\n  \n  // Extract heading content with a more robust approach\n  for (let i = 1; i <= 6; i++) {\n    // This regex pattern matches h1-h6 tags, capturing everything between opening and closing tags\n    // even if there are nested elements\n    const pattern = new RegExp(`<h${i}[^>]*>((?:.|\\n)*?)<\\/h${i}>`, 'gi');\n    let match;\n    \n    while ((match = pattern.exec(html)) !== null) {\n      const fullContent = match[1];\n      const cleanedText = cleanText(fullContent);\n      \n      // Only add non-empty headings\n      if (cleanedText && cleanedText.trim().length > 0) {\n        headingsWithPositions.push({\n          position: match.index,\n          level: i,\n          tag: `h${i}`,\n          text: cleanedText\n        });\n        \n        // Add to headingTexts for n-gram analysis\n        headingTexts.push(cleanedText);\n      }\n    }\n  }\n  \n  // Sort headings by their position in the HTML to maintain the original order\n  headingsWithPositions.sort((a, b) => a.position - b.position);\n  \n  // Process the sorted headings\n  headingsWithPositions.forEach(heading => {\n    // Build the outline based on the heading level with dot-based indentation\n    const indentation = '.'.repeat(heading.level - 1);  // No dots for H1, one for H2, etc.\n    outline.push(`${indentation}${heading.tag.toUpperCase()}: ${heading.text}`);\n  });\n\n  // Generate n-grams (2-gram, 3-gram, and 4-gram) from all headings\n  const ngrams = {\n    '2gram': countNgrams(headingTexts, 2),\n    '3gram': countNgrams(headingTexts, 3),\n    '4gram': countNgrams(headingTexts, 4)\n  };\n  \n  // Filter out n-grams with only one occurrence (optional)\n  // This can be adjusted based on preference\n  const filteredNgrams = {\n    '2gram': ngrams['2gram'].filter(item => item.count > 1),\n    '3gram': ngrams['3gram'].filter(item => item.count > 1),\n    '4gram': ngrams['4gram'].filter(item => item.count > 1)\n  };\n\n  // Extract title tag\n  const titleMatch = html.match(/<title>(.*?)<\\/title>/i);\n  if (titleMatch && titleMatch[1]) {\n    meta.title = cleanText(titleMatch[1]);\n  }\n\n  // Extract meta tags\n  const metaTags = [\n    { name: 'description', regex: /<meta\\s+name=\"description\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'canonical', regex: /<link\\s+rel=\"canonical\"\\s+href=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:locale', regex: /<meta\\s+property=\"og:locale\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:type', regex: /<meta\\s+property=\"og:type\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:title', regex: /<meta\\s+property=\"og:title\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:description', regex: /<meta\\s+property=\"og:description\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:url', regex: /<meta\\s+property=\"og:url\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:site_name', regex: /<meta\\s+property=\"og:site_name\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'article:publisher', regex: /<meta\\s+property=\"article:publisher\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'article:modified_time', regex: /<meta\\s+property=\"article:modified_time\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:image', regex: /<meta\\s+property=\"og:image\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:image:width', regex: /<meta\\s+property=\"og:image:width\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:image:height', regex: /<meta\\s+property=\"og:image:height\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'og:image:type', regex: /<meta\\s+property=\"og:image:type\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'twitter:card', regex: /<meta\\s+name=\"twitter:card\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'twitter:title', regex: /<meta\\s+name=\"twitter:title\"\\s+content=\"([^\"]*)\"[^>]*>/i },\n    { name: 'twitter:site', regex: /<meta\\s+name=\"twitter:site\"\\s+content=\"([^\"]*)\"[^>]*>/i }\n  ];\n\n  // Extract each meta tag\n  metaTags.forEach(tag => {\n    const match = html.match(tag.regex);\n    if (match && match[1]) {\n      meta[tag.name] = cleanText(match[1]);\n    }\n  });\n\n  // Extract all meta tags with name or property attributes (more general approach)\n  const generalMetaRegex = /<meta\\s+(?:name|property)=\"([^\"]*)\"\\s+content=\"([^\"]*)\"[^>]*>/gi;\n  let metaMatch;\n  while ((metaMatch = generalMetaRegex.exec(html)) !== null) {\n    const name = metaMatch[1];\n    const content = cleanText(metaMatch[2]);\n    \n    // Only add if not already captured and has content\n    if (content && !meta[name]) {\n      meta[name] = content;\n    }\n  }\n\n  // Extract JSON-LD schema data\n  const schemaRegex = /<script\\s+type=\"application\\/ld\\+json\"[^>]*>([\\s\\S]*?)<\\/script>/gi;\n  let schemaMatch;\n  \n  while ((schemaMatch = schemaRegex.exec(html)) !== null) {\n    try {\n      const schemaText = schemaMatch[1].trim();\n      if (schemaText) {\n        const schemaData = JSON.parse(schemaText);\n        schemas.push(schemaData);\n      }\n    } catch (e) {\n      // If JSON parsing fails, add the raw text\n      schemas.push({ raw: schemaMatch[1].trim() });\n    }\n  }\n\n  // Add the outline, meta, schema, and ngrams for the current item to the results array\n  results.push({\n    json: {\n      outline: outline,  // Array containing the hierarchy of headings with dot-based indentation\n      meta: Object.keys(meta).length > 0 ? meta : undefined,  // Only include if meta tags were found\n      schema: schemas.length > 0 ? schemas : undefined,  // Only include if schema data was found\n      ngrams: headingTexts.length > 0 ? filteredNgrams : undefined  // Only include if headings were found\n    }\n  });\n});\n\n// Return the results for all items (all URLs in the loop)\nreturn results;"}, "typeVersion": 2}, {"id": "9c873ba5-84b2-4366-ac96-a1380ce66701", "name": "Set URL Data", "type": "n8n-nodes-base.set", "position": [1480, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "186daf52-90b2-4608-9c94-243187069bf4", "name": "Competitor URL", "type": "string", "value": "={{ $('Loop Over Items').item.json.competitor_url }}"}, {"id": "3bb3057c-d84f-4eac-8da2-22740a2c293c", "name": "Outline", "type": "string", "value": "={{ JSON.stringify($json.outline) }}"}, {"id": "53c1b42c-14ce-48a0-8802-5b175d7ab127", "name": "Meta", "type": "string", "value": "={{ JSON.stringify($json.meta) }}"}, {"id": "ca84a96b-f370-42b6-9f4d-a1e4d1ab066a", "name": "Ngrams", "type": "string", "value": "={{ JSON.stringify($json.ngrams) }}"}]}}, "typeVersion": 3.4}, {"id": "5fae604a-d44d-4022-905a-51742ab23144", "name": "Code", "type": "n8n-nodes-base.code", "position": [1100, 520], "parameters": {"jsCode": "let output = '';\n\nfor (const [index, item] of items.entries()) {\n  const data = item.json;\n\n  const formatField = (value) => {\n    if (typeof value === 'string') {\n      try {\n        const parsed = JSON.parse(value);\n        return JSON.stringify(parsed, null, 2); // Pretty print with 2-space indent\n      } catch {\n        return value; // Not JSON, return as-is\n      }\n    }\n    return value;\n  };\n\n  output += `<competitor${index + 1}>\\n`;\n  output += `  <competitor url>\\n    ${data[\"Competitor URL\"]}\\n  </competitor url>\\n\\n`;\n  output += `  <outline>\\n    ${formatField(data[\"Outline\"])}\\n  </outline>\\n\\n`;\n  output += `  <meta>\\n${formatField(data[\"Meta\"])}\\n  </meta>\\n\\n`;\n  output += `  <ngrams>\\n${formatField(data[\"Ngrams\"])}\\n  </ngrams>\\n`;\n  output += `</competitor${index + 1}>\\n\\n`;\n}\n\nreturn [\n  {\n    json: {\n      competitors_data: output.trim()\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "a2d9be92-5a1e-448c-a4c8-e7ca8d6ce8ca", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [1300, 520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "27ea4c8c-3cd1-47e4-9f22-a4bd5b4b6b3a", "name": "competitors_data", "type": "string", "value": "={{ $json.competitors_data }}"}]}}, "typeVersion": 3.4}, {"id": "715317ae-e896-48cf-b732-31a0a1ee7999", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [320, 880], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "credentials": {"googlePalmApi": {"id": "E9AQr0xc0FLNxbSQ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "e96eeecb-9b82-4cba-bf2c-3c5041b724b1", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [940, 720], "webhookId": "2231c6d5-575e-46be-b1e3-6fac3af1a830", "parameters": {"amount": "={{ $('Edit Fields').first().json['Waiting Time (Seconds)'] }}"}, "typeVersion": 1.1}, {"id": "998ac0b0-d3e6-4e4a-b8a6-ae8994dbfa58", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1040, 920], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "credentials": {"googlePalmApi": {"id": "E9AQr0xc0FLNxbSQ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "6ee11671-eb61-4d9f-a4f3-93898809d4e1", "name": "Set Competitor Analysis", "type": "n8n-nodes-base.set", "position": [740, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5f9f0ff2-7e00-449a-a5c4-14233315125a", "name": "Competitor Analysis Report", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<competitor_analysis_report>/, '').replace(/<\\/competitor_analysis_report>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "728cf9c2-7988-4b23-8e00-7511a0f10858", "name": "Set User Intent Analysis", "type": "n8n-nodes-base.set", "position": [1480, 720], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c5b72bf5-a084-4dfe-aad8-d77c64138c54", "name": "User Intent Analysis Report", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<user_intent_report>/, '').replace(/<\\/user_intent_report>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "ecf01be1-fe6a-49f2-836f-8fb2c37a5da5", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [1660, 720], "webhookId": "2231c6d5-575e-46be-b1e3-6fac3af1a830", "parameters": {"amount": "={{ $('Edit Fields').first().json['Waiting Time (Seconds)'] }}"}, "typeVersion": 1.1}, {"id": "8ac6dc7a-d32f-4366-9b21-fb20aaf1043f", "name": "Google Gemini Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [420, 1220], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "typeVersion": 1}, {"id": "ac65fd5d-6e4b-47cc-a8e2-778295651723", "name": "Competitors Analysis", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [400, 720], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>\n<competitors_data>\n{{ $json.competitors_data }}\n</competitors_data>", "messages": {"messageValues": [{"message": "=You are a Data Analyst specializing in SEO and Content Structure Analysis. Your task is to meticulously analyze the provided data from top-ranking competitor pages for a specific target query. Focus on identifying recurring patterns, themes, and structural elements.\n\nAnalyze the following inputs:\n<target_query>{target_query}</target_query>\n<competitors_data>\n{competitors_data}\n</competitors_data>\n\nBased on your analysis, generate a report summarizing:\n1. **List of Competitors**: Create a numbered coding system for competitors by assigning each competitor a unique code (e.g., C1, C2, C3) followed by their full brand name. For example: 'C1 = Nike, C2 = Adidas, C3 = Under Armour'. This coding system will be used for efficient reference throughout the remainder of the report.\n2.  **Meta Title & Description Trends:** Common keywords, angles (e.g., benefit-driven, location-focused, speed-focused), and calls-to-action observed.\n3.  **Common Outline Sections/Topics:** Identify frequently recurring sections (based on H2s/H3s) across competitors (e.g., \"What is X?\", \"Our Process\", \"Pricing\", \"Why Choose Us\", \"FAQs\", specific service features/types).\n4.  **Key Heading Concepts (from N-grams):** List the most prominent and recurring 2, 3, and 4-word phrases found in competitor headings. Highlight concepts that appear critical for demonstrating topic relevance.\n5.  **Structural & Content Element Observations:** Note any common patterns in page structure (e.g., typical flow of sections), use of specific elements (e.g., lists, tables, videos, forms, calculators), and perceived content depth/length.\n\nStructure your entire output within a single XML tag: <competitor_analysis_report>"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "97d45dd2-4278-4294-8759-7b076e41d684", "name": "User Intent Analysis", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1140, 720], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>", "messages": {"messageValues": [{"message": "=You are a User Experience (UX) Researcher and Intent Analyst. Your task is to analyze the provided target query to understand the underlying user needs and expectations, completely independent of any competitor implementations.\n\nAnalyze the following input:\n<target_query>{target_query}</target_query>\n\nGenerate a report based *only* on the query, covering:\n1.  **Primary User Intent:** What is the main goal? (Informational, Navigational, Transactional, Commercial Investigation).\n2.  **Secondary Intents:** What related questions or needs might the user have?\n3.  **Implicit User Persona:** Describe the likely searcher (e.g., role, pain points, context).\n4.  **Stage in Buyer's Journey:** (Awareness, Consideration, Decision).\n5.  **Expected Services/Content:** What specific services or information types would this user logically expect to find on a page satisfying their intent?\n6.  **Problem/Solution Framing:** How should the user's core problem be articulated, and how should a service be positioned as the solution for this specific query?\n\nStructure your entire output within a single XML tag: <user_intent_report>"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "731d7b56-fa16-4f7d-a1af-f7908664e477", "name": "Synthesis & Gap Analysis", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, 1060], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>\n<competitor_analysis_report>\n{{ $('Set Competitor Analysis').first().json['Competitor Analysis Report'] }}\n</competitor_analysis_report>\n<user_intent_report>\n{{ $('Set User Intent Analysis').first().json['User Intent Analysis Report'] }}\n</user_intent_report>", "messages": {"messageValues": [{"message": "=You are an SEO Content Strategist and UX Architect. Your task is to synthesize the findings from the competitor analysis and the user intent analysis to identify strategic opportunities.\n\nAnalyze the following inputs:\n<target_query>{target_query}</target_query>\n<competitor_analysis_report>\n{competitor_analysis_report}\n</competitor_analysis_report>\n<user_intent_report>\n{user_intent_report}\n</user_intent_report>\n\nGenerate a synthesis report identifying:\n1.  **Content Overlaps (\"Table Stakes\"):** List the topics, sections, and information points that are BOTH expected by users (from user intent report) AND commonly covered by competitors (from competitor analysis). These are essential baseline requirements.\n2.  **Content & UX Gaps (Opportunities):** Identify user needs/expectations (from user intent report) that competitors are NOT addressing well or are missing entirely (based on competitor analysis). Highlight areas where you can provide superior value or a better user experience.\n3.  **SEO Keyword/Topic Priorities:** Based on both competitor heading N-grams/themes and user intent, list the most critical keywords, concepts, and semantic topics that the page structure and content must address for relevance and ranking potential.\n4.  **Potential UX/Conversion Advantages:** Suggest high-level ways to improve upon common competitor weaknesses in presentation, clarity, navigation, or calls-to-action, based on the combined analysis.\n\nStructure your entire output within a single XML tag: <synthesis_and_gap_analysis>"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "144f8050-c395-4f02-a786-5dca80bf1f3b", "name": "Set Synthesis & Gap Analysis", "type": "n8n-nodes-base.set", "position": [840, 1060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4d580b58-4843-43c1-9aa2-a46d82842465", "name": "Synthesis & Gap Analysis", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<synthesis_and_gap_analysis>/, '').replace(/<\\/synthesis_and_gap_analysis>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "9b5739a8-ac4a-47c5-882d-5f8ecfe08831", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [1020, 1060], "webhookId": "2231c6d5-575e-46be-b1e3-6fac3af1a830", "parameters": {"amount": "={{ $('Edit Fields').first().json['Waiting Time (Seconds)'] }}"}, "typeVersion": 1.1}, {"id": "0282d60c-c030-4ccd-8716-dd56bbc636e2", "name": "Google Gemini Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1160, 1260], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "credentials": {"googlePalmApi": {"id": "E9AQr0xc0FLNxbSQ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "1f156e28-7b4c-441d-9626-7762d47acd93", "name": "Ideal Page Outline Generation", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1200, 1060], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>\n<synthesis_and_gap_analysis>\n{{ $('Set Synthesis & Gap Analysis').first().json['Synthesis & Gap Analysis'] }}\n</synthesis_and_gap_analysis>\n<is_homepage>{{ $('Start').first().json['Is Homepage?'] }}</is_homepage>\n<brand_name>{{ $('Start').first().json['Brand Name'] }}</brand_name>\n<services_offered>\n{{ $('Start').first().json['Services Offered'] }}\n</services_offered>", "messages": {"messageValues": [{"message": "=You are an SEO Content Architect, Information Designer, **and Conversion-Focused Structuring Expert.** Your task is to create the optimal page outline (H1, H2s, H3s, potentially H4s) for the target query, based on the strategic insights from the synthesis and gap analysis. The outline must satisfy user intent, incorporate SEO best practices derived from competitors, provide a logical user experience, **and be structured to effectively persuade and convert visitors.**\n\nAnalyze the following inputs:\n<target_query>{target_query}</target_query>\n<synthesis_and_gap_analysis>\n{synthesis_and_gap_analysis}\n</synthesis_and_gap_analysis>\n<is_homepage>{is_homepage}</is_homepage>\n<brand_name>{brand_name}</brand_name>\n<services_offered>{services_offered}</services_offered>\n\nBased on the inputs, generate a recommended page outline:\n1.  **H1:** Propose a compelling, keyword-rich, **and benefit-oriented** H1 tag.\n2.  **Logical & Persuasive Section Flow (H2s):** Structure the main sections (H2s) in a sequence that guides the user naturally (e.g., addressing the problem, introducing the solution/value proposition, detailing the service & benefits, building trust/credibility, addressing potential objections, clear call to action path).\n    *   Incorporate the \"Table Stakes\" sections identified in the synthesis.\n    *   Strategically place sections that address the identified \"Gaps\" to offer unique value.\n    *   **Ensure key persuasive elements are included as distinct sections or integrated strategically:** Strong Value Proposition, Key Benefits/Outcomes, Social Proof (e.g., Testimonials/Case Studies placeholder), How it Works/Process, Pricing/Investment (or how pricing is determined), Why Choose Us/Unique Differentiators.\n3.  **Detailed Sub-sections (H3s/H4s):** Flesh out each H2 section with relevant H3s (and H4s if needed) that cover specific details, features, benefits, process steps, etc. Naturally weave in the \"SEO Keyword/Topic Priorities\" identified in the synthesis within these headings. **Ensure H3s under benefit/value sections clearly articulate positive outcomes for the user.**\n4.  **Homepage Consideration:** If <is_homepage> is \"Yes\", adjust the structure to be broader, potentially summarizing multiple services and directing users deeper, while still strongly addressing the core <target_query> intent **and presenting a compelling overall brand value proposition.** If \"No\", keep it focused specifically on the service related to the query.\n5.  **Justification:** Briefly explain the rationale behind the placement and content focus of major H2 sections, linking back to user intent, competitor insights, gap-filling, **and persuasive flow.**\n\nStructure your entire output within a single XML tag: <recommended_page_outline>"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "95003089-66bc-4c5c-b281-71009be099ea", "name": "Set Page Outline", "type": "n8n-nodes-base.set", "position": [1560, 1060], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7c205f79-bdaf-4fd5-8fae-14713a0d2eff", "name": "Page Outline", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<recommended_page_outline>/, '').replace(/<\\/recommended_page_outline>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "e101cf81-973a-44af-8c7c-3c316398ccda", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [1740, 1060], "webhookId": "2231c6d5-575e-46be-b1e3-6fac3af1a830", "parameters": {"amount": "={{ $('Edit Fields').first().json['Waiting Time (Seconds)'] }}"}, "typeVersion": 1.1}, {"id": "bd098536-18dd-4f23-98da-d86c3f0baf2a", "name": "Google Gemini Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [500, 1540], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "typeVersion": 1}, {"id": "5429f86e-2883-4547-a69f-dd8c356b0247", "name": "UX, Conversion & Copywriting Enhancement", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [600, 1400], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>\n<recommended_page_outline>\n{{ $('Set Page Outline').first().json['Page Outline'] }}\n</recommended_page_outline>\n<user_intent_report>\n{{ $('Set User Intent Analysis').first().json['User Intent Analysis Report'] }}\n</user_intent_report>\n<brand_name>{{ $('Start').first().json['Brand Name'] }}</brand_name>\n<services_offered>\n{{ $('Start').first().json['Services Offered'] }}\n</services_offered>", "messages": {"messageValues": [{"message": "=You are a Conversion Rate Optimization (CRO) Specialist and UX Copywriter. Your task is to take the recommended page outline and layer on specific, actionable recommendations to maximize user experience, conversions, and persuasive communication.\n\nAnalyze the following inputs:\n<target_query>{target_query}</target_query>\n<recommended_page_outline>\n{recommended_page_outline}\n</recommended_page_outline>\n<user_intent_report>\n{user_intent_report} <!-- Reference for user needs/pain points -->\n</user_intent_report>\n<brand_name>{brand_name}</brand_name>\n<services_offered>{services_offered}</services_offered> <!-- Reference for service specifics -->\n\nBased on the inputs, provide detailed recommendations covering:\n1.  **Calls-to-Action (CTAs):**\n    *   Suggest specific wording for Primary and Secondary CTAs relevant to the service and user journey stage.\n    *   Recommend optimal placement within the proposed outline (e.g., above the fold, after key sections, end of page).\n    *   Advise on visual prominence/design.\n2.  **Trust Signals:** Recommend specific types of trust signals (e.g., testimonials, case studies, logos, certifications, guarantees, team bios) and suggest where they should be integrated into the outline structure. Tailor suggestions to the likely concerns of the user persona identified in the <user_intent_report>.\n3.  **Copywriting & Tone:**\n    *   Advise on the overall tone of voice (e.g., professional, empathetic, urgent, reassuring) suitable for the <target_query> and <brand_name>.\n    *   Emphasize using benefit-driven language (translating service features into user outcomes) throughout the content. Provide examples related to <services_offered>.\n    *   Suggest how to address user pain points (from <user_intent_report>) directly in the copy.\n4.  **Visual & Interactive Elements:** Recommend types of visuals (e.g., high-quality photos, videos, infographics, icons) or interactive elements (e.g., calculators, quizzes, forms) that would enhance understanding, engagement, and trust, suggesting where they fit within the outline.\n5.  **Risk Reversal:** Suggest potential guarantees, free consultations, trials, or clear explanations of processes that can reduce perceived risk for the user.\n6.  **Readability & UX:** Reinforce the importance of short paragraphs, bullet points, clear headings (already outlined), whitespace, and mobile responsiveness.\n\nStructure your entire output within a single XML tag: <ux_conversion_copy_recommendations>"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "4478e4a6-604b-4a5c-a7a8-bfadcbf0103d", "name": "Set UX & Conversions Enhancements", "type": "n8n-nodes-base.set", "position": [960, 1400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a35489c5-7db5-4d5e-8a3a-9a27ea21c241", "name": "UX & Conversions Enhancements", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<ux_conversion_copy_recommendations>/, '').replace(/<\\/ux_conversion_copy_recommendations>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "1e9482a5-6b59-456c-8a92-49cdea47e973", "name": "Google Gemini Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1100, 1560], "parameters": {"options": {"temperature": 0.4}, "modelName": "=models/{{ $('Edit Fields').first().json['Google Gemini Model'] }}"}, "typeVersion": 1}, {"id": "de163dd6-9e8b-462e-a125-84dfdfbff8ab", "name": "Final Service Page Blueprint", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1160, 1400], "parameters": {"text": "=<target_query>{{ $('Start').first().json['Target Keyword'] }}</target_query>\n<brand_name>{{ $('Start').first().json['Brand Name'] }}</brand_name>\n<services_offered>\n{{ $('Start').first().json['Services Offered'] }}\n</services_offered>\n<is_homepage>{{ $('Start').first().json['Is Homepage?'] }}</is_homepage>\n<competitor_analysis_report>\n{{ $('Set Competitor Analysis').first().json['Competitor Analysis Report'] }}\n</competitor_analysis_report>\n<user_intent_report>\n{{ $('Set User Intent Analysis').first().json['User Intent Analysis Report'] }}\n</user_intent_report>\n<synthesis_and_gap_analysis>\n{{ $('Set Synthesis & Gap Analysis').first().json['Synthesis & Gap Analysis'] }}\n</synthesis_and_gap_analysis>\n<recommended_page_outline>\n{{ $('Set Page Outline').first().json['Page Outline'] }}\n</recommended_page_outline>\n<ux_conversion_copy_recommendations>\n{{ $json['UX & Conversions Enhancements'] }}\n</ux_conversion_copy_recommendations>", "messages": {"messageValues": [{"message": "=You are a Senior Digital Marketing Strategist. Your final task is to compile all the preceding analyses and recommendations into a single, comprehensive, and actionable Service Page Blueprint. This document will serve as the definitive guide for creating the page.\n\nImportant Context: Remember, this blueprint is for a service page. While comprehensive analysis is crucial, the final page structure and content recommendations should prioritize clarity, conciseness, and a direct path towards user action or conversion, rather than exhaustive detail suitable for a long-form blog post.\n\nConsolidate the following inputs:\n<target_query>{target_query}</target_query>\n<brand_name>{brand_name}</brand_name>\n<services_offered>{services_offered}</services_offered>\n<is_homepage>{is_homepage}</is_homepage>\n<competitor_analysis_report>\n{competitor_analysis_report}\n</competitor_analysis_report>\n<user_intent_report>\n{user_intent_report}\n</user_intent_report>\n<synthesis_and_gap_analysis>\n{synthesis_and_gap_analysis}\n</synthesis_and_gap_analysis>\n<recommended_page_outline>\n{recommended_page_outline}\n</recommended_page_outline>\n<ux_conversion_copy_recommendations>\n{ux_conversion_copy_recommendations}\n</ux_conversion_copy_recommendations>\n\n**Output Requirements:**\n\n*   Generate the final blueprint entirely in **Markdown format**.\n*   Enclose the *entire* Markdown output within a single XML tag: `<final_service_page_blueprint>`.\n*   **Do NOT use any XML tags *inside* the `<final_service_page_blueprint>` tag.** Use Markdown headings (`#`, `##`, `###`) to structure the content as specified below.\n*   Ensure the final output is highly readable, well-organized, and suitable for direct presentation to a client.\n\n**Blueprint Structure (Use Markdown Headings):**\n\n#   **1. Executive Summary**\n    *   Briefly state the target query, the primary user intent, and the overall strategy for the page (e.g., \"Create a focused service page for '{target_query}' targeting users with [primary intent]. The strategy is to highlight [key benefit/service aspect], address the common gap of [identified gap], aiming to outperform competitors by offering [unique value proposition] and convert users seeking [user goal].\"). Reference `{brand_name}` where appropriate.\n\n#   **2. User Intent Deep Dive**\n    *   Summarize the key findings from the `<user_intent_report>`.\n    *   Clearly state Primary and Secondary Intents.\n    *   Describe the Target User Persona(s).\n    *   Identify the typical User Journey Stage(s).\n    *   List the Core User Needs and Pain Points this page must address.\n\n#   **3. Competitor Landscape Summary**\n    *   Summarize the key findings from the `<competitor_analysis_report>`.\n    *   Highlight common tactics, topics covered, and keywords targeted by top competitors.\n    *   Describe typical page structures and common elements observed (e.g., types of CTAs, content sections, trust signals used).\n\n#   **4. Strategic Opportunities & Gaps**\n    *   Summarize the core findings from the `<synthesis_and_gap_analysis>`.\n    *   Identify key content/feature overlaps between user intent and competitor offerings.\n    *   Pinpoint specific content, angle, or feature gaps the `{brand_name}` page can exploit for differentiation.\n    *   List the priority SEO elements (keywords, themes, E-E-A-T considerations) based on the analysis.\n\n#   **5. Recommended Page Outline**\n    *   **Page Structure:**\n        *   Present the recommended page structure derived from `<recommended_page_outline>`.\n        *   Use nested Markdown headings (`H1`, `H2`, `H3`, `H4`) to represent the hierarchy.\n        *   Use indentation (e.g., two spaces per level) for visual clarity.\n        *   **Example Structure Format (Illustrates Formatting Only):**\n            H1: Primary Heading\n              H2: First Subheading\n              H2: Second Subheading\n                H3: First Sub-subheading under Second H2\n                H3: Second Sub-subheading under Second H2\n                  H4: Detail under Second H3\n              H2: Third Subheading\n    *   **Heading Justifications:**\n        *   Immediately following the structure, provide a list detailing the justification/purpose for *each* heading included in the structure above. Reference the heading text clearly. (e.g., \"**H1: [Actual H1 Text]:** Justification for H1...\" \"**H2: [Actual H2 Text]:** Justification for H2...\")\n\n#   **6. UX, Conversion & Copywriting Plan**\n    *   Consolidate and present the detailed recommendations from `<ux_conversion_copy_recommendations>`.\n    *   Use subheadings (e.g., `## Calls to Action (CTAs)`, `## Trust Signals`, `## Copywriting & Tone of Voice`, `## Visual Elements`, `## Risk Reversal`, `## Readability & Accessibility`) for clarity.\n    *   Ensure recommendations are actionable and specific to this service page.\n\n#   **7. Key Success Factors**\n    *   Conclude with a bulleted list summarizing the 3-5 most critical elements required for this page's success.\n    *   Focus on factors directly related to satisfying user intent, achieving SEO goals (ranking), and driving conversions based on the preceding analysis."}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "35812205-2573-4c30-9c66-168b7af5a530", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "position": [1520, 1400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4ec86dd3-14e7-420b-94f0-2e13b11ec622", "name": "Final Blueprint", "type": "string", "value": "={{ $json.text.replace(/[\\s\\S]*<final_service_page_blueprint>/, '').replace(/<\\/final_service_page_blueprint>[\\s\\S]*/, '') }}"}]}}, "typeVersion": 3.4}, {"id": "780a5e01-6f26-43da-b650-5cd3cdf1cfa0", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [1740, 1400], "parameters": {"options": {"fileName": "Blueprint.txt"}, "operation": "toText", "sourceProperty": "Final Blueprint"}, "typeVersion": 1.1}, {"id": "008ec54d-0791-41b0-bad6-38db3ae42bde", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [420, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d279426b-1764-4989-9202-9bab9ce295fa", "name": "JINA Reader API Key", "type": "string", "value": "YOUR_API_KEY"}, {"id": "74cc4b3d-6256-453c-ac16-f860c01549ec", "name": "Google Gemini Model", "type": "string", "value": "gemini-2.5-pro-preview-03-25"}, {"id": "eaa9f0cb-478e-4ebc-972a-8e8bd4945602", "name": "Waiting Time (Seconds)", "type": "string", "value": "1"}]}}, "typeVersion": 3.4}, {"id": "54ad00ab-6e54-49a2-a8dc-5d258e6ae64a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 1020, "height": 460, "content": "## Generate High-Level Service Page Blueprint Report\nThis powerful workflow generates comprehensive SEO blueprints for service pages by analyzing competitor websites and user intent. By examining the structure, headings, and meta information of top-ranking competitors for a specific target keyword, the workflow creates a detailed content strategy tailored to your brand and services, designed to outperform the competition and maximize conversions.\n\n### Setup Instructions:\n1. Create a new Jina Reader API key [here](https://jina.ai/api-dashboard/key-manager). You can claim a free API key, which allow you to use up to 1m tokens for free.  \n2. Create a new Google Gemini(PaLM) credentials by following the guide [here](https://docs.n8n.io/integrations/builtin/credentials/googleai/#using-geminipalm-api-key). Please note, if you are using the free tier, you need to set the \"Waiting Time\" to 20s as the free tier allow a maximum of 5 requests per minute.\n3. Update the node \"Set Fields\" with your Jina API Key. Change the Waiting Time to \"20\" if using free Google Gemini API key. You can change the Gemini model from here as well, in the case Gemini make changes to their Gemini models.\n4. Start the form trigger and answer to the following questions:\n4.1. Competitors: A list of direct competitors. Up to 5, use their direct service page URL.\n4.2. Target Keyword: The query related with your service. (E.g. International accounting services, Chicago cleaning services, etc...)\n4.3. Services Offered: Details your complete service offerings. This will be ensure the outline recommended align with your services.\n4.4. Brand Name: The name of your brand, your company name.\n4.5. Homepage: If you try to rank for a homepage, check that box.\n5. Download the .txt file generated at the end, copy/paste it's content (Markdown format) and copy it [here](https://markdownlivepreview.com/). You can after copy/paste the rendered results in Gdocs and share with your client/team.\n\nYou can see a demo of the report [here](https://docs.google.com/document/d/1XDJV3zNB7cLPBzaMXstzEl7ZvPrjiuBbet5C5ZlC4bo/edit). "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "443f606c-8007-41cd-959e-71d17bbabec5", "connections": {"Code": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "User Intent Analysis", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Synthesis & Gap Analysis", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Ideal Page Outline Generation", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "UX, Conversion & Copywriting Enhancement", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert URLs to Items", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Competitors Analysis", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Get URL HTML": {"main": [[{"node": "Extract HTML Elements", "type": "main", "index": 0}]]}, "Set URL Data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Code", "type": "main", "index": 0}], [{"node": "Get URL HTML", "type": "main", "index": 0}]]}, "Set Page Outline": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "Competitors Analysis": {"main": [[{"node": "Set Competitor Analysis", "type": "main", "index": 0}]]}, "User Intent Analysis": {"main": [[{"node": "Set User Intent Analysis", "type": "main", "index": 0}]]}, "Convert URLs to Items": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract HTML Elements": {"main": [[{"node": "Set URL Data", "type": "main", "index": 0}]]}, "Set Competitor Analysis": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Competitors Analysis", "type": "ai_languageModel", "index": 0}]]}, "Set User Intent Analysis": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Synthesis & Gap Analysis": {"main": [[{"node": "Set Synthesis & Gap Analysis", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "User Intent Analysis", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "Synthesis & Gap Analysis", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "Ideal Page Outline Generation", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[{"node": "UX, Conversion & Copywriting Enhancement", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "Final Service Page Blueprint", "type": "ai_languageModel", "index": 0}]]}, "Final Service Page Blueprint": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Set Synthesis & Gap Analysis": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "Ideal Page Outline Generation": {"main": [[{"node": "Set Page Outline", "type": "main", "index": 0}]]}, "Set UX & Conversions Enhancements": {"main": [[{"node": "Final Service Page Blueprint", "type": "main", "index": 0}]]}, "UX, Conversion & Copywriting Enhancement": {"main": [[{"node": "Set UX & Conversions Enhancements", "type": "main", "index": 0}]]}}}