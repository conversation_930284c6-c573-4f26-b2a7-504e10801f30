{"id": "ppsHlJlSpHPQJp4Q", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "tags": [], "nodes": [{"id": "6615e821-d47d-4df9-aa10-4aebdd9e6737", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1100, -540], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "456b6ea3-1360-4a6c-a862-84c022db78e4", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-740, -540], "parameters": {"url": "", "options": {"response": {"response": {"fullResponse": true}}}}, "typeVersion": 4.2}, {"id": "d1155cfc-c27a-40c5-8d70-c0705ce24c9b", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [-240, -520], "parameters": {"to": "", "from": "", "message": "Service Down", "options": {}}, "credentials": {"twilioApi": {"id": "Izc7tLRJsN06wezO", "name": "Twilio account"}}, "typeVersion": 1}, {"id": "f4a781ab-96bf-4801-95d4-df8f8fbd1f8a", "name": "If", "type": "n8n-nodes-base.if", "position": [-520, -540], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "75b05c45-447e-407b-847f-5ed909b3c325", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.statusCode }}", "rightValue": 200}]}}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1918412f-8dd2-404c-ad68-0b48f09ff7fc", "connections": {"If": {"main": [[], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}