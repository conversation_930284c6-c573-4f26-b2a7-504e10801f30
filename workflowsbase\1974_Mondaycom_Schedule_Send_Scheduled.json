{"id": "reQhibpNwU63Y8sn", "meta": {"instanceId": "2128095e13afd30151f0fb53632960213a789cd45ed0afd3a7fb96a985bb4bcf", "templateId": "2454", "templateCredsSetupCompleted": true}, "name": "Microsoft Outlook AI Email Assistant", "tags": [], "nodes": [{"id": "a923cfb0-64fe-499a-8f0e-13fc848731df", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [980, 540], "parameters": {}, "typeVersion": 1}, {"id": "ea865c8e-5c73-4d37-97d1-0349a265b9a4", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2880, -600], "parameters": {"color": 5, "width": 675, "height": 107, "content": "# Microsoft Outlook AI Email Assistant"}, "typeVersion": 1}, {"id": "c835042f-421b-44a0-8dc4-686ac638b358", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1300, 60], "parameters": {"width": 612, "height": 401, "content": "## Outlook Business with filters\nFilters:\n```\nflag/flagStatus eq 'notFlagged' and not categories/any()\n```\n\nThese filters ensure we do not process flagged emails or email that already have a category set."}, "typeVersion": 1}, {"id": "51ae8a4e-2d37-4118-a538-cd0fd4f427f7", "name": "Microsoft Outlook23", "type": "n8n-nodes-base.microsoftOutlook", "position": [1540, 240], "parameters": {"limit": 10, "fields": ["flag", "from", "importance", "replyTo", "sender", "subject", "toRecipients", "body", "categories", "isRead"], "output": "fields", "options": {}, "filtersUI": {"values": {"filters": {"custom": "flag/flagStatus eq 'notFlagged' and not categories/any()", "foldersToInclude": ["AAMkADYyNmQ0YWE1LWQxYjEtNDBhYS1hODI3LTg3MTkyNDAwMzE5NwAuAAAAAAA44w-ZZoU7QLO9GQAyv8UcAQAkfR2JHrRET4CmwDGznLN6AAAAAAEMAAA="]}}}, "operation": "getAll"}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "nv0cz3C6VZDzEgtR", "name": "Microsoft365 Email Account"}}, "typeVersion": 2}, {"id": "a144adad-6fef-4f76-a06e-c889e8f16080", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [2020, 60], "parameters": {"color": 6, "width": 459, "height": 401, "content": "## Sanitise Email \nRemoves HTML and useless information in preparation for the AI Agent"}, "typeVersion": 1}, {"id": "92ccac8f-9ce3-4f81-a499-e55835be3fc7", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2020, 580], "parameters": {"color": 4, "width": 736, "height": 558, "content": "## Get Rules & Categories\nEdit the airtables to set your own categories, rules, contacts and/or delete rules. "}, "typeVersion": 1}, {"id": "5b304e0f-002c-42c6-82a0-9ab1dc858861", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3860, 460], "parameters": {"model": "gpt-4o", "options": {"temperature": 0.2}}, "credentials": {"openAiApi": {"id": "l2JgpErNc5namHVH", "name": "OpenAI account"}}, "typeVersion": 1}, {"id": "210816e8-6a1f-4e63-a90e-d953e0e87ccd", "name": "Set Category", "type": "n8n-nodes-base.microsoftOutlook", "position": [4500, 240], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $json.output.id }}"}, "operation": "update", "updateFields": {"categories": "={{ [$json.output.category] }}"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "nv0cz3C6VZDzEgtR", "name": "Microsoft365 Email Account"}}, "typeVersion": 2}, {"id": "fe4f8e8f-6a5c-4b7b-b5f7-10f1f374397c", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [4040, 460], "parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"string\",\n      \"description\": \"The email id\"\n    },\n    \"subject\": {\n      \"type\": \"string\",\n      \"description\": \"The email subject line\"\n    },\n    \"category\": {\n      \"type\": \"string\",\n      \"description\": \"Primary classification of the email\"\n    },\n    \"subCategory\": {\n      \"type\": \"string\",\n      \"description\": \"Optional sub-classification if applicable\"\n    },\n    \"analysis\": {\n      \"type\": \"string\",\n      \"description\": \"Reasoning behind the categorization\"\n    }\n  },\n  \"required\": [\"id\",\"subject\", \"category\", \"analysis\"]\n}"}, "typeVersion": 1.2}, {"id": "489028ca-f265-4ea2-b8dd-64dd6b06c8f6", "name": "If", "type": "n8n-nodes-base.if", "position": [4740, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6e4ecd0c-d151-4e5b-8d66-558f9f9ec3b0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('AI: Analyse Email').item.json.output.subCategory }}", "rightValue": "Action"}]}}, "typeVersion": 2.2}, {"id": "e2a27071-bac6-4a67-94fb-93e7ac218c89", "name": "Set Importance", "type": "n8n-nodes-base.microsoftOutlook", "position": [5000, 220], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('AI: Analyse Email').item.json.output.id }}"}, "operation": "update", "updateFields": {"importance": "High"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "nv0cz3C6VZDzEgtR", "name": "Microsoft365 Email Account"}}, "typeVersion": 2}, {"id": "61cecccf-589f-4514-b126-cfbfc7d94981", "name": "AI: <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.agent", "position": [3860, 240], "parameters": {"text": "=Categorise the following email:\n<email>\n{{ $('Loop Over Items').item.json.toJsonString() }}\n</email>\n\n<Contact>\n{{ $('Contact').all().toJsonString() }}\n</Contact>\n\n<DeleteRules>\n{{ $('Delete Rules').all().toJsonString() }}\n</DeleteRules>\n\n<Categories>\n{{ $('Categories').all().toJsonString() }}\n</Categories>\n\nEnsure your final output is valid JSON with no additional text or token in the following format:\n\n{\n  \"subject\": \"SUBJECT_LINE\",1\n  \"category\": \"CATEGORY\",\n  \"subCategory\": \"SUBCATEGORY\", //use sparingly\n  \"analysis\": \"ANALYSIS_REASONING\"\n}\n\nRemember you can only use ONE of the following category 'Name' values from the 'Categories' defined above. No other categories can be used. Use the subcategory for additional context, for example, if a client email requires action or if a supplier email requires action. Do not create any additional subcategories; you can only use ONE of the category 'Name' values from the 'Categories' defined above.", "options": {"systemMessage": "=Categories: \"\"\"{{ $('Categories').all().toJsonString() }}\"\"\"\n\nYou are an AI email assistant for the *insert role & title*. Your task is to categorize incoming emails using one of the category 'Name' values defined in 'Categories' above.\n\nYou may also use the subcategory:\n- Action\n\nInstructions:\nAnalyse the email subject, body, and sender's email address to determine the appropriate category by referring to the 'Usage', 'Sender Indicators' and 'Subject Indicators' defined in the 'Categories' above.\n\n\nOutput Format:\nProduce output in valid JSON format:\n{\n  \"id\": \"{{ $('Loop Over Items').item.json.id }}\",\n  \"subject\": \"SUBJECT_LINE\",\n  \"category\": \"PRIMARY CATEGORY\",\n  \"subCategory\": \"SUBCATEGORY\", // use sparingly\n  \"analysis\": \"Brief 1-2 sentence explanation of category choice\"\n}\n- Replace \"SUBJECT_LINE\" with the actual subject of the email.\n- \"PRIMARY CATEGORY\" should be one of the categories listed above.\n- \"SUBCATEGORY\" should be \"Action\" if applicable; otherwise, omit or leave blank.\n- The \"analysis\" should be a brief 1-2 sentence explanation of why the category was chosen. Also, indicate if there was a match for the 'Contact' email and the email sender.\n\nImportant:\nYou may only use the categories and the subcategory listed above; do not create any additional categories or subcategories.\n\nNo additional text or tokens should be included outside the JSON output.\n"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "947eb4d7-9067-4144-819b-f53947ca77f8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1420, -620], "parameters": {"color": 6, "width": 760, "height": 400, "content": "## CRM Contact List Integration \nFor this workflow I am retrieving supplier & client contacts from Monday.com the email assistant has better context to categorise, prioritise and reply to emails.\nThe list is updated daily or you can change the scheduler trigger to update more or less frequently.\nYou could replace this with your own CRM."}, "typeVersion": 1}, {"id": "79815a8f-5650-4ec9-97b3-c0201469d048", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [3640, 60], "parameters": {"width": 700, "height": 580, "content": "## Categorise & Prioritise Emails Agent \n"}, "typeVersion": 1}, {"id": "2e9411a8-30da-4ee5-9597-cb08e34049a5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [4400, 120], "parameters": {"color": 4, "width": 740, "height": 280, "content": "## Set the category & importance using the output from the agent\n"}, "typeVersion": 1}, {"id": "138a734f-0ac5-4e50-a4af-b7255e11e862", "name": "Check Mail Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "disabled": true, "position": [980, 260], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "typeVersion": 1.2}, {"id": "709795fd-68ff-4881-9f30-6270dea83f7c", "name": "Update Contacts Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1080, -420], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "552803ce-3dae-415d-b14d-a7b990450482", "name": "Monday.com - Get Contacts", "type": "n8n-nodes-base.mondayCom", "position": [1520, -440], "parameters": {"boardId": "1840712625", "groupId": "topics", "resource": "boardItem", "operation": "getAll", "returnAll": true}, "credentials": {"mondayComApi": {"id": "wur9UFaP9YKCFZly", "name": "Monday.com - API User"}}, "typeVersion": 1}, {"id": "cf41ebb0-f295-4f1a-a49c-05471a4d9220", "name": "Airtable - Contacts", "type": "n8n-nodes-base.airtable", "position": [1920, -440], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appNmgIGA4Fhculsn", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn", "cachedResultName": "AI Email Assistant"}, "table": {"__rl": true, "mode": "list", "value": "tbl8gTTEn96uFRDHE", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn/tbl8gTTEn96uFRDHE", "cachedResultName": "Contacts"}, "columns": {"value": {"Type": "={{ $json.column_values[1].text }}", "Email": "={{ $json.column_values[6].text }}", "Last Name": "={{ $json.name.split(\" \",2).last() }}", "First Name": "={{ $json.name.split(\" \",2).first() }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "First Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Type", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Type", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "Bgr0Fi30Oek2jpXT", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "6d698b4d-f18c-4e4a-9c83-8a39208aee8c", "name": "Convert to <PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "notes": "Converts the body of the email to markdown", "position": [2120, 240], "parameters": {"html": "={{ $json.body.content }}", "options": {}}, "notesInFlow": true, "typeVersion": 1}, {"id": "012109cc-dcba-464b-b3bc-17201b1ad436", "name": "Email Messages", "type": "n8n-nodes-base.set", "notes": "Set email fields", "position": [2320, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "edb304e1-3e9f-4a77-918c-25646addbc53", "name": "subject", "type": "string", "value": "={{ $json.subject }}"}, {"id": "57a3ef3a-2701-40d9-882f-f43a7219f148", "name": "importance", "type": "string", "value": "={{ $json.importance }}"}, {"id": "d8317f4f-aa0e-4196-89af-cb016765490a", "name": "sender", "type": "object", "value": "={{ $json.sender.emailAddress }}"}, {"id": "908716c8-9ff7-4bdc-a1a3-64227559635e", "name": "from", "type": "object", "value": "={{ $json.from.emailAddress }}"}, {"id": "ce007329-e221-4c5a-8130-2f8e9130160f", "name": "body", "type": "string", "value": "={{ $json.data\n    .replace(/<[^>]*>/g, '')                      // Remove HTML tags\n    .replace(/\\[(.*?)\\]\\((.*?)\\)/g, '')            // Remove Markdown links like [text](link)\n    .replace(/!\\[.*?\\]\\(.*?\\)/g, '')               // Remove Markdown images like ![alt](image-link)\n    .replace(/\\|/g, '')                            // Remove table separators \"|\"\n    .replace(/-{3,}/g, '')                         // Remove horizontal rule \"---\"\n    .replace(/\\n+/g, ' ')                          // Remove multiple newlines\n    .replace(/([^\\w\\s.,!?@])/g, '')                // Remove special characters except essential ones\n    .replace(/\\s{2,}/g, ' ')                       // Replace multiple spaces with a single space\n    .trim()                                        // Trim leading/trailing whitespace\n}}\n"}, {"id": "6abfcc56-7b0a-469e-82fc-ce294ed5162b", "name": "id", "type": "string", "value": "={{ $json.id }}"}]}}, "typeVersion": 3.4}, {"id": "6d3933f3-3f2e-4268-8979-d6c93c961916", "name": "Rules", "type": "n8n-nodes-base.airtable", "position": [2400, 720], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appNmgIGA4Fhculsn", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn", "cachedResultName": "AI Email Assistant"}, "table": {"__rl": true, "mode": "list", "value": "tblMSXbMFKETNToxV", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn/tblMSXbMFKETNToxV", "cachedResultName": "Rules"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "Bgr0Fi30Oek2jpXT", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 2.1}, {"id": "9166d63f-0c16-490f-afb8-e30ef25c49da", "name": "Categories", "type": "n8n-nodes-base.airtable", "position": [2300, 860], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appNmgIGA4Fhculsn", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn", "cachedResultName": "AI Email Assistant"}, "table": {"__rl": true, "mode": "list", "value": "tbliKDp5PoFNF7YI7", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn/tbliKDp5PoFNF7YI7", "cachedResultName": "Categories"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "Bgr0Fi30Oek2jpXT", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 2.1}, {"id": "f48e5a29-0eee-4420-80d9-2b9b016fba0d", "name": "Delete Rules", "type": "n8n-nodes-base.airtable", "position": [2140, 960], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appNmgIGA4Fhculsn", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn", "cachedResultName": "AI Email Assistant"}, "table": {"__rl": true, "mode": "list", "value": "tbl84EJr7y65ed4zh", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn/tbl84EJr7y65ed4zh", "cachedResultName": "Delete Rules"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "Bgr0Fi30Oek2jpXT", "name": "Airtable Personal Access Token account"}}, "executeOnce": true, "typeVersion": 2.1}, {"id": "d6ad6091-2c7e-41b9-a9b3-b8715208cec0", "name": "Contact", "type": "n8n-nodes-base.airtable", "position": [3080, 240], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appNmgIGA4Fhculsn", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn", "cachedResultName": "AI Email Assistant"}, "table": {"__rl": true, "mode": "list", "value": "tbl8gTTEn96uFRDHE", "cachedResultUrl": "https://airtable.com/appNmgIGA4Fhculsn/tbl8gTTEn96uFRDHE", "cachedResultName": "Contacts"}, "options": {}, "operation": "search", "filterByFormula": "={Email}='{{ $('Loop Over Items').item.json.from.address }}'"}, "credentials": {"airtableTokenApi": {"id": "Bgr0Fi30Oek2jpXT", "name": "Airtable Personal Access Token account"}}, "executeOnce": false, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "bc1ede01-fa21-4446-a4e1-1a725a3a4887", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2720, 260], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "fcdd837d-8852-4dcf-924c-aba4f2cddeba", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [3420, 220], "parameters": {"mode": "chooseBranch", "numberInputs": 4}, "typeVersion": 3}, {"id": "f790dd9b-19bb-4649-975e-00a511f2dd9f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3020, 60], "parameters": {"color": 4, "height": 400, "content": "## Match Contact\nCheck if the sender is an existing contact. Note in this workflow the contacts are dynamically loaded from Monday.com"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {}, "versionId": "e0fed20f-21be-4e21-bcc9-8af7062229dd", "connections": {"If": {"main": [[{"node": "Set Importance", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "AI: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Rules": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Contact": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Categories": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Delete Rules": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "Set Category": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Email Messages": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Set Importance": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Contact", "type": "main", "index": 0}]]}, "AI: Analyse Email": {"main": [[{"node": "Set Category", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI: <PERSON><PERSON><PERSON>", "type": "ai_languageModel", "index": 0}]]}, "Convert to Markdown": {"main": [[{"node": "Email Messages", "type": "main", "index": 0}]]}, "Microsoft Outlook23": {"main": [[{"node": "Convert to <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI: <PERSON><PERSON><PERSON>", "type": "ai_outputParser", "index": 0}]]}, "Monday.com - Get Contacts": {"main": [[{"node": "Airtable - Contacts", "type": "main", "index": 0}]]}, "Check Mail Schedule Trigger": {"main": [[{"node": "Microsoft Outlook23", "type": "main", "index": 0}]]}, "Update Contacts Schedule Trigger": {"main": [[{"node": "Monday.com - Get Contacts", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Microsoft Outlook23", "type": "main", "index": 0}, {"node": "Rules", "type": "main", "index": 0}, {"node": "Categories", "type": "main", "index": 0}, {"node": "Delete Rules", "type": "main", "index": 0}]]}}}