{"id": "pDLtBJkNSXXWSvB0", "meta": {"instanceId": "bc5ae5fe2056690823360ec27da902117e87ff22a0f9c9bb0448416fba4527f8"}, "name": "Training Feedback Automation", "tags": [], "nodes": [{"id": "6cdd7521-a16c-4e1a-9b18-c232660522c8", "name": "Airtable Trigger", "type": "n8n-nodes-base.airtableTrigger", "position": [160, 680], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "app216gZPY8ax1Qgd"}, "tableId": {"__rl": true, "mode": "id", "value": "tblaKkOK6RZ4cgXGI"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "Created", "authentication": "airtableOAuth2Api", "additionalFields": {}}, "credentials": {"airtableOAuth2Api": {"id": "qYu4nditWNzeLITf", "name": "Airtable account"}}, "typeVersion": 1}, {"id": "faeb9069-2f25-419c-8192-5ed69a49d192", "name": "Webhook - Action Task Poor", "type": "n8n-nodes-base.webhook", "position": [180, 140], "webhookId": "4ff46f8a-e1d0-4ad9-8dae-99de53838aaf", "parameters": {"path": "4ff46f8a-e1d0-4ad9-8dae-99de53838aaf", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "25f65aa6-9d0a-4a32-b2b9-49c2d6fb94cf", "name": "Switch1", "type": "n8n-nodes-base.switch", "position": [500, 140], "parameters": {"rules": {"values": [{"outputKey": "Validated", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook - Action Task Poor').item.json.body.actionName }}", "rightValue": "Validate"}]}, "renameOutput": true}, {"outputKey": "Other", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "94250338-cb2a-421c-813b-9d8d5d1e02ed", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $('Webhook - Action Task Poor').item.json.body.actionName }}", "rightValue": "Validate"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "50909553-8bea-471f-9030-f3d8898abce5", "name": "LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [1020, 680], "parameters": {"text": "=🌟 Feedback on Our Recent Training Session! 🌟\n\nWe are excited to share the positive feedback from our participants regarding our latest training session. Here are some highlights:\n\nFacilitator: {{ $json[\"fields\"][\"Facilitator name\"][0][\"name\"] }}\nCourse: {{ $json[\"fields\"][\"Course name\"][0] }}\n\nFeedback Details:\n\nContent: {{ $json[\"fields\"][\"Content\"] }}/5\nRelevance: {{ $json[\"fields\"][\"Relevant\"] }}/5\nOverall Satisfaction: {{ $json[\"fields\"][\"Satisfaction\"] }}/5\nRecommendation: {{ $json[\"fields\"][\"Recommend\"] }}/5\n\nA big thank you to {{ $json[\"fields\"][\"Facilitator name\"][0][\"name\"] }} for his excellent work as a facilitator and to all our participants for their valuable feedback. We are committed to continuously improving our training sessions to meet your expectations and needs.\n\n#Training #Feedback #ContinuousLearning #CustomerSatisfaction", "postAs": "organization", "additionalFields": {}}, "credentials": {"linkedInOAuth2Api": {"id": "4sXxHri0PRgxO48n", "name": "LinkedIn account"}}, "typeVersion": 1}, {"id": "b441873f-187c-4777-ab27-d7adf8450d8b", "name": "Poor  - Send Email", "type": "n8n-nodes-base.emailSend", "position": [1580, 320], "parameters": {"html": "=Dear [Recipient Name],\n\nWe would like to inform you that a new task has been created to address the recent training feedback we received. Below are the details of the task:\n\nTask Title: {{ $json[\"title\"] }}\n\nTask Description:\n{{ $json[\"description\"] }}\n\nTask Status: {{ $json[\"statusName\"] }}\n\nInstructions:\n{{ $json[\"instruction\"] }}\n\nActions Required:\n\n- {{ $json[\"task\"][\"actions\"][0][\"name\"] }}\nDescription:\n{{ $json[\"task\"][\"actions\"][0][\"description\"] }}\n\n- {{ $json[\"task\"][\"actions\"][1][\"name\"] }}\nDescription:\n{{ $json[\"task\"][\"actions\"][1][\"description\"] }}\n\n\nPlease address this task at your earliest convenience to ensure we promptly respond to the feedback and improve our training program accordingly.\n\nIf you have any questions or require further information, please do not hesitate to contact us.\n\nLink : https://demo.usertask.io/app/task/instance/form/{{ $json[\"code\"] }}\n\nBest regards,", "options": {}, "subject": "New Task Created - Urgent: Training Feedback Requires Immediate Attention", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "CnHY3ZPBDwo5EnSH", "name": "SMTP account 2"}}, "typeVersion": 2.1}, {"id": "0e9c5ee5-416b-4cb3-9797-417003bc74cd", "name": "Call Usertask - Create task", "type": "n8n-nodes-base.httpRequest", "position": [1020, 60], "parameters": {"url": "http://demo.usertask.io/api/task/create-instance", "method": "POST", "options": {}, "jsonBody": "{\n  \"taskCode\": \"tltJf90mJVEnpUZvuQBi\",\n  \"callbackUrl\": \"https://n8n-hzd1.onrender.com/webhook/4ff46f8a-e1d0-4ad9-8dae-99de53838aaf\",\n  \"description\":\"We have received a training feedback rating of 1 star. It is crucial to address this issue promptly.We recommend scheduling a meeting to discuss the feedback in detail and develop an action plan to improve the training program.\",\n  \"instruction\":\"\",\n  \"title\":\"Urgent: Training Feedback Requires Immediate Attention\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "22d1ce6fa3ae7039fe42d3ddf1ba55d8f5ee9e2c2e6b04788144fca080d1e170"}, {"name": "X-CLIENT-ID", "value": "f3604b6d2d33af2006ecb0d4910871fa"}]}}, "typeVersion": 4.1}, {"id": "27ab7bd3-e3a8-4d87-b28f-767bff9ec0e1", "name": "Call Usertask - Create Task - Pair and good", "type": "n8n-nodes-base.httpRequest", "position": [1020, 420], "parameters": {"url": "http://demo.usertask.io/api/task/create-instance", "method": "POST", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "json"}}}, "jsonBody": "{\n  \"taskCode\": \"tltJf90mJVEnpUZvuQBi\",\n  \"callbackUrl\": \"https://n8n-hzd1.onrender.com/webhook/4ff46f8a-e1d0-4ad9-8dae-99de53838aaf\",\n  \"description\":\"We have received a training feedback rating of 1 star. It is crucial to address this issue promptly.We recommend scheduling a meeting to discuss the feedback in detail and develop an action plan to improve the training program.\",\n  \"instruction\":\"\",\n  \"title\":\"Urgent: Training Feedback Requires Immediate Attention\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "22d1ce6fa3ae7039fe42d3ddf1ba55d8f5ee9e2c2e6b04788144fca080d1e170"}, {"name": "X-CLIENT-ID", "value": "f3604b6d2d33af2006ecb0d4910871fa"}]}}, "typeVersion": 4.1}, {"id": "ec55cbd8-e863-4dea-b2fc-1834f9d27f13", "name": "Send Email after WebHook", "type": "n8n-nodes-base.emailSend", "position": [760, -180], "parameters": {"html": "=Dear Trainer's and HR Manager's,\n\nWe have received a training feedback rating of 1 star. It is crucial to address this issue promptly.\n\nResponse : {{ $json[\"body\"][\"results\"][0][\"actionName\"] }}\n\nWe recommend scheduling a meeting to discuss the feedback in detail and develop an action plan to improve the training program.\n\nBest regards,", "options": {}, "subject": "Urgent: Training Feedback Requires Immediate Attention", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "CnHY3ZPBDwo5EnSH", "name": "SMTP account 2"}}, "typeVersion": 2.1}, {"id": "bd83083f-e1df-41e8-b7b3-9065fa610ee5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [730.*************, 0], "parameters": {"color": 7, "width": 714.*************, "height": 593.***********, "content": "## UserTask\n**Link** https://demo.usertask.io \n\n**Login**\n<EMAIL>\n**Password**\nQSDpo2x10?2020"}, "typeVersion": 1}, {"id": "27e89776-7258-44f5-ac8c-5926f38762b7", "name": "Call Usertask - Detail Task", "type": "n8n-nodes-base.httpRequest", "position": [1300, 320], "parameters": {"url": "=https://demo.usertask.io/api/task/instance/info/{{ $json[\"body\"][\"code\"] }}", "options": {"redirect": {"redirect": {}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "22d1ce6fa3ae7039fe42d3ddf1ba55d8f5ee9e2c2e6b04788144fca080d1e170"}, {"name": "X-CLIENT-ID", "value": "f3604b6d2d33af2006ecb0d4910871fa"}]}}, "typeVersion": 4.1}, {"id": "5576aba7-9051-465f-a095-47a52e35b151", "name": "Send Email - Information for marcketing", "type": "n8n-nodes-base.emailSend", "position": [1280, 680], "parameters": {"options": {}, "subject": "Task Created", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "credentials": {"smtp": {"id": "CnHY3ZPBDwo5EnSH", "name": "SMTP account 2"}}, "typeVersion": 2.1}, {"id": "074b8d06-f78a-4209-9172-d8b1a57c97fb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-20, 540], "parameters": {"color": 7, "width": 373.**************, "height": 320.**************, "content": "## AirTable \n**For exemple, use** Employee training management **template**. [Guide](https://www.airtable.com/templates/employee-training-management/expnOaGvlQDwuWKVk)\n\n"}, "typeVersion": 1}, {"id": "b742a10e-71b2-4022-8c16-53b826512bbe", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-20, 0], "parameters": {"color": 7, "width": 374.**************, "height": 303.************, "content": "## WebHook \nThe webhook allows retrieving the result of a Usertask. Tasks can be completed either via the API or through the Usertask form."}, "typeVersion": 1}, {"id": "98cc7ca2-359a-4329-8910-14b6607daa87", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-11.***************, -460], "parameters": {"width": 709.4232592367164, "height": 434.93437649014015, "content": "## Training Feedback Automation with Usertask and Airtable\nThis n8n workflow is designed to automate the management of training feedback by integrating Airtable, Usertask, and various notification actions. \n\nHere is a detailed description of each step in the workflow:\n\n- **Airtable Trigger**: Captures new or updated feedback entries from Airtable.\n- **Switch Node**: Evaluates the feedback rating and directs the workflow based on the rating.\n- **Webhook**: Retrieves the result of a Usertask task.\n- **Task Creation**:\n  - Creates tasks in Usertask for poor feedback.\n  - Creates follow-up tasks for fair to good feedback.\n  - Documents positive feedback and posts recognition on LinkedIn for very good to excellent ratings.\n- **Notifications**:\n  - Sends email notifications to responsible parties for urgent actions.\n  - Sends congratulatory emails and posts on LinkedIn for positive feedback.\n\nVideo : [https://youtu.be/U14MhTcpqeY](https://youtu.be/U14MhTcpqeY)\n"}, "typeVersion": 1}, {"id": "f12d0516-43a2-4517-a633-60d809cd3413", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [460, 420], "parameters": {"rules": {"values": [{"outputKey": "Dissatisfaction", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.fields.Content }}", "rightValue": 1}]}, "renameOutput": true}, {"outputKey": "Fair", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2d1c10b8-0418-4dcf-aa53-41f0b75ccc08", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.fields.Content }}", "rightValue": 2}]}, "renameOutput": true}, {"outputKey": "Good", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d2be2a3f-32ae-4578-a9aa-4a8f2b19f08f", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.fields.Content }}", "rightValue": 3}]}, "renameOutput": true}, {"outputKey": "Very Good", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4dd5b796-9180-47d8-9ebd-4164a5dfa0d7", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.fields.Content }}", "rightValue": 4}]}, "renameOutput": true}, {"outputKey": "Excellent", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "312f4f14-a341-4dea-881c-3c85a9cea13c", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $json.fields.Content }}", "rightValue": 5}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3}, {"id": "6eb8d928-c331-49d5-830a-9442a367254b", "name": "Call Usertask - Create Task - Dissatisfaction", "type": "n8n-nodes-base.httpRequest", "position": [1020, 240], "parameters": {"url": "http://demo.usertask.io/api/task/create-instance", "method": "POST", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "json"}}}, "jsonBody": "{\n  \"taskCode\": \"tltJf90mJVEnpUZvuQBi\",\n  \"callbackUrl\": \"https://n8n-hzd1.onrender.com/webhook/4ff46f8a-e1d0-4ad9-8dae-99de53838aaf\",\n  \"description\":\"We have received a training feedback rating of 1 star. It is crucial to address this issue promptly.We recommend scheduling a meeting to discuss the feedback in detail and develop an action plan to improve the training program.\",\n  \"instruction\":\"\",\n  \"title\":\"Urgent: Training Feedback Requires Immediate Attention\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "X-API-KEY", "value": "22d1ce6fa3ae7039fe42d3ddf1ba55d8f5ee9e2c2e6b04788144fca080d1e170"}, {"name": "X-CLIENT-ID", "value": "f3604b6d2d33af2006ecb0d4910871fa"}]}}, "typeVersion": 4.1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "955cc31e-3e7b-49b1-85c5-8f4604cbcc9a", "connections": {"Switch": {"main": [[{"node": "Call Usertask - Create Task - Dissatisfaction", "type": "main", "index": 0}], [{"node": "Call Usertask - Create Task - Pair and good", "type": "main", "index": 0}], [{"node": "Call Usertask - Create Task - Pair and good", "type": "main", "index": 0}], [{"node": "LinkedIn", "type": "main", "index": 0}], [{"node": "LinkedIn", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Send Email after WebHook", "type": "main", "index": 0}], [{"node": "Call Usertask - Create task", "type": "main", "index": 0}]]}, "LinkedIn": {"main": [[{"node": "Send Email - Information for marcketing", "type": "main", "index": 0}]]}, "Airtable Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Webhook - Action Task Poor": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Call Usertask - Detail Task": {"main": [[{"node": "Poor  - Send Email", "type": "main", "index": 0}]]}, "Call Usertask - Create Task - Pair and good": {"main": [[{"node": "Call Usertask - Detail Task", "type": "main", "index": 0}]]}, "Call Usertask - Create Task - Dissatisfaction": {"main": [[{"node": "Call Usertask - Detail Task", "type": "main", "index": 0}]]}}}