{"nodes": [{"id": "6abe578b-d503-4da5-9af8-f9977de71139", "name": "Vivid Pop Explosion", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=rule of thirds, golden ratio, hyper-maximalist, vibrant neon, high-contrast, octane render, photorealism, 8k ::7 --ar 16:9 --s 1000\n\nDesign a fun, energetic scene filled with bold, neon colors, and playful shapes that pop off the screen. The image should evoke a sense of joy and movement, using fluid, organic forms and exaggerated, cartoon-like proportions. Focus on creating a lively atmosphere with contrasting, saturated tones and dynamic lighting. Use a mix of asymmetrical and balanced compositions to create a playful visual flow. Render in 8K with a hyper-maximalist approach using Octane Render for vibrant, high-gloss textures and photorealistic lighting effects. Include:"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "7de1ea42-3b18-4bfb-8ea4-a8b6c8d16763", "name": "AI Dystopia", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=golden ratio, rule of thirds, cyberpunk, glitch art, octane render, cinematic realism, 8k ::7 --ar 16:9 --s 1000\n\nGenerate a futuristic, cyberpunk dystopia with metallic textures, digital glitches, and neon lights. Blend cold, dystopian structures with traces of organic life. Use photorealistic lighting and dynamic reflections to enhance the visual depth of the scene. Include:"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "aa17c288-78e0-48d9-9c60-0e63e351d0b6", "name": "Post-Analog Glitchscape", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=rule of thirds, asymmetric composition, glitch art, pixelation, VHS noise, octane render, unreal engine, 8k ::7 --ar 16:9 --s 1200\nDesign a glitchy, post-analog world with digital decay and broken visuals. Utilize pixelated elements, VHS noise, and neon glitches to create a fragmented aesthetic. Use bold, contrasting colors against muted backgrounds for a high-contrast, otherworldly feel. The composition should follow asymmetrical rules, focusing on chaotic yet intentional visual balance. Include:"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "769ff46c-630f-456d-ae19-4c6496270fda", "name": "Neon Fauvism", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=asymmetric composition, golden ratio, neon colors, abstract forms, octane render, cinematic realism, unreal engine, 8k ::7 --ar 16:9 --s 1000\nCreate a bold, vivid composition using neon colors and fluid shapes that break away from reality. Focus on abstract forms, blending Fauvism's exaggerated color palette with modern digital art techniques. Use asymmetric composition and dynamic lighting. Render with a vibrant, high-energy aesthetic. Include:"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "ccc67dcb-84e6-476a-9bc2-b5382b700d5e", "name": "None", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 1160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=Include: "}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "fea2039c-48e5-4077-af2c-ea72838e1a5d", "name": "Serve webpage", "type": "n8n-nodes-base.respondToWebhook", "position": [1460, 580], "parameters": {"options": {}, "respondWith": "text", "responseBody": "=<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n <meta charset=\"UTF-8\">\n <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <title>Flux Image Generation Result</title>\n <style>\n body {\n font-family: 'Open Sans', Tahoma, Geneva, Verdana, sans-serif;\n display: flex;\n flex-direction: column;\n align-items: center;\n justify-content: center;\n min-height: 100vh;\n background-color: #121212;\n color: #e0e0e0;\n }\n .container {\n margin-top: 2em;\n width: 90%;\n max-width: 670px; /* Increased the max-width for the main image area */\n text-align: center;\n background: #1e1e1e;\n padding: 24px;\n border-radius: 12px;\n box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\n margin-bottom: 24px;\n }\n .image-container {\n margin-bottom: 20px;\n }\n .image-container img {\n max-width: 100%;\n height: auto;\n border-radius: 12px;\n border: 2px solid #333;\n }\n .style-text {\n font-size: 18px;\n margin-bottom: 20px;\n color: #bbb;\n }\n .cta {\n display: block;\n width: 100%;\n margin: 20px 0 0;\n padding: 18px 0;\n border: none;\n border-radius: 6px;\n text-decoration: none;\n color: #fff;\n background-color: #1C9985;\n font-size: 18px;\n font-weight: 400;\n cursor: pointer;\n transition: all 0.3s ease;\n }\n .cta:hover {\n background-color: #20B69E;\n transform: translateY(-2px);\n box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n }\n /* New section for recent renders */\n .recent-renders {\n display: flex;\n justify-content: space-between;\n flex-wrap: wrap;\n gap: 16px;\n margin-top: 24px;\n max-width: 670px;\n }\n .recent-render img {\n width: 100%;\n max-width: 180px;\n height: auto;\n border-radius: 8px;\n border: 2px solid #333;\n }\n .recent-render {\n flex: 1;\n max-width: 200px;\n background-color: #2c2c2c;\n padding: 10px;\n border-radius: 10px;\n margin-bottom: 3 rem;\n }\n </style>\n</head>\n<body>\n <div class=\"container\">\n <div class=\"image-container\">\n <img src=\"https://pub-d2d94462851644a78ea607e05f8a2d25.r2.dev/fg-{{ $execution.id }}.jpg\" alt=\"Generated Image\" />\n </div>\n <div class=\"style-text\">Style: {{ $('Route by style').item.json.Style }}</div>\n <a href=\"https://n8n.io/workflows/2417-flux-ai-image-generator?utm_source=30day\" class=\"cta\">Duplicate this AI template</a>\n </div>\n \n <!-- New section to display the last 4 renders -->\n <div class=\"recent-renders\">\n <div class=\"recent-render\">\n <img src=\"https://pub-d2d94462851644a78ea607e05f8a2d25.r2.dev/fg-{{ $execution.id.toNumber() - 1 }}.jpg\" alt=\"Recent Render 1\">\n </div>\n <div class=\"recent-render\">\n <img src=\"https://pub-d2d94462851644a78ea607e05f8a2d25.r2.dev/fg-{{ $execution.id.toNumber() - 2 }}.jpg\" alt=\"Recent Render 2\">\n </div>\n <div class=\"recent-render\">\n <img src=\"https://pub-d2d94462851644a78ea607e05f8a2d25.r2.dev/fg-{{ $execution.id .toNumber() - 3}}.jpg\" alt=\"Recent Render 3\">\n </div>\n <div class=\"recent-render\">\n <img src=\"https://pub-d2d94462851644a78ea607e05f8a2d25.r2.dev/fg-{{ $execution.id.toNumber() - 4 }}.jpg\">\n </div>\n </div>\n</body>\n</html>\n"}, "typeVersion": 1.1}, {"id": "2df7b738-9584-48b4-8adc-cafb0c026928", "name": "Respond with error", "type": "n8n-nodes-base.respondToWebhook", "position": [1460, 820], "parameters": {"options": {}, "respondWith": "json", "responseBody": "{\n \"formSubmittedText\": \"Flux API failed. It does this ~10% of the time. Refresh and try again.\"\n}"}, "typeVersion": 1.1}, {"id": "54cba7c4-db24-4abb-9638-ee66236d8676", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 440], "parameters": {"color": 7, "width": 205.9419250888625, "height": 107.99633347519193, "content": "### Set style prompt\nEach Edit fields node after the Switch sets `stylePrompt`, used in huggingface node."}, "typeVersion": 1}, {"id": "f4aa76f8-d35f-4332-aa39-0c34582618eb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [720, 840], "parameters": {"color": 7, "width": 419.0156901664085, "height": 226.2264013670822, "content": "### Run flux model\nIn `Call huggingface inference api` You can change `black-forest-labs/FLUX.1-schnell` in URL parameter to other models:\n- `black-forest-labs/FLUX.1-dev`\n- `Shakker-Labs/FLUX.1-dev-LoRA-AntiBlur`\n- `XLabs-AI/flux-RealismLora`\n- `ByteDance/Hyper-SD`\n\n[See more models on huggingface.co](https://huggingface.co/models?pipeline_tag=text-to-image&sort=trending)\n"}, "typeVersion": 1}, {"id": "2b0b29ce-82c2-4428-bf12-cb25262e5291", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1120, 440], "parameters": {"color": 7, "width": 247.37323750873333, "height": 90.**************, "content": "### Host image on S3\n[Cloudflare](https://cloudflare.com) has free S3 compatible hosting. They call it \"R2\"."}, "typeVersion": 1}, {"id": "6fccc88f-9e72-49a3-952d-b7b1d9612091", "name": "Upload image to S3", "type": "n8n-nodes-base.s3", "onError": "continueErrorOutput", "position": [1120, 580], "parameters": {"fileName": "=fg-{{ $execution.id }}.jpg", "operation": "upload", "bucketName": "flux-generator", "additionalFields": {}}, "credentials": {"s3": {"id": "HZqaz9hPFlZp3BZ7", "name": "S3 account"}}, "typeVersion": 1}, {"id": "7824dc49-c546-424e-8ba9-5f34b190d5f0", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1460, 440], "parameters": {"color": 7, "width": 302.*************, "height": 90.**************, "content": "### Respond to Form\nServe a webform with image on success. On error, send message to form."}, "typeVersion": 1}, {"id": "71739ba4-b8db-439e-b8c3-06f3208126e3", "name": "Hyper-Surreal Escape", "type": "n8n-nodes-base.set", "notes": " ", "position": [380, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9ec60f33-b940-40a6-9f8a-cb944b7065f1", "name": "stylePrompt", "type": "string", "value": "=golden ratio, rule of thirds, cyberpunk, glitch art, octane render, cinematic realism, 8k ::7 --ar 16:9 --s 1000\nCreate a hyper-realistic yet surreal landscape that bends reality, incorporating dreamlike elements and exaggerated proportions. Use vibrant, almost neon colors, and focus on a sense of wonder, play, and fantasy. Include:\n"}]}, "includeOtherFields": true}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "dcfdb152-a055-4f0f-baa5-7cf8afba36ae", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-320, 440], "parameters": {"color": 7, "width": 186.9444130878394, "height": 103.99685726445023, "content": "### Serve form to user\nCaptures `Prompt to flux` and `Style` from user."}, "typeVersion": 1}, {"id": "310f6c63-9441-4332-82dc-09b56e4f625a", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [-280, 660], "webhookId": "a35eb005-f795-4c85-9d00-0fe9797cb509", "parameters": {"path": "flux4free", "options": {}, "formTitle": "flux.schnell image generator", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Prompt to flux", "placeholder": " An astronaut riding a horse in 35mm style", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Style", "fieldOptions": {"values": [{"option": "Hyper-Surreal Escape"}, {"option": "Neon Fauvism"}, {"option": "Post-Analog Glitchscape"}, {"option": "AI Dystopia"}, {"option": "Vivid Pop Explosion"}]}}]}, "responseMode": "responseNode", "formDescription": "No ads, no BS. Uses hugginface inference API."}, "typeVersion": 2.1}, {"id": "ad10a84f-851a-40f8-b10e-18356c4eeed6", "name": "Call hugginface inference api", "type": "n8n-nodes-base.httpRequest", "notes": " ", "onError": "continueErrorOutput", "position": [740, 660], "parameters": {"url": "https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-schnell", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "inputs", "value": "=Depict {{ $json['Prompt to flux'] }}\n\nStyle: {{ $json.stylePrompt }}"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "r98SNEAnA5arilQO", "name": "huggingface-nathan"}}, "notesInFlow": true, "typeVersion": 4.2}, {"id": "e740dd3c-e23e-485b-bb4c-bb0515897a08", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-880, 600], "parameters": {"color": 7, "width": 506.8102696237577, "height": 337.24177957113216, "content": "### Watch Set Up Video 👇\n[![Flux Generator](https://uploads.n8n.io/devrel/fluxgenerator.png#full-width)](https://youtu.be/Rv_1jt5WvtY)\n\n"}, "typeVersion": 1}, {"id": "71d01821-3e0d-4c08-8571-58a158817e2c", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-880, 440], "parameters": {"color": 7, "width": 506.8102696237577, "height": 134.27496896630808, "content": "# flux image generator\nBuilt by [@maxtkacz](https://x.com/maxtkacz) as part of the [30 Day AI Sprint](https://30dayaisprint.notion.site/)\nCheck out the project's [Notion page](https://30dayaisprint.notion.site/Flux-image-generator-bc94a8d2de8447c6ab70aacf2c4179f2) for more details"}, "typeVersion": 1}, {"id": "0cc26680-ba63-464f-ba84-68c2616f95e2", "name": "Route by style", "type": "n8n-nodes-base.switch", "position": [0, 640], "parameters": {"rules": {"values": [{"outputKey": "Hyper-Surreal Escape", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.Style }}", "rightValue": "Hyper-Surreal Escape"}]}, "renameOutput": true}, {"outputKey": "Post-Analog Glitchscape", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "106969fa-994c-4b1e-b693-fc0b48ce5f3d", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Style }}", "rightValue": "Post-Analog Glitchscape"}]}, "renameOutput": true}, {"outputKey": "AI Dystopia", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "24318e7d-4dc1-4369-b045-bb7d0a484def", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Style }}", "rightValue": "AI Dystopia"}]}, "renameOutput": true}, {"outputKey": "Neon Fauvism", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a80911ff-67fc-416d-b135-0401c336d6d8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Style }}", "rightValue": "Neon Fauvism"}]}, "renameOutput": true}, {"outputKey": "Vivid Pop Explosion", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7fdeec28-194e-415e-8da2-8bac90e4c011", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Style }}", "rightValue": "Vivid Pop Explosion"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.1}], "pinData": {}, "connections": {"None": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "AI Dystopia": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "Neon Fauvism": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "Route by style": {"main": [[{"node": "Hyper-Surreal Escape", "type": "main", "index": 0}], [{"node": "Post-Analog Glitchscape", "type": "main", "index": 0}], [{"node": "AI Dystopia", "type": "main", "index": 0}], [{"node": "Neon Fauvism", "type": "main", "index": 0}], [{"node": "Vivid Pop Explosion", "type": "main", "index": 0}], [{"node": "None", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Route by style", "type": "main", "index": 0}]]}, "Upload image to S3": {"main": [[{"node": "Serve webpage", "type": "main", "index": 0}], [{"node": "Respond with error", "type": "main", "index": 0}]]}, "Vivid Pop Explosion": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "Hyper-Surreal Escape": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "Post-Analog Glitchscape": {"main": [[{"node": "Call hugginface inference api", "type": "main", "index": 0}]]}, "Call hugginface inference api": {"main": [[{"node": "Upload image to S3", "type": "main", "index": 0}], [{"node": "Respond with error", "type": "main", "index": 0}]]}}}