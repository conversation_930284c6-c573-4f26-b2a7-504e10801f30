{"id": "sczRNO4u1HYc5YV7", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Extract & Summarize Wikipedia Data with Bright Data and Gemini AI", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "0f4b4939-6356-4672-ae61-8d1daf66a168", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, -440], "parameters": {}, "typeVersion": 1}, {"id": "167e060a-c36c-462a-826c-81ef379c824b", "name": "Google Gemini Chat Model For Summarization", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1520, -60], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "a51f2634-8b59-4feb-be39-674e8f198714", "name": "Google Gemini Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1000, -240], "parameters": {"options": {}, "modelName": "models/gemini-2.0-pro-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "a1ec001f-6e97-4efb-91d9-9a037fbf472c", "name": "Summary Webhook Notifier", "type": "n8n-nodes-base.httpRequest", "position": [1860, -280], "parameters": {"url": "https://webhook.site/ce41e056-c097-48c8-a096-9b876d3abbf7", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}, {"id": "f4dd93b5-2a33-4ac7-a0c9-9e0956bea363", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [340, -820], "parameters": {"width": 400, "height": 300, "content": "## Note\n\nThis template deals with the Wikipedia data extraction and summarization of content with the Bright Data. \n\nThe LLM Data Extractor is responsible for producing a human readable content.\n\nThe Concise Summary Generator node is responsible for generating the concise summary of the Wikipedia extracted info.\n\n**Please make sure to update the Wikipedia URL with Bright Data Zone. Also make sure to set the Webhook Notification URL.**"}, "typeVersion": 1}, {"id": "9bd6f913-c526-4e54-81f8-8885a0fe974f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [780, -820], "parameters": {"width": 500, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used to demonstrate the data extraction and summarization aspects.\n\nBasic LLM Chain is being used for extracting the html to text\n\nSummarization Chain is being used for summarization of the Wikipedia data.\n\n**Note - Replace Google Gemini with the Open AI or suitable LLM providers of your choice.**"}, "typeVersion": 1}, {"id": "30008ce4-4de2-43c5-bb03-94db58262f86", "name": "Wikipedia Web Request", "type": "n8n-nodes-base.httpRequest", "position": [780, -440], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "format", "value": "raw"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "28656a7d-4bd8-41c8-8471-50d19d88e7f2", "name": "LLM Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1000, -440], "parameters": {"text": "={{ $json.data }}", "messages": {"messageValues": [{"message": "You are an expert Data Formatter. Make sure to format the data in a human readable manner. Please output the human readable content without your own thoughts"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "7045af3b-9e74-42ef-92f0-f8d3266f2890", "name": "Concise Summary Generator", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1440, -280], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Write a concise summary of the following:\n\n\n\"{text}\"\n"}}}, "chunkingMode": "advanced"}, "typeVersion": 2}, {"id": "0cc843c1-252a-4c18-9856-5c7dfc732072", "name": "Set Wikipedia URL with Bright Data Zone", "type": "n8n-nodes-base.set", "notes": "Set the URL which you are interested to scrap the data", "position": [560, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1c132dd6-31e4-453b-a8cf-cad9845fe55b", "name": "url", "type": "string", "value": "https://en.wikipedia.org/wiki/Cloud_computing?product=unlocker&method=api"}, {"id": "0fa387df-2511-4228-b6aa-237cceb3e9c7", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "6cb9930f-1924-4762-8150-f5cd0e063348", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [940, -500], "parameters": {"color": 4, "width": 380, "height": 420, "content": "## Basic LLM Chain Data Extractor\n"}, "typeVersion": 1}, {"id": "47811535-bce5-4946-aaa6-baef87db1100", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1400, -340], "parameters": {"color": 5, "width": 340, "height": 420, "content": "## Summarization Chain\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5b5e78fb-6e5a-4b92-838c-6c4060618e9c", "connections": {"LLM Data Extractor": {"main": [[{"node": "Concise Summary Generator", "type": "main", "index": 0}]]}, "Wikipedia Web Request": {"main": [[{"node": "LLM Data Extractor", "type": "main", "index": 0}]]}, "Concise Summary Generator": {"main": [[{"node": "Summary Webhook Notifier", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "LLM Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Wikipedia URL with Bright Data Zone", "type": "main", "index": 0}]]}, "Set Wikipedia URL with Bright Data Zone": {"main": [[{"node": "Wikipedia Web Request", "type": "main", "index": 0}]]}, "Google Gemini Chat Model For Summarization": {"ai_languageModel": [[{"node": "Concise Summary Generator", "type": "ai_languageModel", "index": 0}]]}}}