{"id": "ZpgJpdtmq6MM1jr2", "meta": {"instanceId": "df9ffe0ce66252bcc29753df3925c45bd5340ded4ecdfc4be9cdb17ed78e229b", "templateCredsSetupCompleted": true}, "name": "AI T-Shirt Redesign Workflow from any Mockup Image", "tags": [], "nodes": [{"id": "97ce19f8-d83b-481d-a5c4-8ed46a06f18d", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [360, -600], "parameters": {"url": "https://api.openai.com/v1/images/generations", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"gpt-image-1\",\n  \"prompt\": \"{{ $json.escapedString }}\",\n  \"n\": 1,\n  \"size\": \"1024x1536\",\n  \"quality\": \"high\"\n}", "sendBody": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "15P9TuEdDQwlWhIR", "name": "OpenAi account 2"}}, "typeVersion": 4.2}, {"id": "3ba73c97-c6d7-4275-8c8c-064a49762edb", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [780, -600], "parameters": {"options": {}, "operation": "toBinary", "sourceProperty": "data[0].b64_json"}, "typeVersion": 1.1}, {"id": "4b0c830c-caea-420c-b547-048ef795e542", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [560, -600], "parameters": {"options": {}, "fieldToSplitOut": "data[0].b64_json"}, "typeVersion": 1}, {"id": "d06e9bde-0fee-42dc-9c3d-004c97c1ee49", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-220, -600], "parameters": {"text": "={{ $json.content }}", "options": {"systemMessage": "You are a creative prompt generation assistant specialized in T-shirt artwork refinement.\nYour job is to analyze an existing T-shirt design user message above and create a new, upgraded version that preserves the original layout, overall structure, and message placement, but enhances its visual style, mood, and artistic quality.\n\n✦ Keep all key design elements and text in their original positions — do not remove or move important words or graphics.\n✦ Improve the typography by suggesting more expressive font styling (e.g., handwritten, retro, bold serif, clean sans-serif, brush script), and enhance the lettering arrangement to feel more dynamic, elegant, or visually balanced.\n✦ Enhance illustrative elements, texture, and background details to feel more artistic, emotional, or premium — without overwhelming the message.\n✦ Use descriptive, natural language to generate a final prompt that can be used with Midjourney, DALL·E, or other image-generation AIs.\n✦ The new version should feel like a refined and artistic redesign, not a complete concept change.\n✦ Solid black background\n\nRule:\n- Output the final design prompt as a single plain-text sentence, without markdown, formatting, or line breaks. Make sure the prompt is concise but expressive, suitable for use inside a JSON payload or passed into an image generation API. All key elements must remain: characters, objects, text styling, and background mood — but the format should be clean, compact, and system-friendly.\n- Format the output as a single line of plain text, using escaped double quotes (\\\") where needed, suitable for inclusion in a JSON string without formatting issues."}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "f54f401d-5fd3-482f-903d-322acabfcce4", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-420, -600], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "resource": "image", "imageUrls": "https://m.media-amazon.com/images/I/B1pppR4gVKL._CLa%7C2140%2C2000%7C91-OyNW80tL.png%7C0%2C0%2C2140%2C2000%2B0.0%2C0.0%2C2140.0%2C2000.0_AC_SX342_SY445_.png", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "l51tyBcX4FuEb6tX", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "b867eeda-8eea-4574-8537-a7130e8710c3", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-260, -380], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "15P9TuEdDQwlWhIR", "name": "OpenAi account 2"}}, "typeVersion": 1.2}, {"id": "8877fbdc-091b-4a1c-82cf-bf980a8c3045", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-1000, -560], "webhookId": "22b3dae3-95e5-4bfa-8187-9dca2dc72f85", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "90fe70c2-3b64-4d28-82a8-c575b26c8b5b", "name": "If", "type": "n8n-nodes-base.if", "position": [-700, -560], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "cb4e9a22-d429-4d11-b536-5d8760dd5042", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.chatInput }}", "rightValue": "https://"}]}}, "typeVersion": 2.2}, {"id": "00509d12-784c-4f9f-a5e4-fdccf5382d2e", "name": "Code", "type": "n8n-nodes-base.code", "position": [140, -600], "parameters": {"jsCode": "const rawContent = $json.output;\n\n// 1. Replace all line breaks with spaces\nlet cleaned = rawContent.replace(/\\n/g, ' ');\n\n// 2. Trim any extra spaces at the beginning and end\ncleaned = cleaned.trim();\n\n// 3. Escape backslashes and double quotes for JSON safety\nlet escaped = cleaned.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"');\n\n// 4. Remove leading or trailing escaped quotes if accidentally included\nescaped = escaped.replace(/^\\\\\\\"/, '').replace(/\\\\\\\"$/, '');\n\n// 5. Return the cleaned and fully escaped string\nreturn [\n  {\n    json: {\n      escapedString: escaped\n    }\n  }\n];\n"}, "typeVersion": 2}, {"id": "caec0c49-a46c-42a5-bb64-f6ba86490eef", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -640], "parameters": {"width": 280, "height": 260, "content": "## Send a mockup image url to chat"}, "typeVersion": 1}, {"id": "d0862a3b-7409-49a9-b68e-ff7046031885", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, -680], "parameters": {"color": 5, "width": 540, "height": 300, "content": "## Analyze image and generate new prompt"}, "typeVersion": 1}, {"id": "cea5c30b-154a-4c51-9b9a-e187c27224d7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [280, -680], "parameters": {"color": 3, "width": 680, "height": 300, "content": "## Generate the new Tshirt design"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "1a42d08d-cca5-4eab-a041-770d1a7da235", "connections": {"If": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}], []]}, "Code": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}}