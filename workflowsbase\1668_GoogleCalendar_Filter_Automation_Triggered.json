{"id": "slP122GjD9meGkS6", "meta": {"instanceId": "178ef8a5109fc76c716d40bcadb720c455319f7b7a3fd5a39e4f336a091f524a"}, "name": "Calendar_scheduling", "tags": [], "nodes": [{"id": "bd1dae81-daea-4539-bf1d-38eb9a2bd2f0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [500, 560], "parameters": {"filters": {"readStatus": "unread", "includeSpamTrash": false}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "kLFedNEM8Zwkergv", "name": "Gmail account"}}, "typeVersion": 1}, {"id": "a97c3ab1-6fbc-441e-af11-3c746936013b", "name": "Chat OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [720, 740], "parameters": {"model": "gpt-4", "options": {"temperature": 0.1}}, "credentials": {"openAiApi": {"id": "wJtZwsVKW5v6R2Iy", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "a1205598-7cd4-4278-ad53-0cfc7c7947ff", "name": "Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1580, 759], "parameters": {"name": "Calendar_Availability", "workflowId": "={{ $workflow.id }}", "description": "Call this tool to get my calendar availability as stringified JSON array."}, "typeVersion": 1}, {"id": "5ba2c2b0-2218-45d2-a417-f86c80643397", "name": "Chat OpenAI1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1420, 759], "parameters": {"model": "gpt-4", "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "wJtZwsVKW5v6R2Iy", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "012835ec-c20a-4b84-bed8-67f6aac30698", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"width": 616.*************, "height": 410.**************, "content": "## Check if incoming email is about appointment\nWe use LLM to check subject and body of the email and determine if it's an appointment request. "}, "typeVersion": 1}, {"id": "ceaa4f77-acc8-437e-9d61-16cf344a7748", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1340, 460], "parameters": {"width": 676.*************, "height": 241.**************, "content": "## Get calendar availability and compose a response\nMake sure to update the Workflow ID if you are running this as 2 workflows"}, "typeVersion": 1}, {"id": "499def23-7dec-4131-91fd-326b1b824762", "name": "Google Calendar", "type": "n8n-nodes-base.googleCalendar", "position": [680, 1120], "parameters": {"options": {"timeMax": "={{ $now.plus(1, 'month').toISO() }}", "timeMin": "={{ $now.minus(1, 'day').toISO() }}", "singleEvents": true}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll", "returnAll": true}, "credentials": {"googleCalendarOAuth2Api": {"id": "s95HsHIMB7oK0dAH", "name": "Google Calendar account"}}, "typeVersion": 1}, {"id": "0f5f43fa-3386-4682-b620-21db35651d3b", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [460, 1120], "parameters": {}, "typeVersion": 1}, {"id": "8b2b82b9-c11f-4e7f-ab23-16ea5e395e11", "name": "Format response", "type": "n8n-nodes-base.itemLists", "position": [1560, 1120], "parameters": {"include": "allFieldsExcept", "options": {}, "aggregate": "aggregateAllItemData", "operation": "concatenateItems", "fieldsToExclude": "sort", "destinationFieldName": "response"}, "typeVersion": 3}, {"id": "ac363d85-5c6e-4a9f-9cfc-ecc15a325b01", "name": "Stringify Response", "type": "n8n-nodes-base.set", "position": [1780, 1120], "parameters": {"values": {"string": [{"name": "response", "value": "={{ JSON.stringify($json.response) }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "399c5bc4-c8bd-4d0b-942a-9889447880a9", "name": "Extract start, end and name", "type": "n8n-nodes-base.set", "position": [1100, 1120], "parameters": {"values": {"string": [{"name": "start", "value": "={{ DateTime.fromISO($json.start.dateTime).toLocaleString(DateTime.DATE_HUGE) }}, {{ DateTime.fromISO($json.start.dateTime).toLocaleString(DateTime.TIME_24_WITH_SHORT_OFFSET) }}"}, {"name": "end", "value": "={{ DateTime.fromISO($json.end.dateTime).toLocaleString(DateTime.DATE_HUGE) }}, {{ DateTime.fromISO($json.end.dateTime).toLocaleString(DateTime.TIME_24_WITH_SHORT_OFFSET) }}"}, {"name": "name", "value": "={{ $json.summary }}"}, {"name": "sort", "value": "={{ $json.start.dateTime }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 2}, {"id": "a39b6c7d-fdcc-452d-9ef5-50b038153330", "name": "<PERSON><PERSON> only confirmed and with set time", "type": "n8n-nodes-base.filter", "position": [880, 1120], "parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "value2": "confirmed"}], "boolean": [{"value1": "={{ $json.start.dateTime }}", "value2": "={{ undefined }}", "operation": "notEqual"}]}}, "typeVersion": 1}, {"id": "0e0a2be9-cde7-497d-94c5-180128382bb7", "name": "Is appointment request", "type": "n8n-nodes-base.if", "position": [1100, 560], "parameters": {"conditions": {"string": [{"value1": "={{ $json.is_appointment }}", "value2": "true"}], "boolean": [{"value1": "={{ $json.is_appointment }}", "value2": true}]}, "combineOperation": "any"}, "typeVersion": 1}, {"id": "a6e11f63-a56a-4fe0-91c8-0dde2720e905", "name": "Classify appointment", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [720, 560], "parameters": {"prompt": "=Please evaluate the following email to determine if it suggests scheduling a meeting or a call:\nSubject: {{ encodeURI($json.Subject) }}\nSnippet: {{ encodeURI($json.snippet) }}\nIndicate your assessment by responding with \"true\" if it suggests a meeting or call, or \"false\" otherwise. Use lowercase for your response.\n"}, "typeVersion": 1}, {"id": "b6411b14-67f6-4195-a834-60a4dc5e4851", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [880, 740], "parameters": {"jsonSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"is_appointment\": {\n \"type\": \"boolean\"\n }\n }\n}"}, "typeVersion": 1}, {"id": "96248431-290b-4fb1-94a3-714e7c0008d4", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [640, 1058.6115582634225], "parameters": {"width": 810.4923211935056, "height": 224.60561166142082, "content": "### Get all query google events for the next month and extract relevant data"}, "typeVersion": 1}, {"id": "48bc7c0c-0b74-418e-8c5c-6a6faf24722c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1513, 1060], "parameters": {"width": 444.4130232558142, "height": 220.42397542781927, "content": "### Wrap the result in `response` object and return "}, "typeVersion": 1}, {"id": "a68f7b27-1891-46c7-92b2-650cc17f94d6", "name": "Sort", "type": "n8n-nodes-base.itemLists", "position": [1320, 1120], "parameters": {"options": {}, "operation": "sort", "sortFieldsUi": {"sortField": [{"fieldName": "sort"}]}}, "typeVersion": 3}, {"id": "2b5b5855-6d3f-4405-9f48-5d6c4ee2475b", "name": "<PERSON> as read", "type": "n8n-nodes-base.gmail", "position": [1840, 739], "parameters": {"messageId": "={{ $('Gmail Trigger').item.json.id }}", "operation": "mark<PERSON><PERSON><PERSON>"}, "credentials": {"gmailOAuth2": {"id": "kLFedNEM8Zwkergv", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "accbe2df-367a-4bd3-a383-12ee79062e12", "name": "Send Reply", "type": "n8n-nodes-base.gmail", "position": [1840, 539], "parameters": {"message": "={{ $json.output }}", "options": {"replyToSenderOnly": true}, "messageId": "={{ $('Gmail Trigger').item.json.id }}", "operation": "reply"}, "credentials": {"gmailOAuth2": {"id": "kLFedNEM8Zwkergv", "name": "Gmail account"}}, "typeVersion": 2}, {"id": "66d62337-d0c1-4744-b169-8e95c1d1492a", "name": "Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1400, 539], "parameters": {"text": "=Sender: {{ $('Gmail Trigger').item.json.From }}\\nSubject: {{ $('Gmail Trigger').item.json.Subject }}\\nEmail Text: {{ $('Gmail Trigger').item.json.snippet }}", "options": {"systemMessage": "=You are an email scheduling assistant. Based on the received email, check my availability and propose an appropriate response. \nAim to get a specific time, rather than just a day. When checking my availability, make sure that there's enough time in between meetings.\nIf I'm not available, ALWAYS propose a new time based on my availability. When proposing a new time, always leave 15 minutes buffer from previous meeting.\nToday date and time is: {{ $now.toISO() }}."}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0cf0768b-ddc0-42a3-9c84-f93d43c66dc7", "connections": {"Sort": {"main": [[{"node": "Format response", "type": "main", "index": 0}]]}, "Agent": {"main": [[{"node": "Send Reply", "type": "main", "index": 0}, {"node": "<PERSON> as read", "type": "main", "index": 0}]]}, "Chat OpenAI": {"ai_languageModel": [[{"node": "Classify appointment", "type": "ai_languageModel", "index": 0}]]}, "Chat OpenAI1": {"ai_languageModel": [[{"node": "Agent", "type": "ai_languageModel", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Classify appointment", "type": "main", "index": 0}]]}, "Workflow Tool": {"ai_tool": [[{"node": "Agent", "type": "ai_tool", "index": 0}]]}, "Format response": {"main": [[{"node": "Stringify Response", "type": "main", "index": 0}]]}, "Google Calendar": {"main": [[{"node": "<PERSON><PERSON> only confirmed and with set time", "type": "main", "index": 0}]]}, "Classify appointment": {"main": [[{"node": "Is appointment request", "type": "main", "index": 0}]]}, "Is appointment request": {"main": [[{"node": "Agent", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Google Calendar", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Classify appointment", "type": "ai_outputParser", "index": 0}]]}, "Extract start, end and name": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Filter only confirmed and with set time": {"main": [[{"node": "Extract start, end and name", "type": "main", "index": 0}]]}}}