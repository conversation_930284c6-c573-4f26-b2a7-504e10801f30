{"id": "QaMO9ji6T6vTZHQ4", "meta": {"instanceId": "8029058e18ae4ed6081000c1270d96039ad05959052aa2034dd96a215849bcf7"}, "name": "Gmail MCP Server", "tags": [{"id": "mce0brNtJ0q1uqig", "name": "Agent <PERSON>", "createdAt": "2025-02-25T18:11:08.555Z", "updatedAt": "2025-02-25T18:11:08.555Z"}, {"id": "Yt5ECnELP8JYcw9w", "name": "Gmail", "createdAt": "2025-04-18T01:59:21.577Z", "updatedAt": "2025-04-18T01:59:21.577Z"}], "nodes": [{"id": "b7c0a52d-cd86-43a6-9b53-acf7d24bfccc", "name": "addLabels", "type": "n8n-nodes-base.gmailTool", "position": [560, 800], "webhookId": "81d61988-8213-4175-b75d-76cb67ce4a3b", "parameters": {"labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "addLabels", "descriptionType": "manual", "toolDescription": "Add one or more labels to an email message. AI-configurable parameters: Message_ID (string) - the ID of the message to label; Label_Names_or_IDs (string) - comma-separated label names or IDs to apply."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "21f26146-97e4-4643-9bf2-0d704ec589e8", "name": "delete", "type": "n8n-nodes-base.gmailTool", "position": [280, 600], "webhookId": "03319c28-ef88-40f4-897c-f44c21dbdf1f", "parameters": {"messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "delete", "descriptionType": "manual", "toolDescription": "Delete an email message. AI-configurable parameters: Message_ID (string) - the ID of the message to delete."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "fd868497-787c-460b-87dc-e99572465c89", "name": "get", "type": "n8n-nodes-base.gmailTool", "position": [400, 600], "webhookId": "cf5acbf3-a08f-4da6-9f14-9751eed6e5b8", "parameters": {"messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "get", "descriptionType": "manual", "toolDescription": "Retrieve details of an email message. AI-configurable parameters: Message_ID (string) - the ID of the message to retrieve."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "43f6229f-c294-41ce-8f4b-ebcab0026730", "name": "search", "type": "n8n-nodes-base.gmailTool", "position": [520, 600], "webhookId": "cb3d028a-6cab-4946-b368-aa56bf271af9", "parameters": {"filters": {"q": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search', ``, 'string') }}", "sender": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Sender', ``, 'string') }}", "receivedAfter": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_After', ``, 'string') }}", "receivedBefore": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_Before', ``, 'string') }}"}, "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "descriptionType": "manual", "toolDescription": "Retrieve multiple email messages based on filters. AI-configurable parameters: Return_All (boolean) - whether to return all matching messages; Search (string) - Gmail query string to filter messages; Received_After (string) - datetime (RFC3339) after which messages are received; Received_Before (string) - datetime before which messages are received; Sender (string) - email address of the sender."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "f01ba35c-a67f-4603-afb2-9990bd73a026", "name": "mark<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [120, 800], "webhookId": "e769b7cf-9622-434d-b98d-4bde7653238d", "parameters": {"messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "mark<PERSON><PERSON><PERSON>", "descriptionType": "manual", "toolDescription": "Mark an email message as read. AI-configurable parameters: Message_ID (string) - the ID of the message to mark as read."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "c8e77334-a50a-4117-beec-f8101d879e9e", "name": "mark<PERSON><PERSON>n<PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [280, 800], "webhookId": "c26a8635-4329-498e-b293-4350baed493d", "parameters": {"messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "mark<PERSON><PERSON>n<PERSON>", "descriptionType": "manual", "toolDescription": "Mark an email message as unread. AI-configurable parameters: Message_ID (string) - the ID of the message to mark as unread."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "ac7339b7-e246-4ad8-a82c-f3abc6b87942", "name": "reply", "type": "n8n-nodes-base.gmailTool", "position": [140, 600], "webhookId": "fbd30b84-25ac-4bab-8a66-5366b9b7a0be", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"ccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('CC', ``, 'string') }}", "bccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('BCC', ``, 'string') }}", "attachmentsUi": {"attachmentsBinary": [{"property": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Attachment_Field_Name', ``, 'string') }}"}]}, "appendAttribution": false}, "emailType": "text", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "reply", "descriptionType": "manual", "toolDescription": "Reply to an email message. AI-configurable parameters: Message_ID (string) - the ID of the message; Message (string) - the reply content; Attachment_Field_Name (string) - input field name containing attachments; BCC (string) - comma-separated BCC recipients; CC (string) - comma-separated CC recipients."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "fd87d9a3-5823-402a-9d9e-0c114a556f8a", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [420, 800], "webhookId": "e83fb7ee-2716-444b-9a4e-208eea215728", "parameters": {"labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "<PERSON><PERSON><PERSON><PERSON>", "descriptionType": "manual", "toolDescription": "Remove one or more labels from an email message. AI-configurable parameters: Message_ID (string) - the ID of the message; Label_Names_or_IDs (string) - comma-separated label names or IDs to remove."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "a36630c8-3b6a-4703-94fa-80747eb4931c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [40, 520], "parameters": {"width": 660, "height": 460, "content": "## Message Tools\n"}, "typeVersion": 1}, {"id": "b5c7fdd7-9842-4720-b13e-1fa3611fc320", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [840, 620], "webhookId": "1f107973-fe4a-440c-aaef-f35e1e8a555a", "parameters": {"resource": "label", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "descriptionType": "manual", "toolDescription": "Retrieve a list of labels. AI-configurable parameters: Return_All (boolean) - whether to return all labels."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "18daa9a3-9e1a-4b4b-ad8d-bf35402baaa6", "name": "get<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [980, 620], "webhookId": "e9d3b2c0-50ea-4b3b-8509-f89dc4f20fb5", "parameters": {"labelId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_ID', ``, 'string') }}", "resource": "label", "operation": "get", "descriptionType": "manual", "toolDescription": "Retrieve details of a specific label. AI-configurable parameters: Label_ID (string) - the ID of the label to retrieve."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "cc7ba925-83c9-4870-9647-11042666fd5b", "name": "deleteLabel", "type": "n8n-nodes-base.gmailTool", "position": [840, 820], "webhookId": "80a61a7c-f7a0-4fc9-a0a8-edd5846b4e11", "parameters": {"labelId": "={{ $fromAI('Label_ID', ``, 'string') }}", "resource": "label", "operation": "delete", "descriptionType": "manual", "toolDescription": "Delete a label. AI-configurable parameters: Label_ID (string) - the ID of the label to delete."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "23b28b37-cc69-4bc9-b0e4-88b09b355f3e", "name": "createLabel", "type": "n8n-nodes-base.gmailTool", "position": [1000, 820], "webhookId": "d24d1672-4f76-4f58-912b-9345d23ba922", "parameters": {"name": "={{ $fromAI('Label_ID', ``, 'string') }}", "options": {}, "resource": "label", "operation": "create", "descriptionType": "manual", "toolDescription": "Create a new label. AI-configurable parameters: Label_ID (string) - the name of the label to create."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "db6f3147-e672-497b-922e-cb8c74dd3006", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [760, 520], "parameters": {"color": 4, "width": 380, "height": 440, "content": "## Label Tools\n\n"}, "typeVersion": 1}, {"id": "16d28e54-ac27-462e-9316-efe2959dd48c", "name": "deleteDraft", "type": "n8n-nodes-base.gmailTool", "position": [1300, 280], "webhookId": "8eb35ae4-6517-421b-b54f-ba0610cf58f4", "parameters": {"resource": "draft", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Draft_ID', ``, 'string') }}", "operation": "delete", "descriptionType": "manual", "toolDescription": "Delete an email draft. AI-configurable parameters: Draft_ID (string) - the ID of the draft to delete."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "cca355a2-2a90-4084-a65f-5a67b7732192", "name": "createDraft", "type": "n8n-nodes-base.gmailTool", "position": [1300, 100], "webhookId": "1cca6c42-ccd9-4144-a2b1-6266d848f6ab", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"ccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('CC', ``, 'string') }}", "bccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('BCC', ``, 'string') }}", "attachmentsUi": {"attachmentsBinary": [{"property": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Attachment_Field_Name__in_Input_', ``, 'string') }}"}]}}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "resource": "draft", "descriptionType": "manual", "toolDescription": "Create an email draft. AI-configurable parameters: Subject (string) - the subject of the draft; Message (string) - the body of the draft; Attachment_Field_Name__in_Input_ (string) - input field name containing attachments; BCC (string) - comma-separated BCC recipients; CC (string) - comma-separated CC recipients."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "5c22063a-2480-4a57-9184-7cf26ff07caa", "name": "getDraft", "type": "n8n-nodes-base.gmailTool", "position": [1480, 100], "webhookId": "80eadc8e-9d6b-42e7-9ac4-5b26d21fb3c5", "parameters": {"options": {}, "resource": "draft", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Draft_ID', ``, 'string') }}", "operation": "get", "descriptionType": "manual", "toolDescription": "Retrieve an email draft. AI-configurable parameters: Draft_ID (string) - the ID of the draft to retrieve."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "fba8022d-9b11-4bb6-b8c2-826e1fa9a8e6", "name": "getManyDrafts", "type": "n8n-nodes-base.gmailTool", "position": [1480, 280], "webhookId": "6aaf2777-d1c1-490b-a82f-eaab6caefe85", "parameters": {"options": {"includeSpamTrash": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Include_Spam_and_Trash', ``, 'boolean') }}"}, "resource": "draft", "operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "descriptionType": "manual", "toolDescription": "Retrieve multiple email drafts. AI-configurable parameters: Return_All (boolean) - whether to return all drafts; Include_Spam_and_Trash (boolean) - whether to include drafts in spam or trash."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "af313dbf-f1d3-44b8-86b0-a8d8deb44359", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1220, 0], "parameters": {"color": 5, "width": 380, "height": 440, "content": "## Draft Tools\n\n\n"}, "typeVersion": 1}, {"id": "34fc23f5-8b5e-4dfb-b7bf-5eca839a1799", "name": "getManyThreads", "type": "n8n-nodes-base.gmailTool", "position": [1260, 620], "webhookId": "233fb55f-2575-4cbd-a327-e27858e98cd9", "parameters": {"filters": {"q": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search', ``, 'string') }}", "receivedAfter": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_After', ``, 'string') }}", "receivedBefore": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_Before', ``, 'string') }}"}, "resource": "thread", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "descriptionType": "manual", "toolDescription": "Retrieve multiple email threads based on filters. AI-configurable parameters: Return_All (boolean) - whether to return all threads; Search (string) - Gmail query string to filter threads; Received_After (string) - datetime after which threads are received; Received_Before (string) - datetime before which threads are received."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "5803ff85-b894-4d9d-bcca-4877d3255dbd", "name": "getThread", "type": "n8n-nodes-base.gmailTool", "position": [1420, 620], "webhookId": "9ecfaf0c-8d43-4b46-86bb-de5117b657c1", "parameters": {"options": {"returnOnlyMessages": true}, "resource": "thread", "threadId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Thread_ID', ``, 'string') }}", "operation": "get", "descriptionType": "manual", "toolDescription": "Retrieve details of an email thread. AI-configurable parameters: Thread_ID (string) - the ID of the thread to retrieve."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "07547fdc-3524-45cf-89c1-d871008e5897", "name": "addLabelThread", "type": "n8n-nodes-base.gmailTool", "position": [1580, 620], "webhookId": "c7a99e26-cb22-4675-b5a8-fb7acd302983", "parameters": {"labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "resource": "thread", "threadId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Thread_ID', ``, 'string') }}", "operation": "addLabels", "descriptionType": "manual", "toolDescription": "Add one or more labels to an email thread. AI-configurable parameters: Thread_ID (string) - the ID of the thread; Label_Names_or_IDs (string) - comma-separated label names or IDs to apply."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "2214607d-2ac2-4885-98b7-0c424f3c4af7", "name": "removeLabelThread", "type": "n8n-nodes-base.gmailTool", "position": [1260, 800], "webhookId": "cb63a038-73ba-4488-b70e-e3b8c48ee1b6", "parameters": {"labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "resource": "thread", "threadId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Thread_ID', ``, 'string') }}", "operation": "<PERSON><PERSON><PERSON><PERSON>", "descriptionType": "manual", "toolDescription": "Remove one or more labels from an email thread. AI-configurable parameters: Thread_ID (string) - the ID of the thread; Label_Names_or_IDs (string) - comma-separated label names or IDs to remove."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "ed15784b-58e1-40c0-8c87-1d0667802188", "name": "replyThread", "type": "n8n-nodes-base.gmailTool", "position": [1420, 800], "webhookId": "b10a9bfd-eca1-40fd-817e-3ab1caf94d97", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"ccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('CC', ``, 'string') }}", "bccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('BCC', ``, 'string') }}"}, "resource": "thread", "threadId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Thread_ID', ``, 'string') }}", "operation": "reply", "descriptionType": "manual", "toolDescription": "Reply to an email thread. AI-configurable parameters: Thread_ID (string) - the ID of the thread; Message (string) - the reply content; BCC (string) - comma-separated BCC recipients; CC (string) - comma-separated CC recipients."}, "credentials": {"gmailOAuth2": {"id": "67JzzUiB1dTa4vYU", "name": "iSJC Gmail"}}, "typeVersion": 2.1}, {"id": "2f8ea31e-3582-4370-8756-3673a60fbe53", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1220, 520], "parameters": {"color": 7, "width": 520, "height": 440, "content": "## <PERSON><PERSON><PERSON>\n\n\n"}, "typeVersion": 1}, {"id": "5beba186-3cf1-4d96-aa1a-69c3e0b729e5", "name": "Gmail MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [500, 40], "webhookId": "a794310b-bca0-4272-99be-a2872c1cadb0", "parameters": {"path": "gmail-enhanced"}, "typeVersion": 1}, {"id": "25736cc4-06ac-4084-9aec-543ba3d2934b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 6, "width": 280, "height": 240, "content": "## USAGE\n\nOpen the Gmail MCP Server node to obtain the SSE server URL.\n\nUse that to configure an N8N AI Agent flow or other AI tool."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "29e40df2-6863-4f37-8068-5dba71c5bac8", "connections": {"get": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "reply": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "delete": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "search": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getDraft": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getLabel": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "addLabels": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getLabels": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getThread": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "markAsRead": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "createDraft": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "createLabel": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "deleteDraft": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "deleteLabel": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "replyThread": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "markAsUnread": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "removeLabels": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getManyDrafts": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "addLabelThread": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "getManyThreads": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}, "removeLabelThread": {"ai_tool": [[{"node": "Gmail MCP Server", "type": "ai_tool", "index": 0}]]}}}