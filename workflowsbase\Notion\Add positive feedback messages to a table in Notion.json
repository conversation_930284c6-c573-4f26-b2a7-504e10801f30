{"nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [0, 400], "webhookId": "ad8a87ef-d293-4e48-8d36-838d69ebce0f", "parameters": {"formId": "fBYjtY5e"}, "credentials": {"typeformApi": ""}, "typeVersion": 1}, {"name": "Google Cloud Natural Language", "type": "n8n-nodes-base.googleCloudNaturalLanguage", "position": [200, 400], "parameters": {"content": "={{$json[\"Any suggestions for us? \"]}}", "options": {}}, "credentials": {"googleCloudNaturalLanguageOAuth2Api": ""}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [400, 400], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Google Cloud Natural Language\"].json[\"documentSentiment\"][\"score\"]}}", "operation": "larger"}]}}, "typeVersion": 1}, {"name": "Notion", "type": "n8n-nodes-base.notion", "position": [600, 300], "parameters": {"resource": "databasePage", "databaseId": "b7d1130a-3756-4bb3-aa56-0c77bf416437", "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{$node[\"Typeform Trigger\"].json[\"Name\"]}}"}, {"key": "Feedback|rich_text", "textContent": "={{$node[\"Typeform Trigger\"].json[\"Any suggestions for us? \"]}}"}]}}, "credentials": {"notionApi": ""}, "typeVersion": 1}, {"name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [800, 300], "parameters": {"channel": "general", "blocksUi": {"blocksValues": []}, "attachments": [{"text": "={{$node[\"Typeform Trigger\"].json[\"Any suggestions for us? \"]}}", "title": "={{$node[\"Typeform Trigger\"].json[\"Name\"]}} {{$node[\"Google Cloud Natural Language\"].json[\"documentSentiment\"][\"score\"]}}"}], "otherOptions": {}}, "credentials": {"slackApi": ""}, "typeVersion": 1}, {"name": "Trello", "type": "n8n-nodes-base.trello", "position": [600, 500], "parameters": {"name": "=Score: {{$json[\"documentSentiment\"][\"score\"]}}", "listId": "5fbb9e2eb1d5cc0a8a7ab8ac", "description": "=Score: {{$json[\"documentSentiment\"][\"score\"]}}\nFeedback: {{$node[\"Typeform Trigger\"].json[\"Any suggestions for us? \"]}}\nUser: {{$node[\"Typeform Trigger\"].json[\"Name\"]}}", "additionalFields": {}}, "credentials": {"trelloApi": ""}, "typeVersion": 1}], "connections": {"IF": {"main": [[{"node": "Notion", "type": "main", "index": 0}], [{"node": "Trello", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Google Cloud Natural Language", "type": "main", "index": 0}]]}, "Google Cloud Natural Language": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}