{"id": "gUx6hY0bOoReluxE", "meta": {"instanceId": "5ce52989094be90be3b3bdd9ed9cee1d7ce1fcecaa598afaec4a50646d32e291", "templateCredsSetupCompleted": true}, "name": "Supabase Setup Postgres", "tags": [{"id": "fSDcaaN3w5sV5e3S", "name": "Templates", "createdAt": "2025-02-23T15:20:47.262Z", "updatedAt": "2025-02-23T15:20:47.262Z"}], "nodes": [{"id": "c2c95cc1-d10e-40c9-9663-625e8a2ab30b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [340, -80], "parameters": {}, "typeVersion": 1}, {"id": "30a4ae0f-c7ae-4b00-b826-a0a2759f2dd5", "name": "Set sample Input Variables", "type": "n8n-nodes-base.set", "position": [600, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ed7bc826-fd48-4c9e-8ba7-11e4e7bb73ac", "name": "session_id", "type": "string", "value": "=491634502879"}, {"id": "d381c930-a92f-404f-ac91-ad6437d6b0c9", "name": "name", "type": "string", "value": "=<PERSON><PERSON>"}, {"id": "4ead1fb5-098b-4cbc-bc78-d65b188ca5b0", "name": "chatInput", "type": "string", "value": "=wie gehts dir?"}]}}, "typeVersion": 3.4}, {"id": "f56b629c-5374-43ce-b55b-efd7f14f1231", "name": "GeminiFlash2.0", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [840, 140], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "credentials": {"googlePalmApi": {"id": "clmB8ZYJMHaHmnsu", "name": "Stardawn#1"}}, "typeVersion": 1}, {"id": "1da22e93-504e-4616-bac3-dabd9a4b145a", "name": "Supabase Postgres Database", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [1100, 140], "parameters": {"tableName": "whatsapp_messages3", "sessionKey": "={{ $json.session_id }}", "sessionIdType": "customKey", "contextWindowLength": 20}, "credentials": {"postgres": {"id": "B2m18ScvYBKPNF9s", "name": "Supabase SD - N8N Demo Chatbot"}}, "typeVersion": 1.3}, {"id": "29a7eb84-2244-41e1-99c0-5daaeb80cf6e", "name": "Update additonal Values e.g. Name, Address ...", "type": "n8n-nodes-base.supabase", "position": [1300, -80], "parameters": {"filters": {"conditions": [{"keyName": "session_id", "keyValue": "={{ $('Set sample Input Variables').item.json.session_id }}", "condition": "eq"}, {"keyName": "name", "keyValue": "NULL", "condition": "is"}]}, "tableId": "whatsapp_messages3", "fieldsUi": {"fieldValues": [{"fieldId": "name", "fieldValue": "={{ $('Set sample Input Variables').item.json.name }}"}]}, "matchType": "allFilters", "operation": "update"}, "credentials": {"supabaseApi": {"id": "GHuUG6pmPATBHgob", "name": "N8N Chatbot"}}, "typeVersion": 1}, {"id": "8094fdd7-f238-47dc-94f9-5e962d5f0c2f", "name": "Sample Agent ", "type": "@n8n/n8n-nodes-langchain.agent", "position": [960, -80], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a helpful assistant"}, "promptType": "define"}, "typeVersion": 1.7}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "49fd22da-2875-49be-a3c0-6c0fcf378a8e", "connections": {"Sample Agent ": {"main": [[{"node": "Update additonal Values e.g. Name, Address ...", "type": "main", "index": 0}]]}, "GeminiFlash2.0": {"ai_languageModel": [[{"node": "Sample Agent ", "type": "ai_languageModel", "index": 0}]]}, "Set sample Input Variables": {"main": [[{"node": "Sample Agent ", "type": "main", "index": 0}]]}, "Supabase Postgres Database": {"ai_memory": [[{"node": "Sample Agent ", "type": "ai_memory", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set sample Input Variables", "type": "main", "index": 0}]]}}}