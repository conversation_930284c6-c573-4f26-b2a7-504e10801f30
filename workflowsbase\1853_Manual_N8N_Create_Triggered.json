{"id": "fEJliGTxbsE0G8z2", "meta": {"instanceId": "c7e0ba199938cfb8dab96f39dc136474614055d46336311a365ac15728164ae1", "templateCredsSetupCompleted": true}, "name": "Create Google Creds", "tags": [], "nodes": [{"id": "92174182-12ab-4903-aa1c-d7a872fcadc0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "e78da252-0302-42d6-b067-aaeb75f4ee3b", "name": "Services", "type": "n8n-nodes-base.set", "position": [600, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "33c46c17-3b0d-43eb-a9c9-3d1b8a71728f", "name": "services", "type": "array", "value": "\n[\n  {\n    \"service\": \"googleDocsOAuth2Api\"\n  },\n  {\n    \"service\": \"googleSheetsOAuth2Api\"\n  },\n  {\n    \"service\": \"googleSlidesOAuth2Api\"\n  },\n  {\n    \"service\": \"googleDriveOAuth2Api\"\n  },\n  {\n    \"service\": \"gmailOAuth2\"\n  },\n  {\n    \"service\": \"googleCalendarOAuth2Api\"\n  },\n  {\n    \"service\": \"googleContactsOAuth2Api\"\n  }\n]"}]}}, "typeVersion": 3.4}, {"id": "1a387d21-b7a0-426b-bacb-6bcd5ec389a7", "name": "Google JSON", "type": "n8n-nodes-base.set", "notes": "Include the entire Google JSON file, which can be obtained either when creating the OAuth 2.0 credentials or afterward from the Credentials page.", "position": [180, 0], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "  {\n    \"web\": {\n      \"client_id\": \"\",\n      \"project_id\": \"\",\n      \"auth_uri\": \"\",\n      \"token_uri\": \"\",\n      \"auth_provider_x509_cert_url\": \"\",\n      \"client_secret\": \"\",\n      \"redirect_uris\": [\n        \"\"\n      ]\n    }\n  }"}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "07096943-ab80-4915-ab59-7e1166303f66", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [820, 0], "parameters": {"options": {}, "fieldToSplitOut": "services"}, "typeVersion": 1}, {"id": "a30743a5-f817-43d2-8b9c-df95b8bb2b81", "name": "Google Email", "type": "n8n-nodes-base.set", "notes": "Set to your email address.", "position": [380, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "18e7a65f-904a-47be-94b6-06c7167c2c62", "name": "Google Email", "type": "string", "value": "<EMAIL>"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "8a24e96a-b3c5-4073-abfc-8177671f7f6c", "name": "n8n Create Credentials", "type": "n8n-nodes-base.n8n", "position": [1100, 0], "parameters": {"data": "={\n \"clientId\":\"{{ $('Google JSON').item.json.web.client_id }}\",\n \"clientSecret\": \"{{ $('Google JSON').item.json.web.client_secret }}\"\n}", "name": "={{ $('Google Email').item.json['Google Email'] }} - {{ $json.service }}", "resource": "credential", "requestOptions": {}, "credentialTypeName": "={{ $json.service }}"}, "credentials": {"n8nApi": {"id": "XMAqFWjBVgTU7khS", "name": "n8n account"}}, "typeVersion": 1}, {"id": "55c3814c-e08f-4541-8229-a197fa0fd0ba", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-460, -460], "parameters": {"color": 6, "width": 360, "height": 520, "content": "## Create Google Creds\nI found manually creating credentials for multiple google accounts to be rather tedious, and if not named well hard to identify later.   \n\nThis will create credentials with the email address for all of the basic services. \n\n### Set the values of:\n* Google JSON\nInclude the entire Google JSON file, which can be obtained either when creating the OAuth 2.0 credentials or afterward from the Credentials page.\n\n* Google Email\nSet to your email address\n\n*n8n\nSet your API connection\n\n### Sign In\nYou still need to sign in to each credential that was created."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": -1}, "versionId": "8924dfed-07be-4f42-8665-d6f4b1dcbd58", "connections": {"Services": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "n8n Create Credentials", "type": "main", "index": 0}]]}, "Google JSON": {"main": [[{"node": "Google Email", "type": "main", "index": 0}]]}, "Google Email": {"main": [[{"node": "Services", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Google JSON", "type": "main", "index": 0}]]}}}