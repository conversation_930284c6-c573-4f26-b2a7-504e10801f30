{"id": "iFkGAiVn3yBlykIG", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "name": "Chinese Translator", "tags": [{"id": "IhTa6egt1w8uqn9Z", "name": "_ACTIVE", "createdAt": "2025-03-12T05:07:05.060Z", "updatedAt": "2025-03-12T05:07:05.060Z"}, {"id": "0xpEHcJjNRRRMtEj", "name": "lin", "createdAt": "2025-03-12T05:06:24.112Z", "updatedAt": "2025-03-12T05:06:24.112Z"}, {"id": "Q0IWVCdrzoxXDC7z", "name": "error_linlinmhee_line", "createdAt": "2025-03-12T06:37:16.225Z", "updatedAt": "2025-03-12T06:37:16.225Z"}, {"id": "U1ozjO3iXQZWUyfG", "name": "_Blueprint", "createdAt": "2025-03-12T06:24:40.268Z", "updatedAt": "2025-03-12T06:24:40.268Z"}], "nodes": [{"id": "3ebfb7f1-b655-405b-8bde-0219fa92d09c", "name": "Line Webhook", "type": "n8n-nodes-base.webhook", "position": [-260, -20], "webhookId": "b2b119e6-6de5-4684-9a51-4706a1c20caf", "parameters": {"path": "cn", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "63ae844f-dfc3-4e8f-97d0-c0ec4be7858f", "name": "Line Loading Animation", "type": "n8n-nodes-base.httpRequest", "position": [120, -20], "parameters": {"url": "https://api.line.me/v2/bot/chat/loading/start", "method": "POST", "options": {}, "jsonBody": "={\n    \"chatId\": \"{{ $json.body.events[0].source.userId }}\",\n    \"loadingSeconds\": 60\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "7e4cc2a0-958c-4111-909c-fba75a428d5e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-380, -100], "parameters": {"color": 4, "width": 360, "height": 560, "content": "**Webhook from Line**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nYou need to set-up this webhook at Line Manager or Line Developer Console\n\nYou'll need to copy Webhook URL from this node to put in Line Console\n\nAlso, don't forget to remove 'test' part when going for production\n\nhttps://developers.line.biz/en/docs/messaging-api/receiving-messages/\n"}, "typeVersion": 1}, {"id": "767827b2-fbca-4dbb-b392-749c25a56c93", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, -100], "parameters": {"color": 4, "width": 360, "height": 560, "content": "**Line Loading Animation**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThis node is to only give ... loading animation back in Line.\n\nIt seems stupid but it actually tells user that the workflow is running and you are not left waiting without hope\n\nTo authorize, you can fill in the Line Token in the node here, or you can you header authorization (shown at the 'reply message' node)\n\nhttps://developers.line.biz/en/docs/messaging-api/use-loading-indicator/"}, "typeVersion": 1}, {"id": "8cdafc15-3bf8-45e9-8081-901d5c5a7880", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1200, -540], "parameters": {"color": 2, "width": 360, "height": 420, "content": "**OpenRouter.ai**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThis is to call LLMs model at Openrouter.ai \n\nYou can use it to call ChatGPT, Lllama, Qwen, Deepseek, and much more with just standardized API call and top-up only once\n\nhttps://openrouter.ai/docs/quickstart"}, "typeVersion": 1}, {"id": "3e2f4acf-771c-4d55-a13e-b4c874136574", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1580, -540], "parameters": {"color": 4, "width": 360, "height": 420, "content": "**Line Reply**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThis node is to send reply message via Line\n\nhttps://developers.line.biz/en/docs/messaging-api/sending-messages/\n"}, "typeVersion": 1}, {"id": "b17eddaf-da3e-4e21-ab33-9e71f385d734", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-380, -200], "parameters": {"color": 5, "width": 780, "height": 80, "content": "## You can test this workflow by adding Line @405jtfqs"}, "typeVersion": 1}, {"id": "5ce9db0a-0c84-48df-828c-591d01a47bc8", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [500, -20], "parameters": {"rules": {"values": [{"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9f8075cf-8f3f-419f-ae0a-833ee29fc063", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "text"}]}, "renameOutput": true}, {"outputKey": "img", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b7770f5b-dfb5-4b7a-8dc1-4404337dbfde", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "image"}]}, "renameOutput": true}, {"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9faa9dd4-32ce-4287-b7e5-885a42a62e32", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Line Webhook').item.json.body.events[0].message.type }}", "rightValue": "audio"}]}, "renameOutput": true}, {"outputKey": "else", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f4dbfa6a-a7f8-4c32-a94d-da384f37c0d1", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": true, "rightValue": ""}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "30e52c17-5231-43df-8da7-e5eb20e88846", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [380, -100], "parameters": {"color": 5, "width": 360, "height": 560, "content": "**Router for Text, Image, Voice, and others\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "e580dcf4-ad46-4a2a-a881-51e6ae71a236", "name": "Get Image", "type": "n8n-nodes-base.httpRequest", "position": [840, -40], "parameters": {"url": "=https://api-data.line.me/v2/bot/message/{{ $('Line Webhook').item.json.body.events[0].message.id }}/content", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "b0efee4c-0904-4774-b962-aee11886e8c7", "name": "OpenRouter : qwen/qwen2.5-vl-72b-instruct:free", "type": "n8n-nodes-base.httpRequest", "position": [1320, 0], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"qwen/qwen2.5-vl-72b-instruct:free\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"please provide chinese character, pinyin and translation in english for all the text in the image\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"image_url\",\n          \"image_url\": {\n            \"url\": \"data:image/jpeg;base64,{{ $json.img_prompt }}\"\n          }\n        }\n      ]\n    }\n  ]\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "7Y8q0dS2Y1fcvzTl", "name": "OpenRouter.ai"}}, "typeVersion": 4.2}, {"id": "b7fc7675-f8d7-4e7e-bec3-f9c626ba1728", "name": "OpenRouter: qwen/qwen-2.5-72b-instruct:free", "type": "n8n-nodes-base.httpRequest", "position": [1320, -460], "parameters": {"url": "https://openrouter.ai/api/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"qwen/qwen-2.5-72b-instruct:free\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"please provide chinese character, pinyin and translation in english. if the input is in english, you will translate and also provide chinese character, pinyin and translation in english for each word\"\n    },\n     {\n      \"role\": \"user\",\n      \"content\": \"{{ $('Line Webhook').item.json.body.events[0].message.text }}\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "7Y8q0dS2Y1fcvzTl", "name": "OpenRouter.ai"}}, "typeVersion": 4.2}, {"id": "84ad9eae-c6fc-4a02-b5cc-0a0b1755d5a8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1580, -100], "parameters": {"color": 4, "width": 360, "height": 300, "content": "**Line Reply**\nSimilar to above but from different route"}, "typeVersion": 1}, {"id": "dade001e-80c6-4add-9c6c-e4583f7fcf72", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1200, -100], "parameters": {"color": 2, "width": 360, "height": 300, "content": "**OpenRouter.ai**\nWe will use image as prompt and change the model to support image. \n"}, "typeVersion": 1}, {"id": "54157315-3898-4e48-9598-1a5533803674", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [780, -100], "parameters": {"color": 6, "width": 380, "height": 300, "content": "**Pre-processing**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWe need to use get media API to get the data from Line and also move it to base64 file to prompt"}, "typeVersion": 1}, {"id": "df058683-5649-4143-b3ce-e39c7b209065", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [1000, -40], "parameters": {"options": {}, "operation": "binaryToPropery", "destinationKey": "img_prompt"}, "typeVersion": 1}, {"id": "23a1ee09-d967-45de-a87a-bf7bc5473f53", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [940, 240], "parameters": {"color": 4, "width": 360, "height": 420, "content": "**Line Reply**\nTo reply that message is not supported\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "9d968370-6c55-480a-b09b-a16e55b855a3", "name": "Line Reply (image)", "type": "n8n-nodes-base.httpRequest", "position": [1700, 0], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.choices[0].message.content.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"\\n\",\"\").removeMarkdown().removeTags().replaceAll('\"',\"\") }}\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "fed14d64-d3ea-4a17-98d5-28d889ac4ffa", "name": "Line Reply (Text)", "type": "n8n-nodes-base.httpRequest", "position": [1700, -460], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.choices[0].message.content.replaceAll(\"\\n\",\"\\\\n\").replaceAll(\"\\n\",\"\").removeMarkdown().removeTags().replaceAll('\"',\"\") }}\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "22b0359f-66f8-4a6a-b2b9-5a516f235aef", "name": "Line Reply (Not Supported 2)", "type": "n8n-nodes-base.httpRequest", "position": [1060, 500], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Please try again. Message type is not supported\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}, {"id": "a5d4ad30-71b8-4544-88a0-5cfbb0a79013", "name": "Line Reply (Not Supported 1)", "type": "n8n-nodes-base.httpRequest", "position": [1060, 320], "parameters": {"url": "https://api.line.me/v2/bot/message/reply", "method": "POST", "options": {}, "jsonBody": "={\n  \"replyToken\": \"{{ $('Line Webhook').item.json.body.events[0].replyToken }}\",\n  \"messages\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Please try again. Message type is not supported\"\n    }\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "3IEOzxKOUr6OEXyU", "name": "Line @405jtfqs LazyChinese"}}, "typeVersion": 4.2}], "active": true, "pinData": {}, "settings": {"timezone": "Asia/Bangkok", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "A8HoJ5iCrAMPbsLh", "executionOrder": "v1"}, "versionId": "7e072a04-5169-4bfd-8391-2797f4714d0c", "connections": {"Switch": {"main": [[{"node": "OpenRouter: qwen/qwen-2.5-72b-instruct:free", "type": "main", "index": 0}], [{"node": "Get Image", "type": "main", "index": 0}], [{"node": "Line Reply (Not Supported 1)", "type": "main", "index": 0}], [{"node": "Line Reply (Not Supported 2)", "type": "main", "index": 0}]]}, "Get Image": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Line Webhook": {"main": [[{"node": "Line Loading Animation", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "OpenRouter : qwen/qwen2.5-vl-72b-instruct:free", "type": "main", "index": 0}]]}, "Line Loading Animation": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenRouter: qwen/qwen-2.5-72b-instruct:free": {"main": [[{"node": "Line Reply (Text)", "type": "main", "index": 0}]]}, "OpenRouter : qwen/qwen2.5-vl-72b-instruct:free": {"main": [[{"node": "Line Reply (image)", "type": "main", "index": 0}]]}}}