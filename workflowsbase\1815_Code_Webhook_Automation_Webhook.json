{"id": "bpq5aoogWibWq94t", "meta": {"instanceId": "ffb0782f8b2cf4278577cb919e0cd26141bc9ff8774294348146d454633aa4e3", "templateCredsSetupCompleted": true}, "name": "puq-docker-influxdb-deploy", "tags": [], "nodes": [{"id": "b1c793ae-265c-420b-8cd1-8ecc180bfb52", "name": "If", "type": "n8n-nodes-base.if", "position": [-2060, -320], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "b702e607-888a-42c9-b9a7-f9d2a64dfccd", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.server_domain }}", "rightValue": "={{ $('API').item.json.body.server_domain }}"}]}}, "typeVersion": 2.2}, {"id": "d93df9de-1c37-46ee-8bbf-77297a1b63d5", "name": "Parametrs", "type": "n8n-nodes-base.set", "position": [-2280, -320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "a6328600-7ee0-4031-9bdb-fcee99b79658", "name": "server_domain", "type": "string", "value": "d01-test.uuq.pl"}, {"id": "370ddc4e-0fc0-48f6-9b30-ebdfba72c62f", "name": "clients_dir", "type": "string", "value": "/opt/docker/clients"}, {"id": "************************************", "name": "mount_dir", "type": "string", "value": "/mnt"}, {"id": "baa52df2-9c10-42b2-939f-f05ea85ea2be", "name": "screen_left", "type": "string", "value": "{{"}, {"id": "2b19ed99-2630-412a-98b6-4be44d35d2e7", "name": "screen_right", "type": "string", "value": "}}"}]}}, "typeVersion": 3.4}, {"id": "35e09115-77bc-48e4-b534-d6015162521f", "name": "API", "type": "n8n-nodes-base.webhook", "position": [-2600, -320], "webhookId": "6760feea-1d9b-466c-82a9-3891a300b0fd", "parameters": {"path": "docker-influxdb", "options": {}, "httpMethod": ["POST"], "responseMode": "responseNode", "authentication": "basicAuth", "multipleMethods": true}, "credentials": {"httpBasicAuth": {"id": "ljwsCBagSzOWlGsf", "name": "InfluxDB"}}, "typeVersion": 2}, {"id": "bddedef2-c43f-4e7b-b599-13fb7d47d504", "name": "422-Invalid server domain", "type": "n8n-nodes-base.respondToWebhook", "position": [-2100, 0], "parameters": {"options": {"responseCode": 422}, "respondWith": "json", "responseBody": "[{\n  \"status\": \"error\",\n  \"error\": \"Invalid server domain\"\n}]"}, "typeVersion": 1.1, "alwaysOutputData": false}, {"id": "eedc340a-f599-4e65-91cc-299a9cc075e6", "name": "Code1", "type": "n8n-nodes-base.code", "position": [800, -240], "parameters": {"mode": "runOnceForEachItem", "jsCode": "try {\n  if ($json.stdout === 'success') {\n    return {\n      json: {\n        status: 'success',\n        message: '',\n        data: '',\n      }\n    };\n  }\n\n  const parsedData = JSON.parse($json.stdout);\n\n  return {\n    json: {\n      status: parsedData.status === 'error' ? 'error' : 'success',\n      message: parsedData.message || (parsedData.status === 'error' ? 'An error occurred' : ''),\n      data: parsedData || '',\n    }\n  };\n\n} catch (error) {\n  return {\n    json: {\n      status: 'error',\n      message: $json.stdout??$json.error,\n      data: '',\n    }\n  };\n}"}, "executeOnce": false, "retryOnFail": false, "typeVersion": 2, "alwaysOutputData": false}, {"id": "82ac9991-aabe-4ebc-8a0a-dc712e219abf", "name": "SSH", "type": "n8n-nodes-base.ssh", "onError": "continueErrorOutput", "position": [500, -240], "parameters": {"cwd": "=/", "command": "={{ $json.sh }}"}, "credentials": {"sshPassword": {"id": "Cyjy61UWHwD2Xcd8", "name": "d01-test.uuq.pl-puq"}}, "executeOnce": true, "typeVersion": 1}, {"id": "825591ef-4b1d-4e4d-84a0-75370d26bbfb", "name": "Container Actions", "type": "n8n-nodes-base.switch", "position": [-1660, -600], "parameters": {"rules": {"values": [{"outputKey": "start", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "66ad264d-5393-410c-bfa3-011ab8eb234a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_start"}]}, "renameOutput": true}, {"outputKey": "stop", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b48957a0-22c0-4ac0-82ef-abd9e7ab0207", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_stop"}]}, "renameOutput": true}, {"outputKey": "mount_disk", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "727971bf-4218-41c1-9b07-22df4b947852", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_mount_disk"}]}, "renameOutput": true}, {"outputKey": "unmount_disk", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0c80b1d9-e7ca-4cf3-b3ac-b40fdf4dd8f8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_unmount_disk"}]}, "renameOutput": true}, {"outputKey": "container_get_acl", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "755e1a9f-667a-4022-9cb5-3f8153f62e95", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_get_acl"}]}, "renameOutput": true}, {"outputKey": "container_set_acl", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d75626f-789e-42fc-be5e-3a4e93a9bbc6", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_set_acl"}]}, "renameOutput": true}, {"outputKey": "container_get_net", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c49d811a-735c-42f4-8b77-d0cd47b3d2b8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_get_net"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "5dddba42-28bf-41b9-ac94-52cc35a753a6", "name": "Service Actions", "type": "n8n-nodes-base.switch", "position": [-800, -1160], "parameters": {"rules": {"values": [{"outputKey": "test_connection", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3afdd2f1-fe93-47c2-95cd-bac9b1d94eeb", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "test_connection"}]}, "renameOutput": true}, {"outputKey": "create", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "102f10e9-ec6c-4e63-ba95-0fe6c7dc0bd1", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "create"}]}, "renameOutput": true}, {"outputKey": "suspend", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f62dfa34-6751-4b34-adcc-3d6ba1b21a8c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "suspend"}]}, "renameOutput": true}, {"outputKey": "unsuspend", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "384d2026-b753-4c27-94c2-8f4fc189eb5f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "unsuspend"}]}, "renameOutput": true}, {"outputKey": "terminate", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0e190a97-827a-4e87-8222-093ff7048b21", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "terminate"}]}, "renameOutput": true}, {"outputKey": "change_package", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6f7832f3-b61d-4517-ab6b-6007998136dd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "change_package"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "b50f9cca-87ec-4cc4-a4a0-6070746b25f2", "name": "API answer", "type": "n8n-nodes-base.respondToWebhook", "position": [820, 0], "parameters": {"options": {"responseCode": 200}, "respondWith": "allIncomingItems"}, "typeVersion": 1.1, "alwaysOutputData": true}, {"id": "7e39251f-9a03-4800-b070-2bf2885c44be", "name": "Inspect", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1140, -980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\n\nINSPECT_JSON=\"{}\"\nif sudo docker ps -a --filter \"name=$CONTAINER_NAME\" | grep -q \"$CONTAINER_NAME\"; then\n  INSPECT_JSON=$(sudo docker inspect \"$CONTAINER_NAME\")\nfi\n\necho \"{\\\"inspect\\\": $INSPECT_JSON}\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "4f63a2e0-dc0d-4373-9441-a57ee8d9bfdf", "name": "Stat", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-980, -880], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\n\n# Initialize empty container data\nINSPECT_JSON=\"{}\"\nSTATS_JSON=\"{}\"\n\n# Check if container is running\nif sudo docker ps -a --filter \"name=$CONTAINER_NAME\" | grep -q \"$CONTAINER_NAME\"; then\n  # Get Docker inspect info in JSON (as raw string)\n  INSPECT_JSON=$(sudo docker inspect \"$CONTAINER_NAME\")\n\n  # Get Docker stats info in JSON (as raw string)\n  STATS_JSON=$(sudo docker stats --no-stream --format \"{{ $('Parametrs').item.json.screen_left }}json .{{ $('Parametrs').item.json.screen_right }}\" \"$CONTAINER_NAME\")\n  STATS_JSON=${STATS_JSON:-'{}'}\nfi\n\n# Initialize disk info variables\nMOUNT_USED=\"N/A\"\nMOUNT_FREE=\"N/A\"\nMOUNT_TOTAL=\"N/A\"\nMOUNT_PERCENT=\"N/A\"\nIMG_SIZE=\"N/A\"\nIMG_PERCENT=\"N/A\"\nDISK_STATS_IMG=\"N/A\"\n\n# Check if mount directory exists and is accessible\nif [ -d \"$MOUNT_DIR\" ]; then\n  if mount | grep -q \"$MOUNT_DIR\"; then\n    # Get disk usage for mounted directory\n    DISK_STATS_MOUNT=$(df -h \"$MOUNT_DIR\" | tail -n 1)\n    MOUNT_USED=$(echo \"$DISK_STATS_MOUNT\" | awk '{print $3}')\n    MOUNT_FREE=$(echo \"$DISK_STATS_MOUNT\" | awk '{print $4}')\n    MOUNT_TOTAL=$(echo \"$DISK_STATS_MOUNT\" | awk '{print $2}')\n    MOUNT_PERCENT=$(echo \"$DISK_STATS_MOUNT\" | awk '{print $5}')\n  fi\nfi\n\n# Check if image file exists\nif [ -f \"$IMG_FILE\" ]; then\n  # Get disk usage for image file\n  IMG_SIZE=$(du -sh \"$IMG_FILE\" | awk '{print $1}')\nfi\n\n# Manually create a combined JSON object\nFINAL_JSON=\"{\\\"inspect\\\": $INSPECT_JSON, \\\"stats\\\": $STATS_JSON, \\\"disk\\\": {\\\"mounted\\\": {\\\"used\\\": \\\"$MOUNT_USED\\\", \\\"free\\\": \\\"$MOUNT_FREE\\\", \\\"total\\\": \\\"$MOUNT_TOTAL\\\", \\\"percent\\\": \\\"$MOUNT_PERCENT\\\"}, \\\"img_file\\\": {\\\"size\\\": \\\"$IMG_SIZE\\\"}}}\"\n\n# Output the result\necho \"$FINAL_JSON\"\n\nexit 0"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "993286dd-8757-40a2-ac62-3e5ab5d1261f", "name": "Start", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1160, -620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\nif ! df -h | grep -q \"$MOUNT_DIR\"; then\n    handle_error \"The file $IMG_FILE is not mounted to $MOUNT_DIR\"\nfi\n\nif sudo docker ps --filter \"name=$CONTAINER_NAME\" --filter \"status=running\" -q | grep -q .; then\n    handle_error \"$CONTAINER_NAME container is running\"\nfi\n\n# Change to the compose directory\ncd \"$COMPOSE_DIR\" > /dev/null 2>&1 || handle_error \"Failed to change directory to $COMPOSE_DIR\"\n\n# Start the Docker containers\nif ! sudo docker compose up -d > /dev/null 2>error.log; then\n    ERROR_MSG=$(tail -n 10 error.log)\n    handle_error \"Docker-compose failed: $ERROR_MSG\"\nfi\n\n# Success\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "32fc208c-cd56-4db0-9a8f-766c52bae9f5", "name": "Stop", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1040, -520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Check if Docker container is running\nif ! sudo docker ps --filter \"name=$CONTAINER_NAME\" --filter \"status=running\" -q | grep -q .; then\n    handle_error \"$CONTAINER_NAME container is not running\"\nfi\n\n# Stop and remove the Docker containers (also remove associated volumes)\nif ! sudo docker compose -f \"$COMPOSE_DIR/docker-compose.yml\" down > /dev/null 2>&1; then\n    handle_error \"Failed to stop and remove docker-compose containers\"\nfi\n\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "9b87a055-2949-4a53-94e9-9ae059ed4913", "name": "Test Connection1", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -1320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Function to log an error, print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Check if Dock<PERSON> is installed\nif ! command -v docker &> /dev/null; then\n    handle_error \"Docker is not installed\"\nfi\n\n# Check if Docker service is running\nif ! systemctl is-active --quiet docker; then\n    handle_error \"Docker service is not running\"\nfi\n\n# Check if nginx-proxy container is running\nif ! sudo docker ps --filter \"name=nginx-proxy\" --filter \"status=running\" -q > /dev/null; then\n    handle_error \"nginx-proxy container is not running\"\nfi\n\n# Check if letsencrypt-nginx-proxy-companion container is running\nif ! sudo docker ps --filter \"name=letsencrypt-nginx-proxy-companion\" --filter \"status=running\" -q > /dev/null; then\n    handle_error \"letsencrypt-nginx-proxy-companion container is not running\"\nfi\n\n# If everything is successful\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "2a2bafe1-ded2-4857-9853-e5ef75fab5d7", "name": "Deploy", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -1120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Get values for variables from templates\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nCOMPOSE_FILE=\"$COMPOSE_DIR/docker-compose.yml\"\nSTATUS_FILE=\"$COMPOSE_DIR/status\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/$DOMAIN\"\nDOCKER_COMPOSE_TEXT='{{ JSON.stringify($('Deploy-docker-compose').item.json['docker-compose']).base64Encode() }}'\n\nNGINX_MAIN_ACL_FILE=\"$NGINX_DIR/$DOMAIN\"_acl\n\nNGINX_MAIN_TEXT='{{ JSON.stringify($('nginx').item.json['main']).base64Encode() }}'\nNGINX_MAIN_FILE=\"$NGINX_DIR/$DOMAIN\"\nVHOST_MAIN_FILE=\"$VHOST_DIR/$DOMAIN\"\n\nNGINX_MAIN_LOCATION_TEXT='{{ JSON.stringify($('nginx').item.json['main_location']).base64Encode() }}'\nNGINX_MAIN_LOCATION_FILE=\"$NGINX_DIR/$DOMAIN\"_location\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\n\n\nDISK_SIZE=\"{{ $('API').item.json.body.disk }}\"\n\n# Function to handle errors: write to the status file and print the message to console\nhandle_error() {\n    STATUS_JSON=\"{\\\"status\\\": \\\"error\\\", \\\"message\\\": \\\"$1\\\"}\"\n    echo \"$STATUS_JSON\" | sudo tee \"$STATUS_FILE\" > /dev/null  # Write error to the status file\n    echo \"error: $1\"  # Print the error message to the console\n    exit 1  # Exit the script with an error code\n}\n\n# Check if the directory already exists. If yes, exit with an error.\nif [ -d \"$COMPOSE_DIR\" ]; then\n    echo \"error: Directory $COMPOSE_DIR already exists\"\n    exit 1\nfi\n\n# Create necessary directories with permissions\nsudo mkdir -p \"$COMPOSE_DIR\" > /dev/null 2>&1 || handle_error \"Failed to create $COMPOSE_DIR\"\nsudo mkdir -p \"$NGINX_DIR\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_DIR\"\nsudo mkdir -p \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to create $MOUNT_DIR\"\n\n# Set permissions on the created directories\nsudo chmod -R 777 \"$COMPOSE_DIR\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $COMPOSE_DIR\"\nsudo chmod -R 777 \"$NGINX_DIR\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $NGINX_DIR\"\nsudo chmod -R 777 \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $MOUNT_DIR\"\n\n# Create docker-compose.yml file\necho -e \"$DOCKER_COMPOSE_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$COMPOSE_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $COMPOSE_FILE\"\n\n# Create NGINX configuration files\necho \"\" | sudo tee \"$NGINX_MAIN_ACL_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_ACL_FILE\"\n\necho -e \"$NGINX_MAIN_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_FILE\"\necho -e \"$NGINX_MAIN_LOCATION_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_LOCATION_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_LOCATION_FILE\"\n\n# Change to the compose directory\ncd \"$COMPOSE_DIR\" > /dev/null 2>&1 || handle_error \"Failed to change directory to $COMPOSE_DIR\"\n\n# Create data.img file if it doesn't exist\nif [ ! -f \"$IMG_FILE\" ]; then\n    sudo fallocate -l \"$DISK_SIZE\"G \"$IMG_FILE\" > /dev/null 2>&1 || sudo truncate -s \"$DISK_SIZE\"G \"$IMG_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $IMG_FILE\"\n    sudo mkfs.ext4 \"$IMG_FILE\" > /dev/null 2>&1 || handle_error \"Failed to format $IMG_FILE\"  # Format the image as ext4\n    sync  # Synchronize the data to disk\nfi\n\n# Add an entry to /etc/fstab for mounting if not already present\nif ! grep -q \"$IMG_FILE\" /etc/fstab; then\n    echo \"$IMG_FILE $MOUNT_DIR ext4 loop 0 0\" | sudo tee -a /etc/fstab > /dev/null || handle_error \"Failed to add entry to /etc/fstab\"\nfi\n\n# Mount all entries in /etc/fstab\nsudo mount -a || handle_error \"Failed to mount entries from /etc/fstab\"\n\n# Set permissions on the mount directory\nsudo chmod -R 777 \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $MOUNT_DIR\"\n\nsudo mkdir -p \"$MOUNT_DIR/lib\" > /dev/null 2>&1 || handle_error \"Failed to create $MOUNT_DIR/lib\"\nsudo chmod -R 777 \"$MOUNT_DIR/lib\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $MOUNT_DIR/lib\"\n\nsudo mkdir -p \"$MOUNT_DIR/etc\" > /dev/null 2>&1 || handle_error \"Failed to create $MOUNT_DIR/etc\"\nsudo chmod -R 777 \"$MOUNT_DIR/etc\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $MOUNT_DIR/etc\"\n\n# Copy NGINX configuration files instead of creating symbolic links\nsudo cp -f \"$NGINX_MAIN_FILE\" \"$VHOST_MAIN_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_FILE to $VHOST_MAIN_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_FILE\"\n\nsudo cp -f \"$NGINX_MAIN_LOCATION_FILE\" \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_LOCATION_FILE to $VHOST_MAIN_LOCATION_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_LOCATION_FILE\"\n\n# Start Docker containers using docker-compose\nif ! sudo docker compose up -d > /dev/null 2>error.log; then\n    ERROR_MSG=$(tail -n 10 error.log)  # Read the last 10 lines from error.log\n    handle_error \"Docker-compose failed: $ERROR_MSG\"\nfi\n\n# If everything is successful, update the status file and print success message\necho \"active\" | sudo tee \"$STATUS_FILE\" > /dev/null\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "cc74e942-766c-43f3-9789-1eccb139d58d", "name": "Suspend", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -960], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nCOMPOSE_FILE=\"$COMPOSE_DIR/docker-compose.yml\"\nSTATUS_FILE=\"$COMPOSE_DIR/status\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/$DOMAIN\"\n\nVHOST_MAIN_FILE=\"$VHOST_DIR/$DOMAIN\"\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"$1\" | sudo tee \"$STATUS_FILE\" > /dev/null\n    echo \"error: $1\"\n    exit 1\n}\n\n# Stop and remove Docker containers (also remove associated volumes)\nif [ -f \"$COMPOSE_FILE\" ]; then\n    if ! sudo docker compose -f \"$COMPOSE_FILE\" down > /dev/null 2>&1; then\n        handle_error \"Failed to stop and remove docker-compose containers\"\n    fi\nelse\n    echo \"Warning: docker-compose.yml not found, skipping container stop.\"\nfi\n\n# Remove mount entry from /etc/fstab if it exists\nif grep -q \"$IMG_FILE\" /etc/fstab; then\n    sudo sed -i \"\\|$(printf '%s\\n' \"$IMG_FILE\" | sed 's/[.[\\*^$]/\\\\&/g')|d\" /etc/fstab\nfi\n\n# Unmount the image if it is mounted\nif mount | grep -q \"$MOUNT_DIR\"; then\n    sudo umount \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to unmount $MOUNT_DIR\"\nfi\n\n# Remove the mount directory\nif [ -d \"$MOUNT_DIR\" ]; then\n    sudo rm -rf \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to remove $MOUNT_DIR\"\nfi\n\n# Remove NGINX configuration files\n[ -f \"$VHOST_MAIN_FILE\" ] && sudo rm -f \"$VHOST_MAIN_FILE\" || handle_error \"Warning: $VHOST_MAIN_FILE not found.\"\n[ -f \"$VHOST_MAIN_LOCATION_FILE\" ] && sudo rm -f \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Warning: $VHOST_MAIN_LOCATION_FILE not found.\"\n\n# Update status\necho \"suspended\" | sudo tee \"$STATUS_FILE\" > /dev/null\n\n# Success\necho \"success\"\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "bc93158d-cab4-4161-aa34-f54905869eae", "name": "Terminated", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nCOMPOSE_FILE=\"$COMPOSE_DIR/docker-compose.yml\"\nSTATUS_FILE=\"$COMPOSE_DIR/status\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\n\nVHOST_MAIN_FILE=\"$VHOST_DIR/$DOMAIN\"\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\nVHOST_CONSOLE_FILE=\"$VHOST_DIR/console.$DOMAIN\"\nVHOST_CONSOLE_LOCATION_FILE=\"$VHOST_DIR/console.$DOMAIN\"_location\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/$DOMAIN\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Stop and remove the Docker containers\nif [ -f \"$COMPOSE_FILE\" ]; then\n    sudo docker compose -f \"$COMPOSE_FILE\" down > /dev/null 2>&1\nfi\n\n# Remove the mount entry from /etc/fstab if it exists\nif grep -q \"$IMG_FILE\" /etc/fstab; then\n    sudo sed -i \"\\|$(printf '%s\\n' \"$IMG_FILE\" | sed 's/[.[\\*^$]/\\\\&/g')|d\" /etc/fstab\nfi\n\n# Unmount the image if it is still mounted\nif mount | grep -q \"$MOUNT_DIR\"; then\n    sudo umount \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to unmount $MOUNT_DIR\"\nfi\n\n# Remove all related directories and files\nfor item in \"$MOUNT_DIR\" \"$COMPOSE_DIR\" \"$VHOST_MAIN_FILE\" \"$VHOST_MAIN_LOCATION_FILE\" \"$VHOST_CONSOLE_FILE\" \"$VHOST_CONSOLE_LOCATION_FILE\"; do\n    if [ -e \"$item\" ]; then\n        sudo rm -rf \"$item\" || handle_error \"Failed to remove $item\"\n    fi\ndone\n\necho \"success\"\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "ab47d0f6-502b-43f6-acd1-547adb8c6bda", "name": "Unsuspend", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -800], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nCOMPOSE_FILE=\"$COMPOSE_DIR/docker-compose.yml\"\nSTATUS_FILE=\"$COMPOSE_DIR/status\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/$DOMAIN\"\nDOCKER_COMPOSE_TEXT='{{ JSON.stringify($('Deploy-docker-compose').item.json['docker-compose']).base64Encode() }}'\n\nNGINX_MAIN_ACL_FILE=\"$NGINX_DIR/$DOMAIN\"_acl\n\nNGINX_MAIN_TEXT='{{ JSON.stringify($('nginx').item.json['main']).base64Encode() }}'\nNGINX_MAIN_FILE=\"$NGINX_DIR/$DOMAIN\"\nVHOST_MAIN_FILE=\"$VHOST_DIR/$DOMAIN\"\n\nNGINX_MAIN_LOCATION_TEXT='{{ JSON.stringify($('nginx').item.json['main_location']).base64Encode() }}'\nNGINX_MAIN_LOCATION_FILE=\"$NGINX_DIR/$DOMAIN\"_location\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\n\nDISK_SIZE=\"{{ $('API').item.json.body.disk }}\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"$1\" | sudo tee \"$STATUS_FILE\" > /dev/null\n    echo \"error: $1\"\n    exit 1\n}\n\nupdate_nginx_acl() {\n    ACL_FILE=$1\n    LOCATION_FILE=$2\n    \n    if [ -s \"$ACL_FILE\" ]; then  # Проверяем, что файл существует и не пустой\n        VALID_LINES=$(grep -vE '^\\s*$' \"$ACL_FILE\")  # Убираем пустые строки\n        if [ -n \"$VALID_LINES\" ]; then  # Если есть непустые строки\n            while IFS= read -r line; do\n                echo \"allow $line;\" | sudo tee -a \"$LOCATION_FILE\" > /dev/null || handle_error \"Failed to update $LOCATION_FILE\"\n            done <<< \"$VALID_LINES\"\n            echo \"deny all;\" | sudo tee -a \"$LOCATION_FILE\" > /dev/null || handle_error \"Failed to update $LOCATION_FILE\"\n        fi\n    fi\n}\n\n# Create necessary directories with permissions\nfor dir in \"$COMPOSE_DIR\" \"$NGINX_DIR\" \"$MOUNT_DIR\"; do\n    sudo mkdir -p \"$dir\" || handle_error \"Failed to create $dir\"\n    sudo chmod -R 777 \"$dir\" || handle_error \"Failed to set permissions on $dir\"\ndone\n\n# Check if the image is already mounted using fstab\nif ! grep -q \"$IMG_FILE\" /etc/fstab; then\n    echo \"$IMG_FILE $MOUNT_DIR ext4 loop 0 0\" | sudo tee -a /etc/fstab > /dev/null || handle_error \"Failed to add fstab entry for $IMG_FILE\"\nfi\n\n# Apply the fstab changes and mount the image\nif ! mount | grep -q \"$MOUNT_DIR\"; then\n    sudo mount -a || handle_error \"Failed to mount image using fstab\"\nfi\n\n# Create docker-compose.yml file\necho -e \"$DOCKER_COMPOSE_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$COMPOSE_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $COMPOSE_FILE\"\n\n# Create NGINX configuration files\necho -e \"$NGINX_MAIN_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_FILE\"\necho -e \"$NGINX_MAIN_LOCATION_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_LOCATION_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_LOCATION_FILE\"\n\n# Copy NGINX configuration files instead of creating symbolic links\nsudo cp -f \"$NGINX_MAIN_FILE\" \"$VHOST_MAIN_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_FILE to $VHOST_MAIN_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_FILE\"\n\nsudo cp -f \"$NGINX_MAIN_LOCATION_FILE\" \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_LOCATION_FILE to $VHOST_MAIN_LOCATION_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_LOCATION_FILE\"\n\nupdate_nginx_acl \"$NGINX_MAIN_ACL_FILE\" \"$VHOST_MAIN_LOCATION_FILE\"\n\n# Change to the compose directory\ncd \"$COMPOSE_DIR\" || handle_error \"Failed to change directory to $COMPOSE_DIR\"\n\n# Start Docker containers using docker-compose\n> error.log\nif ! sudo docker compose up -d > error.log 2>&1; then\n    ERROR_MSG=$(tail -n 10 error.log)  # Read the last 10 lines from error.log\n    handle_error \"Docker-compose failed: $ERROR_MSG\"\nfi\n\n# If everything is successful, update the status file and print success message\necho \"active\" | sudo tee \"$STATUS_FILE\" > /dev/null\necho \"success\"\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "93a5ca68-9397-43f4-a277-64f98544a6e2", "name": "Mount Disk", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1160, -400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Create necessary directories with permissions\nsudo mkdir -p \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to create $MOUNT_DIR\"\nsudo chmod 777 \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to set permissions on $MOUNT_DIR\"\n\nif df -h | grep -q \"$MOUNT_DIR\"; then\n    handle_error \"The file $IMG_FILE is mounted to $MOUNT_DIR\"\nfi\n\nif ! grep -q \"$IMG_FILE\" /etc/fstab; then\n    echo \"$IMG_FILE $MOUNT_DIR ext4 loop 0 0\" | sudo tee -a /etc/fstab > /dev/null || handle_error \"Failed to add entry to /etc/fstab\"\nfi\n\nsudo mount -a || handle_error \"Failed to mount entries from /etc/fstab\"\n\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "99041aec-cc6b-4994-a185-316233261a11", "name": "Unmount Disk", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1040, -300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/{{ $('API').item.json.body.domain }}\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\nif ! df -h | grep -q \"$MOUNT_DIR\"; then\n    handle_error \"The file $IMG_FILE is not mounted to $MOUNT_DIR\"\nfi\n\n# Remove the mount entry from /etc/fstab if it exists\nif grep -q \"$IMG_FILE\" /etc/fstab; then\n    sudo sed -i \"\\|$(printf '%s\\n' \"$IMG_FILE\" | sed 's/[.[\\*^$]/\\\\&/g')|d\" /etc/fstab\nfi\n\n# Unmount the image if it is mounted (using fstab)\nif mount | grep -q \"$MOUNT_DIR\"; then\n    sudo umount \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to unmount $MOUNT_DIR\"\nfi\n\n# Remove the mount directory (if needed)\nif ! sudo rm -rf \"$MOUNT_DIR\" > /dev/null 2>&1; then\n    handle_error \"Failed to remove $MOUNT_DIR\"\nfi\n\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "64c83aad-dd98-4418-8a73-8cdf6fdf0580", "name": "Log", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-840, -780], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\nLOGS_JSON=\"{}\"\n\n# Function to return error in JSON format\nhandle_error() {\n    echo \"{\\\"status\\\": \\\"error\\\", \\\"message\\\": \\\"$1\\\"}\"\n    exit 1\n}\n\n# Check if the container exists\nif ! sudo docker ps -a | grep -q \"$CONTAINER_NAME\" > /dev/null 2>&1; then\n    handle_error \"Container $CONTAINER_NAME not found\"\nfi\n\n# Get logs of the container\nLOGS=$(sudo docker logs --tail 1000 \"$CONTAINER_NAME\" 2>&1)\nif [ $? -ne 0 ]; then\n    handle_error \"Failed to retrieve logs for $CONTAINER_NAME\"\nfi\n\n# Format logs as JSON\necho \"$LOGS\" | jq -R -s '{\"logs\": .}'\n\nexit 0"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "77d2d93f-d006-493c-84ed-5e318ebacfcd", "name": "ChangePackage", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-220, -460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Get values for variables from templates\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nCOMPOSE_FILE=\"$COMPOSE_DIR/docker-compose.yml\"\nSTATUS_FILE=\"$COMPOSE_DIR/status\"\nIMG_FILE=\"$COMPOSE_DIR/data.img\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\nMOUNT_DIR=\"{{ $('Parametrs').item.json.mount_dir }}/$DOMAIN\"\nDOCKER_COMPOSE_TEXT='{{ JSON.stringify($('Deploy-docker-compose').item.json['docker-compose']).base64Encode() }}'\n\nNGINX_MAIN_TEXT='{{ JSON.stringify($('nginx').item.json['main']).base64Encode() }}'\nNGINX_MAIN_FILE=\"$NGINX_DIR/$DOMAIN\"\nVHOST_MAIN_FILE=\"$VHOST_DIR/$DOMAIN\"\n\nNGINX_MAIN_LOCATION_TEXT='{{ JSON.stringify($('nginx').item.json['main_location']).base64Encode() }}'\nNGINX_MAIN_LOCATION_FILE=\"$NGINX_DIR/$DOMAIN\"_location\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\n\nDISK_SIZE=\"{{ $('API').item.json.body.disk }}\"\n\n# Function to log an error, write to status file, and print to console\nhandle_error() {\n    STATUS_JSON=\"{\\\"status\\\": \\\"error\\\", \\\"message\\\": \\\"$1\\\"}\"\n    echo \"$STATUS_JSON\" | sudo tee \"$STATUS_FILE\" > /dev/null\n    echo \"error: $1\"\n    exit 1\n}\n\n# Create docker-compose.yml file\necho -e \"$DOCKER_COMPOSE_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$COMPOSE_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $COMPOSE_FILE\"\n\n# Check if the compose file exists before stopping the container\nif [ -f \"$COMPOSE_FILE\" ]; then\n    sudo docker compose -f \"$COMPOSE_FILE\" down > /dev/null 2>&1 || handle_error \"Failed to stop container\"\nelse\n    handle_error \"docker-compose.yml not found\"\nfi\n\n# Unmount the image if it is currently mounted\nif mount | grep -q \"$MOUNT_DIR\"; then\n    sudo umount \"$MOUNT_DIR\" > /dev/null 2>&1 || handle_error \"Failed to unmount $MOUNT_DIR\"\nfi\n\n# Create docker-compose.yml file\necho -e \"$DOCKER_COMPOSE_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$COMPOSE_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $COMPOSE_FILE\"\n\n# Create NGINX configuration files\necho -e \"$NGINX_MAIN_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_FILE\"\necho -e \"$NGINX_MAIN_LOCATION_TEXT\" | base64 --decode | sed 's/\\\\n/\\n/g' | sed 's/\\\\\"/\"/g' | sed '1s/^\"//' | sed '$s/\"$//' | sudo tee \"$NGINX_MAIN_LOCATION_FILE\" > /dev/null 2>&1 || handle_error \"Failed to create $NGINX_MAIN_LOCATION_FILE\"\n\n# Resize the disk image if it exists\nif [ -f \"$IMG_FILE\" ]; then\n    sudo truncate -s \"$DISK_SIZE\"G \"$IMG_FILE\" > /dev/null 2>&1 || handle_error \"Failed to resize $IMG_FILE (truncate)\"\n    sudo e2fsck -fy \"$IMG_FILE\" > /dev/null 2>&1 || handle_error \"Filesystem check failed on $IMG_FILE\"\n    sudo resize2fs \"$IMG_FILE\" > /dev/null 2>&1 || handle_error \"Failed to resize filesystem on $IMG_FILE\"\nelse\n    handle_error \"Disk image $IMG_FILE does not exist\"\nfi\n\n# Mount the disk only if it is not already mounted\nif ! mount | grep -q \"$MOUNT_DIR\"; then\n    sudo mount -a || handle_error \"Failed to mount entries from /etc/fstab\"\nfi\n\n# Change to the compose directory\ncd \"$COMPOSE_DIR\" > /dev/null 2>&1 || handle_error \"Failed to change directory to $COMPOSE_DIR\"\n\n# Copy NGINX configuration files instead of creating symbolic links\nsudo cp -f \"$NGINX_MAIN_FILE\" \"$VHOST_MAIN_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_FILE to $VHOST_MAIN_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_FILE\"\n\nsudo cp -f \"$NGINX_MAIN_LOCATION_FILE\" \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_LOCATION_FILE to $VHOST_MAIN_LOCATION_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_LOCATION_FILE\"\n\n# Start Docker containers using docker-compose\nif ! sudo docker compose up -d > /dev/null 2>error.log; then\n    ERROR_MSG=$(tail -n 10 error.log)  # Read the last 10 lines from error.log\n    handle_error \"Docker-compose failed: $ERROR_MSG\"\nfi\n\n# Update status file\necho \"active\" | sudo tee \"$STATUS_FILE\" > /dev/null\n\necho \"success\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "b6508dbe-809f-45c6-b182-e6aa148d467a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2640, -1280], "parameters": {"color": 6, "width": 639, "height": 909, "content": "## 👋 Welcome to PUQ Docker InfluxDB deploy!\n## Template for InfluxDB: API Backend for WHMCS/WISECP by PUQcloud\n\nv.1\n\nThis is an n8n template that creates an API backend for the WHMCS/WISECP module developed by PUQcloud.\n\n## Setup Instructions\n\n### 1. Configure API Webhook and SSH Access\n- Create a Credential (Basic Auth) for the **Webhook API Block** in n8n.\n- Create a Credential for **SSH access** to a server with Docker installed (**SSH Block**).\n\n### 2. Modify Template Parameters\nIn the **Parameters** block of the template, update the following settings:\n\n- `server_domain` – must match the domain of the WHMCS/WISECP Docker server.\n- `clients_dir` – directory where user data related to Docker and disks will be stored.\n- `mount_dir` – default mount point for the container disk (recommended not to change).\n\n**Do not modify** the following technical parameters:\n\n- `screen_left`\n- `screen_right`\n\n## Additional Resources\n- Full documentation: [https://doc.puq.info/books/docker-influxdb-whmcs-module](https://doc.puq.info/books/docker-influxdb-whmcs-module)\n- WHMCS module: [https://puqcloud.com/whmcs-module-docker-influxdb.php](https://puqcloud.com/whmcs-module-docker-influxdb.php)\n\n"}, "typeVersion": 1}, {"id": "eee5aa6b-b346-4341-aead-12715e55ee95", "name": "Deploy-docker-compose", "type": "n8n-nodes-base.set", "position": [-1140, -1260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "docker-compose", "type": "string", "value": "=name: \"{{ $('API').item.json.body.domain }}_influxdb\"\n\nservices:\n  {{ $('API').item.json.body.domain }}_influxdb:\n    container_name: {{ $('API').item.json.body.domain }}_influxdb\n    image: influxdb:2.7\n    restart: unless-stopped\n    volumes:\n      - {{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}/lib:/var/lib/influxdb2\n      - {{ $('Parametrs').item.json.mount_dir }}/{{ $('API').item.json.body.domain }}/etc:/etc/influxdb2\n    environment:\n      - LETSENCRYPT_HOST={{ $('API').item.json.body.domain }}\n      - VIRTUAL_HOST={{ $('API').item.json.body.domain }}\n      - DOCKER_INFLUXDB_INIT_MODE=setup\n      - DOCKER_INFLUXDB_INIT_USERNAME={{ $('API').item.json.body.username }}\n      - DOCKER_INFLUXDB_INIT_PASSWORD={{ $('API').item.json.body.password }}\n      - DOCKER_INFLUXDB_INIT_ORG={{ $('API').item.json.body.username }}_ORG\n      - DOCKER_INFLUXDB_INIT_BUCKET={{ $('API').item.json.body.username }}_BUCKET\n    healthcheck:\n      disable: false\n    networks:\n      - nginx-proxy_web\n    mem_limit: \"{{ $('API').item.json.body.ram }}G\"\n    cpus: \"{{ $('API').item.json.body.cpu }}\"\n\nnetworks:\n  nginx-proxy_web:\n    external: true\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "bfcb35da-1053-4eaa-8ed4-c270333dd28f", "name": "Version", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1040, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\nVERSION_JSON=\"{}\"\n\n# Function to return error in JSON format\nhandle_error() {\n    echo \"{\\\"status\\\": \\\"error\\\", \\\"message\\\": \\\"$1\\\"}\"\n    exit 1\n}\n\n# Check if the container exists\nif ! sudo docker ps -a | grep -q \"$CONTAINER_NAME\" > /dev/null 2>&1; then\n    handle_error \"Container $CONTAINER_NAME not found\"\nfi\n\n# Get the MinIO version from the container (first line only)\nVERSION=$(sudo docker exec \"$CONTAINER_NAME\" influxd version)\n\n# Format version as JSON\nVERSION_JSON=\"{\\\"version\\\": \\\"$VERSION\\\"}\"\n\necho \"$VERSION_JSON\"\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "4cc9c309-0044-4c11-9cff-c1b43616d5f8", "name": "If1", "type": "n8n-nodes-base.if", "position": [-1680, -1120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "8602bd4c-9693-4d5f-9e7d-5ee62210baca", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "create"}, {"id": "1c630b59-0e5a-441d-8aa5-70b31338d897", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "change_package"}, {"id": "b3eb7052-a70f-438e-befd-8c5240df32c7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "unsuspend"}]}}, "typeVersion": 2.2}, {"id": "6ebe8bea-ff92-4e02-b683-36b15b1826f5", "name": "nginx", "type": "n8n-nodes-base.set", "position": [-1420, -1260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "main", "type": "string", "value": "="}, {"id": "6507763a-21d4-4ff0-84d2-5dc9d21b7430", "name": "main_location", "type": "string", "value": "=proxy_pass_header Server;\nproxy_set_header X-Real-IP $remote_addr;\nproxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\nproxy_set_header X-Scheme $scheme;\nproxy_set_header Host $http_host;"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "5d1a92d8-1559-4d2f-837c-c1ed597cd531", "name": "Container Stat", "type": "n8n-nodes-base.switch", "position": [-1620, -880], "parameters": {"rules": {"values": [{"outputKey": "inspect", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "66ad264d-5393-410c-bfa3-011ab8eb234a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_information_inspect"}]}, "renameOutput": true}, {"outputKey": "stats", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b48957a0-22c0-4ac0-82ef-abd9e7ab0207", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_information_stats"}]}, "renameOutput": true}, {"outputKey": "log", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "50ede522-af22-4b7a-b1fd-34b27fd3fadd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "container_log"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "297dd668-c6b4-47fd-9ed7-1ed7aeda83bd", "name": "GET ACL", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1160, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Get values for variables from templates\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\n\nNGINX_MAIN_ACL_FILE=\"$NGINX_DIR/$DOMAIN\"_acl\n\n# Function to log an error and exit\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Read files if they exist, else assign empty array\nif [[ -f \"$NGINX_MAIN_ACL_FILE\" ]]; then\n    MAIN_IPS=$(cat \"$NGINX_MAIN_ACL_FILE\" | jq -R -s 'split(\"\\n\") | map(select(length > 0))')\nelse\n    MAIN_IPS=\"[]\"\nfi\n\n# Output JSON\necho \"{ \\\"main_ips\\\": $MAIN_IPS}\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "1fd4caf1-bc7c-4dd0-8a82-028f548611bf", "name": "SET ACL", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1080, -40], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Get values for variables from templates\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nVHOST_DIR=\"/opt/docker/nginx-proxy/nginx/vhost.d\"\n\nNGINX_MAIN_ACL_FILE=\"$NGINX_DIR/$DOMAIN\"_acl\nNGINX_MAIN_ACL_TEXT=\"{{ $('API').item.json.body.main_ips }}\"\nVHOST_MAIN_LOCATION_FILE=\"$VHOST_DIR/$DOMAIN\"_location\nNGINX_MAIN_LOCATION_FILE=\"$NGINX_DIR/$DOMAIN\"_location\n\n# Function to log an error and exit\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\nupdate_nginx_acl() {\n    ACL_FILE=$1\n    LOCATION_FILE=$2\n    \n    if [ -s \"$ACL_FILE\" ]; then\n        VALID_LINES=$(grep -vE '^\\s*$' \"$ACL_FILE\")\n        if [ -n \"$VALID_LINES\" ]; then\n            while IFS= read -r line; do\n                echo \"allow $line;\" | sudo tee -a \"$LOCATION_FILE\" > /dev/null || handle_error \"Failed to update $LOCATION_FILE\"\n            done <<< \"$VALID_LINES\"\n            echo \"deny all;\" | sudo tee -a \"$LOCATION_FILE\" > /dev/null || handle_error \"Failed to update $LOCATION_FILE\"\n        fi\n    fi\n}\n\n# Create or overwrite the file with the content from variables\necho \"$NGINX_MAIN_ACL_TEXT\" | sudo tee \"$NGINX_MAIN_ACL_FILE\" > /dev/null\n\nsudo cp -f \"$NGINX_MAIN_LOCATION_FILE\" \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to copy $NGINX_MAIN_LOCATION_FILE to $VHOST_MAIN_LOCATION_FILE\"\nsudo chmod 777 \"$VHOST_MAIN_LOCATION_FILE\" || handle_error \"Failed to set permissions on $VHOST_MAIN_LOCATION_FILE\"\n\nupdate_nginx_acl \"$NGINX_MAIN_ACL_FILE\" \"$VHOST_MAIN_LOCATION_FILE\"\n\n# Reload Nginx with sudo\nif sudo docker exec nginx-proxy nginx -s reload; then\n    echo \"success\"\nelse\n    handle_error \"Failed to reload Nginx.\"\nfi\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "965d9170-c157-4a43-a13a-1c696908827f", "name": "GET NET", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1160, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\n# Get values for variables from templates\nDOMAIN=\"{{ $('API').item.json.body.domain }}\"\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\nCOMPOSE_DIR=\"{{ $('Parametrs').item.json.clients_dir }}/$DOMAIN\"\nNGINX_DIR=\"$COMPOSE_DIR/nginx\"\nNET_IN_FILE=\"$COMPOSE_DIR/net_in\"\nNET_OUT_FILE=\"$COMPOSE_DIR/net_out\"\n\n# Function to log an error and exit\nhandle_error() {\n    echo \"error: $1\"\n    exit 1\n}\n\n# Get current network statistics from container\nSTATS=$(sudo docker exec \"$CONTAINER_NAME\" cat /proc/net/dev | grep eth0) || handle_error \"Failed to get network stats\"\nNET_IN_NEW=$(echo \"$STATS\" | awk '{print $2}')  # RX bytes (received)\nNET_OUT_NEW=$(echo \"$STATS\" | awk '{print $10}') # TX bytes (transmitted)\n\n# Ensure directory exists\nmkdir -p \"$COMPOSE_DIR\"\n\n# Read old values, create files if they don't exist\nif [[ -f \"$NET_IN_FILE\" ]]; then\n    NET_IN_OLD=$(sudo cat \"$NET_IN_FILE\")\nelse\n    NET_IN_OLD=0\nfi\n\nif [[ -f \"$NET_OUT_FILE\" ]]; then\n    NET_OUT_OLD=$(sudo cat \"$NET_OUT_FILE\")\nelse\n    NET_OUT_OLD=0\nfi\n\n# Save new values\necho \"$NET_IN_NEW\" | sudo tee \"$NET_IN_FILE\" > /dev/null\necho \"$NET_OUT_NEW\" | sudo tee \"$NET_OUT_FILE\" > /dev/null\n\n# Output JSON\necho \"{ \\\"net_in_new\\\": $NET_IN_NEW, \\\"net_out_new\\\": $NET_OUT_NEW, \\\"net_in_old\\\": $NET_IN_OLD, \\\"net_out_old\\\": $NET_OUT_OLD }\"\n\nexit 0\n"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}, {"id": "75d4b504-5c6f-4337-bdd4-08c9a6198c16", "name": "InfluxDB", "type": "n8n-nodes-base.switch", "position": [-1500, 320], "parameters": {"rules": {"values": [{"outputKey": "version", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "66ad264d-5393-410c-bfa3-011ab8eb234a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "app_version"}]}, "renameOutput": true}, {"outputKey": "change_password", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f7b833d7-fb58-49d2-af25-3aa93a3faa00", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('API').item.json.body.command }}", "rightValue": "change_password"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "df933a19-b866-4559-b32c-7d951ae75062", "name": "Change Password", "type": "n8n-nodes-base.set", "onError": "continueRegularOutput", "position": [-1040, 440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "21f4453e-c136-4388-be90-1411ae78e8a5", "name": "sh", "type": "string", "value": "=#!/bin/bash\n\nCONTAINER_NAME=\"{{ $('API').item.json.body.domain }}_influxdb\"\nUSERNAME=\"{{ $('API').item.json.body.username }}\"\nNEW_PASSWORD=\"{{ $('API').item.json.body.password }}\"\n\n# Function to return error in JSON format\nhandle_error() {\n    echo \"{\\\"status\\\": \\\"error\\\", \\\"message\\\": \\\"$1\\\"}\"\n    exit 1\n}\n\n# Run the password reset command for InfluxDB\nRESET_RESULT=$(sudo docker exec $CONTAINER_NAME influx user password --name $USERNAME --password $NEW_PASSWORD 2>&1)\n\n# Check if the reset was successful\nif [[ $RESET_RESULT == *\"Successfully updated password for user\"* ]]; then\n    echo \"{\\\"status\\\": \\\"success\\\"}\"\n    exit 0\nelse\n    handle_error \"Failed to reset password: $RESET_RESULT\"\nfi"}]}}, "typeVersion": 3.4, "alwaysOutputData": true}], "active": true, "pinData": {}, "settings": {"timezone": "America/Winnipeg", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "39fee67c-21b0-471c-9f9c-4bad8aeeffd6", "connections": {"If": {"main": [[{"node": "Container Stat", "type": "main", "index": 0}, {"node": "Container Actions", "type": "main", "index": 0}, {"node": "InfluxDB", "type": "main", "index": 0}, {"node": "If1", "type": "main", "index": 0}], [{"node": "422-Invalid server domain", "type": "main", "index": 0}]]}, "API": {"main": [[{"node": "Parametrs", "type": "main", "index": 0}], []]}, "If1": {"main": [[{"node": "nginx", "type": "main", "index": 0}], [{"node": "Service Actions", "type": "main", "index": 0}]]}, "Log": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "SSH": {"main": [[{"node": "Code1", "type": "main", "index": 0}], [{"node": "Code1", "type": "main", "index": 0}]]}, "Stat": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Stop": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "API answer", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "nginx": {"main": [[{"node": "Deploy-docker-compose", "type": "main", "index": 0}]]}, "Deploy": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "GET ACL": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "GET NET": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Inspect": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "SET ACL": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Suspend": {"main": [[{"node": "SSH", "type": "main", "index": 0}], []]}, "Version": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "InfluxDB": {"main": [[{"node": "Version", "type": "main", "index": 0}], [{"node": "Change Password", "type": "main", "index": 0}]]}, "Parametrs": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Unsuspend": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Mount Disk": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Terminated": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Unmount Disk": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "ChangePackage": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Container Stat": {"main": [[{"node": "Inspect", "type": "main", "index": 0}], [{"node": "Stat", "type": "main", "index": 0}], [{"node": "Log", "type": "main", "index": 0}]]}, "Change Password": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Service Actions": {"main": [[{"node": "Test Connection1", "type": "main", "index": 0}], [{"node": "Deploy", "type": "main", "index": 0}], [{"node": "Suspend", "type": "main", "index": 0}], [{"node": "Unsuspend", "type": "main", "index": 0}], [{"node": "Terminated", "type": "main", "index": 0}], [{"node": "ChangePackage", "type": "main", "index": 0}]]}, "Test Connection1": {"main": [[{"node": "SSH", "type": "main", "index": 0}]]}, "Container Actions": {"main": [[{"node": "Start", "type": "main", "index": 0}], [{"node": "Stop", "type": "main", "index": 0}], [{"node": "Mount Disk", "type": "main", "index": 0}], [{"node": "Unmount Disk", "type": "main", "index": 0}], [{"node": "GET ACL", "type": "main", "index": 0}], [{"node": "SET ACL", "type": "main", "index": 0}], [{"node": "GET NET", "type": "main", "index": 0}]]}, "Deploy-docker-compose": {"main": [[{"node": "Service Actions", "type": "main", "index": 0}]]}}}