{"id": "oNJCLq4egGByMeSl", "meta": {"instanceId": "1bc0f4fa5e7d17ac362404cbb49337e51e5061e019cfa24022a8667c1f1ce287", "templateCredsSetupCompleted": true}, "name": "Remove Advanced Background from Google Drive Images", "tags": [], "nodes": [{"id": "99582f98-3707-4480-954a-f091e4e8133a", "name": "Config", "type": "n8n-nodes-base.set", "position": [820, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "42b02a2f-a642-42db-a565-fd2a01a26fb9", "name": "bg_color", "type": "string", "value": "white"}, {"id": "f68b2280-ec85-4400-8a98-10e644b56076", "name": "padding", "type": "string", "value": "5%"}, {"id": "8bdee3a1-9107-4bf8-adea-332d299e43ae", "name": "keepInputSize", "type": "boolean", "value": true}, {"id": "89d9e4fb-ed14-4ee2-b6f0-73035bafbc39", "name": "outputSize", "type": "string", "value": "1600x1600"}, {"id": "ad53bf64-5493-4c4d-a52c-cd4d657cc9f9", "name": "inputFileName", "type": "string", "value": "={{ $json.originalFilename }}"}, {"id": "9fc440c6-289b-4a6a-8391-479a6660836f", "name": "OutputDriveFolder", "type": "string", "value": "ENTER GOOGLE DRIVE FOLDER URL"}, {"id": "f0f1767a-b659-48c4-bef6-8ee4111cb939", "name": "api-key", "type": "string", "value": "ENTER API KEY"}]}}, "typeVersion": 3.4}, {"id": "7b5973d4-0d9f-4d17-8b71-e6c4f81d682e", "name": "remove background", "type": "n8n-nodes-base.httpRequest", "position": [2300, 520], "parameters": {"url": "https://image-api.photoroom.com/v2/edit", "method": "POST", "options": {"response": {"response": {}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "background.color", "value": "={{ $json.bg_color }}"}, {"name": "imageFile", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "padding", "value": "={{ $json.padding }}"}, {"name": "outputSize", "value": "={{ $json.Geometry }}"}]}, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $json['api-key'] }}"}]}}, "typeVersion": 4.1}, {"id": "66d4f5c2-3d63-4e4a-8ea7-358c17061198", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1260, 420], "parameters": {"options": {"includeBinary": true}, "fieldToSplitOut": "Geometry"}, "typeVersion": 1}, {"id": "10f8a6cf-d1d0-4c5f-9983-5d574f98a7ba", "name": "Upload Picture to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [2520, 320], "parameters": {"name": "=BG-Removed-{{$json.inputFileName.split('.').slice(0, -1).join('.') }}.png", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "url", "value": "={{ $json.OutputDriveFolder }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "X2y13wEmbPaV3QGI", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "5e4e91ff-346e-414d-bbe2-0724469183b4", "name": "remove background fixed size", "type": "n8n-nodes-base.httpRequest", "position": [2300, 320], "parameters": {"url": "https://image-api.photoroom.com/v2/edit", "method": "POST", "options": {"response": {"response": {}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "background.color", "value": "={{ $json.bg_color }}"}, {"name": "imageFile", "parameterType": "formBinaryData", "inputDataFieldName": "data"}, {"name": "padding", "value": "={{ $json.padding }}"}, {"name": "outputSize", "value": "={{ $json.outputSize }}"}]}, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $json['api-key'] }}"}]}}, "typeVersion": 4.1}, {"id": "16924a69-2711-4dc6-b7ab-c0e2001edfa4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1600, 460], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "********-ef45-4159-8286-00a1b21aaec4", "name": "Upload Picture to Google Drive1", "type": "n8n-nodes-base.googleDrive", "position": [2540, 520], "parameters": {"name": "=BG-Removed-{{$json.inputFileName.split('.').slice(0, -1).join('.') }}.png", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "url", "value": "={{ $json.OutputDriveFolder }}"}}, "credentials": {"googleDriveOAuth2Api": {"id": "X2y13wEmbPaV3QGI", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "a2f15d9a-5458-4d83-995a-e41491c997bd", "name": "Download Image", "type": "n8n-nodes-base.googleDrive", "position": [800, 420], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "X2y13wEmbPaV3QGI", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "3e2bef4d-22f8-465d-8d11-f9fe25e67cd9", "name": "Get Image Size", "type": "n8n-nodes-base.editImage", "position": [1060, 420], "parameters": {"operation": "information"}, "typeVersion": 1}, {"id": "e497d10f-0727-4bb7-b016-42ffe2faf773", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [420, -280], "parameters": {"color": 5, "width": 613.*************, "height": 653.*************, "content": "## About this worfklow \n\n## How it works\nThis workflow does watch out for new images uploaded within Google Drive. \nOnce there are new images it will download the image. And then run some logic, remove the background and add some padding to the output image. \n**By default Images are saved as .png**\nOnce done upload it to Google Drive again.\n## Features* Select Google Drive Credentials within the Google Drive Nodes\n### This workflow supports\n* Remove Background\n* Transparent Background\n* Coloured Background (1 Color)\n* Add Padding\n* Choose Output Size\n\n## Customize it!\n* Feel free to customize the workflow to your needs\n* Speed up the workflow: Using fixed output size\n### Examples \n* Send Final Images to another service\n* For Products: Let ChatGPT Analyze the Product Type\n* Add Text with the \"Edit Image\" Node\n\n### Photroom API Playground\n[Click me](https://www.photoroom.com/api/playground)"}, "typeVersion": 1}, {"id": "e892caf8-b9c7-4880-a096-f9d1c8c52c0c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, -20], "parameters": {"color": 4, "width": 437.4768568353068, "height": 395.45317545748134, "content": "## Setup\n\n### Requirements\n* Photoroom API Key [Click me](https://docs.photoroom.com/getting-started/how-can-i-get-my-api-key)\n* Google Drive Credential Setup\n\n\n## Config\n* Select Google Drive Credentials within the Google Drive Nodes\n\n* **Please refer to the \"Config\" Node**\n\nFor the API Key you can also setup an Header Authentication"}, "typeVersion": 1}, {"id": "7f79d9e0-a7ac-422c-869f-76ada147917c", "name": "Watch for new images", "type": "n8n-nodes-base.googleDriveTrigger", "position": [440, 520], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "X2y13wEmbPaV3QGI", "name": "Google Drive account"}}, "typeVersion": 1}, {"id": "f67556bb-b463-4ba5-a472-577a8d5ab0ca", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [420, 680], "parameters": {"color": 3, "width": 160.**************, "height": 80, "content": "Select Input Folder"}, "typeVersion": 1}, {"id": "04913b7f-1949-4e8e-b2c4-f9e3bacbc78c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [780, 780], "parameters": {"color": 3, "width": 263.*************, "height": 227.**************, "content": "### Configuration\n* Provide Your API Key\n* Set Background Color\n-HEX or values like white, transparent...\n* Select if Output Size / or Original  Size should be used \n* Output Drive Folder\n ->Copy URL\n* Padding (Default 5%)"}, "typeVersion": 1}, {"id": "e3b262d2-c367-4733-8cde-abd485c3d81b", "name": "check which output size method is used", "type": "n8n-nodes-base.if", "position": [2040, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d11ca8bb-0801-480f-b99a-249c5920b876", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.keepInputSize }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "0cc4f416-7341-4bf7-8fb8-f3c746f8b9e4", "name": "loop all over your images", "type": "n8n-nodes-base.splitInBatches", "position": [1820, 460], "parameters": {"options": {}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cff1146a-4dfd-4d87-a819-2420652e6c5e", "connections": {"Merge": {"main": [[{"node": "loop all over your images", "type": "main", "index": 0}]]}, "Config": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Get Image Size", "type": "main", "index": 0}]]}, "Get Image Size": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "remove background": {"main": [[{"node": "Upload Picture to Google Drive1", "type": "main", "index": 0}]]}, "Watch for new images": {"main": [[{"node": "Download Image", "type": "main", "index": 0}, {"node": "Config", "type": "main", "index": 0}]]}, "loop all over your images": {"main": [[], [{"node": "check which output size method is used", "type": "main", "index": 0}]]}, "remove background fixed size": {"main": [[{"node": "Upload Picture to Google Drive", "type": "main", "index": 0}]]}, "Upload Picture to Google Drive": {"main": [[{"node": "loop all over your images", "type": "main", "index": 0}]]}, "Upload Picture to Google Drive1": {"main": [[{"node": "loop all over your images", "type": "main", "index": 0}]]}, "check which output size method is used": {"main": [[{"node": "remove background fixed size", "type": "main", "index": 0}], [{"node": "remove background", "type": "main", "index": 0}]]}}}