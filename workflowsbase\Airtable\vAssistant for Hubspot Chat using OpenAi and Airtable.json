{"id": "C2pB17EpXAJwOcst", "meta": {"instanceId": "ba379c9b99d35340c90344105e7e5d06ca0de3e88926f0384d2c23099dad1937"}, "name": "OpenAI Assistant for <PERSON><PERSON><PERSON> Cha<PERSON>", "tags": [], "nodes": [{"id": "7f11a684-911b-4fbc-ba1b-a8e7bce8e914", "name": "getHubspotMessage", "type": "n8n-nodes-base.httpRequest", "position": [280, 580], "parameters": {"url": "=https://api.hubapi.com/conversations/v3/conversations/threads/{{ $json[\"body\"][0][\"objectId\"] }}/messages/{{ $json[\"body\"][0][\"messageId\"] }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotAppToken"}, "credentials": {"hubspotAppToken": {"id": "56nluFhXiGjYN1EY", "name": "HubSpot App Token tinder"}, "hubspotOAuth2Api": {"id": "y6819fYl4TsW9gl6", "name": "HubSpot account 6"}, "hubspotDeveloperApi": {"id": "dHB9nVcnZTqf2JDX", "name": "HubSpot Developer account"}}, "typeVersion": 4.1}, {"id": "687bcbb8-38c8-4d21-a46f-186e880d003c", "name": "OpenAi Create Thread", "type": "n8n-nodes-base.httpRequest", "position": [1260, 420], "parameters": {"url": "https://api.openai.com/v1/threads", "method": "POST", "options": {}, "jsonBody": "={\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"{{ $('getHubspotMessage').item.json[\"text\"] }}\"\n }\n ]\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1}, {"id": "8b51d465-d298-4b7a-b939-026bd51469d3", "name": "OpenAI Run", "type": "n8n-nodes-base.httpRequest", "position": [1620, 420], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $json[\"OpenAI Thread ID\"] }}/runs", "method": "POST", "options": {}, "jsonBody": "={\n \"assistant_id\": \"asst_MA71Jq0SElVpdjmJa212CTFd\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1}, {"id": "3e645c55-a236-466f-9983-2a3e91c250db", "name": "Get Run", "type": "n8n-nodes-base.httpRequest", "position": [1920, 600], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $json[\"thread_id\"] }}/runs/{{ $json[\"id\"] }}", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "a69a1d1e-b932-481e-8d36-8d121c63ad4b", "name": "Get Last Message", "type": "n8n-nodes-base.httpRequest", "position": [2520, 460], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $json[\"thread_id\"] }}/messages", "options": {}, "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1}, {"id": "d9758207-56d4-4180-aac7-f0ebafab1064", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [2820, 960], "parameters": {"url": "=https://www.listafirme.ro/api/search-v1.asp", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "982dc86a0c1bd4c71185d39ae9f36998"}, {"name": "src", "value": "={{JSON.parse($json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"function\"][\"arguments\"]).src}}"}]}}, "typeVersion": 4.1}, {"id": "5c6f30fd-3ac2-401c-897a-54c7e998c97b", "name": "Completed, Action or Inprogress", "type": "n8n-nodes-base.switch", "position": [2120, 600], "parameters": {"rules": {"rules": [{"value2": "completed"}, {"output": 1, "value2": "requires_action"}, {"output": 2, "value2": "in_progress", "operation": "=equal"}, {"output": 3, "value2": "queued"}]}, "value1": "={{ $json.status }}", "dataType": "string"}, "typeVersion": 1}, {"id": "c1bc0adf-3552-43a3-b38f-bfc76e2683cd", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [2360, 1000], "webhookId": "e15c2bb6-e022-4c6d-869b-f361b1ec1259", "parameters": {"unit": "seconds"}, "typeVersion": 1}, {"id": "2e0c4528-5b2b-4d3c-9b53-166ea0f2a28e", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [2340, 760], "webhookId": "3a175bf4-c569-431e-bc56-abed3653ce9d", "parameters": {"unit": "seconds"}, "typeVersion": 1}, {"id": "f80a2cd8-6691-4186-909b-cfed95318014", "name": "Submit Data", "type": "n8n-nodes-base.httpRequest", "position": [3360, 960], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Select Function').item.json[\"thread_id\"] }}/runs/{{ $('Select Function').item.json[\"id\"] }}/submit_tool_outputs", "method": "POST", "options": {}, "jsonBody": "={\n \"tool_outputs\": [\n {\n \"tool_call_id\": \"{{ $('Select Function').item.json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"id\"] }}\",\n \"output\": \"{{$json.escapedJsonString}}\"\n }\n ]\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "eb114cfd-1af2-4c8b-bfba-583453a1d7ca", "name": "Select Function", "type": "n8n-nodes-base.switch", "position": [2520, 700], "parameters": {"rules": {"rules": [{"value2": "getAWBbyOrder"}, {"output": 1, "value2": "get_awb_history"}]}, "value1": "={{ $json.required_action.submit_tool_outputs.tool_calls[0].function.name }}", "dataType": "string"}, "typeVersion": 1}, {"id": "4d1ad478-a9a4-4e9f-9b06-e2a9b7b2b55c", "name": "Code1", "type": "n8n-nodes-base.code", "position": [3080, 960], "parameters": {"jsCode": "const item1 = $input.all()[0]?.json;\nconst jsonString = JSON.stringify(item1);\nconst escapedJsonString = jsonString.replace(/\"/g, '\\\\\"');\n\nreturn { escapedJsonString };\n"}, "typeVersion": 2}, {"id": "39cab0c4-1d7d-41cb-a88d-00acc8e79a24", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [3720, 1400], "webhookId": "68ae5068-6a39-424c-b88d-019bfee78b6f", "parameters": {"unit": "seconds"}, "typeVersion": 1}, {"id": "54205ed2-7c96-44b6-9637-20830300310a", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [2820, 1180], "parameters": {"url": "=https://www.listafirme.ro/api/info-v1.asp", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "982dc86a0c1bd4c71185d39ae9f36998"}, {"name": "data", "value": "={\"TaxCode\":\"{{JSON.parse($json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"function\"][\"arguments\"]).src}}\",\"NACE\":\"info\",\"VAT\":\"\", \"RegNo\":\"\", \"Status\":\"\", \"LegalForm\":\"\", \"Name\":\"\", \"Date\":\"\", \"TownCode\":\"\", \"County\":\"\", \"City\":\"\", \"Address\":\"\", \"Administrators\":\"\", \"Shareholders\":\"\", \"Balance\":\"latest\", \"Phone\":\"\", \"Mobile\":\"\", \"Fax\":\"\", \"Email\":\"\", \"Web\":\"\", \"Geolocation\":\"\", \"Description\":\"\", \"Trademarks\":\"\", \"Subsidiaries\":\"\", \"Branches\":\"\", \"FiscalActivity\":\"\", \"Obligations\":\"\", \"Links\":\"\"}"}]}}, "typeVersion": 4.1}, {"id": "862ab78d-0288-4c78-9e02-7ad4ff794a6d", "name": "Code", "type": "n8n-nodes-base.code", "position": [3060, 1180], "parameters": {"jsCode": "const item1 = $input.all()[0]?.json;\nconst jsonString = JSON.stringify(item1);\nconst escapedJsonString = jsonString.replace(/\"/g, '\\\\\"');\n\nreturn { escapedJsonString };\n"}, "typeVersion": 2}, {"id": "e9d1d277-107d-403c-9911-5faa4ae75671", "name": "Submit Data1", "type": "n8n-nodes-base.httpRequest", "position": [3260, 1180], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Select Function').item.json[\"thread_id\"] }}/runs/{{ $('Select Function').item.json[\"id\"] }}/submit_tool_outputs", "method": "POST", "options": {}, "jsonBody": "={\n \"tool_outputs\": [\n {\n \"tool_call_id\": \"{{ $('Select Function').item.json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"id\"] }}\",\n \"output\": \"{{$json.escapedJsonString}}\"\n }\n ]\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1, "alwaysOutputData": true}, {"id": "28e7637b-9a3b-49ba-b4c7-efd3f6cf0522", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [3460, 1360], "webhookId": "6d7d039c-8a4b-4178-8d31-57fb3c24ac14", "parameters": {"unit": "seconds"}, "typeVersion": 1}, {"id": "2b954546-8bc6-4028-9826-37a64d2aed04", "name": "respondHubspotMessage1", "type": "n8n-nodes-base.httpRequest", "position": [2820, 420], "parameters": {"url": "=https://api.hubapi.com/conversations/v3/conversations/threads/{{ $('getHubspotMessage').item.json[\"conversationsThreadId\"] }}/messages", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "type", "value": "MESSAGE"}, {"name": "richText", "value": "={{ $json.data[0].content[0].text.value }}"}, {"name": "senderActorId", "value": "A-5721819"}, {"name": "channelId", "value": "={{ $('getHubspotMessage').item.json.channelId }}"}, {"name": "channelAccountId", "value": "={{ $('getHubspotMessage').item.json.channelAccountId }}"}, {"name": "text", "value": "{{ $json.data[0].content[0].text.value }}"}]}, "nodeCredentialType": "hubspotAppToken"}, "credentials": {"hubspotAppToken": {"id": "56nluFhXiGjYN1EY", "name": "HubSpot App Token tinder"}, "hubspotOAuth2Api": {"id": "y6819fYl4TsW9gl6", "name": "HubSpot account 6"}, "hubspotDeveloperApi": {"id": "dHB9nVcnZTqf2JDX", "name": "HubSpot Developer account"}}, "typeVersion": 4.1}, {"id": "6facd7e9-5cbd-4eb7-ab22-84b4fbf35885", "name": "IF", "type": "n8n-nodes-base.if", "position": [640, 600], "parameters": {"conditions": {"string": [{"value1": "={{ $('getHubspotMessage').item.json[\"senders\"][0][\"actorId\"] }}", "value2": "A-5721819", "operation": "notEqual"}]}}, "typeVersion": 1}, {"id": "9410bce8-3a2d-4852-acbd-8baa7ee4964d", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [860, 600], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appGAPr0tOy8J0NXC", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC", "cachedResultName": "Hubspot Conversations ChatGPT"}, "table": {"__rl": true, "mode": "list", "value": "tbljZ0POq35jgnKES", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC/tbljZ0POq35jgnKES", "cachedResultName": "Conversations"}, "options": {}, "operation": "search", "filterByFormula": "={Hubspot Thread ID}=\"{{ $json.conversationsThreadId }}\""}, "credentials": {"airtableTokenApi": {"id": "Ha1BL7JqKQIwX3H1", "name": "Hubspot Conversations Makeitfuture Management"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "06449687-7521-4151-89c5-050a2768af13", "name": "IF1", "type": "n8n-nodes-base.if", "position": [1040, 640], "parameters": {"conditions": {"string": [{"value1": "={{ $('Airtable').item.json.id }}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"id": "65c3015e-760f-41e8-9d18-05492cf908c8", "name": "createThread", "type": "n8n-nodes-base.airtable", "position": [1440, 420], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appGAPr0tOy8J0NXC", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC", "cachedResultName": "Hubspot Conversations ChatGPT"}, "table": {"__rl": true, "mode": "list", "value": "tbljZ0POq35jgnKES", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC/tbljZ0POq35jgnKES", "cachedResultName": "Conversations"}, "columns": {"value": {"OpenAI Thread ID": "={{ $json[\"id\"] }}", "Hubspot Thread ID": "={{ $('getHubspotMessage').item.json.conversationsThreadId }}"}, "schema": [{"id": "Hubspot Thread ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Hubspot Thread ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "OpenAI Thread ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "OpenAI Thread ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "Ha1BL7JqKQIwX3H1", "name": "Hubspot Conversations Makeitfuture Management"}}, "typeVersion": 2}, {"id": "14cd4854-34fa-4a40-8bd2-cce2d9da9571", "name": "OpenAI Run1", "type": "n8n-nodes-base.httpRequest", "position": [1620, 780], "parameters": {"url": "=https://api.openai.com/v1/threads/{{ $('Airtable').item.json[\"OpenAI Thread ID\"] }}/runs", "method": "POST", "options": {}, "jsonBody": "={\n \"assistant_id\": \"asst_MA71Jq0SElVpdjmJa212CTFd\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 4.1, "continueOnFail": true, "alwaysOutputData": false}, {"id": "7c37641f-b0a4-4031-b289-3d6aed5a5bd6", "name": "IF2", "type": "n8n-nodes-base.if", "position": [60, 600], "parameters": {"conditions": {"string": [{"value1": "={{ $json[\"body\"][0][\"messageId\"] }}", "operation": "isNotEmpty"}]}}, "typeVersion": 1}, {"id": "12744ebd-1d36-4f3c-9cbe-2ed7d18d37e3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, 440], "parameters": {"width": 640.1970959824021, "height": 428.68258455167785, "content": "Watch for new message on the chatbot. \nThis can be triggered with [n8n chat widget](https://www.npmjs.com/package/@n8n/chat), hubspot or other chat services. \n\n"}, "typeVersion": 1}, {"id": "9c200085-e9aa-4e11-93c2-da8184976229", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2480, 340], "parameters": {"width": 615.2010006500725, "height": 279.76857176586907, "content": "Post assistant Message back to chat service, in this case Hubspot"}, "typeVersion": 1}, {"id": "************************************", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1200, 300], "parameters": {"width": 636.6434938094908, "height": 304.69360473583896, "content": "Create a new Thread, save it to database and RUN"}, "typeVersion": 1}, {"id": "f13f45aa-47c9-4a76-a69c-f13f51d9434f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [480, 440], "parameters": {"width": 328.9155262250898, "height": 421.64797280574976, "content": "UPDATE USER FILTER FOR <PERSON><PERSON>LICATION"}, "typeVersion": 1}, {"id": "ba0d0a2c-5014-44b8-a281-9d5014b78bcc", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [840, 440], "parameters": {"width": 328.9155262250898, "height": 421.64797280574976, "content": "Search for Thread ID in a database. \n\nThis database is maintaing references between messaging service thread id and OpenI Thread ID. "}, "typeVersion": 1}, {"id": "3d3562b5-631f-405c-b671-6856214f167f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1200, 680], "parameters": {"width": 636.6434938094908, "height": 304.69360473583896, "content": "POST a new message to existing thread."}, "typeVersion": 1}, {"id": "9ad1622c-5b42-4279-bf16-edf7bcbb5155", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1900, 320], "parameters": {"width": 393.4831089305742, "height": 629.4777449641093, "content": "Get Run Status:\nIf still in progress, run again. \nIf action needed go to respective action.\nIf Completed, post message."}, "typeVersion": 1}, {"id": "e51965ef-7694-41b3-9c9a-9f78c00af3f3", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2538.191410231545, 840], "parameters": {"width": 1361.867818730004, "height": 731.995091888263, "content": "Run required actions based on Assistant answer and respond to Assistant with the function answer. \n\nEach route is a function that you need to define inside your assistant configuration.\n"}, "typeVersion": 1}, {"id": "706fb261-724e-4c22-8def-24a320d213a2", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1280, 780], "parameters": {"text": "={{ $('getHubspotMessage').item.json[\"text\"] }}", "prompt": "define", "options": {"baseURL": "https://api.openai.com/v1/threads/{{ $('Airtable').item.json[\"OpenAI Thread ID\"] }}/messages"}, "resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_wVbEcnRttQ8K65DOV0fk1DJU", "cachedResultName": "Lista Firma Agent"}}, "credentials": {"openAiApi": {"id": "sCh1Lrc1ZT8NVcgn", "name": "OpenAi Makeitfuture.eu"}}, "typeVersion": 1.3}, {"id": "b8f686cc-33d6-4e99-987c-d1f91864e81d", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-160, 600], "webhookId": "637d5b46-b35f-4943-92a2-864ddce170f4", "parameters": {"path": "hubspot-tinder", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d9763b45-9092-490f-85b4-926354cdeb47", "connections": {"IF": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "IF1": {"main": [[{"node": "OpenAi Create Thread", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}]]}, "IF2": {"main": [[{"node": "getHubspotMessage", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Submit Data1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Submit Data", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "OpenAI Run1", "type": "main", "index": 0}]]}, "Get Run": {"main": [[{"node": "Completed, Action or Inprogress", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "IF2", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "OpenAI Run": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "OpenAI Run1": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Submit Data": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Submit Data1": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "createThread": {"main": [[{"node": "OpenAI Run", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Select Function": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Get Last Message": {"main": [[{"node": "respondHubspotMessage1", "type": "main", "index": 0}]]}, "getHubspotMessage": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "OpenAi Create Thread": {"main": [[{"node": "createThread", "type": "main", "index": 0}]]}, "Completed, Action or Inprogress": {"main": [[{"node": "Get Last Message", "type": "main", "index": 0}], [{"node": "Select Function", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}}}