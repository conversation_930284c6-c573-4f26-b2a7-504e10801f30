{"nodes": [{"id": "293b70f0-06e8-4db5-befd-bfaed1f3575a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-460, 80], "parameters": {}, "typeVersion": 1}, {"id": "1c473546-6280-412d-9f8e-b43962365d78", "name": "Set Params", "type": "n8n-nodes-base.set", "position": [-160, -60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8b5c6ca0-5ca8-4f67-abc1-44341cf419bc", "name": "system_prompt", "type": "string", "value": "You are an n8n fanboy."}, {"id": "7c36c362-6269-4564-b6fe-f82126bc8f5e", "name": "user_prompt", "type": "string", "value": "What are the differences between n8n and Make?"}, {"id": "4366d2b5-ad22-445a-8589-fddab1caa1ab", "name": "domains", "type": "string", "value": "n8n.io, make.com"}]}}, "typeVersion": 3.4}, {"id": "894bd6a4-5db7-45fb-a8e0-1a81af068bbf", "name": "Clean Output", "type": "n8n-nodes-base.set", "position": [580, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5859093c-6b22-41db-ac6c-9a9f6f18b7e3", "name": "output", "type": "string", "value": "={{ $json.choices[0].message.content }}"}, {"id": "13208fff-5153-45a7-a1cb-fe49e32d9a03", "name": "citations", "type": "array", "value": "={{ $json.citations }}"}]}}, "typeVersion": 3.4}, {"id": "52d3a832-8c9b-4356-ad2a-377340678a58", "name": "Perplexity Request", "type": "n8n-nodes-base.httpRequest", "position": [240, 40], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"sonar\",\n \"messages\": [\n {\n \"role\": \"system\",\n \"content\": \"{{ $json.system_prompt }}\"\n },\n {\n \"role\": \"user\",\n \"content\": \"{{ $json.user_prompt }}\"\n }\n ],\n \"temperature\": 0.2,\n \"top_p\": 0.9,\n \"search_domain_filter\": {{ (JSON.stringify($json.domains.split(','))) }},\n \"return_images\": false,\n \"return_related_questions\": false,\n \"search_recency_filter\": \"month\",\n \"top_k\": 0,\n \"stream\": false,\n \"presence_penalty\": 0,\n \"frequency_penalty\": 1,\n \"response_format\": null\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpBasicAuth": {"id": "yEocL0NSpUWzMsHG", "name": "Perplexity"}, "httpHeaderAuth": {"id": "TngzgS09J1YvLIXl", "name": "Perplexity"}}, "typeVersion": 4.2}, {"id": "48657f2c-d1dd-4d7e-8014-c27748e63e58", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-140, -440], "parameters": {"width": 480, "height": 300, "content": "## Credentials Setup\n\n1/ Go to the perplexity dashboard, purchase some credits and create an API Key\n\nhttps://www.perplexity.ai/settings/api\n\n2/ In the perplexity Request node, use Generic Credentials, <PERSON><PERSON> Auth. \n\nFor the name, use the value \"Authorization\"\nAnd for the value \"Bearer pplx-e4...59ea\" (Your Perplexity Api Key)\n\n"}, "typeVersion": 1}, {"id": "e0daabee-c145-469e-93c2-c759c303dc2a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [100, 260], "parameters": {"color": 5, "width": 480, "height": 120, "content": "**Sonar Pro** is the current top model used by perplexity. \nIf you want to use a different one, check this page: \n\nhttps://docs.perplexity.ai/guides/model-cards"}, "typeVersion": 1}], "pinData": {}, "connections": {"Set Params": {"main": [[{"node": "Perplexity Request", "type": "main", "index": 0}]]}, "Perplexity Request": {"main": [[{"node": "Clean Output", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Params", "type": "main", "index": 0}]]}}}