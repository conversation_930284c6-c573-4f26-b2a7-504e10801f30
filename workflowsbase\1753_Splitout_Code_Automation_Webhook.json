{"id": "mKGMYXJottl0PDtM", "meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "name": "Write a WordPress post with AI (starting from a few keywords)", "tags": [], "nodes": [{"id": "a4f19a81-6101-48c2-9560-9cf231bc240b", "name": "Form", "type": "n8n-nodes-base.formTrigger", "position": [-580, 320], "webhookId": "4b937814-e829-4df7-aaba-31192babf7e1", "parameters": {"path": "create-wordpress-post", "formTitle": "Create a WordPress post with AI", "formFields": {"values": [{"fieldLabel": "Keywords (comma-separated)", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Number of chapters", "fieldOptions": {"values": [{"option": "1"}, {"option": "2"}, {"option": "3"}, {"option": "4"}, {"option": "5"}, {"option": "6"}, {"option": "7"}, {"option": "8"}, {"option": "9"}, {"option": "10"}]}, "requiredField": true}, {"fieldType": "number", "fieldLabel": "Max words count", "requiredField": true}]}, "responseMode": "responseNode", "formDescription": "Fill this form with the required information to create a draft post on WordPress"}, "typeVersion": 2}, {"id": "e4cf75f7-00e7-473a-a944-af635581715f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [209.98769233621147, 140], "parameters": {"color": 4, "width": 301.3874093724939, "height": 371.765663140765, "content": "## Data check"}, "typeVersion": 1}, {"id": "e949a487-6701-4650-b9be-08146b4e93ad", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [225.20535922952297, 200], "parameters": {"color": 7, "width": 272.8190508599808, "height": 80, "content": "Checks that the data returned by OpenAI is correct"}, "typeVersion": 1}, {"id": "662fe28b-c0b7-4aef-b99c-a8c4c641251c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1580, 140], "parameters": {"color": 5, "width": 282.3398199598652, "height": 371.7656631407652, "content": "## Draft on WordPress"}, "typeVersion": 1}, {"id": "85996d51-ab98-41f5-b525-d926f04f50a8", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1595, 200], "parameters": {"color": 7, "width": 254.77269221373095, "height": 80, "content": "The article is posted as a draft on WordPress"}, "typeVersion": 1}, {"id": "46f67505-f2dc-4110-b1d4-a27d7814cb52", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1881, 140], "parameters": {"color": 3, "width": 557.7592769264069, "height": 369.2595606183891, "content": "## Featured image"}, "typeVersion": 1}, {"id": "a1beeb4f-f171-4c6a-ac19-7086b09757ab", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1900, 200], "parameters": {"color": 7, "width": 517.9195082760601, "height": 80, "content": "The image is generated with Dall-E, uploaded to WordPress, and then connected to the post as its featured image"}, "typeVersion": 1}, {"id": "d1fd737b-7f14-4371-8720-7742f708e641", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-117.99507693448459, 200], "parameters": {"color": 7, "width": 287.370178643191, "height": 80, "content": "Starting from the given keywords, generates the article title, subtitle, chapters, and image prompt"}, "typeVersion": 1}, {"id": "ccaaf851-613b-4d0c-8b3d-99a35ec9cdad", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-129.93405171072595, 142], "parameters": {"color": 6, "width": 319.697690939268, "height": 370.512611879577, "content": "## Article structure"}, "typeVersion": 1}, {"id": "69bebd7b-8ad5-4b0d-a8df-1b2e6d4be96e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-640, 140], "parameters": {"color": 7, "width": 239.97343293577688, "height": 370.512611879577, "content": "## User form"}, "typeVersion": 1}, {"id": "2037f81b-189c-4dc4-a4dc-179e4283544c", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-623, 200], "parameters": {"color": 7, "width": 199.7721486302032, "height": 80, "content": "The user triggers the post creation"}, "typeVersion": 1}, {"id": "e8d7f711-185d-499b-ba58-de52ac6a4e58", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2461, 140], "parameters": {"color": 7, "width": 219.70753707029849, "height": 370.512611879577, "content": "## User feedback"}, "typeVersion": 1}, {"id": "d89bebca-3607-4c66-a13d-07c32262e01a", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2481, 200], "parameters": {"color": 7, "width": 183.38125554060056, "height": 80, "content": "Final confirmation to the user"}, "typeVersion": 1}, {"id": "7df452e2-52f3-4efe-94a4-7d4eab0670c8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [534.9876923362115, 530.9889231025903], "parameters": {"color": 7, "width": 281.2716777103785, "height": 288.4116890365125, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nUser is notified to try again since some data is missing"}, "typeVersion": 1}, {"id": "f881bcd9-c7d2-4a1c-bc1a-beb515d52ade", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-128.98646156983267, 532.991384635348], "parameters": {"color": 7, "width": 319.8306137081817, "height": 275.3956890735875, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWikipedia is used to write the article"}, "typeVersion": 1}, {"id": "1b788b37-b8b5-47f6-8198-547dac8c76d6", "name": "Settings", "type": "n8n-nodes-base.set", "position": [-320, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3a433b0f-9957-4b64-ad81-359ab5e521d5", "name": "wordpress_url", "type": "string", "value": "https://you-wordpress-url-here.com/"}, {"id": "ec5430e3-92c5-46e4-8c2c-c87291680892", "name": "keywords", "type": "string", "value": "={{ $json['Keywords (comma-separated)'] }}"}, {"id": "5defb0a2-d921-4909-b10d-da59e1768496", "name": "chapters", "type": "number", "value": "={{ $json['Number of chapters'] }}"}, {"id": "230ebd0b-73c2-4265-9b3c-57af7fbc48c8", "name": "words", "type": "number", "value": "={{ $json['Max words count'] }}"}]}}, "typeVersion": 3.3}, {"id": "af29ed91-84b5-43f8-b1ce-1c8dc35c2c1b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-377, 140], "parameters": {"color": 2, "width": 226.71615243495023, "height": 370.512611879577, "content": "## Settings"}, "typeVersion": 1}, {"id": "a6fe2238-22ba-4c54-adef-663bd3955dcc", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-360, 200], "parameters": {"color": 7, "width": 179.37633247508526, "height": 80, "content": "Set the URL of your WordPress here"}, "typeVersion": 1}, {"id": "358ac79f-be7d-44eb-a353-b2ad4ac8d582", "name": "Check data consistency", "type": "n8n-nodes-base.if", "position": [300, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9c8c53ea-6079-48da-9d6e-dd527167b123", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.title }}", "rightValue": ""}, {"id": "a7fabfe1-3539-453a-93d9-8d6d395c3de4", "operator": {"type": "array", "operation": "lengthGte", "rightType": "number"}, "leftValue": "={{ $json.message.content.chapters }}", "rightValue": "={{ 1 }}"}, {"id": "a687081e-24e2-423c-a2da-b7c18baf0715", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.subtitle }}", "rightValue": ""}, {"id": "0a435a69-3699-4b98-b46f-40954c7a7816", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.introduction }}", "rightValue": ""}, {"id": "1a440144-21f3-42bd-9222-774bd564f3ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.conclusions }}", "rightValue": ""}, {"id": "834ce92d-b1e9-48ef-ae63-1d0841c900b5", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.content.imagePrompt }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "479f474a-1687-4588-8485-d793afc6757d", "name": "Split out chapters", "type": "n8n-nodes-base.splitOut", "position": [600, 320], "parameters": {"options": {}, "fieldToSplitOut": "message.content.chapters"}, "typeVersion": 1}, {"id": "bde7b7db-45c6-4ab3-a705-358000cefbec", "name": "Merge chapters title and text", "type": "n8n-nodes-base.merge", "position": [1220, 460], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "0079022b-eaa2-481b-8c78-f8623a63645b", "name": "Final article text", "type": "n8n-nodes-base.code", "position": [1400, 320], "parameters": {"jsCode": "let article = \"\";\n\n// Introduction\narticle += $('Create post title and structure').first().json.message.content.introduction;\narticle += \"<br><br>\";\n\nfor (const item of $input.all()) {\n article += \"<strong>\" + item.json.title + \"</strong>\";\n article += \"<br><br>\";\n article += item.json.message.content;\n article += \"<br><br>\";\n}\n\n// Conclusions\narticle += \"<strong>Conclusions</strong>\";\narticle += \"<br><br>\";\narticle += $('Create post title and structure').first().json.message.content.conclusions;\n\n\nreturn [\n {\n \"article\": article\n }\n];"}, "typeVersion": 1}, {"id": "d892f00a-90fd-4bbb-bac6-4684d7d0c638", "name": "Post on Wordpress", "type": "n8n-nodes-base.wordpress", "position": [1680, 320], "parameters": {"title": "={{ $('Create post title and structure').all()[0].json.message.content.title }}", "additionalFields": {"status": "draft", "content": "={{ $json.article }}"}}, "credentials": {"wordpressApi": {"id": "xxxxxxxxxxx", "name": "WordPress Credentials"}}, "typeVersion": 1}, {"id": "a609d80d-f586-4e5f-a72d-01257f676574", "name": "Upload media", "type": "n8n-nodes-base.httpRequest", "position": [2120, 320], "parameters": {"url": "https://wp-demo.mondo.surf/wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "attachment; filename=\"example.jpg\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "xxxxxxxxxxx", "name": "WordPress Credentials"}}, "typeVersion": 4.1}, {"id": "bdb2ef52-0201-4fe1-a7a6-59e34e21bf5e", "name": "Set image ID for the post", "type": "n8n-nodes-base.httpRequest", "position": [2280, 320], "parameters": {"url": "=https://wp-demo.mondo.surf/wp-json/wp/v2/posts/{{ $('Post on Wordpress').item.json.id }}", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}]}, "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "xxxxxxxxxxx", "name": "WordPress Credentials"}}, "typeVersion": 4.1}, {"id": "a721762f-168d-4c87-ab6d-0d31deecd9a5", "name": "Respond: Success", "type": "n8n-nodes-base.respondToWebhook", "position": [2520, 320], "parameters": {"options": {}, "respondWith": "json", "responseBody": "={\n \"formSubmittedText\": \"The article {{ $json.title.rendered }} was correctly created as a draft on WordPress!\"\n}"}, "typeVersion": 1}, {"id": "51b79bc2-035d-4db8-87bb-db6c889b164e", "name": "Respond: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "position": [620, 580], "parameters": {"options": {}, "respondWith": "json", "responseBody": "={\n 'formSubmittedText': 'There was a problem creating the article, please refresh the form and try again!'\n}\n\n"}, "typeVersion": 1}, {"id": "d8748498-0800-4208-b993-f233d14da7b6", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [533.7711864406776, 140], "parameters": {"color": 2, "width": 225.47038972308582, "height": 370.512611879577, "content": "## Chapters split"}, "typeVersion": 1}, {"id": "4115de31-d4e9-4d77-a055-3dead31c4dc5", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [550.7711864406779, 200], "parameters": {"color": 7, "width": 185.6051460344073, "height": 80, "content": "Splits out chapter contents from the previous node"}, "typeVersion": 1}, {"id": "aff8edf6-4e1e-4522-86f7-f0ce88cd0cd4", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [792, 198], "parameters": {"color": 7, "width": 287.370178643191, "height": 80, "content": "Writes the text for each chapter"}, "typeVersion": 1}, {"id": "e45715a8-b1ca-4499-a16a-854f8bd4f370", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [780, 140], "parameters": {"color": 6, "width": 333.40108076977657, "height": 370.512611879577, "content": "## Chapters text"}, "typeVersion": 1}, {"id": "5c4cd7a1-7dc9-4159-9bd2-dbe5f8feb663", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [1138.423429009716, 140], "parameters": {"color": 4, "width": 420.4253447940705, "height": 514.2177254645992, "content": "## Content preparation"}, "typeVersion": 1}, {"id": "7a6d3f7d-0436-4844-b09a-37e805b95a2f", "name": "Sticky Note22", "type": "n8n-nodes-base.stickyNote", "position": [1160, 200], "parameters": {"color": 7, "width": 368.1523541074699, "height": 80, "content": "Merges the content and prepare it before sending it to WordPress"}, "typeVersion": 1}, {"id": "903b695d-015a-4956-9c63-45802dfb9fdb", "name": "Generate featured image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1940, 320], "parameters": {"prompt": "=Generate a photographic image to be used as the cover image for the article titled: {{ $('Create post title and structure').all()[0].json.message.content.title }}. This is the prompt for the image: {{ $('Create post title and structure').all()[0].json.message.content.imagePrompt }}, photography, realistic, sigma 85mm f/1.4", "options": {"size": "1792x1024", "style": "natural", "quality": "hd"}, "resource": "image"}, "credentials": {"openAiApi": {"id": "xxxxxxxxxxx", "name": "OpenAI Credentials"}}, "typeVersion": 1}, {"id": "faa847cb-9702-4207-aa1e-6d9f62493527", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [-20, 620], "parameters": {}, "typeVersion": 1}, {"id": "9d09c92e-11c0-4ea9-81d6-13bc9266741a", "name": "Create post title and structure", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-100, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4-1106-preview", "cachedResultName": "GPT-4-1106-PREVIEW"}, "options": {"maxTokens": 2048}, "messages": {"values": [{"content": "=Write the title, the subtitle, the chapters details, the introduction, the conclusions, and an image prompt for a SEO-friendly article about these topics:\n{{ $json.keywords }}.\n\nInstructions:\n- Place the article title in a JSON field called `title`\n- Place the subtitle in a JSON field called `subtitle`\n- Place the introduction in a JSON field called `introduction`\n- In the introduction introduce the topic that is then explored in depth in the rest of the text\n- The introduction should be around 60 words\n- Place the conclusions in a JSON field called `conclusions`\n- The conclusions should be around 60 words\n- Use the conclusions to sum all said in the article and offer a conclusion to the reader\n- The image prompt will be used to produce a photographic cover image for the article and should depict the topics discussed in the article\n- Place the image prompt in a JSON field called `imagePrompt`\n- There should be {{ $json.chapters.toString() }} chapters.\n- For each chapter provide a title and an exaustive prompt that will be used to write the chapter text.\n- Place the chapters in an array field called `chapters`\n- For each chapter provide the fields `title` and `prompt`\n- The chapters should follow a logical flow and not repeat the same concepts.\n- The chapters should be one related to the other and not isolated blocks of text. The text should be fluent and folow a linear logic.\n- Don't start the chapters with \"Chapter 1\", \"Chapter 2\", \"Chapter 3\"... just write the title of the chapter\n- For the title and the capthers' titles don't use colons (`:`)\n- For the text, use HTML for formatting, but limited to bold, italic and lists.\n- Don't use markdown for formatting.\n- Always search on Wikipedia for useful information or verify the accuracy of what you write.\n- Never mention it if you don't find information on Wikipedia or the web\n- Go deep in the topic you treat, don't just throw some superficial info"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "xxxxxxxxxxx", "name": "OpenAI Credentials"}}, "typeVersion": 1}, {"id": "2ecd3a50-a34f-4ab9-ad31-e4e6608708fb", "name": "Create chapters text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [820, 320], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4-0125-preview", "cachedResultName": "GPT-4-0125-PREVIEW"}, "options": {"maxTokens": 2048}, "messages": {"values": [{"content": "=Write a chapter for the article: {{ $('Create post title and structure').item.json.message.content.title }}, {{ $('Create post title and structure').item.json.message.content.subtitle }}, that talks about {{ $('Settings').item.json[\"keywords\"] }}\n\nThis is the prompt for the chapter titled {{ $json.title }}: {{ $json.prompt }}.\n\nGuidelines:\n- Just return the plain text for each chapter (no JSON structure).\n- Don't use markdown for formatting.\n- Use HTML for formatting, but limited to bold, italic and lists.\n- Don't add internal titles or headings.\n- The length of each chapther should be around {{ Math.round(($('Settings').item.json.words - 120)/ $('Settings').item.json.chapters) }} words long\n- Go deep in the topic you treat, don't just throw some superficial info\n{{ $itemIndex > 0 ? \"- The previous chapter talks about \" + $input.all()[$itemIndex-1].json.title : \"\" }}\n{{ $itemIndex > 0 ? \"- The promt for the previous chapter is \" + $input.all()[$itemIndex-1].json.prompt : \"\" }}\n{{ $itemIndex < $input.all().length ? \"- The following chapter will talk about \" + $input.all()[$itemIndex+1].json.title: \"\" }}\n{{ $itemIndex < $input.all().length ? \"- The prompt for the following chapter is \" + $input.all()[$itemIndex+1].json.prompt : \"\" }}\n- Consider the previous and following chapters what writing the text for this chapter. The text must be coherent with the previous and following chapters.\n- This chapter should not repeat the concepts already exposed in the previous chapter.\n- This chapter is a part of a larger article so don't include an introduction or conclusions. This chapter should merge with the rest of the article.\n"}]}}, "credentials": {"openAiApi": {"id": "xxxxxxxxxxx", "name": "OpenAI Credentials"}}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true, "saveDataSuccessExecution": "all"}, "versionId": "64d94f1e-51c8-40f7-a6b3-80fc43d9e71a", "connections": {"Form": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "Settings": {"main": [[{"node": "Create post title and structure", "type": "main", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "Create post title and structure", "type": "ai_tool", "index": 0}]]}, "Upload media": {"main": [[{"node": "Set image ID for the post", "type": "main", "index": 0}]]}, "Post on Wordpress": {"main": [[{"node": "Generate featured image", "type": "main", "index": 0}]]}, "Final article text": {"main": [[{"node": "Post on Wordpress", "type": "main", "index": 0}]]}, "Split out chapters": {"main": [[{"node": "Merge chapters title and text", "type": "main", "index": 1}, {"node": "Create chapters text", "type": "main", "index": 0}]]}, "Create chapters text": {"main": [[{"node": "Merge chapters title and text", "type": "main", "index": 0}]]}, "Check data consistency": {"main": [[{"node": "Split out chapters", "type": "main", "index": 0}], [{"node": "Respond: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate featured image": {"main": [[{"node": "Upload media", "type": "main", "index": 0}]]}, "Set image ID for the post": {"main": [[{"node": "Respond: Success", "type": "main", "index": 0}]]}, "Merge chapters title and text": {"main": [[{"node": "Final article text", "type": "main", "index": 0}]]}, "Create post title and structure": {"main": [[{"node": "Check data consistency", "type": "main", "index": 0}]]}}}