{"\"nodes\"": "[", "\"id\"": "\"a80e6528-cf79-4229-8c58-6856fd86b6e7\",", "\"name\"": "\"Sticky Note6\",", "\"type\"": "\"main\",", "\"position\"": "[", "\"parameters\"": "{", "\"fileId\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"list\",", "\"value\"": "\"={\\n \\\"name\\\": \\\"generate_schema\\\",\\n \\\"description\\\": \\\"Generate schema for an array of objects representing items with their descriptions, quantities, unit prices, and amounts.\\\",\\n \\\"strict\\\": true,\\n \\\"schema\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"required\\\": [\\n \\\"items\\\"\\n ],\\n \\\"properties\\\": {\\n \\\"items\\\": {\\n \\\"type\\\": \\\"array\\\",\\n \\\"description\\\": \\\"Array of item objects\\\",\\n \\\"items\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"required\\\": [\\n \\\"description\\\",\\n \\\"qty\\\",\\n \\\"unit_price\\\",\\n \\\"amount\\\"\\n ],\\n \\\"properties\\\": {\\n \\\"description\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Description of the item\\\"\\n },\\n \\\"qty\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Quantity of the item\\\"\\n },\\n \\\"unit_price\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Unit price of the item formatted as a string\\\"\\n },\\n \\\"amount\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Total amount for the item formatted as a string\\\"\\n }\\n },\\n \\\"additionalProperties\\\": false\\n }\\n }\\n },\\n \\\"additionalProperties\\\": false\\n }\\n}\"", "\"options\"": "{},", "\"operation\"": "\"create\"", "\"credentials\"": "{", "\"googleDriveOAuth2Api\"": "{", "\"typeVersion\"": "1", "\"url\"": "\"=https://api.openai.com/v1/chat/completions\",", "\"method\"": "\"POST\",", "\"sendBody\"": "true,", "\"contentType\"": "\"multipart-form-data\",", "\"sendHeaders\"": "true,", "\"bodyParameters\"": "{", "\"parameterType\"": "\"formBinaryData\",", "\"inputDataFieldName\"": "\"data\"", "\"headerParameters\"": "{", "\"event\"": "\"fileCreated\",", "\"pollTimes\"": "{", "\"item\"": "[", "\"triggerOn\"": "\"specificFolder\",", "\"folderToWatch\"": "{", "\"cachedResultUrl\"": "\"https://airtable.com/appndgSF4faN4jPXi/tblIuVR9ocAomznzK\",", "\"cachedResultName\"": "\"Line Items\"", "\"base\"": "{", "\"table\"": "{", "\"columns\"": "{", "\"schema\"": "[", "\"display\"": "true,", "\"removed\"": "false,", "\"readOnly\"": "false,", "\"required\"": "false,", "\"displayName\"": "\"Invoices\",", "\"defaultMatch\"": "false,", "\"canBeUsedToMatch\"": "true", "\"mappingMode\"": "\"defineBelow\",", "\"matchingColumns\"": "[]", "\"airtableTokenApi\"": "{", "\"Qty\"": "\"={{ $json.qty }}\",", "\"Amount\"": "\"={{ parseFloat($json.amount.replace('$', '').trim()) }}\",", "\"Invoices\"": "\"=[\\\"{{ $('Create Invoice').item.json.id }}\\\"]\",", "\"Unit price\"": "\"={{ parseFloat($json.unit_price.replace('$', '').trim()) }}\",", "\"Description\"": "\"={{ $json.description }}\"", "\"jsonBody\"": "\"={\\n \\\"model\\\": \\\"gpt-4o-mini\\\",\\n \\\"messages\\\": [\\n {\\n \\\"role\\\": \\\"system\\\",\\n \\\"content\\\": {{ JSON.stringify($('Set Fields').item.json.prompt) }}\\n },\\n {\\n \\\"role\\\": \\\"user\\\",\\n \\\"content\\\": {{ JSON.stringify( JSON.stringify($('Webhook').item.json.body.json[0].items) ) }}\\n }\\n ],\\n \\\"response_format\\\":{ \\\"type\\\": \\\"json_schema\\\", \\\"json_schema\\\": {{ $('Set Fields').item.json.schema }}\\n\\n }\\n }\",", "\"specifyBody\"": "\"json\",", "\"authentication\"": "\"predefinedCredentialType\",", "\"nodeCredentialType\"": "\"openAiApi\"", "\"openAiApi\"": "{", "\"assignments\"": "[", "\"jsCode\"": "\"// Get the input from the \\\"OpenAI - Extract Line Items\\\" node\\nconst input = $(\\\"OpenAI - Extract Line Items\\\").first().json;\\n\\n// Initialize an array for the output\\nconst outputItems = [];\\n\\n// Navigate to the 'content' field in the choices array\\nconst content = input.choices[0]?.message?.content;\\n\\nif (content) {\\n try {\\n // Parse the stringified JSON in the 'content' field\\n const parsedContent = JSON.parse(content);\\n\\n // Extract 'items' and add them to the output array\\n if (Array.isArray(parsedContent.items)) {\\n outputItems.push(...parsedContent.items.map(i => ({ json: i })));\\n }\\n } catch (error) {\\n // Handle any parsing errors\\n console.error('Error parsing content:', error);\\n }\\n}\\n\\n// Return the extracted items\\nreturn outputItems;\\n\"", "\"webhookId\"": "\"0f7f5ebb-8b66-453b-a818-20cc3647c783\",", "\"path\"": "\"0f7f5ebb-8b66-453b-a818-20cc3647c783\",", "\"httpMethod\"": "\"POST\"", "\"width\"": "280,", "\"height\"": "626,", "\"content\"": "\"### Set up steps\\n\\n1. **Google Drive Trigger**: \\n - Set up a trigger to detect new files in a specified folder dedicated to invoices.\\n\\n2. **File Upload to LlamaParse**: \\n - Create an HTTP request that sends the invoice file to LlamaParse for parsing, including relevant header settings and webhook URL.\\n\\n3. **Webhook Processing**: \\n - Establish a webhook node to handle parsed results from LlamaParse, extracting needed invoice details effectively.\\n\\n4. **Invoice Record Creation**: \\n - Create initial records for invoices in your database using the parsed details received from the webhook.\\n\\n5. **Line Item Processing**: \\n - Transform string data into structured line item arrays and create individual records for each item linked to the main invoice.\"", "\"color\"": "7,", "\"pinData\"": "{},", "\"connections\"": "{", "\"Webhook\"": "{", "\"main\"": "[", "\"node\"": "\"Create Invoice\",", "\"index\"": "0", "\"Set Fields\"": "{", "\"Google Drive\"": "{", "\"Create Invoice\"": "{", "\"Process Line Items\"": "{", "\"Google Drive Trigger\"": "{", "\"OpenAI - Extract Line Items\"": "{"}