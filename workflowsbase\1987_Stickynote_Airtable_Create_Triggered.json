{"id": "uLHpFu2ndN6ZKClZ", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa", "templateCredsSetupCompleted": true}, "name": "Sync New Files From Google Drive with Airtable", "tags": [{"id": "uScnF9NzR3PLIyvU", "name": "Published", "createdAt": "2025-03-21T07:22:28.491Z", "updatedAt": "2025-03-21T07:22:28.491Z"}], "nodes": [{"id": "f648b663-8adb-4587-bf80-cff7554b72c4", "name": "Share File with Recipient", "type": "n8n-nodes-base.googleDrive", "notes": "Share File via Email", "position": [660, -20], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "share", "permissionsUi": {"permissionsValues": {"role": "writer", "type": "user", "emailAddress": "<EMAIL>"}}}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 3}, {"id": "29c9dacf-e9fa-49b7-81e5-0416dbdbc9ba", "name": " Log File Metadata", "type": "n8n-nodes-base.airtable", "notes": "Store File Metadata", "position": [940, -160], "parameters": {"base": {"__rl": true, "mode": "url", "value": ""}, "table": {"__rl": true, "mode": "url", "value": ""}, "columns": {"value": {"FileId": "={{ $('Google Drive').item.json.id }}", "sentId": "={{ $json.id }}", "FileName": "={{ $('Google Drive').item.json.name }}", "CreatedTime": "={{ $('Google Drive').item.json.createdTime }}", "ModifiedTime": "={{ $('Google Drive').item.json.modifiedTime }}"}, "schema": [{"id": "FileName", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "FileName", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "FileId", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "FileId", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CreatedTime", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CreatedTime", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ModifiedTime", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "ModifiedTime", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sentId", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "sentId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 2.1}, {"id": "f2a4c6af-cf00-4549-88af-1a3e125508d6", "name": "Google Drive", "type": "n8n-nodes-base.googleDriveTrigger", "notes": "Fetch New File", "position": [420, -180], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "url", "value": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 1}, {"id": "14da3a1a-def0-4718-8456-f3f11c0fb238", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [400, -20], "parameters": {"width": 150, "height": 140, "content": "This node retrieves the newly added file from the specified folder in Google Drive."}, "typeVersion": 1}, {"id": "d9224406-31e5-46a6-a2da-56effb86c8eb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, -180], "parameters": {"width": 170, "height": 140, "content": "This node sends the fetched file via email to the specified recipient."}, "typeVersion": 1}, {"id": "cad9869a-cf58-4786-8d0a-d696bf3a0c84", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [920, 0], "parameters": {"width": 180, "content": "This node stores the file’s metadata (name, ID, creation time, modification time, and recipient email) in Airtable."}, "typeVersion": 1}, {"id": "0f6c1ffc-7d9e-41ee-b5f4-ee65f792222e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [320, -240], "parameters": {"width": 860, "height": 420, "content": "### Automatic File Upload & Sharing Workflow with Google Drive & Airtable Integration\n\n"}, "typeVersion": 1}, {"id": "99acf1d1-ce4e-4942-bb6c-d053ef886a29", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [320, 220], "parameters": {"width": 860, "height": 120, "content": "### Description:\nThis workflow automatically fetches newly uploaded files from a specific folder in Google Drive, shares them via email with specified recipients, and logs the file details (name, ID, created time, modified time) into Airtable for easy tracking. It streamlines the process of file sharing and management while keeping track of important metadata in a central place.)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c4ff2316-a648-4cd2-9af8-b29c29115ac6", "connections": {"Google Drive": {"main": [[{"node": "Share File with Recipient", "type": "main", "index": 0}]]}, "Share File with Recipient": {"main": [[{"node": " Log File Metadata", "type": "main", "index": 0}]]}}}