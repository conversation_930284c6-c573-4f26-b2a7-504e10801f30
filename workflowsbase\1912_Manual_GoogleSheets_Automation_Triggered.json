{"id": "lifB7iUXlDzr5dmI", "meta": {"instanceId": "660cf2c29eb19fa42319afac3bd2a4a74c6354b7c006403f6cba388968b63f5d", "templateCredsSetupCompleted": true}, "name": "LinkedIn Profile Discovery", "tags": [{"id": "a8B9vqj0vNLXcKVQ", "name": "template", "createdAt": "2025-04-04T15:38:37.785Z", "updatedAt": "2025-04-04T15:38:37.785Z"}], "nodes": [{"id": "9ae64a3a-c7e7-45ca-88ee-ebf6144f3197", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "a22416bb-ef9e-422f-b480-cd52d8c93bfa", "name": "Person info", "type": "n8n-nodes-base.googleSheets", "position": [220, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E/edit?usp=drivesdk", "cachedResultName": "Linkedin Profile URLs"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "CwpCAR1HwgHZpRtJ", "name": "Google Drive"}}, "typeVersion": 4.5}, {"id": "a4699dd8-54ef-478e-9ff8-2c2046ad6ea8", "name": "Search profile", "type": "n8n-nodes-base.airtop", "notes": "This could take a few minutes depending on the number of rows", "position": [440, 0], "parameters": {"url": "=https://www.google.com/search?q={{ encodeURI($json['Person Info']) }}", "prompt": "=This is Google Search results. the first results should be the Linkedin Page of {{ $json['Person Info'] }} \nReturn the Linkedin URL and nothing else.\nIf you cannot find the Linkedin URL, return an empty string. \nA valid Linkedin profile URL starts with \"https://www.linkedin.com/in/\"", "resource": "extraction", "operation": "query", "sessionMode": "new", "additionalFields": {}}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "Airtop"}}, "notesInFlow": true, "typeVersion": 1}, {"id": "2dd4d350-743e-48a7-ab69-d0996bc46f49", "name": "Parse response", "type": "n8n-nodes-base.code", "position": [660, 0], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const linkedInProfile = $json.data.modelResponse\nconst rowData = $('Person info').item.json\n\nreturn { json: {\n  ...rowData,\n  'LinkedIn URL': linkedInProfile\n}};"}, "typeVersion": 2}, {"id": "3efc182a-8707-4c8d-8263-a2aebe62b0a7", "name": "Update row", "type": "n8n-nodes-base.googleSheets", "position": [880, 0], "parameters": {"columns": {"value": {}, "schema": [{"id": "Person Info", "type": "string", "display": true, "required": false, "displayName": "Person Info", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Linkedin URL", "type": "string", "display": true, "required": false, "displayName": "Linkedin URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Validated", "type": "string", "display": true, "required": false, "displayName": "Validated", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1rjlKzphEbknNh_ToS9pR_dP_Tw93FsxDte5AI4LH5_E/edit?usp=drivesdk", "cachedResultName": "Linkedin Profile URLs"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "CwpCAR1HwgHZpRtJ", "name": "Google Drive"}}, "typeVersion": 4.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "97cd5141-63d5-4ece-83eb-e544455097d3", "connections": {"Person info": {"main": [[{"node": "Search profile", "type": "main", "index": 0}]]}, "Parse response": {"main": [[{"node": "Update row", "type": "main", "index": 0}]]}, "Search profile": {"main": [[{"node": "Parse response", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Person info", "type": "main", "index": 0}]]}}}