{"id": "JiSesGjDIXIPYtbt", "meta": {"instanceId": "e2c978396c9c745cf0aaa9ed3abe4464dbcef93c5fe2df809b9e14440e628df6"}, "name": "Shopify + Mautic", "tags": [], "nodes": [{"id": "592b2608-e77e-4988-8f77-8820645b56ee", "name": "Shopify Trigger", "type": "n8n-nodes-base.shopifyTrigger", "position": [540, 320], "webhookId": "252052f3-e844-4500-8c34-c57f583d4432", "parameters": {"topic": "customers/update", "authentication": "accessToken"}, "credentials": {"shopifyAccessTokenApi": {"id": "WbxXaLMHozAgY3Rz", "name": "Shopify Access Token account"}}, "typeVersion": 1}, {"id": "f2e6931d-0279-4142-9b9e-1092657d91e2", "name": "Accepts Marketing?", "type": "n8n-nodes-base.if", "position": [1240, 180], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8e207eec-212a-48a9-b64e-2d15094c987f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Shopify Trigger').item.json.email_marketing_consent.state }}", "rightValue": "subscribed"}]}}, "typeVersion": 2}, {"id": "2a31657b-f1b3-4e4a-8f6a-3d3ee15aa052", "name": "Accepts Marketing?1", "type": "n8n-nodes-base.if", "position": [1240, 500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8e207eec-212a-48a9-b64e-2d15094c987f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Shopify Trigger').item.json.email_marketing_consent.state }}", "rightValue": "subscribed"}]}}, "typeVersion": 2}, {"id": "266a07ca-915a-4afd-9a0a-60eb974ee263", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [1540, 720], "parameters": {}, "typeVersion": 1}, {"id": "b9aee974-4d75-4b1e-80b7-8aab9d4f403d", "name": "Add to confirmed segment", "type": "n8n-nodes-base.mautic", "position": [1840, 40], "parameters": {"resource": "contactSegment", "contactId": "={{ $json.id }}", "segmentId": 1, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": {"id": "cBmAMa80nofah9QG", "name": "Mautic account"}}, "typeVersion": 1}, {"id": "a0fbd9e7-a60d-430b-b5df-4ec063e181c9", "name": "Remove from confirmed segment", "type": "n8n-nodes-base.mautic", "position": [1840, 300], "parameters": {"resource": "contactSegment", "contactId": "={{ $json.id }}", "operation": "remove", "segmentId": 1, "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": {"id": "cBmAMa80nofah9QG", "name": "Mautic account"}}, "typeVersion": 1}, {"id": "0bc8fc06-3bf5-4c49-acc2-1ed499a07ea1", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [540, 1500], "webhookId": "6485fca6-c641-4067-b19a-192709b88e45", "parameters": {"path": "6485fca6-c641-4067-b19a-192709b88e45", "options": {"rawBody": true}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "001eff12-6f14-4fb2-afdd-e6b7956437bf", "name": "Crypto", "type": "n8n-nodes-base.crypto", "position": [760, 1500], "parameters": {"type": "SHA256", "action": "hmac", "secret": "a031f434181d2bce0a81694c11fafb5887c78a48c50da98d62b7ab7c6d57080c", "encoding": "base64", "binaryData": true}, "typeVersion": 1}, {"id": "6cde7c3c-e1f9-4584-908c-9b4a82e4f2a4", "name": "If", "type": "n8n-nodes-base.if", "position": [980, 1500], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "eb322a60-a1e6-46ed-b7b9-539373f9d881", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.headers['webhook-signature'] }}", "rightValue": "={{ $json.data }}"}]}}, "typeVersion": 2}, {"id": "e848a1d3-2f9b-4d27-8862-b3284654f818", "name": "No Operation, do nothing1", "type": "n8n-nodes-base.noOp", "position": [1260, 1700], "parameters": {}, "typeVersion": 1}, {"id": "ff27ee31-c13d-4a52-a260-aef9df8c64a6", "name": "GraphQL", "type": "n8n-nodes-base.graphql", "position": [1260, 1480], "parameters": {"query": "=query {\n  customers(first: 1, query: \"email:'{{ $json[\"body\"][\"mautic.lead_channel_subscription_changed\"][0][\"contact\"][\"fields\"][\"core\"][\"email\"][\"value\"] }}'\") {\n    edges {\n      node {\n        id\n        state\n      }\n    }\n  }\n}\n", "endpoint": "=https://{{ $('Set Shopify Subdomain').params[\"fields\"][\"values\"][0][\"stringValue\"] }}.myshopify.com/admin/api/2024-01/graphql.json", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "Z98cM8akgh1jPtG7", "name": "Header Auth  <PERSON>"}}, "typeVersion": 1}, {"id": "168b255b-f796-426c-80fd-c2fe79ef973f", "name": "Marketing Consent - subscribed", "type": "n8n-nodes-base.graphql", "position": [2120, 1160], "parameters": {"query": "mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {\n  customerEmailMarketingConsentUpdate(input: $input) {\n    customer {\n      id\n    }\n    userErrors {\n      field\n      message\n    }\n  }\n}\n", "endpoint": "=https://{{ $('Set Shopify Subdomain').params[\"fields\"][\"values\"][0][\"stringValue\"] }}.myshopify.com/admin/api/2024-01/graphql.json", "variables": "={\n  \"input\": {\n    \"customerId\": \"{{ $json[\"data\"][\"customers\"][\"edges\"][0][\"node\"][\"id\"] }}\",\n    \"emailMarketingConsent\": {\n      \"consentUpdatedAt\": \"{{ $now }}\",\n      \"marketingOptInLevel\": \"CONFIRMED_OPT_IN\",\n      \"marketingState\": \"SUBSCRIBED\"\n    }\n  }\n}\n", "requestFormat": "json", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "Z98cM8akgh1jPtG7", "name": "Header Auth  <PERSON>"}}, "typeVersion": 1}, {"id": "f7db8ec3-27d4-41f7-a0c9-6249bf6e805a", "name": "Marketing Consent - unsubscribed", "type": "n8n-nodes-base.graphql", "position": [2120, 1500], "parameters": {"query": "mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {\n  customerEmailMarketingConsentUpdate(input: $input) {\n    customer {\n      id\n    }\n    userErrors {\n      field\n      message\n    }\n  }\n}\n", "endpoint": "=https://{{ $('Set Shopify Subdomain').params[\"fields\"][\"values\"][0][\"stringValue\"] }}.myshopify.com/admin/api/2024-01/graphql.json", "variables": "={\n  \"input\": {\n    \"customerId\": \"{{ $json[\"data\"][\"customers\"][\"edges\"][0][\"node\"][\"id\"] }}\",\n    \"emailMarketingConsent\": {\n      \"consentUpdatedAt\": \"{{ $now }}\",\n      \"marketingOptInLevel\": \"CONFIRMED_OPT_IN\",\n      \"marketingState\": \"UNSUBSCRIBED\"\n    }\n  }\n}\n", "requestFormat": "json", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "Z98cM8akgh1jPtG7", "name": "Header Auth  <PERSON>"}}, "typeVersion": 1}, {"id": "ff62697d-29d0-4b7e-b695-cd90535e5fa8", "name": "Mautic - Accepts Marketing?", "type": "n8n-nodes-base.if", "position": [1820, 1180], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "960e386c-4198-4b04-88a6-715162729064", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Webhook').item.json.body['mautic.lead_channel_subscription_changed'][0].new_status }}", "rightValue": "contactable"}]}}, "typeVersion": 2}, {"id": "8515256f-c49b-4000-9a1e-be9958720dd6", "name": "Customer exists?", "type": "n8n-nodes-base.if", "position": [1540, 1480], "parameters": {"options": {"looseTypeValidation": false}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "4d2f02ab-f5e3-437d-8a35-f301888a8597", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.data.customers.edges }}", "rightValue": 0}]}}, "typeVersion": 2}, {"id": "616ae364-82a0-456a-abaf-b8855ca56f8f", "name": "No Operation, do nothing2", "type": "n8n-nodes-base.noOp", "position": [1820, 1640], "parameters": {}, "typeVersion": 1}, {"id": "afdeb330-06d0-4fdf-9f42-1d8ce74d483d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [540, 1680], "parameters": {"width": 580.807881773399, "content": "## Webhook Validation\nWe use the same key shared with Mautic to hash the incoming request. If the computed hash is identical to the one delivered the request is valid and can be processed"}, "typeVersion": 1}, {"id": "38633539-cc9a-4bde-92f7-39e697bd803f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2040, 40], "parameters": {"width": 279.1188177339898, "height": 383.7477832512317, "content": "## Mautic Segments\nThe n8n Shopify node cannot Please make sure to select your segment on both nodes"}, "typeVersion": 1}, {"id": "4107ce14-58bc-4171-87bb-5fc74a506e38", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1080, 1300], "parameters": {"width": 279.1188177339898, "content": "## Shopify \nThe n8n Shopify node cannot get customer marketing consent, so we get this from the Shopify GraphQL API"}, "typeVersion": 1}, {"id": "33f97be7-a4bf-42ee-96ae-dbbe54f5248f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [580, 1140], "parameters": {"width": 279.1188177339898, "content": "## Set your Shopify Subdomain here"}, "typeVersion": 1}, {"id": "7f203946-a365-44a3-8de0-1fe0b1964eab", "name": "Set Shopify Subdomain", "type": "n8n-nodes-base.set", "position": [760, 1320], "parameters": {"fields": {"values": [{"name": "Shopify Subdomain", "stringValue": "n8n-mautic-demo"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "544a4da2-11a4-465b-a30e-02ed618a77a6", "name": "Search for Contact by Email", "type": "n8n-nodes-base.mautic", "position": [760, 320], "parameters": {"limit": 1, "options": {"search": "={{ $json.email }}"}, "operation": "getAll", "authentication": "oAuth2"}, "credentials": {"mauticOAuth2Api": {"id": "cBmAMa80nofah9QG", "name": "Mautic account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "1332db6d-99ab-4d38-89b8-7f7ba25d4cf1", "name": "Contact exists?", "type": "n8n-nodes-base.if", "position": [980, 320], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5872c5d4-655f-4cd6-9b21-622acdcb0814", "operator": {"type": "number", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.id }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "488494df-6e8c-4f8c-b7ed-5d6d67f083c8", "name": "Create a new contact", "type": "n8n-nodes-base.mautic", "position": [1540, 480], "parameters": {"email": "={{ $('Shopify Trigger').item.json.email }}", "company": "=", "options": {}, "lastName": "={{ $('Shopify Trigger').item.json.last_name }}", "firstName": "={{ $('Shopify Trigger').item.json.first_name }}", "authentication": "oAuth2", "additionalFields": {}}, "credentials": {"mauticOAuth2Api": {"id": "cBmAMa80nofah9QG", "name": "Mautic account"}}, "typeVersion": 1}, {"id": "586c736b-cd46-4c5c-ac2f-c4380cf465bb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [0, 1420], "parameters": {"color": 4, "width": 360.************, "height": 315.*************, "content": "## Mautic to Shopify\n\nThis part uses GraphQL calls to the Shopify Admin API. In order to get a better understanding for the queries and mutations please check the API Docs.\n\nExamples from the Shopify Docs used in this workflow:\n\n[Get the first ten customers with an enabled customer account](https://shopify.dev/docs/api/admin-graphql/2024-01/queries/customers#examples-Get_the_first_ten_customers_with_an_enabled_customer_account)\n\n\n[Mutation to update the Email Marketing consent](https://shopify.dev/docs/api/admin-graphql/2024-01/mutations/customerEmailMarketingConsentUpdate)"}, "typeVersion": 1}, {"id": "47e8a011-b2ba-4839-ac85-24dd45e18020", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [20, 220], "parameters": {"color": 4, "width": 360.************, "height": 315.*************, "content": "## Shopify to Mautic\n\nThis part uses the Shopify Trigger to watch for changes on customer data.\n\nTo use the Mautic nodes please make sure you have the API enabled in your Mautic Instance."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "71208adf-ebfb-4961-b23b-fb46c86ca09f", "connections": {"If": {"main": [[{"node": "GraphQL", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Crypto": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "GraphQL": {"main": [[{"node": "Customer exists?", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Crypto", "type": "main", "index": 0}, {"node": "Set Shopify Subdomain", "type": "main", "index": 0}]]}, "Contact exists?": {"main": [[{"node": "Accepts Marketing?", "type": "main", "index": 0}], [{"node": "Accepts Marketing?1", "type": "main", "index": 0}]]}, "Shopify Trigger": {"main": [[{"node": "Search for Contact by Email", "type": "main", "index": 0}]]}, "Customer exists?": {"main": [[{"node": "Mautic - Accepts Marketing?", "type": "main", "index": 0}], [{"node": "No Operation, do nothing2", "type": "main", "index": 0}]]}, "Accepts Marketing?": {"main": [[{"node": "Add to confirmed segment", "type": "main", "index": 0}], [{"node": "Remove from confirmed segment", "type": "main", "index": 0}]]}, "Accepts Marketing?1": {"main": [[{"node": "Create a new contact", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Create a new contact": {"main": [[{"node": "Add to confirmed segment", "type": "main", "index": 0}]]}, "Mautic - Accepts Marketing?": {"main": [[{"node": "Marketing Consent - subscribed", "type": "main", "index": 0}], [{"node": "Marketing Consent - unsubscribed", "type": "main", "index": 0}]]}, "Search for Contact by Email": {"main": [[{"node": "Contact exists?", "type": "main", "index": 0}]]}}}