{"id": "grxwlyzZb3z4WLAa", "meta": {"instanceId": "6d46e25379ef430a7067964d1096b885c773564549240cb3ad4c087f6cf94bd3", "templateCredsSetupCompleted": true}, "name": "MCP_CALENDAR", "tags": [], "nodes": [{"id": "10e49f09-5ef8-4945-adcf-f8b99879a31c", "name": "MCP_CALENDAR", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [0, 0], "webhookId": "ceb17fa5-1937-405f-8000-ea3be7d2b032", "parameters": {"path": "/mcp/:tool/calendar"}, "typeVersion": 1}, {"id": "54e84792-4f4a-4501-8aae-e40f06e958c1", "name": "GET_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [860, 240], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "operation": "get"}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "c428d7b1-aed4-4a18-962e-fd29b8a2ac54", "name": "GET_ALL_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [240, 240], "parameters": {"options": {"orderBy": "startTime", "recurringEventHandling": "expand"}, "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', ``, 'string') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "operation": "getAll", "returnAll": true}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "26fef8a3-5802-4f3d-ae47-b81aad813728", "name": "DELETE_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [480, 240], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "operation": "delete", "descriptionType": "manual"}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "e46ea1b3-8597-46aa-b37a-6660aa72f74d", "name": "UPDATE_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [680, 240], "parameters": {"eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "operation": "update", "updateFields": {}, "useDefaultReminders": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Use_Default_Reminders', ``, 'boolean') }}"}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "b9c7618d-b79a-4273-a540-3d21a1c0bfb0", "name": "AVALIABILITY_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [80, 240], "parameters": {"options": {"timezone": {"__rl": true, "mode": "list", "value": "America/Sao_Paulo", "cachedResultName": "America/Sao_Paulo"}}, "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', ``, 'string') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "resource": "calendar", "descriptionType": "manual", "toolDescription": "verifica disponibilidade"}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}, {"id": "4fda260a-4d0c-4bf3-807b-e752f06037ff", "name": "CREATE_CALENDAR", "type": "n8n-nodes-base.googleCalendarTool", "position": [1000, 240], "parameters": {"end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "ODONTOLOGIA"}, "descriptionType": "manual", "toolDescription": "CRIA EVENTOS NOVOS COM O GOOGLE API", "additionalFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}"}, "useDefaultReminders": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Use_Default_Reminders', ``, 'boolean') }}"}, "credentials": {"googleCalendarOAuth2Api": {"id": "49eGhpwvfLcCZ0h3", "name": "Google Calendar account"}}, "typeVersion": 1.3}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d13dc7da-f510-474c-87be-68fea85c81f2", "connections": {"GET_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}, "CREATE_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}, "DELETE_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}, "UPDATE_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}, "GET_ALL_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}, "AVALIABILITY_CALENDAR": {"ai_tool": [[{"node": "MCP_CALENDAR", "type": "ai_tool", "index": 0}]]}}}