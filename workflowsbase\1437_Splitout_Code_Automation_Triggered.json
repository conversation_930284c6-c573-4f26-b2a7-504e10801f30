{"id": "dDAqkobn2pqgdl2N", "meta": {"instanceId": "9e331a89ae45a204c6dee51c77131d32a8c962ec20ccf002135ea60bd285dba9"}, "name": "AI Logo Sheet Extractor to Airtable", "tags": [], "nodes": [{"id": "f7ecadb8-dc5d-4e8c-96b8-52c1dbad49b6", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-660, -220], "webhookId": "43837a27-f752-40a8-852a-d5d63d647bfd", "parameters": {"options": {"path": "logo-sheet-feeder"}, "formTitle": "AI Logo Sheet Feeder", "formFields": {"values": [{"fieldType": "file", "fieldLabel": "The Logo-Sheet as Image", "requiredField": true}, {"fieldLabel": "Addional Prompt (e.g.: What the meaning of the graphic?) *optional but helps from time to time.", "placeholder": "It's a graph chart comparing AI Tools"}]}, "formDescription": "Provide a Image with multiple Logos comparing or bringing multiple Tools into Context with one another."}, "typeVersion": 2.2}, {"id": "b1530578-bde9-4ee3-9cdb-545a621cdb84", "name": "Retrieve and Parser Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-180, -220], "parameters": {"options": {"systemMessage": "Your task is to retrieve Information from the given Input. Extract Categories and Attributes of all given and shown Tools, Softwares or Products you've got by the user.\n\nProvide the Output Array of Tools with the following Structure as JSON:\n\n[{\n\"name\": \"Name of the Tool, Software, etc.\",\n\"attributes\": [\"Some category or attribute\", \"something else you can see from the context or image\"],\n\"similar\": [\"similar tool, product, etc. from shown context\", \"another similar software, product, tool from context\"]\n},{\n\"name\": \"Name of anotherTool, Software, etc.\",\n\"attributes\": [\"Some category, subcategory or general attribute\", \"something else you can see from the context or image\"],\n\"similar\": [\"similar tool, product, etc. from shown context\", \"another similar software, product, tool from context\"]\n}]\n\nList these structure for all the Products you see!\n\nHere a description of the JSON fields:\n\"name\": Just the Name of the Software.\n\"attribute\": Turn any information from the context or image into multiple useful Attributes for this tool. Could be a category, could be a feature, etc. Try to split this information in multiple specific Attributes or Categories.\n\"similar\": if multiple tools are shown that could compare to this one (like on the same level or in the same category), list those here\n\nTake a deep breath and think step by step.\nTry to extract every mentioned tool. There are for sure multiple listed.", "passthroughBinaryImages": true}, "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "51642a02-51a4-4894-adf0-f364736dabc1", "name": "JSON it", "type": "n8n-nodes-base.set", "position": [220, -220], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $json.output }}"}, "typeVersion": 3.4}, {"id": "ec0f0575-eb33-48a9-b3fe-c4f5b71ff548", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [40, 20], "parameters": {"jsonSchemaExample": "{\n\t\"tools\": [{\n\"name\": \"Name of the Tool, Software, etc.\",\n\"attributes\": [\"Some category or attribute\", \"something else you can see from the context or image\"],\n\"similar\": [\"similar tool, product, etc. from shown context\", \"another similar software, product, tool from context\"]\n},{\n\"name\": \"Name of anotherTool, Software, etc.\",\n\"attributes\": [\"Some category, subcategory or general attribute\", \"something else you can see from the context or image\"],\n\"similar\": [\"similar tool, product, etc. from shown context\", \"another similar software, product, tool from context\"]\n}]}"}, "typeVersion": 1.2}, {"id": "6d78005e-7277-40a9-9f10-e3d8e475cbaf", "name": "Check if Attribute exists", "type": "n8n-nodes-base.airtable", "position": [1380, 0], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appq0gcmxHAZQhswW", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW", "cachedResultName": "AI Tools"}, "table": {"__rl": true, "mode": "list", "value": "tblX2rj8yNAZZRhwt", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW/tblX2rj8yNAZZRhwt", "cachedResultName": "Attributes"}, "columns": {"value": {"Name": "={{$json.attributes}}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Tools", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Tools", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Name"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "jMqH6HkKUYTgyHVm", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "1c468a4b-4563-4f78-ba1b-138b18ac4821", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1620, 80], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "4f597962-48e5-4367-a329-bc07d42ff86d", "name": "Map Attribute ID", "type": "n8n-nodes-base.set", "position": [1840, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "675510b1-97e7-4a71-9c9e-d3ee792d9919", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "87cc9086-effd-4f4e-84c1-9adec5774e94", "name": "attribute", "type": "string", "value": "={{ $json.attributes }}"}]}}, "typeVersion": 3.4}, {"id": "********-360c-468f-b624-a9f6853e29f4", "name": "Loop Over Attributes", "type": "n8n-nodes-base.splitInBatches", "position": [720, -40], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "835a09ae-2e51-488c-b0b3-d895696a135e", "name": "All Attributes", "type": "n8n-nodes-base.set", "position": [940, -60], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $json }}"}, "typeVersion": 3.4}, {"id": "b8ca6d98-ab37-4393-8a2c-561912aeff2b", "name": "Wait for Attribute Creation", "type": "n8n-nodes-base.merge", "position": [1120, -200], "parameters": {"mode": "chooseBranch"}, "typeVersion": 3}, {"id": "9eaf87d4-910b-4a6e-9cdf-ee51ff4180cc", "name": "Change each Attribute to the corresponding RecID", "type": "n8n-nodes-base.code", "position": [1340, -200], "parameters": {"jsCode": "let knownAttributesOutput = $('All Attributes').all();\nlet knownAttributes = new Map();\nknownAttributesOutput.forEach((nodeOutput)=>{\nknownAttributes.set(nodeOutput.json.attribute.toString().trim(), nodeOutput.json.id);\n});\n\n\nfor (const item of $input.all()) {\n item.json.attributes.forEach((attribute, index)=>{\n item.json.attributes[index] = knownAttributes.get(attribute.toString().trim());\n });\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "ecfedff4-f6f9-429e-8514-cf8208e70048", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [600, -280], "parameters": {"color": 5, "width": 1460, "height": 600, "content": "## Attribute Creation and Mapping those created or existing Ids "}, "typeVersion": 1}, {"id": "ad2fafed-0a42-4615-a882-01306af7caf5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, -360], "parameters": {"color": 6, "width": 420, "height": 540, "content": "## Eat the provided Images, Extract the Information out of them as \"Tool -> Attributes\" list."}, "typeVersion": 1}, {"id": "5eb89e50-7a2f-415c-82f2-99eb8a7ff82f", "name": "Split Out Tools", "type": "n8n-nodes-base.splitOut", "position": [440, -220], "parameters": {"options": {}, "fieldToSplitOut": "tools"}, "typeVersion": 1}, {"id": "680dfb4b-dde4-4d8f-852d-c3eba82e6607", "name": "Split Out each Attribute String", "type": "n8n-nodes-base.splitOut", "position": [1140, 100], "parameters": {"options": {}, "fieldToSplitOut": "attributes"}, "typeVersion": 1}, {"id": "a33465e9-d469-498f-9178-7c30e15d2782", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2120, -280], "parameters": {"color": 4, "width": 880, "height": 600, "content": "## Create the Tools (if not exists)"}, "typeVersion": 1}, {"id": "5b5ab9f2-d4ac-437f-ab0a-b113a8af34ab", "name": "Generate Unique Hash for Name", "type": "n8n-nodes-base.crypto", "position": [2180, -200], "parameters": {"value": "={{ $json.name.toLowerCase().trim() }}", "dataPropertyName": "hash"}, "typeVersion": 1}, {"id": "ea8f7e6f-9004-4271-80d3-333701cce488", "name": "Create if not Exist", "type": "n8n-nodes-base.airtable", "position": [2400, -100], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appq0gcmxHAZQhswW", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW", "cachedResultName": "AI Tools"}, "table": {"__rl": true, "mode": "list", "value": "tblrikRHbX1N6P2JI", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW/tblrikRHbX1N6P2JI", "cachedResultName": "Tools"}, "columns": {"value": {"Hash": "={{$json.hash}}", "Name": "={{$json.name}}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "array", "display": true, "options": [], "removed": true, "readOnly": false, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attributes", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attributes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Hash", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Hash", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Hash"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "jMqH6HkKUYTgyHVm", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "85ac3cbb-4103-4184-b686-9e5b8d48f421", "name": "Merge Old Data + RecID", "type": "n8n-nodes-base.merge", "position": [2820, -180], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "hash"}, "typeVersion": 3}, {"id": "29d6369f-f233-46f8-8bee-aa3be854bb0c", "name": "Only what we need", "type": "n8n-nodes-base.set", "position": [2600, -100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0ff954ec-1d71-429b-b2e8-dca17ff0478d", "name": "hash", "type": "string", "value": "={{ $json.fields.Hash }}"}, {"id": "a7f4c2e7-fa63-45d7-ad22-ce8c3aaae4d6", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "081a7613-7c06-4578-8aa4-25d21952b727", "name": "existingAttributes", "type": "array", "value": "={{ $json.fields.Attributes ? $json.fields.Attributes : [] }}"}, {"id": "e3ace89b-d818-4448-8328-b36cdf08da2a", "name": "existingSimilars", "type": "array", "value": "={{ $json.fields.Similar ? $json.fields.Similar : [] }}"}]}}, "typeVersion": 3.4}, {"id": "bdf9c435-3994-4c25-9520-8dfa76e625eb", "name": "Determine Attributes we should save", "type": "n8n-nodes-base.code", "position": [3040, -180], "parameters": {"mode": "runOnceForEachItem", "jsCode": "let savingAttributes = $input.item.json.existingAttributes ? $input.item.json.existingAttributes : [];\n$input.item.json.attributes.forEach((attrId)=>{\nif($input.item.json.existingAttributes.indexOf(attrId) == -1) savingAttributes.push(attrId);\n});\n\n$input.item.json.savingAttributes = savingAttributes;\n\nreturn $input.item;"}, "typeVersion": 2}, {"id": "88e9f499-87d3-46e2-b3ea-1833c14aaa1b", "name": "Split Out similar", "type": "n8n-nodes-base.splitOut", "position": [3300, 20], "parameters": {"options": {}, "fieldToSplitOut": "similar"}, "typeVersion": 1}, {"id": "733a8d0c-c6ea-4386-9fd1-075980289e9c", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [3960, 0], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "dabb7e11-b4de-44d9-a80f-3302f49194fb", "name": "Generate Unique Hash for Similar", "type": "n8n-nodes-base.crypto", "position": [3520, -100], "parameters": {"value": "={{ $json.similar.toLowerCase().trim() }}", "dataPropertyName": "hash"}, "typeVersion": 1}, {"id": "a1bbda24-f75c-4316-b2bd-645827d7af1f", "name": "It Should exists", "type": "n8n-nodes-base.airtable", "position": [3740, -100], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appq0gcmxHAZQhswW", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW", "cachedResultName": "AI Tools"}, "table": {"__rl": true, "mode": "list", "value": "tblrikRHbX1N6P2JI", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW/tblrikRHbX1N6P2JI", "cachedResultName": "Tools"}, "columns": {"value": {"Hash": "={{$json.hash}}", "Name": "={{$json.similar}}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "array", "display": true, "options": [], "removed": true, "readOnly": false, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attributes", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attributes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Hash", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Hash", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Hash"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "jMqH6HkKUYTgyHVm", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "9853b85d-fcb9-4183-8fe4-6e32d318ab01", "name": "All Similar", "type": "n8n-nodes-base.set", "position": [4180, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "675510b1-97e7-4a71-9c9e-d3ee792d9919", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "87cc9086-effd-4f4e-84c1-9adec5774e94", "name": "similar", "type": "string", "value": "={{ $json.similar }}"}]}}, "typeVersion": 3.4}, {"id": "0e98acd2-4aa5-4df0-b36b-6ac1a8a2263b", "name": "Merge2", "type": "n8n-nodes-base.merge", "position": [4400, -160], "parameters": {"mode": "chooseBranch"}, "typeVersion": 3}, {"id": "ed94900a-78cd-4f61-a705-30f7cb8eb9b8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3200, -280], "parameters": {"color": 2, "width": 1600, "height": 600, "content": "## Map Competitors"}, "typeVersion": 1}, {"id": "74f0f703-ce73-457c-9137-88d613d2e480", "name": "Change each Smiliar to the corresponding RecID", "type": "n8n-nodes-base.code", "position": [4600, -160], "parameters": {"jsCode": "let knownSimilarsOutput = $('All Similar').all();\nlet knownSimilars = new Map();\nknownSimilarsOutput.forEach((nodeOutput)=>{\n knownSimilars.set(nodeOutput.json.similar.toString().trim(), nodeOutput.json.id);\n});\n\nfor (const item of $input.all()) {\n item.json.similar.forEach((similar, index)=>{\n item.json.similar[index] = knownSimilars.get(similar.toString().trim());\n });\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "c9187902-f67f-4639-906b-d6b14ace6a0e", "name": "Determine Similar we should save", "type": "n8n-nodes-base.code", "position": [4880, -160], "parameters": {"mode": "runOnceForEachItem", "jsCode": "let savingSimilar = $input.item.json.existingSimilars ? $input.item.json.existingSimilars : [];\n$input.item.json.similar.forEach((simId)=>{\nif($input.item.json.existingSimilars.indexOf(simId) == -1) savingSimilar.push(simId);\n});\n\n$input.item.json.savingSimilars = savingSimilar;\n\nreturn $input.item;"}, "typeVersion": 2}, {"id": "e925a388-05e2-49e4-92ad-984517f44057", "name": "Save all this juicy data", "type": "n8n-nodes-base.airtable", "position": [5120, -160], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appq0gcmxHAZQhswW", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW", "cachedResultName": "AI Tools"}, "table": {"__rl": true, "mode": "list", "value": "tblrikRHbX1N6P2JI", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW/tblrikRHbX1N6P2JI", "cachedResultName": "Tools"}, "columns": {"value": {"Hash": "={{$json.hash}}", "Name": "={{$json.name}}", "Similar": "={{ $json.savingSimilars }}", "Attributes": "={{ $json.savingAttributes }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "array", "display": true, "options": [], "removed": true, "readOnly": false, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attributes", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Attributes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Hash", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Hash", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Similar", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Similar", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Hash"]}, "options": {}, "operation": "upsert"}, "credentials": {"airtableTokenApi": {"id": "jMqH6HkKUYTgyHVm", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "d2532094-9c71-4fc0-8195-fb2e29169086", "name": "Map Agent Input", "type": "n8n-nodes-base.set", "position": [-440, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ace29464-a2a1-44a1-87f9-255fbde042cf", "name": "chatInput", "type": "string", "value": "={{$json.Prompt}}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "8fa7273b-ebc8-40e4-9f11-e4b26784f60d", "name": "gpt-4o", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-200, 20], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "25", "name": "Key 3 vom 15. Jan. 2023\t"}}, "typeVersion": 1}, {"id": "fb282ffe-4871-4560-97ce-43cc381db874", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1440, -580], "parameters": {"width": 668, "height": 786, "content": "## Instructions\n\nThis automation enables you to just upload any Image (via Form) of a Logo Sheet, containing multiple Images of Products, most likely and bringing them in some context to one another. \n\nAfter submitting an AI-Agent eats **that Logo Sheet**, turning it into an List of \"Productname\" and \"Attributes\", also checks if Tools are kind of similar to another, given the Context of the Image.\n\nWe utilize AI Vision capabilities for that. **NOTE:** It might not be able to extract all informations. For a \"upload and forget it\" Workflow it works for me. You can even run it multiple times, to be sure. \n\nBut if you need to make sure it extracts **everything** you might need to think about an Multi-Agent Setup with Validation-Agent Steps.\n\nOnce the Agent finishes the extraction, it will traditionally and deterministicly add those Attributes to Airtable (**Creates** those, if not already existing.) and also **Upserts** the Tool Informations.\n\nIt uses MD5 **Hashes** for turning Product Names into.. something fancy really, you could also use it without that, but I wanted to have something that looks atleast like an ID. \n\n### Setup\n\n1. Set Up the Airtable like shown below.\n2. Update and set Credentials for all Airtable Nodes.\n3. Check or Adjust the Prompt of the Agent matching your use-case.\n4. Activate the Workflow. \n5. Open the Form (default: https://your-n8n.io/form/logo-sheet-feeder)\n6. Enjoy growing your Airtable.\n\n![Image](https://cloud.let-the-work-flow.com/logo-64.png) \nEnjoy the workflow! ❤️ \n[let the workf low](https://let-the-work-flow.com) — Workflow Automation & Development"}, "typeVersion": 1}, {"id": "9ea45b9b-ac2a-4498-b96f-5f5de50acade", "name": "Table: Tools", "type": "n8n-nodes-base.noOp", "position": [-1340, 340], "parameters": {}, "typeVersion": 1}, {"id": "6dfbc02e-36b3-4640-b9f2-940c7cd6f86e", "name": "Table: Attributes", "type": "n8n-nodes-base.noOp", "position": [-1000, 340], "parameters": {}, "typeVersion": 1}, {"id": "d8ffeff8-8df7-4fc0-9f18-49a44d10eb7d", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 240], "parameters": {"color": 7, "width": 668, "height": 786, "content": "## Airtable Structure\n"}, "typeVersion": 1}, {"id": "7023be89-ee1d-41e6-bcf5-ee28f1284e07", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1420, 580], "parameters": {"color": 5, "width": 300, "height": 320, "content": "### Tools Table Fields\n\n**Required:**\nName (singleLineText) \nAttributes (multipleRecordLinks=Link to Attributes Table) \nHash (singleLineText) \nSimilar (multipleRecordLinks=Link to the Same Table:\"Tools\") \n\n_Description (multilineText)_ \n_Website (url)_\n_Category (multipleSelects)_"}, "typeVersion": 1}, {"id": "0c999f6f-11fb-472a-aa10-0915fbcd1254", "name": "make it a readable list", "type": "n8n-nodes-base.html", "disabled": true, "position": [-420, 800], "parameters": {"html": ""}, "typeVersion": 1.2}, {"id": "ae351db3-5c47-4e53-bf9e-e34434ad9522", "name": "<PERSON>", "type": "n8n-nodes-base.airtable", "disabled": true, "position": [-640, 800], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appq0gcmxHAZQhswW", "cachedResultUrl": "https://airtable.com/appq0gcmxHAZQhswW", "cachedResultName": "AI Tools"}, "resource": "base", "operation": "getSchema"}, "credentials": {"airtableTokenApi": {"id": "jMqH6HkKUYTgyHVm", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "9da286e2-2a06-4d2a-bd5b-b6c828683ff2", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [-720, 660], "parameters": {"color": 7, "width": 488, "height": 366, "content": "## Helper for Documentation (ignore or enjoy it)\n"}, "typeVersion": 1}, {"id": "901a0c48-82a9-4fd3-a007-8f4b257348d3", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1080, 580], "parameters": {"color": 5, "width": 280, "height": 320, "content": "### Attributes Table Fields\n\n**Required:**\nName (singleLineText)\nTools (multipleRecordLinks=Link to Tools Table) "}, "typeVersion": 1}, {"id": "966243fa-a1a3-4201-9df7-6a01aa762ae8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-160, -460], "parameters": {"color": 3, "width": 220, "height": 80, "content": "### Might want to Adjust Prompt to your \"Use-Case\" 🤖"}, "typeVersion": 1}, {"id": "1a4e5b87-68a6-499e-9374-e067fae12c84", "name": "Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2440, -580], "parameters": {"color": 7, "width": 968, "height": 646, "content": "## Example Logo Sheet\n### For these kind of sheets the Prompt is designed per default\n\n![Image](https://cloud.let-the-work-flow.com/workflow-data/example-ai-logo-sheet.jpg) "}, "typeVersion": 1}], "active": true, "pinData": {"Retrieve and Parser Agent": [{"json": {"output": {"tools": [{"name": "airOps", "similar": ["Cognition", "Grad<PERSON>"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "Cognition", "similar": ["airOps", "Grad<PERSON>"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "Grad<PERSON>", "similar": ["Cognition", "airOps"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "Cognosys", "similar": ["FIXIE", "continuia"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "FIXIE", "similar": ["Cognosys", "continuia"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "continuia", "similar": ["Cognosys", "FIXIE"], "attributes": ["Agentic Application", "AI infrastructure"]}, {"name": "Agentlabs", "similar": ["OpenAI", "<PERSON><PERSON><PERSON><PERSON>"], "attributes": ["Presentation Tool", "Utilizes OpenAI and LangChain"]}, {"name": "TINY FISH", "similar": ["Superagent", "basepilot"], "attributes": ["UI Automation", "Agent as a Service"]}, {"name": "Superagent", "similar": ["TINY FISH", "basepilot"], "attributes": ["UI Automation", "Agent as a Service"]}, {"name": "basepilot", "similar": ["TINY FISH", "Superagent"], "attributes": ["UI Automation", "Agent as a Service"]}, {"name": "Browserbase", "similar": ["browsersless", "APIFY"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "browsersless", "similar": ["Browserbase", "APIFY"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "APIFY", "similar": ["Browserbase", "browsersless"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "Cloudflare", "similar": ["bright data", "platform.sh"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "bright data", "similar": ["Cloudflare", "platform.sh"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "platform.sh", "similar": ["Cloudflare", "bright data"], "attributes": ["Browser Infrastructure", "Web services"]}, {"name": "ingest", "similar": ["hatchet", "Trigger.dev"], "attributes": ["Persistence Tool", "Data management"]}, {"name": "hatchet", "similar": ["ingest", "Trigger.dev"], "attributes": ["Persistence Tool", "Data management"]}, {"name": "Trigger.dev", "similar": ["ingest", "hatchet"], "attributes": ["Persistence Tool", "Data management"]}, {"name": "DSPy", "similar": ["AutoGen", "Scma4.ai"], "attributes": ["Orchestration Tool", "AI Workflow Management"]}, {"name": "AutoGen", "similar": ["DSPy", "Scma4.ai"], "attributes": ["Orchestration Tool", "AI Workflow Management"]}, {"name": "Scma4.ai", "similar": ["DSPy", "AutoGen"], "attributes": ["Orchestration Tool", "AI Workflow Management"]}, {"name": "WhyHowAI", "similar": ["Graphlit", "Lang<PERSON><PERSON>"], "attributes": ["Personalization Tool", "Memory management"]}, {"name": "Graphlit", "similar": ["WhyHowAI", "Lang<PERSON><PERSON>"], "attributes": ["Personalization Tool", "Memory management"]}, {"name": "Lang<PERSON><PERSON>", "similar": ["WhyHowAI", "Graphlit"], "attributes": ["Personalization Tool", "Memory management"]}, {"name": "Pinecone", "similar": ["Chroma", "Weaviate"], "attributes": ["Storage Tool", "Memory management"]}, {"name": "Chroma", "similar": ["Pinecone", "Weaviate"], "attributes": ["Storage Tool", "Memory management"]}, {"name": "Weaviate", "similar": ["Pinecone", "Chroma"], "attributes": ["Storage Tool", "Memory management"]}, {"name": "MongoDB", "similar": ["WhiteLodge", "Chroma"], "attributes": ["Context Management", "Data storage"]}, {"name": "LangServe", "similar": ["E2B", "Ollama"], "attributes": ["Agent Hosting", "Deployment platform"]}, {"name": "E2B", "similar": ["LangServe", "Ollama"], "attributes": ["Agent Hosting", "Deployment platform"]}, {"name": "Ollama", "similar": ["LangServe", "E2B"], "attributes": ["Agent Hosting", "Deployment platform"]}, {"name": "LangGraph", "similar": ["<PERSON><PERSON><PERSON>", "LlamaIndex"], "attributes": ["Framework Tool", "Graph Management"]}, {"name": "LlamaIndex", "similar": ["LangGraph", "<PERSON><PERSON><PERSON>"], "attributes": ["Framework Tool", "Graph Management"]}, {"name": "<PERSON><PERSON><PERSON>", "similar": ["LangGraph", "LlamaIndex"], "attributes": ["Framework Tool", "Graph Management"]}, {"name": "agentops", "similar": ["context", "<PERSON><PERSON><PERSON>"], "attributes": ["Agent Evaluation Tool", "Performance Assessment"]}, {"name": "context", "similar": ["agentops", "<PERSON><PERSON><PERSON>"], "attributes": ["Agent Evaluation Tool", "Performance Assessment"]}, {"name": "<PERSON><PERSON><PERSON>", "similar": ["agentops", "context"], "attributes": ["Agent Evaluation Tool", "Performance Assessment"]}, {"name": "WHYLabs", "similar": ["griptape", "braintrust"], "attributes": ["Developer Tools", "Data Management"]}, {"name": "griptape", "similar": ["WHYLabs", "braintrust"], "attributes": ["Developer Tools", "Data Management"]}, {"name": "braintrust", "similar": ["WHYLabs", "griptape"], "attributes": ["Developer Tools", "Data Management"]}]}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "cd74efad-4f0c-45ea-bc7e-3f7c5554c204", "connections": {"Merge": {"main": [[{"node": "Map Attribute ID", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "All Similar", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Change each Smiliar to the corresponding RecID", "type": "main", "index": 0}]]}, "gpt-4o": {"ai_languageModel": [[{"node": "Retrieve and Parser Agent", "type": "ai_languageModel", "index": 0}]]}, "JSON it": {"main": [[{"node": "Split Out Tools", "type": "main", "index": 0}]]}, "Get Schema": {"main": [[{"node": "make it a readable list", "type": "main", "index": 0}]]}, "All Similar": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "Table: Tools": {"main": [[{"node": "Table: Tools", "type": "main", "index": 0}, {"node": "Table: Attributes", "type": "main", "index": 0}]]}, "All Attributes": {"main": [[{"node": "Wait for Attribute Creation", "type": "main", "index": 1}]]}, "Map Agent Input": {"main": [[{"node": "Retrieve and Parser Agent", "type": "main", "index": 0}]]}, "Split Out Tools": {"main": [[{"node": "Loop Over Attributes", "type": "main", "index": 0}, {"node": "Wait for Attribute Creation", "type": "main", "index": 0}]]}, "It Should exists": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Map Attribute ID": {"main": [[{"node": "Loop Over Attributes", "type": "main", "index": 0}]]}, "Only what we need": {"main": [[{"node": "Merge Old Data + RecID", "type": "main", "index": 1}]]}, "Split Out similar": {"main": [[{"node": "Generate Unique Hash for Similar", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Table: Attributes": {"main": [[]]}, "On form submission": {"main": [[{"node": "Map Agent Input", "type": "main", "index": 0}]]}, "Create if not Exist": {"main": [[{"node": "Only what we need", "type": "main", "index": 0}]]}, "Loop Over Attributes": {"main": [[{"node": "All Attributes", "type": "main", "index": 0}], [{"node": "Split Out each Attribute String", "type": "main", "index": 0}]]}, "Merge Old Data + RecID": {"main": [[{"node": "Determine Attributes we should save", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Retrieve and Parser Agent", "type": "ai_outputParser", "index": 0}]]}, "Check if Attribute exists": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Retrieve and Parser Agent": {"main": [[{"node": "JSON it", "type": "main", "index": 0}]]}, "Wait for Attribute Creation": {"main": [[{"node": "Change each Attribute to the corresponding RecID", "type": "main", "index": 0}]]}, "Generate Unique Hash for Name": {"main": [[{"node": "Create if not Exist", "type": "main", "index": 0}, {"node": "Merge Old Data + RecID", "type": "main", "index": 0}]]}, "Split Out each Attribute String": {"main": [[{"node": "Check if Attribute exists", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Determine Similar we should save": {"main": [[{"node": "Save all this juicy data", "type": "main", "index": 0}]]}, "Generate Unique Hash for Similar": {"main": [[{"node": "It Should exists", "type": "main", "index": 0}]]}, "Determine Attributes we should save": {"main": [[{"node": "Split Out similar", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}, "Change each Smiliar to the corresponding RecID": {"main": [[{"node": "Determine Similar we should save", "type": "main", "index": 0}]]}, "Change each Attribute to the corresponding RecID": {"main": [[{"node": "Generate Unique Hash for Name", "type": "main", "index": 0}]]}}}