{"id": "KKCfXEpBjjhp1LC8", "meta": {"instanceId": "494d0146a0f47676ad70a44a32086b466621f62da855e3eaf0ee51dee1f76753", "templateCredsSetupCompleted": true}, "name": "Entra User to Zammad User Sync", "tags": [], "nodes": [{"id": "0007443e-b0d4-4f98-a613-3ec7c2842aa3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-2140, 140], "parameters": {}, "typeVersion": 1}, {"id": "2b285a4f-7e39-411b-88b9-cb55c5cf62e3", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1700, 380], "parameters": {"width": 1635.910561370123, "height": 329.7269624573379, "content": "## Select Entra Users in a named Entra Group that should be synced to <PERSON>ammad\n\n\n\n"}, "typeVersion": 1}, {"id": "929e529e-a4a3-4663-b9dc-e2300a860fed", "name": "Get Groups from Entra", "type": "n8n-nodes-base.httpRequest", "position": [-1660, 480], "parameters": {"url": "https://graph.microsoft.com/v1.0/groups", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "microsoftOAuth2Api"}, "credentials": {"microsoftOAuth2Api": {"id": "U2E5p3lreqSi8v1N", "name": "ms365test.zammad.org"}, "microsoftGraphSecurityOAuth2Api": {"id": "b09tqOxzkl0P8UQD", "name": "ms365test.zammad.org"}}, "typeVersion": 4.2}, {"id": "3390b2ed-6070-429c-bc1a-f0ab324117c7", "name": "Remove outer Array", "type": "n8n-nodes-base.splitOut", "position": [-1400, 480], "parameters": {"options": {}, "fieldToSplitOut": "value"}, "typeVersion": 1}, {"id": "b0e9531a-7fc0-4de0-8ec5-4be476b18a26", "name": "Select Entra Zammad default Group", "type": "n8n-nodes-base.if", "notes": "Please enter the Entra group name of users to be synchronized.", "position": [-1120, 480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2dbb2484-2424-4095-a5a2-76ab4e3aaae8", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.displayName }}", "rightValue": "ENTRA"}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "1be2a745-aea3-46ec-ab84-be2e39358b95", "name": "Remove outer Array from Entra User", "type": "n8n-nodes-base.splitOut", "position": [-700, 460], "parameters": {"options": {}, "fieldToSplitOut": "value"}, "typeVersion": 1}, {"id": "3b1fc962-7546-4bad-b637-e018649a0652", "name": "Zammad Univeral User Object", "type": "n8n-nodes-base.set", "position": [-240, 440], "parameters": {"values": {"number": [{"name": "entra_key", "value": "={{ $json.id }}"}], "string": [{"name": "email", "value": "={{ $json.userPrincipalName }}"}, {"name": "lastname", "value": "={{ $json.surname }}"}, {"name": "firstname", "value": "={{ $json.givenName }}"}, {"name": "mobile", "value": "={{ $json.mobilePhone }}"}, {"name": "phone", "value": "={{ $json.businessPhones[0] }}"}, {}, {}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "9e36e6a9-cf56-4548-a1af-b1e33dbc61dd", "name": "Get Zammad Users", "type": "n8n-nodes-base.zammad", "position": [-1020, 140], "parameters": {"filters": {}, "operation": "getAll", "returnAll": true}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "c9a342b1-b5f2-4d31-9737-15f145dc7318", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [240, 140], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "email"}, "typeVersion": 3}, {"id": "a04ebfea-e5fe-4903-841a-8ef29d75ff1a", "name": "Get Members of the default group", "type": "n8n-nodes-base.httpRequest", "position": [-880, 460], "parameters": {"url": "=https://graph.microsoft.com/v1.0/groups/{{ $json.id }}/members ", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "microsoftOAuth2Api"}, "credentials": {"microsoftOAuth2Api": {"id": "U2E5p3lreqSi8v1N", "name": "ms365test.zammad.org"}, "microsoftGraphSecurityOAuth2Api": {"id": "b09tqOxzkl0P8UQD", "name": "ms365test.zammad.org"}}, "typeVersion": 4.2}, {"id": "2e68992e-3080-41fd-9aae-c44dc60dc3b0", "name": "Find new Zammad Users", "type": "n8n-nodes-base.compareDatasets", "position": [240, 460], "parameters": {"options": {}, "mergeByFields": {"values": [{"field1": "email", "field2": "email"}]}}, "typeVersion": 2.3}, {"id": "86dc2c72-d54a-40a9-a64b-fc0bde9a2387", "name": "Update <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.zammad", "position": [560, 140], "parameters": {"id": "={{ $json.id }}", "operation": "update", "updateFields": {"phone": "={{ $json.phone }}", "mobile": "={{ $json.mobile }}", "lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}, {"name": "entra_object_type", "value": "user"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "bc883c6d-ec53-4854-824a-bd76b28077d2", "name": "Create <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.zammad", "position": [580, 540], "parameters": {"lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "additionalFields": {"email": "={{ $json.email }}", "phone": "={{ $json.phone }}", "mobile": "={{ $json.mobile }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}, {"name": "entra_object_type", "value": "user"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "3b57e278-e755-407c-b261-7fe76ce82bb5", "name": "Deactivate Zammad User", "type": "n8n-nodes-base.zammad", "position": [600, 840], "parameters": {"id": "={{ $json.id }}", "operation": "update", "updateFields": {"phone": "={{ $json.phone }}", "active": false, "mobile": "={{ $json.mobile }}", "lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "cdaf8b51-9b4c-4ad0-b8f0-c6921849ed4c", "name": "Find removed Users", "type": "n8n-nodes-base.compareDatasets", "position": [240, 880], "parameters": {"options": {}, "resolve": "preferInput1", "mergeByFields": {"values": [{"field1": "entra_key", "field2": "entra_key"}]}}, "typeVersion": 2.3}, {"id": "9b37b75e-d694-441e-b5a5-8abeccbf4ed7", "name": "If", "type": "n8n-nodes-base.if", "position": [-500, 460], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "15da9b4f-46fa-4e9b-bd33-40ae79b88cd5", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "13ac19a6-6689-4e75-86d4-02ec1c0c64cd", "name": "Select only active Users and entra_obect_type=\"user\"", "type": "n8n-nodes-base.if", "position": [-220, 140], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1c9ca19d-18e3-470e-84cd-593794613c59", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.entra_object_type }}", "rightValue": "user"}, {"id": "9187eea8-48ec-4488-9bc9-45235ff88114", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.active }}", "rightValue": ""}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b726c830-9d26-4289-8f66-485850762df7", "connections": {"If": {"main": [[{"node": "Zammad Univeral User Object", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Update <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Zammad Users": {"main": [[{"node": "Select only active Users and entra_obect_type=\"user\"", "type": "main", "index": 0}]]}, "Find removed Users": {"main": [[{"node": "Deactivate Zammad User", "type": "main", "index": 0}], [], []]}, "Remove outer Array": {"main": [[{"node": "Select Entra Zammad default Group", "type": "main", "index": 0}]]}, "Find new Zammad Users": {"main": [[], [], [], [{"node": "Create <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Groups from Entra": {"main": [[{"node": "Remove outer Array", "type": "main", "index": 0}]]}, "Zammad Univeral User Object": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "Find new Zammad Users", "type": "main", "index": 1}, {"node": "Find removed Users", "type": "main", "index": 1}]]}, "Get Members of the default group": {"main": [[{"node": "Remove outer Array from Entra User", "type": "main", "index": 0}]]}, "Select Entra Zammad default Group": {"main": [[{"node": "Get Members of the default group", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Zammad Users", "type": "main", "index": 0}, {"node": "Get Groups from Entra", "type": "main", "index": 0}]]}, "Remove outer Array from Entra User": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Select only active Users and entra_obect_type=\"user\"": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Find new Zammad Users", "type": "main", "index": 0}, {"node": "Find removed Users", "type": "main", "index": 0}]]}}}