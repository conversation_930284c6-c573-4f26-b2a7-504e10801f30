{"id": "rtsvydad1MOCryia", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "🐋🤖 DeepSeek AI Agent + Telegram + LONG TERM Memory 🧠", "tags": [], "nodes": [{"id": "23b50c07-39a8-4166-ab13-9683b3ee25e6", "name": "Check User & Chat ID", "type": "n8n-nodes-base.if", "position": [-80, 160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5fe3c0d8-bd61-4943-b152-9e6315134520", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.first_name }}", "rightValue": "={{ $json.first_name }}"}, {"id": "98a0ea91-0567-459c-bbce-06abc14a49ce", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.last_name }}", "rightValue": "={{ $json.last_name }}"}, {"id": "18a96c1f-f2a0-4a2a-b789-606763df4423", "operator": {"type": "number", "operation": "equals"}, "leftValue": "={{ $('Listen for Telegram Events').item.json.body.message.from.id }}", "rightValue": "={{ $json.id }}"}]}, "looseTypeValidation": "="}, "typeVersion": 2.2}, {"id": "ecbc13fe-305d-4cdd-b35c-3e119e8e8b5d", "name": "Error message", "type": "n8n-nodes-base.telegram", "position": [160, 440], "parameters": {"text": "=Unable to process your message.", "chatId": "={{ $json.body.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "be722bc7-0b22-4892-967c-fdd398a7b129", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-540, -20], "parameters": {"color": 6, "width": 949, "height": 652, "content": "# Receive Telegram Message with Webhook"}, "typeVersion": 1}, {"id": "a3866585-bfee-4025-a8f4-f06fde16171a", "name": "Listen for Telegram Events", "type": "n8n-nodes-base.webhook", "position": [-480, 160], "webhookId": "097f36f3-1574-44f9-815f-58387e3b20bf", "parameters": {"path": "wbot", "options": {"binaryPropertyName": "data"}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "f70571d5-3680-4616-90fa-3358b0883368", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1380, -20], "parameters": {"color": 7, "width": 800, "height": 860, "content": "# How to set up a Telegram Bot WebHook\n\n## WebHook Setup Process\n\n**Basic Concept**\nA WebHook allows your Telegram bot to automatically receive updates instead of manually polling the Bot API.\n\n**Setup Method**\nTo set a WebHook, make a GET request using this URL format:\n```\nhttps://api.telegram.org/bot{my_bot_token}/setWebhook?url={url_to_send_updates_to}\n```\nWhere:\n- `my_bot_token`: Your bot token from BotFather\n- `url_to_send_updates_to`: Your HTTPS endpoint that handles bot updates\n\n\n**Verification**\nTo verify the WebHook setup, use:\n```\nhttps://api.telegram.org/bot{my_bot_token}/getWebhookInfo\n```\n\nA successful response looks like:\n```json\n{\n \"ok\": true,\n \"result\": {\n \"url\": \"https://www.example.com/my-telegram-bot/\",\n \"has_custom_certificate\": false,\n \"pending_update_count\": 0,\n \"max_connections\": 40\n }\n}\n```\n\n\nThis method provides a simple and efficient way to handle Telegram bot updates automatically through webhooks rather than manual polling."}, "typeVersion": 1}, {"id": "2b6149d5-ffd6-46ef-9840-149508251a77", "name": "Validation", "type": "n8n-nodes-base.set", "position": [-260, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0cea6da1-652a-4c1e-94c3-30608ced90f8", "name": "first_name", "type": "string", "value": "FirstName"}, {"id": "b90280c6-3e36-49ca-9e7e-e15c42d256cc", "name": "last_name", "type": "string", "value": "LastName"}, {"id": "f6d86283-16ca-447e-8427-7d3d190babc0", "name": "id", "type": "number", "value": 12345667891}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "41c965ea-b67d-4d6b-82e4-0e57f5fc13bb", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-320, 100], "parameters": {"color": 7, "width": 420, "height": 260, "content": "## Validate Telegram User\n"}, "typeVersion": 1}, {"id": "164f5e91-1958-4dc5-b38c-db1cec0579d4", "name": "Message Router", "type": "n8n-nodes-base.switch", "position": [160, 160], "parameters": {"rules": {"values": [{"outputKey": "audio", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.voice }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "342f0883-d959-44a2-b80d-379e39c76218", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.text }}", "rightValue": ""}]}, "renameOutput": true}, {"outputKey": "image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ded3a600-f861-413a-8892-3fc5ea935ecb", "operator": {"type": "array", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.message.photo }}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "7947173d-39fa-4d4b-9b1e-60de809a9950", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "onError": "continueErrorOutput", "position": [860, 340], "parameters": {"text": "={{ $('Message Router').item.json.body.message.text }}", "options": {"systemMessage": "=## ROLE \nYou are a friendly, attentive, and helpful AI assistant. Your primary goal is to assist the user while maintaining a personalized and engaging interaction. The current user's first name is **{{ $json.body.message.from.first_name }}**.\n\n---\n\n## RULES \n\n1. **Memory Management**: \n - When the user sends a new message, evaluate whether it contains noteworthy or personal information (e.g., preferences, habits, goals, or important events). \n - If such information is identified, use the **Save Memory** tool to store this data in memory. \n - Always send a meaningful response back to the user, even if your primary action was saving information. This response should not reveal that information was stored but should acknowledge or engage with the user’s input naturally.\n\n2. **Context Awareness**: \n - Use stored memories to provide contextually relevant and personalized responses. \n - Always consider the **date and time** when a memory was collected to ensure your responses are up-to-date and accurate.\n\n3. **User-Centric Responses**: \n - Tailor your responses based on the user's preferences and past interactions. \n - Be proactive in recalling relevant details from memory when appropriate but avoid overwhelming the user with unnecessary information.\n\n4. **Privacy and Sensitivity**: \n - Handle all user data with care and sensitivity. Avoid making assumptions or sharing stored information unless it directly enhances the conversation or task at hand.\n\n5. **Fallback Responses**: \n - **IMPORTANT** If no specific task or question arises from the user’s message (e.g., when only saving information), respond in a way that keeps the conversation flowing naturally. For example:\n - Acknowledge their input: “Thanks for sharing that!” \n - Provide a friendly follow-up: “Is there anything else I can help you with today?”\n - DO NOT tell Jokes as a fall back response.\n\n---\n\n## TOOLS \n\n### Save Memory \n- Use this tool to store summarized, concise, and meaningful information about the user. \n- Extract key details from user messages that could enhance future interactions (e.g., likes/dislikes, important dates, hobbies). \n- Ensure that the summary is clear and devoid of unnecessary details.\n\n---\n\n## MEMORIES \n\n### Recent Noteworthy Memories \nHere are the most recent memories collected from the user, including their date and time of collection: \n\n**{{ $('Retrieve Long Term Memories').item.json.content }}**\n\n### Guidelines for Using Memories: \n- Prioritize recent memories but do not disregard older ones if they remain relevant. \n- Cross-reference memories to maintain consistency in your responses. For example, if a user shares conflicting preferences over time, clarify or adapt accordingly.\n\n---\n\n## ADDITIONAL INSTRUCTIONS \n\n- Think critically before responding to ensure your answers are thoughtful and accurate. \n- Strive to build trust with the user by being consistent, reliable, and personable in your interactions. \n- Avoid robotic or overly formal language; aim for a conversational tone that aligns with being \"friendly and helpful.\" \n"}, "promptType": "define"}, "typeVersion": 1.7, "alwaysOutputData": true}, {"id": "6111c771-d8af-4586-8829-213d86dc4f47", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [860, 100], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "94a01b4f-549d-4e49-88e0-143c90dd200e", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [920, 780], "parameters": {"sessionKey": "={{ $json.id }}", "sessionIdType": "customKey", "contextWindowLength": 50}, "typeVersion": 1.3}, {"id": "d1182e11-025e-4885-abb1-b76a9b617b84", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "disabled": true, "position": [-480, 420], "webhookId": "701ddc24-2637-466e-9789-5d47145333a8", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "97d4cdcd-b016-44aa-882c-eb2ec38968eb", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [440, -20], "parameters": {"color": 5, "width": 1033, "height": 1029, "content": "# Process Text Message"}, "typeVersion": 1}, {"id": "73156ecc-af5f-4e3d-82c6-4668db52b511", "name": "Telegram Response", "type": "n8n-nodes-base.telegram", "position": [1240, 160], "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "5f342299-40fe-44cf-9b58-8a9d3bfac1df", "name": "Save Long Term Memories", "type": "n8n-nodes-base.googleDocsTool", "position": [1260, 780], "parameters": {"actionsUi": {"actionFields": [{"text": "= Memory: {{ $fromAI('memory') }} - Date: {{ $now }} ", "action": "insert"}]}, "operation": "update", "documentURL": "[Google Doc ID]", "descriptionType": "manual", "toolDescription": "Save memories"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "aba001a8-68f9-4870-9cd0-60a4c59ecd5b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [460, 220], "parameters": {"color": 4, "width": 300, "height": 340, "content": "## Retrieve Long Term Memories\nGoogle Docs"}, "typeVersion": 1}, {"id": "e5ec71ec-9527-4ccd-87c3-3aa2f09192e8", "name": "Retrieve Long Term Memories", "type": "n8n-nodes-base.googleDocs", "position": [560, 360], "parameters": {"operation": "get", "documentURL": "[Google Doc ID]"}, "credentials": {"googleDocsOAuth2Api": {"id": "YWEHuG28zOt532MQ", "name": "Google Docs account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "4764383a-3c4b-4e64-b391-5dc9fb4b9de6", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [820, 660], "parameters": {"width": 280, "height": 320, "content": "## Save To Current Chat Memory (Optional)"}, "typeVersion": 1}, {"id": "e11995b8-e061-4b40-b4b6-9ec03c7e5a06", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1160, 660], "parameters": {"color": 4, "width": 280, "height": 320, "content": "## Save Long Term Memories\nGoogle Docs"}, "typeVersion": 1}, {"id": "1b53aef2-ca99-409b-bd10-3fc1fd87f540", "name": "Response Error message", "type": "n8n-nodes-base.telegram", "position": [1240, 360], "parameters": {"text": "=Unable to process your message.", "chatId": "={{ $('Listen for Telegram Events').item.json.body.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "e5d79084-d7f1-44fd-a1db-73cc76a148ec", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-60, 660], "parameters": {"color": 7, "width": 820, "height": 600, "content": "# DeepSeek API Call\n\nThe DeepSeek API uses an API format compatible with OpenAI. By modifying the configuration, you can use the OpenAI SDK or softwares compatible with the OpenAI API to access the DeepSeek API.\n\nhttps://api-docs.deepseek.com/\n\n## Configuration Parameters\n\n| Parameter | Value |\n|-----------|--------|\n| base_url | https://api.deepseek.com |\n| api_key | https://platform.deepseek.com/api_keys |\n\n\n\n## Important Notes\n\n- To be compatible with OpenAI, you can also use `https://api.deepseek.com/v1` as the base_url. Note that the v1 here has NO relationship with the model's version.\n\n- The deepseek-chat model has been upgraded to DeepSeek-V3. The API remains unchanged. You can invoke DeepSeek-V3 by specifying `model='deepseek-chat'`.\n\n- deepseek-reasoner is the latest reasoning model, DeepSeek-R1, released by DeepSeek. You can invoke DeepSeek-R1 by specifying `model='deepseek-reasoner'`."}, "typeVersion": 1}, {"id": "af14e803-44a5-4b0e-a675-b1e860bf6d29", "name": "DeepSeek-R1 Reasoning", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [440, 880], "parameters": {"model": "=deepseek-reasoner", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}, {"id": "e8be6a32-ba4c-4895-b34b-c5e7d0ded5e8", "name": "DeepSeek-V3 Chat", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 880], "parameters": {"model": "=deepseek-chat", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "2e669c98-e6ad-42f0-a642-de05e372937e", "connections": {"Merge": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram Response", "type": "main", "index": 0}], [{"node": "Response Error message", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Check User & Chat ID", "type": "main", "index": 0}]]}, "Message Router": {"main": [[], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Retrieve Long Term Memories", "type": "main", "index": 0}], [], [{"node": "Error message", "type": "main", "index": 0}]]}, "DeepSeek-V3 Chat": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Check User & Chat ID": {"main": [[{"node": "Message Router", "type": "main", "index": 0}], [{"node": "Error message", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[]]}, "Save Long Term Memories": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Listen for Telegram Events": {"main": [[{"node": "Validation", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[]]}, "Retrieve Long Term Memories": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}}}