{"id": "VU0kmvnWzctSFm2M", "meta": {"instanceId": "534a4ec070e550167af0cc407c76e029ac0ae69bef901c2f9ef440d79bfa5792"}, "name": "Convert Parquet, Avro, ORC & Feather via ParquetReader to JSON", "tags": [{"id": "1PTaY70kFjD8F12p", "name": "Convert", "createdAt": "2025-03-30T09:38:16.466Z", "updatedAt": "2025-03-30T09:38:16.466Z"}, {"id": "98v0QSKrvfH5dl34", "name": "Avro", "createdAt": "2025-03-30T09:38:06.656Z", "updatedAt": "2025-03-30T09:38:06.656Z"}, {"id": "Q0sqo52DKATPDab2", "name": "ORC", "createdAt": "2025-03-30T09:38:09.923Z", "updatedAt": "2025-03-30T09:38:09.923Z"}, {"id": "b1s8WFj3TfMpoOQu", "name": "<PERSON><PERSON>", "createdAt": "2025-03-30T09:38:12.227Z", "updatedAt": "2025-03-30T09:38:12.227Z"}, {"id": "fFnESRcynarFqlLf", "name": "Pa<PERSON><PERSON>", "createdAt": "2025-03-30T09:38:04.286Z", "updatedAt": "2025-03-30T09:38:04.286Z"}], "nodes": [{"id": "651a10dc-1c91-4957-bcdd-3e55d7328f04", "name": "Send to Parquet API", "type": "n8n-nodes-base.httpRequest", "position": [1100, 440], "parameters": {"url": "https://api.parquetreader.com/parquet?source=n8n", "options": {"bodyContentType": "multipart-form-data"}, "requestMethod": "POST", "jsonParameters": true, "sendBinaryData": true, "binaryPropertyName": "=file0"}, "typeVersion": 1}, {"id": "42a7e623-ca11-4d38-94bb-cfb48d021a5c", "name": "Webhook", "type": "n8n-nodes-base.webhook", "notes": "Example trigger flow:\n\ncurl -X POST http://localhost:5678/webhook-test/convert \\\n  -F \"file=@converted.parquet\"", "position": [900, 440], "webhookId": "0b1223c9-c117-45f9-9931-909f2db28955", "parameters": {"path": "convert", "options": {"binaryPropertyName": "file"}, "httpMethod": "POST", "responseData": "allEntries", "responseMode": "lastNode"}, "notesInFlow": false, "typeVersion": 2}, {"id": "9b87f027-7ef2-40a7-88d7-a0ae9ef73375", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 840, "height": 580, "content": "### ✅ **How to Use This Flow**\n\n#### 📥 Trigger via File Upload\n\nYou can trigger this flow by sending a `POST` request with a file using **curl**, **Postman**, or **from another n8n flow**.\n\n#### 🔧 Example (via `curl`):\n```bash\ncurl -X POST http://localhost:5678/webhook-test/convert \\\n-F \"file=@converted.parquet\"\n```\n> Replace `converted.parquet` with your local file path. You can also send Avro, ORC or Feather files.\n\n#### 🔁 Reuse from Other Flows\nYou can **reuse this flow** by calling the webhook from another n8n workflow using an **HTTP Request** node.  \nMake sure to send the file as **form-data** with the field name `file`.\n\n#### 🔍 What This Flow Does:\n- Receives the uploaded file via webhook (`file`)\n- Sends it to `https://api.parquetreader.com/parquet` as `multipart/form-data` (field name: `file`)\n- Receives parsed data, schema, and metadata\n"}, "typeVersion": 1}, {"id": "06d3e569-8724-48f2-951f-a1af5e0f9362", "name": "Parse API Response", "type": "n8n-nodes-base.code", "position": [1280, 440], "parameters": {"jsCode": "const item = items[0];\n\n// Convert `data` (stringified JSON array) → actual array\nif (typeof item.json.data === 'string') {\n  item.json.data = JSON.parse(item.json.data);\n}\n\n// Convert `meta_data` (stringified JSON object) → actual object\nif (typeof item.json.meta_data === 'string') {\n  item.json.meta_data = JSON.parse(item.json.meta_data);\n}\n\nreturn [item];\n"}, "typeVersion": 2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c10e1897-094e-42c6-bde9-1724972ada3e", "connections": {"Webhook": {"main": [[{"node": "Send to Parquet API", "type": "main", "index": 0}]]}, "Send to Parquet API": {"main": [[{"node": "Parse API Response", "type": "main", "index": 0}]]}}}