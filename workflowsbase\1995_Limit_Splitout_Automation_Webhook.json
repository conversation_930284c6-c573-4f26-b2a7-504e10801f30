{"id": "w434EiZ2z7klQAyp", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Scrape Trustpilot Reviews with DeepSeek, Analyze Sentiment with OpenAI", "tags": [{"id": "2VG6RbmUdJ2VZbrj", "name": "Google Drive", "createdAt": "2024-12-04T16:50:56.177Z", "updatedAt": "2024-12-04T16:50:56.177Z"}, {"id": "paTcf5QZDJsC2vKY", "name": "OpenAI", "createdAt": "2024-12-04T16:52:10.768Z", "updatedAt": "2024-12-04T16:52:10.768Z"}], "nodes": [{"id": "095a8e10-1630-4a1a-b6c9-7950ae1ed803", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [320, -380], "parameters": {"options": {}, "fieldToSplitOut": "recensioni"}, "typeVersion": 1}, {"id": "6ff4dd9d-eedd-4d84-b13a-b3c0db717409", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [-440, 140], "parameters": {"text": "=You need to extract the review from the following HTML:  {{ $json.recensione }}", "options": {"systemPromptTemplate": "You are a review expert. You need to extract only the required information and report it without changing anything.\nAll the required information is in the text."}, "attributes": {"attributes": [{"name": "autore", "required": true, "description": "Extract the name of the review author"}, {"name": "valutazione", "type": "number", "required": true, "description": "Extract the rating given to the review (from 1 to 5)"}, {"name": "data", "required": true, "description": "Extract review date in YYYY-MM-DD format"}, {"name": "titolo", "required": true, "description": "Extract the review title"}, {"name": "testo", "required": true, "description": "Extract the review text"}, {"name": "n_recensioni", "type": "number", "required": true, "description": "Extract the total number of reviews made by the user"}, {"name": "nazione", "required": true, "description": "Extract the country of the user who wrote the review. Must be two characters"}]}}, "typeVersion": 1}, {"id": "0036f3b1-4832-4a35-8694-0893475a4119", "name": "If", "type": "n8n-nodes-base.if", "position": [60, -100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "ab666549-4eec-40e2-a702-0575c094a2d4", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Valutazione }}", "rightValue": "={{ $('Split Out').item.json.recensioni.replace('/reviews/','') }}"}]}, "looseTypeValidation": true}, "executeOnce": false, "typeVersion": 2.2}, {"id": "5423b55d-eb6c-41c6-9b26-410e3c92b85d", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-700, -380], "parameters": {}, "typeVersion": 1}, {"id": "506cdaa1-e0ba-4f29-b137-69d321b13c94", "name": "Limit1", "type": "n8n-nodes-base.limit", "position": [540, -380], "parameters": {"maxItems": 3}, "typeVersion": 1}, {"id": "40f1e30d-8aed-4995-b4e4-2239248bd6e7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-460, -480], "parameters": {"width": 212.25249169435213, "height": 245.55481727574733, "content": "Change to the name of the company registered on Trustpilot and the maximum number of pages to scrape"}, "typeVersion": 1}, {"id": "e6d2fec1-7255-4270-86b4-6d6f39f44ccb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, 80], "parameters": {"width": 381, "height": 177, "content": "Extract all information with DeepSeek (remember to change base_url with https://api.deepseek.com/v1)"}, "typeVersion": 1}, {"id": "af5e962c-4faf-41cc-a8b8-2fbb145b7af6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-240, -160], "parameters": {"width": 501.28903654485043, "height": 195.84053156146172, "content": "Check if the review has already been saved to Google Drive"}, "typeVersion": 1}, {"id": "400dff0c-8b2e-4fe2-933e-1f4d14624ca1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [40, 80], "parameters": {"width": 301.27574750830576, "height": 177.34219269102988, "content": "Analyze review sentiment"}, "typeVersion": 1}, {"id": "52757ade-4206-40f9-bf4f-c3aefb004d2e", "name": "Set Parameters", "type": "n8n-nodes-base.set", "position": [-440, -380], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "556e201d-242a-4c0e-bc13-787c2b60f800", "name": "company_id", "type": "string", "value": "COMPANY"}, {"id": "a1f239df-df08-41d8-8b78-d6502266a581", "name": "max_page", "type": "number", "value": 2}]}}, "typeVersion": 3.4}, {"id": "cd7e9d36-7ecd-4d9c-b552-8f46b0cfcc03", "name": "Get reviews", "type": "n8n-nodes-base.httpRequest", "position": [-200, -380], "parameters": {"url": "=https://it.trustpilot.com/review/{{ $json.company_id }}", "options": {"pagination": {"pagination": {"parameters": {"parameters": [{"name": "page", "value": "={{ $pageCount + 1 }}"}]}, "maxRequests": "={{ $json.max_page }}", "requestInterval": 5000, "limitPagesFetched": true}}}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "sort", "value": "recency"}]}}, "typeVersion": 4.2}, {"id": "476ff7b6-ab30-4674-a7fe-b032128ee51a", "name": "Extract", "type": "n8n-nodes-base.html", "position": [60, -380], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "recensioni", "attribute": "href", "cssSelector": "article section a", "returnArray": true, "returnValue": "attribute"}]}}, "typeVersion": 1.2}, {"id": "a2a35455-7d3e-4c4c-aa66-6cbbd48d867a", "name": "Get rows", "type": "n8n-nodes-base.googleSheets", "position": [-200, -100], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "={{ $('Split Out').item.json.recensioni.replace('/reviews/','') }}", "lookupColumn": "Id"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1QZhQqg79-HVBQh8Y2ihMq67UIYIRrJFKLQalcFvtDaY", "cachedResultUrl": "", "cachedResultName": "Trustpilot Review"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "2d507fe6-a4fc-42ff-97ff-dfd552c651ab", "name": "Get Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [-440, -100], "parameters": {"columns": {"value": {"Id": "={{ $('Split Out').item.json.recensioni.replace('/reviews/','') }}"}, "schema": [{"id": "Id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Data", "type": "string", "display": true, "required": false, "displayName": "Data", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Nome", "type": "string", "display": true, "required": false, "displayName": "Nome", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Località", "type": "string", "display": true, "required": false, "displayName": "Località", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Valutazione", "type": "string", "display": true, "required": false, "displayName": "Valutazione", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Sentiment", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1QZhQqg79-HVBQh8Y2ihMq67UIYIRrJFKLQalcFvtDaY", "cachedResultUrl": "", "cachedResultName": "Trustpilot Reviews"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "executeOnce": false, "typeVersion": 4.5}, {"id": "0a1fab6e-96b7-403b-884e-f67be6e23fa5", "name": "Get Single review", "type": "n8n-nodes-base.httpRequest", "position": [320, -120], "parameters": {"url": "=https://it.trustpilot.com{{ $('Split Out').item.json.recensioni }}", "options": {}}, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "7d322d76-1032-405a-9d46-2958761a184d", "name": "Extract review", "type": "n8n-nodes-base.html", "position": [540, -120], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "recensione", "cssSelector": "article", "returnArray": true}]}}, "typeVersion": 1.2}, {"id": "952484e5-8e87-4eb3-99a6-5bf26c701ba8", "name": "Update sheet", "type": "n8n-nodes-base.googleSheets", "position": [520, 120], "parameters": {"columns": {"value": {"Id": "={{ $('Split Out').item.json.recensioni.replace('/reviews/','') }}", "URL": "=https://it.trustpilot.com{{ $('Split Out').item.json.recensioni }}", "Data": "={{ $('Information Extractor').item.json.output.data }}", "Nome": "={{ $json.output.autore }}", "Testo": "={{ $('Information Extractor').item.json.output.testo }}", "Titolo": "={{ $('Information Extractor').item.json.output.titolo }}", "Località": "={{ $('Information Extractor').item.json.output.nazione }}", "Sentiment": "={{ $json.sentimentAnalysis.category }}", "Valutazione": "={{ $('Information Extractor').item.json.output.valutazione }}", "N. Recensioni": "={{ $('Information Extractor').item.json.output.n_recensioni }}"}, "schema": [{"id": "Id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Data", "type": "string", "display": true, "required": false, "displayName": "Data", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Nome", "type": "string", "display": true, "required": false, "displayName": "Nome", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Località", "type": "string", "display": true, "required": false, "displayName": "Località", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Valutazione", "type": "string", "display": true, "required": false, "displayName": "Valutazione", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Sentiment", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Sentiment", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1QZhQqg79-HVBQh8Y2ihMq67UIYIRrJFKLQalcFvtDaY", "cachedResultUrl": "", "cachedResultName": "Trustpilot Reviews"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "eb853885-816d-4df7-b5ac-900fa89d3df9", "name": "Sentiment Analysis", "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "position": [60, 140], "parameters": {"options": {"categories": "Positive, Neutral, Negative", "systemPromptTemplate": "You are highly intelligent and accurate sentiment analyzer. Analyze the sentiment of the provided text. Categorize it into one of the following: {categories}. Use the provided formatting instructions. Only output the JSON."}, "inputText": "={{ $json.output.testo }}"}, "typeVersion": 1}, {"id": "79f1b9ea-6297-4735-9c0f-9f28dd65efa0", "name": "DeepSeek Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-460, 320], "parameters": {"model": "deepseek-reasoner", "options": {"baseURL": "https://api.deepseek.com/v1"}}, "credentials": {"openAiApi": {"id": "97Cz4cqyiy1RdcQL", "name": "DeepSeek"}}, "typeVersion": 1}, {"id": "159cc88e-1dd3-4bba-a3c8-59a9aad14c88", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [40, 320], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "43c8ee74-159c-4217-9cb4-554c63a3b183", "connections": {"If": {"main": [[{"node": "Get Single review", "type": "main", "index": 0}]]}, "Limit1": {"main": [[{"node": "Get Google Sheets", "type": "main", "index": 0}]]}, "Extract": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Get rows": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Limit1", "type": "main", "index": 0}]]}, "Get reviews": {"main": [[{"node": "Extract", "type": "main", "index": 0}]]}, "Extract review": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Set Parameters": {"main": [[{"node": "Get reviews", "type": "main", "index": 0}]]}, "Get Google Sheets": {"main": [[{"node": "Get rows", "type": "main", "index": 0}]]}, "Get Single review": {"main": [[{"node": "Extract review", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Sentiment Analysis", "type": "ai_languageModel", "index": 0}]]}, "Sentiment Analysis": {"main": [[{"node": "Update sheet", "type": "main", "index": 0}], [{"node": "Update sheet", "type": "main", "index": 0}], [{"node": "Update sheet", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Sentiment Analysis", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Parameters", "type": "main", "index": 0}]]}}}