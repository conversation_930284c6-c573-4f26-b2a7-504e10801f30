{"id": "gkOayLvJnwcTiHbk", "meta": {"instanceId": "bd0e051174def82b88b5cd547222662900558d74b239c4048ea0f6b7ed61c642"}, "name": "itemMatching() example", "tags": [], "nodes": [{"id": "ba0e23f6-aec6-4c22-8e7c-ab4fc65c7767", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [640, 500], "parameters": {}, "typeVersion": 1}, {"id": "8434c3b4-5b80-48e5-803b-b84eb750b2c5", "name": "Customer Datastore (n8n training)", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [880, 500], "parameters": {"operation": "getAllPeople", "returnAll": true}, "typeVersion": 1}, {"id": "4750754a-92a6-44d2-a353-22fbb51a4d00", "name": "Code", "type": "n8n-nodes-base.code", "position": [1440, 500], "parameters": {"language": "python", "pythonCode": "for i,item in enumerate(_input.all()):\n  _input.all()[i].json.restoreEmail = _('Customer Datastore (n8n training)').itemMatching(i).json.email\n\nreturn _input.all();"}, "typeVersion": 2}, {"id": "9ac437bd-0d0d-4d92-845a-a1c9a7976d4d", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1180, 500], "parameters": {"fields": {"values": [{"name": "name", "stringValue": "={{ $json.name }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "d59c512c-2dca-4960-b287-b4908713b0a3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [820, 400], "parameters": {"height": 304, "content": "## Generate example data"}, "typeVersion": 1}, {"id": "fad37032-13cc-461e-b48e-a2f470d07823", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1100, 398], "parameters": {"height": 303, "content": "## Reduce the data\n\nRemove all data except the names"}, "typeVersion": 1}, {"id": "d0751fce-d9f0-40bf-aeb2-9dbc5d0e9bdb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1380, 400], "parameters": {"height": 304, "content": "## Restore\n\nRestore the email address data"}, "typeVersion": 1}, {"id": "2b1a67e9-60d6-411e-8ae7-94b02da6be34", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [430, 220], "parameters": {"width": 352, "height": 264, "content": "## About this workflow\n\nThis workflow provides a simple example of how to use `itemMatching(itemIndex: Number)` in the Code node to retrieve linked items from earlier in the workflow.\n\nThis example uses JavaScript. Refer to [Retrieve linked items from earlier in the workflow](https://docs.n8n.io/code/cookbook/builtin/itemmatching/) for the Python code.\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "02e18c8e-1bec-4170-a2d0-72ec6e063273", "connections": {"Edit Fields": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Customer Datastore (n8n training)", "type": "main", "index": 0}]]}, "Customer Datastore (n8n training)": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}}