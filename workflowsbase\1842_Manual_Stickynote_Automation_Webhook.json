{"id": "do4h6jnTGWDjCXV7", "meta": {"instanceId": "5f49f71237f6d19bde83a657cffcbbba83eaa1c826edc52411d381e8bedfe5ce", "templateCredsSetupCompleted": true}, "name": "<PERSON><PERSON>", "tags": [], "nodes": [{"id": "513658bc-f898-431c-a005-973013fb12a3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-840, -200], "parameters": {}, "typeVersion": 1}, {"id": "5ba22d17-af12-4665-85c5-f704835f71a4", "name": "Write Result File to Disk", "type": "n8n-nodes-base.readWriteFile", "position": [-100, -200], "parameters": {"options": {}, "fileName": "document.pdf", "operation": "write", "dataPropertyName": "=data1"}, "typeVersion": 1}, {"id": "2bf6b07f-068d-4bbd-8d59-93768118c07c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -340], "parameters": {"width": 218, "height": 132, "content": "## Authentication\nConversion requests must be authenticated. Please create \n[ConvertAPI account to get authentication secret](https://www.convertapi.com/a/signin)"}, "typeVersion": 1}, {"id": "e1f7f9b3-6806-42e6-83f8-cdd3f45f236e", "name": "Download first remote PDF File", "type": "n8n-nodes-base.httpRequest", "position": [-640, -200], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo.pdf", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "data1"}}}}, "typeVersion": 4.2}, {"id": "00acf3e1-1bb8-4dc0-826a-a2c58f5d82d5", "name": "Download second PDF File", "type": "n8n-nodes-base.httpRequest", "position": [-460, -200], "parameters": {"url": "https://cdn.convertapi.com/public/files/demo2.pdf", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "data2"}}}}, "typeVersion": 4.2}, {"id": "6c2a428e-e0af-485b-bdde-70785bc0d508", "name": "PDF merge API HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-280, -200], "parameters": {"url": "https://v2.convertapi.com/convert/pdf/to/merge", "method": "POST", "options": {"response": {"response": {"neverError": "=", "responseFormat": "file", "outputPropertyName": "=data"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "files[0]", "parameterType": "formBinaryData", "inputDataFieldName": "data1"}, {"name": "files[1]", "parameterType": "formBinaryData", "inputDataFieldName": "data2"}]}, "genericAuthType": "httpQueryAuth", "headerParameters": {"parameters": [{"name": "Accept", "value": "application/octet-stream"}]}}, "credentials": {"httpQueryAuth": {"id": "F3A9wZC8zcgbB8sr", "name": "Query Auth account"}}, "notesInFlow": true, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "22a14e07-f11e-46e4-a238-ecdb9f1a8f81", "connections": {"Download second PDF File": {"main": [[{"node": "PDF merge API HTTP Request", "type": "main", "index": 0}]]}, "PDF merge API HTTP Request": {"main": [[{"node": "Write Result File to Disk", "type": "main", "index": 0}]]}, "Download first remote PDF File": {"main": [[{"node": "Download second PDF File", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download first remote PDF File", "type": "main", "index": 0}]]}}}