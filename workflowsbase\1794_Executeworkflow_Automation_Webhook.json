{"name": "🤖Content Creator Agent", "nodes": [{"parameters": {"toolDescription": "Use this tool to search the internet", "method": "POST", "url": "https://api.tavily.com/search", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"api_key\": \"your-api-key\",\n    \"query\": \"{searchTerm}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": true,\n    \"topic\": \"news\",\n    \"include_raw_content\": true,\n    \"max_results\": 3\n} ", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user has requested to write a blog about", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [240, 180], "id": "0fb22922-121d-4f1c-8423-77c3cb7893ce", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.query}}", "options": {"systemMessage": "=# Overview\nYou are a skilled AI blog writer specializing in engaging, well-structured, and informative content. Your writing style is clear, compelling, and tailored to the target audience. You optimize for readability, SEO, and value, ensuring blogs are well-researched, original, and free of fluff.\n\n## Tools\nTavily - Use this to search the web about the requested topic for the blog post.\n\n## Blog Requirements\nFormat all blog content in HTML, using proper headings (<h1>, <h2>), paragraphs (<p>), bullet points (<ul><li>), and links (<a href=\"URL\">) for citations. All citations from the Tavily tool must be preserved, with clickable hyperlinks so readers can access the original sources.\n\nMaintain a natural, human-like tone, use varied sentence structures, and include relevant examples or data when needed. Structure content for easy reading with concise paragraphs and logical flow. Always ensure factual accuracy and align the tone with the intended brand or purpose.\""}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [120, -100], "id": "585eaf6a-3f7b-4a85-973e-fd78806ba230", "name": "Content Creator Agent", "onError": "continueErrorOutput"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [-40, 140], "id": "0ad7fbd5-5317-4979-9688-d99bc3a3fad2", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "iEsH2oywXIJiWHnM", "name": "Anthropic account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "14d9076e-27ea-4846-8b44-f83cf4022b9e", "name": "response", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [560, -180], "id": "ec1997eb-6a99-488b-b496-8355df6c003c", "name": "Response"}, {"parameters": {"assignments": {"assignments": [{"id": "f2a8ff2d-6b59-4ad6-a2e7-8705354f4105", "name": "response", "value": "Error occurred. Please try again.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [560, 0], "id": "0cf971a0-cd9f-4bcf-b020-4839fd3a3708", "name": "Try Again"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-140, -100], "id": "9ad2ac76-7c2b-40ca-9bf2-9c30ac8d132b", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"Tavily": {"ai_tool": [[{"node": "Content Creator Agent", "type": "ai_tool", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "Content Creator Agent", "type": "ai_languageModel", "index": 0}]]}, "Content Creator Agent": {"main": [[{"node": "Response", "type": "main", "index": 0}], [{"node": "Try Again", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Content Creator Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "18d27333-3b4c-4fe7-a85d-bbc7000820cf", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "WWSu94V939ATcqvi", "tags": []}