{"id": "aswQJmksAOmHmn8c", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa"}, "name": "Fetch the Most Recent Document from Google Drive", "tags": [{"id": "uScnF9NzR3PLIyvU", "name": "Published", "createdAt": "2025-03-21T07:22:28.491Z", "updatedAt": "2025-03-21T07:22:28.491Z"}], "nodes": [{"id": "d9df98fe-bf03-45bd-9cb9-ed32371b7970", "name": "Google Docs", "type": "n8n-nodes-base.googleDocs", "position": [100, 500], "parameters": {"operation": "get", "documentURL": "={{ $json.id }}"}, "credentials": {"googleDocsOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 2}, {"id": "46daf9a2-0d13-49c3-8272-e366888e1960", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [440, 440], "parameters": {}, "typeVersion": 1}, {"id": "9dafd444-257c-4f44-9550-1dbd19dc44d4", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [700, 440], "parameters": {}, "typeVersion": 1}, {"id": "259a7fa0-4b37-453e-a730-fb2fc7bc3eb0", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1040, 540], "parameters": {"columns": {"value": {"Name": "={{ $('Google Drive ').item.json.lastModifyingUser.displayName }}", "Email ": "={{ $('Google Drive ').item.json.lastModifyingUser.emailAddress }}", "Summarise Conetent data ": "={{ $json.message.content }}"}, "schema": [{"id": "Email ", "type": "string", "display": true, "required": false, "displayName": "Email ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Summarise Conetent data ", "type": "string", "display": true, "required": false, "displayName": "Summarise Conetent data ", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "", "__regex": "https:\\/\\/(?:drive|docs)\\.google\\.com(?:\\/.*|)\\/d\\/([0-9a-zA-Z\\-_]+)(?:\\/.*|)"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 4.5}, {"id": "d5b63de6-bc9a-4e44-a9a2-85026a16aec7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-320, 180], "parameters": {"color": 5, "height": 260, "content": "## Get Latest File\n"}, "typeVersion": 1}, {"id": "d00720d9-a344-48c9-9c31-7c4391ecda70", "name": "Google Drive ", "type": "n8n-nodes-base.googleDriveTrigger", "notes": "Received the doc", "position": [-240, 260], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "url", "value": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 1}, {"id": "4e326b5d-f116-4de7-bf4b-bac11772e54d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [20, 400], "parameters": {"color": 5, "width": 260, "height": 260, "content": "## Get Document Content\n"}, "typeVersion": 1}, {"id": "b2f25e20-0c61-4af4-b2b5-dbeb20720c3b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 200], "parameters": {"color": 5, "width": 440, "height": 380, "content": "## AI Summarization\n"}, "typeVersion": 1}, {"id": "af9b81f3-b65d-4957-8471-978dc90970f2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [920, 420], "parameters": {"color": 5, "width": 300, "height": 280, "content": "## Store Summary in Sheet\n"}, "typeVersion": 1}, {"id": "4cd99298-968b-4a47-bcf9-b4e006d8dab0", "name": "Generate Summary AI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [460, 280], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Summarise the below content\n {{ $json.content }}"}]}}, "typeVersion": 1.7}, {"id": "af7afd98-8707-4db6-acb0-796427f6e304", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 5, "width": 800, "height": 80, "content": "# Google Doc Summarizer to Google Sheets\n"}, "typeVersion": 1}, {"id": "d0c4ae80-d120-457a-975d-7cfcb963b922", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-260, 760], "parameters": {"color": 5, "width": 1280, "content": "## Description\nThis workflow streamlines and automates the end-to-end process of managing recently added document files in Google Drive. It begins by identifying the most recently uploaded .doc file in a designated folder within Google Drive. The document's content is then directly retrieved and passed through an AI-powered summarization model that condenses the content into a concise and meaningful summary. Finally, the summarized content, along with relevant metadata such as the document's name, upload date, and other details, is systematically stored in a Google Sheet. This ensures easy reference, enhanced organization, and quick access to key information, making it an ideal solution for managing and summarizing large volumes of document data efficiently."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b3ee0a62-7c2f-4dc4-9e2c-f16211e02008", "connections": {"Wikipedia": {"ai_tool": [[{"node": "Generate Summary AI", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Generate Summary AI", "type": "ai_tool", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Generate Summary AI", "type": "main", "index": 0}]]}, "Google Drive ": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Generate Summary AI": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}