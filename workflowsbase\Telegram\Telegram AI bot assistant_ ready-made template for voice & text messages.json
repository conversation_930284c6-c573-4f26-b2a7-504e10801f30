{"id": "HJwTWtzlhK8Q5SOv", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a", "templateCredsSetupCompleted": true}, "name": "Telegram AI multi-format chatbot", "tags": [], "nodes": [{"id": "65196267-0d57-4af4-9081-962701478146", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [660, 640], "parameters": {"model": "gpt-4o", "options": {"temperature": 0.7, "frequencyPenalty": 0.2}}, "credentials": {"openAiApi": {"id": "rveqdSfp7pCRON1T", "name": "Ted's Tech Talks OpenAi"}}, "typeVersion": 1}, {"id": "fc446ef0-2f15-42e7-a993-7960d76d8876", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [800, 640], "parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "typeVersion": 1}, {"id": "51c3cddd-fc21-4fff-b615-ea7080c47947", "name": "Correct errors", "type": "n8n-nodes-base.telegram", "position": [1220, 580], "parameters": {"text": "={{ $('AI Agent').item.json.output.replace(/&/g, \"&amp;\").replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\") }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.1}, {"id": "d931b7e1-bc17-431e-ae67-967b6ef79236", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-440, 480], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "parameters": {"updates": ["*"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1}, {"id": "b33335ff-5dea-4fff-8f63-fea2b11b8241", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [60, 600], "parameters": {"fileId": "={{$json.message.voice.file_id}}", "resource": "file"}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.2}, {"id": "2954ced6-ab98-42e6-bf64-237146a433e0", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [440, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}}, "typeVersion": 3.4}, {"id": "e18de374-941f-4c2e-ab6c-6c6f68f2ce12", "name": "Send final reply", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [1040, 460], "parameters": {"text": "={{ $json.output }} \n\nThank you for your{{ $('Combine content and set properties').item.json['Source Type'] }} {{ $('Combine content and set properties').item.json['Message Type '] }} 🤗", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.1}, {"id": "b47a9583-ce5c-464f-a9e6-153fb42e685f", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [60, 300], "parameters": {"text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.2}, {"id": "0196b49e-90a1-4f2f-8b94-492fced37dbf", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [240, 600], "parameters": {"options": {"language": "", "temperature": 0.7}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "rveqdSfp7pCRON1T", "name": "Ted's Tech Talks OpenAi"}}, "typeVersion": 1.5}, {"id": "66505b83-e0c3-4d9d-8e1a-9b54030e29e7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-466.12784869794086, 220], "parameters": {"width": 1035.4478381373049, "height": 547.5630890194532, "content": "## Receive and pre-process messages \n"}, "typeVersion": 1}, {"id": "44087d3f-86c8-407c-8791-645d167165cb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [620, 220], "parameters": {"color": 2, "width": 861.262180151035, "height": 550.5748478134515, "content": "## 1. Send incoming message to the AI Agent\n## 2. Deliver agent reply to the user \n"}, "typeVersion": 1}, {"id": "d7e58831-de97-483f-8b8a-583f85397245", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [20, 553.0639243489702], "parameters": {"color": 6, "width": 367.73614918993235, "height": 194.83713159725437, "content": "## Transcribe audio"}, "typeVersion": 1}, {"id": "89515d80-6efc-40a8-95ce-343d4ff4dbee", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [-180, 300], "parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "operation": "sendChatAction"}, "credentials": {"telegramApi": {"id": "9dexJXnlVPA6wt8K", "name": "Chat & Sound"}}, "typeVersion": 1.2}, {"id": "c925d059-f843-473c-bfd4-3c563d80ca0f", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [680, 460], "parameters": {"text": "={{ $json.CombinedMessage }}", "options": {"humanMessage": "TOOLS\n------\nAssistant can ask the user to use tools to look up information that may be helpful in answering the users original question. The tools the human can use are:\n\n{tools}\n\n{format_instructions}\n\nUSER'S INPUT\n--------------------\nHere is the user's input (remember to respond with a markdown code snippet of a json blob with a single action, and NOTHING else):\n\n{{input}}", "systemMessage": "=You are a helpful AI assistant. You are chatting with the user named `{{ $('Determine content type').item.json.message.from.first_name }}`. You need to address the user by their name. Today is {{ DateTime.fromISO($now).toLocaleString(DateTime.DATETIME_FULL) }}\n\nIn your reply, always send a message in Telegram-supported HTML format. Here are the formatting instructions:\n1. The following tags are currently supported:\n<b>bold</b>, <strong>bold</strong>\n<i>italic</i>, <em>italic</em>\n<u>underline</u>, <ins>underline</ins>\n<s>strikethrough</s>, <strike>strikethrough</strike>, <del>strikethrough</del>\n<span class=\"tg-spoiler\">spoiler</span>, <tg-spoiler>spoiler</tg-spoiler>\n<b>bold <i>italic bold <s>italic bold strikethrough <span class=\"tg-spoiler\">italic bold strikethrough spoiler</span></s> <u>underline italic bold</u></i> bold</b>\n<a href=\"http://www.example.com/\">inline URL</a>\n<code>inline fixed-width code</code>\n<pre>pre-formatted fixed-width code block</pre>\n2. Any code that you send should be wrapped in these tags: <pre><code class=\"language-python\">pre-formatted fixed-width code block written in the Python programming language</code></pre>\nOther programming languages are supported as well.\n3. All <, > and & symbols that are not a part of a tag or an HTML entity must be replaced with the corresponding HTML entities (< with &lt;, > with &gt; and & with &amp;)\n4. If the user sends you a message starting with / sign, it means this is a Telegram bot command. For example, all users send /start command as their first message. Try to figure out what these commands mean and reply accodringly\n"}}, "typeVersion": 1.1}, {"id": "2c56536d-1a86-4a49-b495-3e877adb308a", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [-180, 480], "parameters": {"rules": {"values": [{"outputKey": "Text", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/"}]}, "renameOutput": true}, {"outputKey": "Voice", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "15ae799b-6868-4519-b579-3f202e4de5b2", "connections": {"AI Agent": {"main": [[{"node": "Send final reply", "type": "main", "index": 0}]]}, "Send final reply": {"main": [[], [{"node": "Correct errors", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}