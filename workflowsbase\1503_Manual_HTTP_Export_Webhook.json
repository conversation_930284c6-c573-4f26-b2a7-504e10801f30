{"id": "IXumIzS9WtPAhKFX", "meta": {"instanceId": "494d0146a0f47676ad70a44a32086b466621f62da855e3eaf0ee51dee1f76753", "templateCredsSetupCompleted": true}, "name": "Export Zammad Objects Users, Roles, Groups and Organizations to Excel", "tags": [], "nodes": [{"id": "59b12a25-d90f-47f0-a043-a51f71f5761e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-120, -80], "parameters": {}, "typeVersion": 1}, {"id": "259acda6-75be-4011-b021-56321ab10478", "name": "Zammad Univeral User Object", "type": "n8n-nodes-base.set", "position": [600, -80], "parameters": {"values": {"number": [{"name": "user_id", "value": "={{ $json.id }}"}, {"name": "organization_id", "value": "={{ $json.organization_id }}"}], "string": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "firstname", "value": "={{ $json.firstname }}"}, {"name": "lastname", "value": "={{ $json.lastname }}"}, {"name": "role_ids", "value": "={{ $json.role_ids.join() }}\n"}, {"name": "groups", "value": "={{ $json.group_ids }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "57c68cc2-f5d6-4425-9dc2-b2d6b21f0026", "name": "Zammad Univeral Organization Object", "type": "n8n-nodes-base.set", "position": [600, 160], "parameters": {"values": {"number": [{"name": "organization_id", "value": "={{ $json.id }}"}, {"name": "name", "value": "={{ $json.name }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "c40b275c-1d33-4604-8073-3651641c94ed", "name": "<PERSON><PERSON><PERSON> Univeral Role Object", "type": "n8n-nodes-base.set", "position": [600, 400], "parameters": {"values": {"number": [{"name": "role_id", "value": "={{ $json.id }}"}, {"name": "name", "value": "={{ $json.name }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "29a257db-955d-4ff3-a7bb-f9a888f96e78", "name": "Get all Organizations", "type": "n8n-nodes-base.zammad", "position": [340, 160], "parameters": {"resource": "organization", "operation": "getAll", "returnAll": true}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "b4a9c2ca-b110-46ba-b5b9-2e8d8e357dfb", "name": "Get all Roles", "type": "n8n-nodes-base.httpRequest", "position": [340, 400], "parameters": {"url": "={{ $json.zammad_base_url }}/api/v1/roles", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.zammad_api_key }}"}]}}, "typeVersion": 4.2}, {"id": "9f5049cc-37ca-4069-86a1-75dffa9c2c96", "name": "Convert to Excel Organizations", "type": "n8n-nodes-base.convertToFile", "position": [1320, 140], "parameters": {"options": {"fileName": "Zammad_Organizations.xlsx"}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "1a05b494-919c-4e53-8772-8c504e667f1c", "name": "Convert to Excel Roles", "type": "n8n-nodes-base.convertToFile", "position": [1340, 380], "parameters": {"options": {"fileName": "Zammad_Roles.xlsx"}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "f1160af5-fcee-421d-9ede-b6f56ac0ce8d", "name": "Convert to Excel Users", "type": "n8n-nodes-base.convertToFile", "position": [1300, -100], "parameters": {"options": {"fileName": "Zammad_Users.xslx"}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "192c5342-5140-48f9-acb0-d14a41064fa3", "name": "Get all Users", "type": "n8n-nodes-base.zammad", "position": [340, -80], "parameters": {"filters": {}, "operation": "getAll", "returnAll": true}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "ae687777-c1cb-4a23-ae1e-aa34febc27d6", "name": "Zammad Univeral Group Object", "type": "n8n-nodes-base.set", "position": [620, 620], "parameters": {"values": {"number": [{"name": "group_id", "value": "={{ $json.id }}"}, {"name": "name", "value": "={{ $json.name }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "0d38e0b3-1a59-4a8f-9a04-8526aba91fd5", "name": "Get all Groups", "type": "n8n-nodes-base.httpRequest", "position": [340, 620], "parameters": {"url": "={{ $json.zammad_base_url }}/api/v1/groups", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.zammad_api_key }}"}]}}, "typeVersion": 4.2}, {"id": "e30bd0ad-9772-4af7-9012-99199fee65b2", "name": "If", "type": "n8n-nodes-base.if", "position": [900, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ca9d3a3-b726-4396-8cec-4a74c8e3949b", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": 1781}]}}, "typeVersion": 2.2}, {"id": "2a83536e-e250-425a-aac7-f26ede0caf54", "name": "Basic Variables", "type": "n8n-nodes-base.set", "position": [60, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "68b32087-5e23-4590-8042-0061234ce479", "name": "zammad_base_url", "type": "string", "value": "-put-your-zammad-base-url-"}, {"id": "7db7572e-2524-4f2a-a1d6-b44330662c30", "name": "zammad_api_key", "type": "string", "value": "-put-your-api-key-"}]}}, "typeVersion": 3.4}, {"id": "db6a3024-9778-4d1e-8b25-34f2ee3ec26f", "name": "Convert to Excel Groups", "type": "n8n-nodes-base.convertToFile", "position": [1340, 600], "parameters": {"options": {"fileName": "Zammad_Groups.xlsx"}, "operation": "xlsx"}, "typeVersion": 1.1}, {"id": "bd191e0d-927d-44ca-afe6-fa6c7f3d59a2", "name": "Filter Groups if needed", "type": "n8n-nodes-base.if", "position": [900, 620], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ca9d3a3-b726-4396-8cec-4a74c8e3949b", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": {}}]}}, "typeVersion": 2.2}, {"id": "c7c7b6b4-7faa-48b4-b7d8-6782dd1e4187", "name": "Filter Roles if needed", "type": "n8n-nodes-base.if", "position": [900, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ca9d3a3-b726-4396-8cec-4a74c8e3949b", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": 1781}]}}, "typeVersion": 2.2}, {"id": "a255bc7b-5d35-4671-852e-53f2b0980c26", "name": "Filter Organizations if needed", "type": "n8n-nodes-base.if", "position": [900, 160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0ca9d3a3-b726-4396-8cec-4a74c8e3949b", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": 1781}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8282fc5a-1ed4-4730-8e08-3d9f279dc3b5", "connections": {"If": {"main": [[{"node": "Convert to Excel Users", "type": "main", "index": 0}]]}, "Get all Roles": {"main": [[{"node": "<PERSON><PERSON><PERSON> Univeral Role Object", "type": "main", "index": 0}]]}, "Get all Users": {"main": [[{"node": "Zammad Univeral User Object", "type": "main", "index": 0}]]}, "Get all Groups": {"main": [[{"node": "Zammad Univeral Group Object", "type": "main", "index": 0}]]}, "Basic Variables": {"main": [[{"node": "Get all Roles", "type": "main", "index": 0}, {"node": "Get all Groups", "type": "main", "index": 0}]]}, "Get all Organizations": {"main": [[{"node": "Zammad Univeral Organization Object", "type": "main", "index": 0}]]}, "Filter Roles if needed": {"main": [[{"node": "Convert to Excel Roles", "type": "main", "index": 0}]]}, "Filter Groups if needed": {"main": [[{"node": "Convert to Excel Groups", "type": "main", "index": 0}]]}, "Zammad Univeral Role Object": {"main": [[{"node": "Filter Roles if needed", "type": "main", "index": 0}]]}, "Zammad Univeral User Object": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Zammad Univeral Group Object": {"main": [[{"node": "Filter Groups if needed", "type": "main", "index": 0}]]}, "Filter Organizations if needed": {"main": [[{"node": "Convert to Excel Organizations", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get all Users", "type": "main", "index": 0}, {"node": "Basic Variables", "type": "main", "index": 0}, {"node": "Get all Organizations", "type": "main", "index": 0}]]}, "Zammad Univeral Organization Object": {"main": [[{"node": "Filter Organizations if needed", "type": "main", "index": 0}]]}}}