{"id": "XYz1JYUXFHFVdlLj", "meta": {"instanceId": "e634e668fe1fc93a75c4f2a7fc0dad807ca318b79654157eadb9578496acbc76", "templateCredsSetupCompleted": true}, "name": "Restore your workflows from GitHub", "tags": [{"id": "2RWIfLUVCa0bnmGX", "name": "N8n", "createdAt": "2025-03-06T09:58:39.214Z", "updatedAt": "2025-03-06T09:58:39.214Z"}], "nodes": [{"id": "cab3a8b6-4106-4449-8b12-d57cc93477ab", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1040, -160], "parameters": {}, "typeVersion": 1}, {"id": "733ce565-bd6e-4297-8166-52f79d68a0f2", "name": "Globals", "type": "n8n-nodes-base.set", "position": [-840, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6cf546c5-5737-4dbd-851b-17d68e0a3780", "name": "repo.owner", "type": "string", "value": "BeyondspaceStudio"}, {"id": "452efa28-2dc6-4ea3-a7a2-c35d100d0382", "name": "repo.name", "type": "string", "value": "n8n-backup"}, {"id": "81c4dc54-86bf-4432-a23f-22c7ea831e74", "name": "repo.path", "type": "string", "value": "workflows"}]}}, "typeVersion": 3.4}, {"id": "e20b94c3-f33a-48ff-b1df-aa8acc8f6f44", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1460, -280], "parameters": {"width": 320, "height": 420, "content": "## Restore from GitHub \nThis workflow will restore all instance workflows from GitHub backups.\n\n\n### Setup\nOpen `Globals` node and update the values below 👇\n\n- **repo.owner:** your Github username\n- **repo.name:** the name of your repository\n- **repo.path:** the folder to use within the repository.\n\n\nIf your username was `john-doe` and your repository was called `n8n-backups` and you wanted the workflows to go into a `workflows` folder you would set:\n\n- repo.owner - john-doe\n- repo.name - n8n-backups\n- repo.path - workflows/\n"}, "typeVersion": 1}, {"id": "34db1b48-6629-4760-b264-e782949d34bc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-900, -280], "parameters": {"color": 4, "width": 150, "height": 80, "content": "## Edit this node 👇"}, "typeVersion": 1}, {"id": "088b7e98-001c-4a24-b8c1-44c82285b894", "name": "Get all files in given path", "type": "n8n-nodes-base.httpRequest", "position": [-1000, 160], "parameters": {"url": "=https://api.github.com/repos/{{ $json.repo.owner }}/{{ $json.repo.name }}/contents/{{ $json.repo.path }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi"}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 4.2}, {"id": "9a148510-3e72-4cb1-a194-a7c90122be7e", "name": "Split the result", "type": "n8n-nodes-base.splitOut", "position": [-760, 160], "parameters": {"options": {}, "fieldToSplitOut": "path"}, "typeVersion": 1}, {"id": "cbcfa116-056b-4493-8f74-0c9f3744a5d1", "name": "Get file content from GitHub", "type": "n8n-nodes-base.github", "position": [-540, 160], "parameters": {"owner": {"__rl": true, "mode": "name", "value": "BeyondspaceStudio"}, "filePath": "={{ $('Get all files in given path').item.json.path }}", "resource": "file", "operation": "get", "repository": {"__rl": true, "mode": "name", "value": "n8n-backup"}, "additionalParameters": {}}, "credentials": {"githubApi": {"id": "3FYHiPFtycAFT8V0", "name": "GitHub account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "78e7e4cd-dbde-4767-9b26-503063ea35fc", "name": "Convert files to JSON", "type": "n8n-nodes-base.extractFromFile", "position": [-320, 160], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "ee851935-f8fd-4999-a4c3-50e0c28b915a", "name": "Restore n8n Workflows", "type": "n8n-nodes-base.n8n", "position": [-100, 160], "parameters": {"operation": "create", "requestOptions": {}, "workflowObject": "={{ JSON.stringify($json.data) }}"}, "credentials": {"n8nApi": {"id": "dzYjDgtEXtpRPKhe", "name": "n8n account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "f8d4cd76-d31e-4842-9ec3-64ab3253728c", "connections": {"Globals": {"main": [[{"node": "Get all files in given path", "type": "main", "index": 0}]]}, "Split the result": {"main": [[{"node": "Get file content from GitHub", "type": "main", "index": 0}]]}, "Convert files to JSON": {"main": [[{"node": "Restore n8n Workflows", "type": "main", "index": 0}]]}, "Get all files in given path": {"main": [[{"node": "Split the result", "type": "main", "index": 0}]]}, "Get file content from GitHub": {"main": [[{"node": "Convert files to JSON", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}}}