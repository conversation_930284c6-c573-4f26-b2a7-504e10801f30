{"id": "g25bM3Hj71T3ZVVe", "meta": {"instanceId": "21754f977ce20b07e6fe64be3fbc663f6e6f730423d6e46c6cd2bf5b5e70a383"}, "name": "Streamline data from an n8n form into Google Sheet and Airtable", "tags": [], "nodes": [{"id": "32bd3bcb-7de7-4ca3-ba31-897e90f663b1", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [720, -400], "webhookId": "c07c8eb6-cf56-4941-91cc-e3cb31c90b5c", "parameters": {"path": "c07c8eb6-cf56-4941-91cc-e3cb31c90b5c", "options": {}, "formTitle": "Data Colleacation", "formFields": {"values": [{"fieldLabel": "What's your name ?", "requiredField": true}, {"fieldLabel": "Where do you live ?", "requiredField": true}, {"fieldLabel": "Your Email ?", "requiredField": true}]}}, "typeVersion": 2}, {"id": "bf341165-2698-4f42-a92d-bc5e9a750bf1", "name": "Extracting Date and Time Fields from 'submittedAt' Field", "type": "n8n-nodes-base.code", "position": [920, -400], "parameters": {"jsCode": "// Loop over input items and separate date and time into two new fields\nfor (const item of $input.all()) {\n  // Extract date and time from 'submittedAt' field\n  const submittedAt = new Date(item.json['submittedAt']);\n  const date = submittedAt.toISOString().split('T')[0]; // Get date part\n  const time = submittedAt.toISOString().split('T')[1].split('.')[0]; // Get time part\n\n  // Remove the old 'submittedAt' field\n  delete item.json['submittedAt'];\n\n  // Add new 'Date' and 'Time' fields with respective values\n  item.json['Date'] = date;\n  item.json['Time'] = time;\n}\n\nreturn $input.all();\n"}, "typeVersion": 2}, {"id": "c9955ba1-0aa4-476b-b2ac-8a458b1547b3", "name": "Format the Fields", "type": "n8n-nodes-base.set", "position": [1100, -400], "parameters": {"fields": {"values": [{"name": "Name", "stringValue": "={{ $json['What\\'s your name ?'] }}"}, {"name": "City", "stringValue": "={{ $json['Where do you live ?'] }}"}, {"name": "Date", "stringValue": "={{ $json.Date }}"}, {"name": "Time", "stringValue": "={{ $json.Time }}"}, {"name": "Email", "stringValue": "={{ $json['Your Email ?'] }}"}]}, "include": "selected", "options": {}}, "typeVersion": 3.2}, {"id": "50e6057e-4b26-40f6-adc1-1721818f7a46", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [1320, -260], "parameters": {"base": {"__rl": true, "mode": "id", "value": "appIIeJ18fnPkNyNS"}, "table": {"__rl": true, "mode": "id", "value": "tblZvKuOMmtHnv5TH"}, "columns": {"value": {"City": "={{ $json.City }}", "Date": "={{ $json.Date }}", "Name": "={{ $json.Name }}", "Time": "={{ $json.Time }}", "Email": "={{ $json.Email }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "City", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Time", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Time", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "maZEeRzOyC8Q06Zf", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2}, {"id": "4f561bd8-a5dd-4ff2-9d3e-cdac6f762bd4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, -680], "parameters": {"color": 5, "width": 2256.*************, "height": 851.*************, "content": "### Workflow Description:\n\n1. **n8n Form Trigger:**\n   - A trigger node that initiates the workflow when a form is submitted.\n   - Form fields include Name, City, and Email.\n\n2. **Extracting Date and Time Fields from 'submittedAt' Field:**\n   - A code node that extracts Date and Time from the submittedAt field.\n\n3. **Format the Fields:**\n   - Sets the format for the extracted fields (Name, City, Date, Time, Email).\n\n4. **Airtable:**\n   - Creates a new record in Airtable with the formatted data.\n   - Includes columns for Name, City, Email, Time, and Date.\n\n5. **Google Sheets:**\n   - Appends the formatted data to a Google Sheet.\n   - Includes columns for Name, City, Email, Date, and Time.\n\n6. **Gmail:**\n   - Sends an email to the provided Email address.\n   - Subject: \"Testing Text Message Delivery\"\n   - Message: Customized message with Name placeholder.\n\n7. **Gmail1:**\n   - Sends another email using a different template.\n   - Subject includes the Date field.\n   - Similar message content with a different subject line.\n\n### Workflow Connections:\n- n8n Form Trigger -> Extracting Date and Time Fields -> Format the Fields -> Google Sheets & Airtable -> Gmail\n- Google Sheets -> Gmail1\n\nThis workflow collects data from a form submission, processes it to extract Date and Time fields, saves the formatted data to Google Sheets and Airtable, and sends customized emails to the submitter.\n\n\n"}, "typeVersion": 1}, {"id": "a0d53cb5-27c8-4dfb-a1ea-e2403bde1fc4", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1320, -540], "parameters": {"columns": {"value": {"City": "={{ $json.City }}", "Date": "={{ $json.Date }}", "Name": "={{ $json.Name }}", "Time": "={{ $json.Time }}", "Email": "={{ $json.Email }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "City", "type": "string", "display": true, "required": false, "displayName": "City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Time", "type": "string", "display": true, "required": false, "displayName": "Time", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Ss6AEwaXpAl54YQAQDf1z6SRyh6pj719-A9eOzf2Dv4/edit#gid=0", "cachedResultName": "Page"}, "documentId": {"__rl": true, "mode": "list", "value": "1Ss6AEwaXpAl54YQAQDf1z6SRyh6pj719-A9eOzf2Dv4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Ss6AEwaXpAl54YQAQDf1z6SRyh6pj719-A9eOzf2Dv4/edit?usp=drivesdk", "cachedResultName": "Streamline data from an n8n form into Google Sheet and Airtable"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "L5CnisK8R3BgVGcO", "name": "Omar sheet"}}, "typeVersion": 4.2}, {"id": "de697574-f547-4374-86d9-c6d9f709c404", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1560, -260], "parameters": {"sendTo": "={{ $json.fields.Email }}", "message": "=Dear {{ $json.fields.Name }} ..\n\nHey there! Just testing to see if this message goes through. Let me know if you receive it. \n\nThanks! \nSupport Team", "options": {}, "subject": "Testing Text Message Delivery", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "UJx4Tiq8WRtxWEIP", "name": "<PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "66677b7e-d053-4050-a65c-9c9f8f689646", "name": "Gmail1", "type": "n8n-nodes-base.gmail", "position": [1560, -540], "parameters": {"sendTo": "={{ $json.Email }}", "message": "=Dear {{ $json.Name }} ..\n\nHey there! Just testing to see if this message goes through. Let me know if you receive it. \n\nThanks! \nSupport Team  ", "options": {}, "subject": "=Testing Text Message Delivery , ( {{ $json.Date }} ) ", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "UJx4Tiq8WRtxWEIP", "name": "<PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "e440b4cb-6910-4bc7-b3df-7c14dd9c43a9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1820, -640], "parameters": {"width": 510.8381838182147, "height": 206.48715095387286, "content": "### Links to Node Documentation:\n1. [n8n Form Trigger Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.formTrigger)\n2. [Code Node Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.code)\n3. [Set Node Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.set)\n4. [Airtable Node Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.airtable)\n5. [Google Sheets Node Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.googleSheets)\n6. [Gmail Node Documentation](https://docs.n8n.io/nodes/n8n-nodes-base.gmail)\n"}, "typeVersion": 1}, {"id": "541ecd4c-22bc-4bc9-8364-ca73b4650092", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [680, -640], "parameters": {"width": 1105.0652438372836, "height": 630.9350509674927, "content": ""}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "de903de6-c793-4a64-9d3c-0ade08d6994e", "connections": {"Airtable": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Extracting Date and Time Fields from 'submittedAt' Field", "type": "main", "index": 0}]]}, "Format the Fields": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}, {"node": "Airtable", "type": "main", "index": 0}]]}, "Extracting Date and Time Fields from 'submittedAt' Field": {"main": [[{"node": "Format the Fields", "type": "main", "index": 0}]]}}}