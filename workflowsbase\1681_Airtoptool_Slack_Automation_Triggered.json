{"id": "TS1wT16JCcy1Dt9Q", "meta": {"instanceId": "28a947b92b197fc2524eaba16e57560338657b2b0b5796300b2f1cedc1d0d355", "templateCredsSetupCompleted": true}, "name": "Airtop Web Agent", "tags": [{"id": "zGYM9A88nf1YAceR", "name": "Agent", "createdAt": "2025-04-16T21:49:22.104Z", "updatedAt": "2025-04-16T21:49:22.104Z"}, {"id": "zKNO4Omjzfu6J25M", "name": "Demo", "createdAt": "2025-04-15T18:59:57.364Z", "updatedAt": "2025-04-15T18:59:57.364Z"}], "nodes": [{"id": "43e674dd-82e5-49b3-8e4d-f13793e5e6c9", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [220, 60], "parameters": {"text": "={{ $json.Prompt }}", "options": {"maxIterations": 20, "systemMessage": "=# Agent goal\nYou are a smart, advanced web agent connected with tools that allow you to manage a remote web browser. Your goal is to fulfill the human's request.\n\n## Start the browser\nYou should always start by using the `Start browser` tool to get the `sessionId` and `windowId` required by other tools.\n\n## Use the `Query` tool\nYou don't have access to the browser screen but you can call the `Query` tool to get knowladge and hints of the current browser window. This tool accepts queries in natural language and can output information in plain text, markdown or JSON.\n\n## Use the `Click` tool\nUse the `Click` tool to click on an element on the web page. Describe the element in details in the \"Element Description\" field, Be specific and refer to elements that are on the page. \n\n## Use the `Type` tool\nUse the `Type` tool to type in text boxes. Describe the text box in \"Element Description\" field and the text to type in the \"Text\" field. The 'Type' tool is clicking Enter after typing the text so don't send [ENTER] commandes.\n\n### Examples for how to use the `Query` tool\n- Ask something about the current page:\n  \"Is the user logged in? Answer with Yes or No\"\n- Retrieve information:\n  \"Extract all the posts in the page, include the title and copy, output in JSON format\"\n- Get hints on the UI:\n  \"Is there a link to the contact form? If yes, describe the element in detail\"\n\n\n## Important\n- Start by calling `Start browser` to begin using the browser and provide the initial URL\n- The human ONLY sees your last message\n- Make sure to include all the important information requested by the human in your LAST message\n- Call `End session` with the `sessionId` once you have finished using the browser\n\n", "passthroughBinaryImages": true, "returnIntermediateSteps": false}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.8, "waitBetweenTries": 5000}, {"id": "5e29189a-2b5a-4193-bff1-f4afe1c4b838", "name": "Click", "type": "n8n-nodes-base.airtopTool", "position": [0, 280], "parameters": {"resource": "interaction", "windowId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Window_ID', \"The `windowId` returned by the `Open window` tool\", 'string') }}", "sessionId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Session_ID', \"The `sessionId` returned by the `Create session` tool\", 'string') }}", "descriptionType": "manual", "toolDescription": "Click on any element in the window", "additionalFields": {"waitForNavigation": "load"}, "elementDescription": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Element_Description', `Describe in detail the element to click on, e.g. The menu item \"about us\" at the top of the page`, 'string') }}"}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "********-5acd-4e9b-9a02-7fc5e9a03557", "name": "Query", "type": "n8n-nodes-base.airtopTool", "position": [120, 280], "parameters": {"prompt": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt', `Ask anything and request to extract information from the current page, e.g. \"Is there any sign-up form? yes or no\"`, 'string') }}", "resource": "extraction", "windowId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Window_ID', \"The `windowId` returned by the `Open window` tool\", 'string') }}", "operation": "query", "sessionId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Session_ID', \"The `sessionId` returned by the `Create session` tool\", 'string') }}", "descriptionType": "manual", "toolDescription": "Query the page, ask and extract information", "additionalFields": {}}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "67cee508-c2c0-423e-b1fb-576de026f3ed", "name": "Load URL", "type": "n8n-nodes-base.airtopTool", "position": [240, 280], "parameters": {"url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('URL', ``, 'string') }}", "resource": "window", "windowId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Window_ID', \"The `windowId` returned by the `Open window` tool\", 'string') }}", "operation": "load", "sessionId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Session_ID', \"The `sessionId` returned by the `Create session` tool\", 'string') }}", "descriptionType": "manual", "toolDescription": "Load a URL into the browser window", "additionalFields": {}}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "05262c94-fb9a-4a51-b1c6-8b4981888850", "name": "End session", "type": "n8n-nodes-base.airtopTool", "position": [360, 280], "parameters": {"operation": "terminate", "sessionId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Session_ID', \"The `sessionId` returned by the `Create session` tool\", 'string') }}"}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "e1bab1f8-8127-49a2-b3a3-f672c0687ed6", "name": "Type", "type": "n8n-nodes-base.airtopTool", "position": [480, 280], "parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "resource": "interaction", "windowId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Window_ID', \"The `windowId` returned by the `Open window` tool\", 'string') }}", "operation": "type", "sessionId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Session_ID', \"The `sessionId` returned by the `Create session` tool\", 'string') }}", "pressEnterKey": true, "descriptionType": "manual", "toolDescription": "Type text into the page's element described", "additionalFields": {}, "elementDescription": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Element_Description', `Describe in detail the element to type into, e.g. The search box at the top of the page`, 'string') }}"}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "385c94f1-27b4-492b-8224-41d26dbb76b4", "name": "Start browser", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [600, 280], "parameters": {"name": "Start_Browser", "workflowId": {"__rl": true, "mode": "list", "value": "TS1wT16JCcy1Dt9Q", "cachedResultName": "Airtop Web Agent"}, "description": "Start a new browser session and window", "workflowInputs": {"value": {"url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('url', `URL to load in the browser window`, 'string') }}", "profile_name": "={{ $json['Airtop Profile Name (for sites that require authentication)'] }}"}, "schema": [{"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_name", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["url"], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "7c646c5f-8c98-49a1-9e37-b04a2fa5b9e3", "name": "Claude 3.5 Haiku", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [-120, 280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-5-haiku-20241022", "cachedResultName": "Claude 3.5 Haiku"}, "options": {}}, "credentials": {"anthropicApi": {"id": "npcV2ZKvGmXTUIj9", "name": "<PERSON><PERSON>'s Key"}}, "typeVersion": 1.3}, {"id": "4e810287-ade5-40c6-af4f-5f7df4b3d928", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-340, 160], "webhookId": "dbbb8b5a-a81c-4cde-9f46-f4808d7f0dc4", "parameters": {"options": {}, "formTitle": "Instruction for the Web AI Agent", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Prompt", "placeholder": "e.g. Find the top 10 products in producthunt.com", "requiredField": true}, {"fieldLabel": "Airtop Profile Name (for sites that require authentication)", "placeholder": "e.g. my-airtop-profile"}]}, "formDescription": "Provide detailed instructions to the web AI agent. Use an [Airtop Profile](https://docs.airtop.ai/guides/how-to/saving-a-profile) for websites that need login."}, "typeVersion": 2.2}, {"id": "5bc4020c-f677-45c2-b9f6-dc6cf847df1e", "name": "<PERSON><PERSON>ck", "type": "n8n-nodes-base.slack", "position": [520, 2100], "webhookId": "72d47d9c-6860-4248-8e83-7790264fdaf2", "parameters": {"text": "={{ $json.data.liveViewUrl }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08E83RDJN9", "cachedResultName": "n8n-debug"}, "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "QPfv40eAdL5Eax7G", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "b68a951d-b45b-46a6-bddc-cd0107fd952d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-200, 1960], "parameters": {"color": 5, "width": 220, "height": 300, "content": "## Note\nThis sub-workflow simplifies the session management for the agent"}, "typeVersion": 1}, {"id": "********-e62b-4e30-b42b-039f82c5aab8", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [720, 280], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"results\": {\n\t\t\t\"type\": \"string\",\n            \"description\": \"Synthesis of the agent's results. Must include all the relevant information related to the user's request\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "1d0e64b8-18e3-4de2-b089-96deb51b1e9e", "name": "Output", "type": "n8n-nodes-base.set", "position": [920, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e1d6ab7c-2f45-44fd-9457-bb3046fad4c5", "name": "output", "type": "string", "value": "={{ $json.output.results }}"}]}}, "typeVersion": 3.4}, {"id": "c40e9b06-cac3-4714-b58b-4b60c978f321", "name": "Session", "type": "n8n-nodes-base.airtop", "position": [80, 2100], "parameters": {"profileName": "={{ $json.profile_name }}"}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "fd8f9014-eff8-45c2-9483-0e6eda4a4979", "name": "Window", "type": "n8n-nodes-base.airtop", "position": [300, 2100], "parameters": {"url": "={{ $('When Executed by Another Workflow').item.json.url }}", "resource": "window", "getLiveView": true, "additionalFields": {}}, "credentials": {"airtopApi": {"id": "3urzXgC1IDRxzgbv", "name": "Airtop account 2"}}, "typeVersion": 1}, {"id": "a6dbea48-3be2-4b31-9ee8-dba1c7049d3b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [460, 2080], "parameters": {"color": 7, "width": 220, "height": 340, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### See the agent in action\nEnable this Slack node to receive the URL for the Live View in a message\n\n"}, "typeVersion": 1}, {"id": "9e310e8c-910e-431f-9919-94a06eb2eca0", "name": "Return IDs", "type": "n8n-nodes-base.set", "position": [740, 2100], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0a0680af-39bd-4bc7-b9cd-84c1766c79a1", "name": "sessionId", "type": "string", "value": "={{ $('Session').item.json.sessionId }}"}, {"id": "13940ee8-c1d4-4718-a7b4-176c44c097b7", "name": "windowId", "type": "string", "value": "={{ $('Window').item.json.data.windowId }}"}, {"id": "a0f2005c-2cd2-4a8d-891b-a4759b72a124", "name": "output", "type": "string", "value": "Session and window created successfully"}]}}, "typeVersion": 3.4}, {"id": "a84101b9-165f-47f4-bbc4-8705abfb6e41", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-140, 2100], "parameters": {"workflowInputs": {"values": [{"name": "url"}, {"name": "profile_name"}]}}, "typeVersion": 1.1}, {"id": "b32534f6-3b62-4961-9d54-1e3e288fc185", "name": "Slack1", "type": "n8n-nodes-base.slack", "position": [1180, 160], "webhookId": "72d47d9c-6860-4248-8e83-7790264fdaf2", "parameters": {"text": "={{ $json.output }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08E83RDJN9", "cachedResultName": "n8n-debug"}, "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "QPfv40eAdL5Eax7G", "name": "Slack account 2"}}, "typeVersion": 2.3}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "685b3999-f85a-43fa-8bff-21f9ddbbebd7", "connections": {"Type": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Click": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Query": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Slack": {"main": [[{"node": "Return IDs", "type": "main", "index": 0}]]}, "Output": {"main": [[{"node": "Slack1", "type": "main", "index": 0}]]}, "Window": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Session": {"main": [[{"node": "Window", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Output", "type": "main", "index": 0}]]}, "Load URL": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "End session": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Start browser": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Claude 3.5 Haiku": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "On form submission": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Session", "type": "main", "index": 0}]]}}}