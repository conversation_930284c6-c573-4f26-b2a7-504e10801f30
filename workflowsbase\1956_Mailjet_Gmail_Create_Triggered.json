{"id": "pkw1vY5q1p2nNfNC", "meta": {"instanceId": "24bd2f3b51439b955590389bfa4dd9889fbd30343962de0b7daedce624cf4a71"}, "name": "Forward Netflix emails to multiple email addresses with GMail and Mailjet", "tags": [{"id": "NfcTamKf2RPwzXbo", "name": "automate-everything", "createdAt": "2024-02-14T20:01:44.966Z", "updatedAt": "2024-02-14T20:01:44.966Z"}], "nodes": [{"id": "653e1069-b231-41e4-8257-5276934ec124", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [600, 360], "parameters": {"simple": false, "filters": {"sender": "netflix.com", "includeSpamTrash": true}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "rbqlV0L0SJmc5Qr6", "name": "Gmail account"}}, "typeVersion": 1}, {"id": "2edc2a63-b3ce-45a4-ad37-fde991453be5", "name": "Mailjet", "type": "n8n-nodes-base.mailjet", "position": [1540, 360], "parameters": {"html": "={{ $json.html }}", "text": "={{ $json.text }}", "subject": "={{ $json.subject }}", "toEmail": "={{ $json.recipient }}", "fromEmail": "<EMAIL>", "additionalFields": {}}, "credentials": {"mailjetEmailApi": {"id": "ToQvJxEpa4shhXkA", "name": "Mailjet Email account"}}, "typeVersion": 1}, {"id": "255de753-a0f5-458d-ac7f-ca354076e336", "name": "Set all recipients", "type": "n8n-nodes-base.set", "position": [940, 360], "parameters": {"fields": {"values": [{"name": "recipients", "type": "arrayValue", "arrayValue": "['<EMAIL>','<EMAIL>','<EMAIL>']"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "fe3affe4-0655-42b4-a0a6-b8b231180fbd", "name": "Split Out recipients", "type": "n8n-nodes-base.splitOut", "position": [1240, 360], "parameters": {"include": "allOtherFields", "options": {"destinationFieldName": "recipient"}, "fieldToSplitOut": "recipients"}, "typeVersion": 1}, {"id": "c53493f0-8584-43a2-9f93-60c5c7776e60", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [520, 200], "parameters": {"width": 257, "height": 332, "content": "## Gmail\n1. Connect your Gmail Account, where you are receiving emails from your Netflix account. \n2. Set the polling intervall"}, "typeVersion": 1}, {"id": "d07ae854-39ae-4cab-a59f-26c96da99958", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [860, 200], "parameters": {"width": 249, "height": 338, "content": "## Set all recipients\nReplace the sample emails in the array with the email addresses of your friends and family to whom you want to forward the Netflix emails"}, "typeVersion": 1}, {"id": "5393381b-d96d-4b68-aeac-39facafdd0aa", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1460, 200], "parameters": {"width": 265, "height": 335, "content": "## Mailjet\n1. Connect your Mailjet Account to forward the Netflix emails\n2. Set your sender email address"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Berlin", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "JgJIAy1RwwG892Kn", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "6e57d138-9909-46ac-bde4-b59bde72b3e1", "connections": {"Gmail Trigger": {"main": [[{"node": "Set all recipients", "type": "main", "index": 0}]]}, "Set all recipients": {"main": [[{"node": "Split Out recipients", "type": "main", "index": 0}]]}, "Split Out recipients": {"main": [[{"node": "Mailjet", "type": "main", "index": 0}]]}}}