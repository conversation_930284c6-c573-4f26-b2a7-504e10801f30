{"id": "Mbuax8L8jEmBBYkz", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "The Ultimate Guide to Optimize WordPress Blog Posts with AI", "tags": [{"id": "2VG6RbmUdJ2VZbrj", "name": "Google Drive", "createdAt": "2024-12-04T16:50:56.177Z", "updatedAt": "2024-12-04T16:50:56.177Z"}, {"id": "kH2Zf266Nh4c1aga", "name": "DeepSeek", "createdAt": "2025-01-02T15:24:47.534Z", "updatedAt": "2025-01-02T15:24:47.534Z"}, {"id": "sAbYDJMthctD4UVJ", "name": "Wordpress", "createdAt": "2024-12-17T17:37:24.345Z", "updatedAt": "2024-12-17T17:37:24.345Z"}], "nodes": [{"id": "5c88bf7d-cd9d-4b76-8233-6e5927692e7a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-540, -80], "parameters": {}, "typeVersion": 1}, {"id": "d3fc9ed5-9ddc-480d-9fa8-f0a66bb69520", "name": "Get context", "type": "n8n-nodes-base.googleSheets", "position": [-320, -80], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupColumn": "ID POST"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit?usp=drivesdk", "cachedResultName": "Complete Plan Blog post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "27a1aaf2-435f-4fcc-b7be-f88f2e51a6c7", "name": "Set context", "type": "n8n-nodes-base.set", "position": [-100, -80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3e8d2523-66aa-46fe-adcc-39dc78b9161e", "name": "prompt", "type": "string", "value": "={{ $json.PROMPT }}"}]}}, "typeVersion": 3.4}, {"id": "b2f0a242-ef9f-42d9-974b-e823f23bf37f", "name": "Generate title", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [360, -80], "parameters": {"modelId": {"__rl": true, "mode": "id", "value": "=deepseek-chat"}, "options": {"maxTokens": 2048}, "messages": {"values": [{"content": "=You are an SEO Copywriter and you need to find a title of maximum 60 characters for the following article:\n{{ $json.message.content }}\n\nInstructions:\n- Use keywords contained in the article\n- Do not use any HTML characters\n- Output only the string containing the title.\n- Do not use quotes. The only characters allowed are: and ,"}]}}, "credentials": {"openAiApi": {"id": "97Cz4cqyiy1RdcQL", "name": "DeepSeek"}}, "typeVersion": 1.8}, {"id": "b3b30328-0dfe-4471-9195-d411e737df5e", "name": "Generate article", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [140, -80], "parameters": {"modelId": {"__rl": true, "mode": "id", "value": "=deepseek-chat"}, "options": {"maxTokens": 2048}, "messages": {"values": [{"content": "=an SEO-friendly article on these topics:\n{{ $json.prompt }}\n\nInstructions:\n- In the introduction, introduce the topic that will be covered in more detail in the rest of the text\n- The introduction should be about 60 words\n- The conclusion should be about 60 words\n- Use the conclusion to summarize everything said in the article and offer a conclusion to the reader\n- Write a maximum of 2-3 chapters.\n- Chapters should follow a logical flow and not repeat the same ideas.\n- Chapters should be related to each other and not isolated blocks of text. The text should flow and follow a linear logic.\n- Do not start chapters with \"Chapter 1\", \"Chapter 2\", \"Chapter 3\" ... write only the chapter title\n- For the text, use HTML for formatting, but limit yourself to bold, italics, paragraphs and lists.\n- Do not put the output between ```html but only the text\n- Do not use markdown for formatting.\n- Go deeper into the topic you are talking about, don't just throw superficial information out there\n- I only want HTML format in output"}]}}, "credentials": {"openAiApi": {"id": "97Cz4cqyiy1RdcQL", "name": "DeepSeek"}}, "typeVersion": 1.8}, {"id": "e3f7d28b-ac23-4192-bd46-127491240ee2", "name": "Add draft to WP", "type": "n8n-nodes-base.wordpress", "position": [620, -80], "parameters": {"title": "={{ $json.message.content }}", "additionalFields": {"status": "draft", "content": "={{ $('Generate article').item.json.message.content }}"}}, "credentials": {"wordpressApi": {"id": "OE4AgquSkMWydRqn", "name": "Wordpress (wp.test.7hype.com)"}}, "typeVersion": 1}, {"id": "3ebcf40b-7a3b-43fa-a075-0e6dfaa2665e", "name": "Generate image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-540, 200], "parameters": {"prompt": "=Generate a real photo image used as a blog post cover:\n\nImage prompt:\n{{ $('Generate title').item.json.message.content }}, photography, realistic, sigma 85mm f/1.4", "options": {"size": "1024x1024", "style": "natural", "quality": "hd"}, "resource": "image"}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "2eedf2db-660b-4e1e-a38d-34ad2ddb1314", "name": "Upload image", "type": "n8n-nodes-base.httpRequest", "position": [-320, 200], "parameters": {"url": "https://URL/wp-json/wp/v2/media", "method": "POST", "options": {}, "sendBody": true, "contentType": "binaryData", "sendHeaders": true, "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "=attachment; filename=\"copertina-{{ $('Add draft to WP').item.json.id }}.jpg\""}]}, "inputDataFieldName": "data", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "OE4AgquSkMWydRqn", "name": "Wordpress (wp.test.7hype.com)"}}, "typeVersion": 4.2}, {"id": "58e3aa00-a1d7-4b19-916e-4d07dd7c2009", "name": "Set image", "type": "n8n-nodes-base.httpRequest", "position": [-120, 200], "parameters": {"url": "=https://URL/wp-json/wp/v2/posts/{{ $('Add draft to WP').item.json.id }}", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}]}, "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "OE4AgquSkMWydRqn", "name": "Wordpress (wp.test.7hype.com)"}}, "typeVersion": 4.2}, {"id": "6f9277be-26c4-4d01-9e61-acf3ab90739d", "name": "Update Sheet", "type": "n8n-nodes-base.googleSheets", "position": [140, 200], "parameters": {"columns": {"value": {"URL": "={{ $('Add draft to WP').item.json.guid.rendered }}", "DATA": "={{ $now.format('dd/LL/yyyy') }}", "TITLE": "={{ $('Add draft to WP').item.json.title.rendered }}", "ID POST": "={{ $('Add draft to WP').item.json.id }}", "row_number": "={{ $('Get context').item.json.row_number }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PROMPT", "type": "string", "display": true, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TITLE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "TITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID POST", "type": "string", "display": true, "required": false, "displayName": "ID POST", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METATITLE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "METATITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METADESCRIPTION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "METADESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16VFeCrE5BkMBoA_S5HD-9v7C0sxcXAUiDbq5JvkDqnI/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit?usp=drivesdk", "cachedResultName": "Complete Plan Blog post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "1d880c40-af32-4c78-9fa2-fb6b120039d3", "name": "SEO Expert", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [500, 200], "parameters": {"text": "=Create metatitle and metadescription in the language of the following product:\n- Title: {{ $('Add draft to WP').item.json.title.rendered }}\n- Content: {{ $('Add draft to WP').item.json.content.rendered }}\n- Excerpt: {{ $('Add draft to WP').item.json.excerpt.rendered }}", "messages": {"messageValues": [{"message": "=You are an expert SEO content optimizer specialized in creating meta tags for both web pages and blog articles. Your task is to analyze the provided content and generate optimized meta tags based on the content type.\n\nMETA TAG REQUIREMENTS:\n\n1. Meta Title (max 60 characters):\n   - Include primary keyword near the beginning\n   - State the main value proposition\n   - Include brand name when relevant\n   - Use action-oriented language for commercial pages\n   - Format: Primary Keyword | Main Benefit | Brand (if applicable)\n\n2. Meta Description (max 160 characters):\n   - Lead with unique selling proposition\n   - Include primary and secondary keywords naturally\n   - Add clear call-to-action\n   - Highlight key benefits or features\n   - Address user pain points\n   - Tease the main insights or findings\n   - Mention key topics covered\n   - Use engaging question or statement\n   - Include expertise indicators\n   - Promise value or solution\n\nANALYSIS PROCESS:\n1. Content Assessment:\n   - Identify content type and purpose\n   - Extract main topic and keywords\n   - Determine search intent\n   - Identify target audience\n   - Note key differentiators\n\n2. Optimization Strategy:\n   - Align with search intent\n   - Consider competition level\n   - Factor in brand guidelines\n   - Account for content freshness\n   - Optimize for click-through rate\n\nOUTPUT FORMAT:\nPlease provide meta tags in the required format\n\nVALIDATION CHECKLIST:\n- Title length ≤ 60 characters\n- Description length ≤ 160 characters\n- Primary keyword included in both\n- Matches identified search intent\n- Appropriate for content type\n- Clear value proposition\n- Compelling call-to-action\n- Natural keyword placement\n- Proper grammar and spelling\n- Mobile display optimized\n- Don't use placeholder\n\nBEST PRACTICES:\n- Avoid clickbait or misleading content\n- Use power words appropriately\n- Maintain brand voice and tone\n- Ensure mobile snippet optimization\n- Consider local SEO when relevant\n- Keep seasonal content timely\n- Use numbers and data when available\n- Include price/offers if applicable\n- Add emotional triggers when appropriate\n- Consider SERP feature optimization\n\nTECHNICAL GUIDELINES:\n- No double quotes in meta description\n- Avoid unnecessary special characters\n- Use UTF-8 encoding\n- Capitalize properly\n- Avoid keyword cannibalization\n- Respect brand guidelines\n- Consider local market variations\n- Update meta tags for seasonal content\n- Monitor CTR performance\n- A/B test when possible\n\nRemember: The goal is to create meta tags that serve both search engines and users effectively while maintaining specificity for the content type and maximizing organic click-through rates."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "d26e13c4-e26d-4f3c-9977-82d32879530e", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [480, 440], "parameters": {"model": "google/gemini-2.0-flash-exp:free", "options": {}}, "credentials": {"openRouterApi": {"id": "pb06rfB4xmxzVe3Q", "name": "OpenRouter"}}, "typeVersion": 1}, {"id": "c4833efe-1b43-42e7-a5ec-cca1908f327b", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [700, 440], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"metatitle\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"metadescription\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "962e5d6f-df35-4e5c-b46e-9c2164a3648d", "name": "Set metatag", "type": "n8n-nodes-base.httpRequest", "position": [-540, 480], "parameters": {"url": "=https://URL/wp-json/wp/v2/posts/{{ $('Add draft to WP').item.json.id }}", "method": "PUT", "options": {}, "jsonBody": "={\n  \"meta\":{\n    \"_yoast_wpseo_title\":\"{{ $json.output.metatitle }}\",\n    \"_yoast_wpseo_metadesc\":\"{{ $json.output.metadescription }}\"\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpBasicAuth": {"id": "WCjOvm7HOMwGEMWs", "name": "WP (wp.test.7hype.com)"}}, "typeVersion": 4.2}, {"id": "55e8e3c3-2270-4227-863c-e7cf1337c13b", "name": "Finish work", "type": "n8n-nodes-base.googleSheets", "position": [-320, 480], "parameters": {"columns": {"value": {"METATITLE": "={{ $('SEO Expert').item.json.output.metatitle }}", "row_number": "={{ $('Update Sheet').item.json.row_number }}", "METADESCRIPTION": "={{ $('SEO Expert').item.json.output.metadescription }}"}, "schema": [{"id": "DATA", "type": "string", "display": true, "required": false, "displayName": "DATA", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PROMPT", "type": "string", "display": true, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TITLE", "type": "string", "display": true, "required": false, "displayName": "TITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID POST", "type": "string", "display": true, "required": false, "displayName": "ID POST", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METATITLE", "type": "string", "display": true, "required": false, "displayName": "METATITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METADESCRIPTION", "type": "string", "display": true, "required": false, "displayName": "METADESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit?usp=drivesdk", "cachedResultName": "Complete Plan Blog post"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f50be61f-7502-4186-9952-22e740de896e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-360, -160], "parameters": {"width": 180, "height": 240, "content": "Fetches the context of the article you want to generate via AI"}, "typeVersion": 1}, {"id": "149e3535-60fb-4547-8dad-567a47f9bc4b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [540, -160], "parameters": {"width": 280, "height": 240, "content": "Create the draft post on Wordpress with the article text and the title that were previously generated"}, "typeVersion": 1}, {"id": "779e3a0d-1429-4973-9757-fa1645796cbd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-360, 140], "parameters": {"width": 400, "height": 220, "content": "Upload the generated image to Wordpress media and then associate it with the newly created article"}, "typeVersion": 1}, {"id": "9b375c3e-3c3a-44e9-b234-2538e6f33d32", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [400, 140], "parameters": {"width": 420, "height": 220, "content": "The SEO expert analyzes the created article and generates the appropriate meta title and meta description"}, "typeVersion": 1}, {"id": "d4c503c9-9d0f-4411-a3a6-bdd77f782a77", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-600, 420], "parameters": {"width": 220, "height": 200, "content": "The generated metadata is associated with the post"}, "typeVersion": 1}, {"id": "8d7a1d61-3fbe-4d76-9f2d-68b4c9de23bb", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-560, -460], "parameters": {"color": 3, "width": 820, "content": "## Optimize WordPress Blog Posts with AI\n\nThis is a powerful tool for automating the creation and optimization of blog posts, saving time and ensuring high-quality, SEO-friendly content for Wordpress with Yoast plugin. \n\n[Clone this Sheet](https://docs.google.com/spreadsheets/d/1WlmjnObleBuHRno_axjc-GjV7Wg9gCoIsOK1PD4TxGU/edit?usp=sharing) and in the \"Prompt\" column write the topic that will then be developed by the AI"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "8d701d17-18ae-478c-881d-eedb422e9700", "connections": {"Set image": {"main": [[{"node": "Update Sheet", "type": "main", "index": 0}]]}, "SEO Expert": {"main": [[{"node": "Set metatag", "type": "main", "index": 0}]]}, "Get context": {"main": [[{"node": "Set context", "type": "main", "index": 0}]]}, "Set context": {"main": [[{"node": "Generate article", "type": "main", "index": 0}]]}, "Set metatag": {"main": [[{"node": "Finish work", "type": "main", "index": 0}]]}, "Update Sheet": {"main": [[{"node": "SEO Expert", "type": "main", "index": 0}]]}, "Upload image": {"main": [[{"node": "Set image", "type": "main", "index": 0}]]}, "Generate image": {"main": [[{"node": "Upload image", "type": "main", "index": 0}]]}, "Generate title": {"main": [[{"node": "Add draft to WP", "type": "main", "index": 0}]]}, "Add draft to WP": {"main": [[{"node": "Generate image", "type": "main", "index": 0}]]}, "Generate article": {"main": [[{"node": "Generate title", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "SEO Expert", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "SEO Expert", "type": "ai_outputParser", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get context", "type": "main", "index": 0}]]}}}