{"id": "ES4TSw9HacxoNhLZ", "meta": {"instanceId": "5219bc76ea806909b58e13e2acac1c19192522e70dc3c90467e1800e94864105", "templateCredsSetupCompleted": true}, "name": "AI CV Screening Workflow", "tags": [], "nodes": [{"id": "e77fbc32-5ee9-49b4-93d5-f2ffda134b08", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1230, 530], "parameters": {"options": {}}, "credentials": {"googlePalmApi": {"id": "UcdfdADI6w9nkgg5", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "9e24167f-cac6-4b98-95da-30065510d79a", "name": "Confirmation of CV Submission", "type": "n8n-nodes-base.gmail", "position": [1780, 460], "webhookId": "954756dc-2946-4b78-b208-06f3df612ab5", "parameters": {"sendTo": "={{ $('Application Form').item.json['E-mail'] }}", "message": "=Dear {{ $('Application Form').item.json['Full Name'] }}, \n\nThank you for submitting your CV. We have received it and will review it shortly. \n\nBest regards,\nMediusware", "options": {}, "subject": "We Have Received Your CV"}, "credentials": {"gmailOAuth2": {"id": "taFlf0vD5S4QlOKM", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "ff49d370-b4eb-4426-b396-763455e647e7", "name": "Inform HR New CV Received", "type": "n8n-nodes-base.gmail", "position": [1760, 200], "webhookId": "e969a9f5-631b-4719-a4f6-87e6063cef6a", "parameters": {"sendTo": "<EMAIL>", "message": "=Hello HR,\n\nA new CV has been successfully received in our system. Please review the candidate's details at your earliest convenience.\n\nCandidate Name: {{ $('Application Form').item.json['Full Name'] }}\nCandidate E-mail: {{ $('Application Form').item.json['E-mail'] }}\nCandidate Linkedin: {{ $('Application Form').item.json.Linkedin }}\nCandidate Expectation: {{ $('Application Form').item.json.Expectation }}\nCandidate AI Rating: {{ $('Using AI Analysis & Rating').item.json.text }}\n\nThank you for your attention.\n\nBest regards,\nAutomated CV Screening", "options": {}, "subject": "New Candidate CV Awaiting Review"}, "credentials": {"gmailOAuth2": {"id": "taFlf0vD5S4QlOKM", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "8479fa4c-10bc-4914-896d-f5b00d063fa8", "name": "Using AI Analysis & Rating", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1320, 240], "parameters": {"text": "={{ $json.text }}", "messages": {"messageValues": [{"message": "Rule 1 : Do not exceed maximum of 75 words. As an AI with advanced capabilities in talent acquisition and human resources, your task is to conduct a thorough and intricate analysis of a candidate's resume or CV against a specific job description. You will assist hiring professionals in discerning the alignment between the candidate's skills, experience, qualifications, and the requirements of the job. Your expert insights will equip employers with a lucid understanding of the candidate's suitability for the role. Very important for you to write output text in ${output_language} language. It's VERY IMPORTANT for me for text be in ${output_language} or I will be fired. Your analysis should follow this structured format: 1. **Compatibility Rating**: Propose an overall compatibility rating on a scale from 1 (not compatible) to 10 (perfect fit). Support your rating by elucidating the rationale behind it. 2. **Recommendation**: Informed by your analysis and compatibility rating, offer a recommendation on whether the employer should consider this candidate for an interview. Furnish a well-argued explanation for your recommendation. Remember, your analysis should be comprehensive, professional, and actionable. It should equip an employer with a vivid understanding of the candidate's suitability for the role. This isn't merely about ticking off boxes; it's about illustrating a comprehensive picture of how well the candidate might fit into the role and complement the existing team. Here is your task: Analyze the compatibility of the following candidate's resume with the provided job description. Endeavor to apply your deep understanding of talent evaluation to provide the most insightful analysis. Job description: \"Software Engineer\" Resume: ${resume}\nNo Markdown Please, only plain text. Please no double '**'"}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "da0fd18b-2420-471e-b930-9aabc45bc2ca", "name": "Convert Binary to <PERSON><PERSON>", "type": "n8n-nodes-base.extractFromFile", "position": [1080, 220], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "Your_Resume_CV"}, "retryOnFail": false, "typeVersion": 1}, {"id": "bc5480c1-d9c2-414b-8cd4-0b3e49d4dde9", "name": "Application Form", "type": "n8n-nodes-base.formTrigger", "position": [820, 380], "webhookId": "0cd422d3-e69f-4ec0-92ab-05362808c4da", "parameters": {"options": {}, "formTitle": "Application for Software Engineer Position", "formFields": {"values": [{"fieldLabel": "Full Name", "requiredField": true}, {"fieldLabel": "E-mail", "requiredField": true}, {"fieldLabel": "Expectation", "placeholder": "2000-3000$", "requiredField": true}, {"fieldLabel": "Linkedin", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Your Resume/CV", "requiredField": true, "acceptFileTypes": ".pdf"}]}}, "typeVersion": 2.2}, {"id": "d2dfbf1e-8d88-49e6-940d-e1717de97b30", "name": "Candidate Lists", "type": "n8n-nodes-base.googleSheets", "position": [1540, 480], "parameters": {"columns": {"value": {"CV": "={{ $('Application Form').item.json['Your Resume/CV'][0].filename }}", "E-mail": "={{ $('Application Form').item.json['E-mail'] }}", "Linkedin": "={{ $('Application Form').item.json.Linkedin }}", "AI Rating": "={{ $json.text }}", "Full Name": "={{ $('Application Form').item.json['Full Name'] }}", "Expectation": "={{ $('Application Form').item.json.Expectation }}"}, "schema": [{"id": "CV", "type": "string", "display": true, "required": false, "displayName": "CV", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Full Name", "type": "string", "display": true, "required": false, "displayName": "Full Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "E-mail", "type": "string", "display": true, "required": false, "displayName": "E-mail", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Expectation", "type": "string", "display": true, "required": false, "displayName": "Expectation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Linkedin", "type": "string", "display": true, "required": false, "displayName": "Linkedin", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AI Rating", "type": "string", "display": true, "required": false, "displayName": "AI Rating", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1y4FFMXTuznSf2wWUraK57eBJnu4MVtgkxrGYRzRMwDQ/edit#gid=0", "cachedResultName": "পত্রক1"}, "documentId": {"__rl": true, "mode": "list", "value": "1y4FFMXTuznSf2wWUraK57eBJnu4MVtgkxrGYRzRMwDQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1y4FFMXTuznSf2wWUraK57eBJnu4MVtgkxrGYRzRMwDQ/edit?usp=drivesdk", "cachedResultName": "CV of Software Engineers"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "YdlTTXiu8194dEFE", "name": "Google Sheets account"}}, "typeVersion": 4.5}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2036fff4-ab9c-4981-a8b4-44be4654630d", "connections": {"Candidate Lists": {"main": [[{"node": "Inform HR New CV Received", "type": "main", "index": 0}]]}, "Application Form": {"main": [[{"node": "Convert Binary to <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Convert Binary to Json": {"main": [[{"node": "Using AI Analysis & Rating", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Using AI Analysis & Rating", "type": "ai_languageModel", "index": 0}]]}, "Inform HR New CV Received": {"main": [[{"node": "Confirmation of CV Submission", "type": "main", "index": 0}]]}, "Using AI Analysis & Rating": {"main": [[{"node": "Candidate Lists", "type": "main", "index": 0}]]}}}