{"id": "0pVPSW4PzJZLLqSf", "meta": {"instanceId": "8e47d02981c11ba904b56e6bd77877c35ef5c9aa1cdc4076bcb72bbb235efa38"}, "name": "Notion to Linkedin", "tags": [], "nodes": [{"id": "d922cf0c-f1c2-40ff-927c-d0d3e2fb7f27", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2140, 460], "parameters": {"mode": "combine", "options": {}, "combinationMode": "mergeByPosition"}, "typeVersion": 2.1}, {"id": "8c5f68d8-f11d-4b37-b0d8-3abd1b681b56", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [780, 440], "parameters": {"color": 4, "height": 141.4092845296238, "content": "## Start the flow every day at the same time"}, "typeVersion": 1}, {"id": "d10de4f3-6e90-474f-bd68-25aae2037b7b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1240, 312.5835468325357], "parameters": {"color": 6, "width": 367.12018536439575, "height": 382.294335406698, "content": "## Fetch the day's post from my Notion database"}, "typeVersion": 1}, {"id": "a63bcc85-ec8b-424f-a53c-e4c07db3c7c8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1642.6949426092506, 313.81962236044654], "parameters": {"color": 6, "width": 627.4768047417825, "height": 380.3367219655605, "content": "## Process and format the post"}, "typeVersion": 1}, {"id": "d7c0f13c-ebbe-4000-bd8f-d1180d65d02a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1060, 460], "parameters": {"rule": {"interval": [{"triggerAtHour": 15}]}}, "typeVersion": 1.2}, {"id": "7bebcb2d-1642-48ce-a511-bb0f561ca5cf", "name": "Filter the table for the day's post", "type": "n8n-nodes-base.notion", "position": [1280, 460], "parameters": {"filters": {"conditions": [{"key": "Date|date", "date": "={{ $today.format(\"yyyy/mM/dd\") }}", "condition": "equals"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "mode": "list", "value": "f09dc21b-1070-4d5a-bf7f-a9ab3dbb69fb", "cachedResultUrl": "https://www.notion.so/f09dc21b10704d5abf7fa9ab3dbb69fb", "cachedResultName": "Postagens"}, "filterType": "manual"}, "credentials": {"notionApi": {"id": "faERNMuBrkAfVaJR", "name": "Notion Weck"}}, "typeVersion": 2.2}, {"id": "ee61bc59-164b-45b4-8b49-57cdba7d298b", "name": "Fetch the content on the page", "type": "n8n-nodes-base.notion", "position": [1480, 460], "parameters": {"blockId": {"__rl": true, "mode": "url", "value": "={{ $json.url }}"}, "resource": "block", "operation": "getAll", "returnAll": true}, "credentials": {"notionApi": {"id": "faERNMuBrkAfVaJR", "name": "Notion Weck"}}, "typeVersion": 2.2}, {"id": "52d1ffef-11e8-4635-bbb8-05e915034379", "name": "Aggregate the Notion blocks", "type": "n8n-nodes-base.aggregate", "position": [1680, 460], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}, {"fieldToAggregate": "image.file.url"}]}}, "typeVersion": 1}, {"id": "8617c530-382b-402b-9c0b-aeb4df2bb920", "name": "Format the post", "type": "n8n-nodes-base.code", "position": [1900, 360], "parameters": {"jsCode": "const notionData = items[0].json.content;\n\nlet formattedText = notionData[0] \n\nfor (let i = 1; i < notionData.length; i++) {\n    if (notionData[i].startsWith('-')) {\n        formattedText += '\\n\\n' + notionData[i];\n    } else {\n        formattedText += '\\n' + notionData[i];\n    }\n}\n\nreturn [{ formattedText: formattedText }];\n"}, "typeVersion": 2}, {"id": "0f226cfe-eb31-469a-8e7c-a21192adbd4c", "name": "Download image", "type": "n8n-nodes-base.httpRequest", "position": [1900, 560], "parameters": {"url": "={{ $json.url[0] }}", "options": {}}, "typeVersion": 4.2}, {"id": "81a1e39c-c7ea-4635-84fc-a8ae05cfd8f3", "name": "Publish on LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [2360, 460], "parameters": {"text": "={{ $json.formattedText }}", "person": "CcS-_lLyzG", "additionalFields": {}, "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": {"id": "HZbihVPNwXzWRzgU", "name": "LinkedIn account"}}, "typeVersion": 1}, {"id": "61b92eb8-1bf8-4e57-9e07-1a39e457ecfb", "name": "Update post status in notion database", "type": "n8n-nodes-base.notion", "position": [2620, 460], "parameters": {"pageId": {"__rl": true, "mode": "url", "value": "={{ $('Filter the table for the day\\'s post').item.json.url }}"}, "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Status|select", "selectValue": "Published"}]}}, "credentials": {"notionApi": {"id": "faERNMuBrkAfVaJR", "name": "Notion Weck"}}, "typeVersion": 2.2}, {"id": "397f3772-bb2b-4e58-99f8-2b62cc514b7a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [760, 120], "parameters": {"color": 3, "width": 567.*************, "height": 137.*************, "content": "## 1. Setup\nSet up your Notion and LinkedIn credentials.\nAttention to the LinkedIn credential: to post on your personal or company profile, you need to have a company page assigned to your profile. After that, you can choose where you want to post."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": 30, "saveManualExecutions": true}, "versionId": "d6f51bb9-7320-4984-a009-b0f49073349a", "connections": {"Merge": {"main": [[{"node": "Publish on LinkedIn", "type": "main", "index": 0}]]}, "Download image": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Format the post": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Filter the table for the day's post", "type": "main", "index": 0}]]}, "Publish on LinkedIn": {"main": [[{"node": "Update post status in notion database", "type": "main", "index": 0}]]}, "Aggregate the Notion blocks": {"main": [[{"node": "Format the post", "type": "main", "index": 0}, {"node": "Download image", "type": "main", "index": 0}]]}, "Fetch the content on the page": {"main": [[{"node": "Aggregate the Notion blocks", "type": "main", "index": 0}]]}, "Filter the table for the day's post": {"main": [[{"node": "Fetch the content on the page", "type": "main", "index": 0}]]}}}