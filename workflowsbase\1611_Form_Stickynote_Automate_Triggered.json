{"id": "QObDE85a2ArfJkxV", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa", "templateCredsSetupCompleted": true}, "name": "Automated Form Submission Data Storage in Airtable", "tags": [{"id": "Fcqhqfi5hGHHR7nn", "name": "UserData", "createdAt": "2025-03-17T13:06:42.859Z", "updatedAt": "2025-03-17T13:06:42.859Z"}, {"id": "uScnF9NzR3PLIyvU", "name": "Published", "createdAt": "2025-03-21T07:22:28.491Z", "updatedAt": "2025-03-21T07:22:28.491Z"}], "nodes": [{"id": "fef66f10-a3eb-4e71-9493-3d90ebd52fde", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "notes": "Create User Form", "position": [120, 80], "webhookId": "39d82b4d-4d27-40de-a12b-0dafab18bb93", "parameters": {"options": {}, "formTitle": "Create User", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "Enter Your Name", "requiredField": true}, {"fieldType": "number", "fieldLabel": "Age", "placeholder": "Enter Your Age", "requiredField": true}, {"fieldType": "email", "fieldLabel": "email", "placeholder": "Enter Your Email", "requiredField": true}, {"fieldLabel": "address", "placeholder": "Enter Your Address"}, {"fieldType": "dropdown", "fieldLabel": "You have Subscription ?", "fieldOptions": {"values": [{"option": "Yes"}, {"option": "No"}]}, "requiredField": true}]}, "formDescription": "Provide the necessary information here"}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "1745c697-93ca-4374-8d1e-92e047ad7339", "name": "User Data Storage", "type": "n8n-nodes-base.airtable", "notes": "Store User Data", "position": [380, 80], "parameters": {"base": {"__rl": true, "mode": "url", "value": ""}, "table": {"__rl": true, "mode": "url", "value": ""}, "columns": {"value": {"Age": "={{ $json.Age }}", "Name": "={{ $json.Name }}", "Email": "={{ $json.email }}", "Address": "={{ $json.address }}", "Submitted": "={{ $json.submittedAt }}", "Subscription": "={{ $json['You have Subscription ?'] }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Age", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Age", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subscription", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Subscription", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Submitted", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Submitted", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 2.1}, {"id": "ac2f27d8-0922-49cc-9e40-316b3de7a4d1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"width": 720, "height": 260, "content": "Automated Form Submission Data Storage in Airtable"}, "typeVersion": 1}, {"id": "e85c44f2-c268-41b8-9b98-f4ada81b2824", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 280], "parameters": {"width": 720, "height": 100, "content": "This workflow automatically captures data submitted through a form and stores it in Airtable. By using a form submission trigger, the workflow ensures that every time a form is filled out, the data is instantly recorded in Airtable without manual effort. This streamlines data management, making it easy to store and organize form data in a structured database for future reference."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "3363354f-4c97-4090-a2ff-3139e663549b", "connections": {"On form submission": {"main": [[{"node": "User Data Storage", "type": "main", "index": 0}]]}}}