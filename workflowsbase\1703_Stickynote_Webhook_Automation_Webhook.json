{"id": "iGAzT789R7Q1fOOE", "meta": {"instanceId": "7a1e9dd164c758cbdeb7cf88274e567a937a36ed99d4d22ff24b645841097c48", "templateId": "3577", "templateCredsSetupCompleted": true}, "name": "Travel Planning Agent with Couchbase Vector Search, Gemini 2.0 Flash and OpenAI", "tags": [], "nodes": [{"id": "0f361616-a552-43ed-9754-794780113955", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [380, 240], "webhookId": "c22b2240-ff07-44e5-a1aa-63584150a1cb", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "e8b9815d-0fe5-4e7c-a20b-1602384580cd", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [560, 480], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash"}, "typeVersion": 1}, {"id": "a4b15997-de4d-4c78-b623-e936442134af", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1260, 280], "parameters": {"color": 3, "width": 800, "height": 500, "content": "## AI Travel Agent Powered by Couchbase.\n\n### You will need to:\n1. Setup your Google API Credentials for the Gemini LLM\n2. Setup your OpenAI Credentials for the OpenAI embedding nodes.\n3. Create a Couchbase cluster (using [Couchbase Capella](https://cloud.couchbase.com/) in the cloud, or Couchbase Server)\n4. Add [Database credentials](https://docs.couchbase.com/cloud/clusters/manage-database-users.html#create-database-credentials) with appropriate permissions for the operations you want to perform\n5. Configure [Allowed IP addresses](https://docs.couchbase.com/cloud/clusters/allow-ip-address.html) for your n8n instance. Use `0.0.0.0/0` for easier testing.\n6. Create a bucket, scope, and collection. We recommend the following:\n   - Bucket: `travel-agent`\n   - Scope: `vectors`\n   - Collection: `points-of-interest`\n7. Navigate to the Data Tools, click the Search tab, and click Import Search Index. Upload the following JSON file found [here](https://gist.github.com/ejscribner/6f16343d4b44b1af31e8f344557814b0).\n\n\nOnce all of that is configured you will need to send the loading webhook with some data points (see example).\n\nThis should create vectorized data in  `points-of-interest` collection.\n\nOnce you have data points there try to ask the Agent questions about the data points and test the response. Eg. \"Where should I go for a romantic getaway?\""}, "typeVersion": 1}, {"id": "34866f8e-00b0-4706-82d7-491b9531a8b6", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [800, 1000], "webhookId": "3ca6fbdd-a157-4e9d-9042-237048da85b6", "parameters": {"path": "3ca6fbdd-a157-4e9d-9042-237048da85b6", "options": {"rawBody": true}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "26d4e62a-42b0-4e09-8585-827e5bcc9fff", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1180, 1360], "parameters": {"options": {}, "jsonData": "={{ $json.body.raw_body.point_of_interest.title }} - {{ $json.body.raw_body.point_of_interest.description }}", "jsonMode": "expressionData"}, "typeVersion": 1}, {"id": "63fc308f-4d1c-4d24-9b20-68d7e6c2dbba", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1280, 1540], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "84f8c32b-8e0c-457c-aaec-17827042674d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-60, 1060], "parameters": {"width": 720, "height": 460, "content": "## CURL Command to Ingest Data.\n\nHere is an example of how you can load data into your webhook once its active and ready to get requests.\n\n```\ncurl -X POST \"webhook url\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"raw_body\": {\n      \"point_of_interest\": {\n        \"title\": \"Eiffel Tower\",\n        \"description\": \"Iconic iron lattice tower located on the Champ de Mars in Paris, France.\"\n      }\n    }\n  }'\n```\n\n(replace webhook url with the URL listed in the webhook node)\n\nA shell script to bulk insert six data points can be found [here](https://gist.github.com/ejscribner/355a46a0a383a4878e65e2230b92c6b5). Be sure to activate the workflow and use the production Webhook URL when running the script."}, "typeVersion": 1}, {"id": "b2cf8788-849c-4420-b448-bd49caa4941e", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [720, 480], "parameters": {}, "typeVersion": 1.3}, {"id": "0bf7fef9-f999-42a8-a6a8-ab111fe9a084", "name": "AI Travel Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [600, 240], "parameters": {"options": {"maxIterations": 10, "systemMessage": "You are a helpful assistant for a trip planner. You have a vector search capability to locate points of interest, Use it and don't invent much."}}, "typeVersion": 1.8}, {"id": "3af3c8ce-582b-407c-847a-8063f9ad2e1a", "name": "Retrieve docs with Couchbase Search Vector", "type": "n8n-nodes-couchbase.vectorStoreCouchbaseSearch", "position": [860, 500], "parameters": {"mode": "retrieve-as-tool", "topK": 10, "options": {}, "toolName": "PointofinterestKB", "embedding": "embedding", "textFieldKey": "description", "couchbaseScope": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "couchbaseBucket": {"__rl": true, "mode": "list", "value": ""}, "toolDescription": "The list of Points of Interest from the database.", "vectorIndexName": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "couchbaseCollection": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "typeVersion": 1.1}, {"id": "77a4e857-607a-4bbc-a28d-8a715f9415d5", "name": "Insert docs with Couchbase Search Vector", "type": "n8n-nodes-couchbase.vectorStoreCouchbaseSearch", "position": [1100, 1120], "parameters": {"mode": "insert", "options": {}, "embedding": "embedding", "textFieldKey": "description", "couchbaseScope": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "couchbaseBucket": {"__rl": true, "mode": "list", "value": ""}, "vectorIndexName": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "embeddingBatchSize": 1, "couchbaseCollection": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}}, "typeVersion": 1.1}, {"id": "4c0274c3-6647-4f45-b7d4-d63cfe2102ea", "name": "Generate OpenAI Embeddings using text-embedding-3-small", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [960, 740], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "83f864fa-a298-4738-a102-ca2d283377de", "name": "Generate OpenAI Embeddings using text-embedding-3-small1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1000, 1340], "parameters": {"options": {}}, "typeVersion": 1.2}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "80e40e5a-35a3-4fa4-b90e-ac9d76897bbd", "connections": {"Webhook": {"main": [[{"node": "Insert docs with Couchbase Search Vector", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Travel Agent", "type": "ai_memory", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert docs with Couchbase Search Vector", "type": "ai_document", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Travel Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Travel Agent", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Retrieve docs with Couchbase Search Vector": {"ai_tool": [[{"node": "AI Travel Agent", "type": "ai_tool", "index": 0}]]}, "Generate OpenAI Embeddings using text-embedding-3-small": {"ai_embedding": [[{"node": "Retrieve docs with Couchbase Search Vector", "type": "ai_embedding", "index": 0}]]}, "Generate OpenAI Embeddings using text-embedding-3-small1": {"ai_embedding": [[{"node": "Insert docs with Couchbase Search Vector", "type": "ai_embedding", "index": 0}]]}}}