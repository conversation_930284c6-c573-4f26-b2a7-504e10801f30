{"id": "Fdbft9uw8mLGXMoE", "meta": {"instanceId": "13d96e1ebd7901d1ed300d36db3a4447107e9ad60df51fe711e45683875362aa", "templateCredsSetupCompleted": true}, "name": "Speech Support Workflow", "tags": [{"id": "88Rkm7VaAFefsT34", "name": "AI", "createdAt": "2025-05-06T22:52:26.053Z", "updatedAt": "2025-05-06T22:52:26.053Z"}, {"id": "s1UA6FThbKhQYbLu", "name": "MultiModal", "createdAt": "2025-05-06T22:52:35.914Z", "updatedAt": "2025-05-06T22:52:35.914Z"}, {"id": "ANT04PP2WxQmkjzl", "name": "Integrations", "createdAt": "2025-05-06T22:53:02.798Z", "updatedAt": "2025-05-06T22:53:02.798Z"}], "nodes": [{"id": "8868fc75-4a21-4900-b2b9-7860ee981a9e", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1640, 240], "parameters": {"text": "={{ $('Route Flow Based on Message Content').item.json.text }}", "options": {"systemMessage": "={{ $json.system_prompt }}\n\nYou are generating text for a Telegram message. The text should be plain. No * or **"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "23f48680-a190-48a5-bb7c-e070db41b9e7", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1620, 800], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-001"}, "credentials": {"googlePalmApi": {"id": "zCkkU4GKPR7wANF5", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "6a909fb0-f550-4b5e-94db-6e16682d70bd", "name": "Recieve Telegram Message", "type": "n8n-nodes-base.telegramTrigger", "position": [-480, 240], "webhookId": "20140af0-c902-44db-9c53-051def981f9a", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "WvBkWguhZJQm5FpM", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "73d19e09-efc4-43c4-a4e9-382ae66c7651", "name": "Check For Text or Voice Message", "type": "n8n-nodes-base.set", "position": [-260, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b37e51e5-e2c7-4328-b02b-80d08164d595", "name": "text", "type": "string", "value": "={{ $json.message.text||\"\" }}"}]}}, "typeVersion": 3.4}, {"id": "a7ade841-258d-45b2-9150-a56490a4c37f", "name": "Download Audio File", "type": "n8n-nodes-base.telegram", "position": [180, 120], "webhookId": "68e0f93e-5dd0-41aa-89e4-4e7a6be9d3b2", "parameters": {"fileId": "={{ $('Recieve Telegram Message').item.json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "WvBkWguhZJQm5FpM", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "73cb448e-f00e-4879-8fa2-facb259b76b2", "name": "Transcribe Audio File", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [400, 120], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "cDXozPn1syyex1aJ", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "a1999ecd-cabf-4740-a9a6-98486a868b7f", "name": "If Voice Message", "type": "n8n-nodes-base.if", "position": [-60, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d60f6ce2-afd0-4ee1-a7c3-3d5bbdb68ea2", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.text }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "ac5cad34-3756-4fe3-9269-ae96e5b49e8f", "name": "Code to remove unwanted characters from LLM response", "type": "n8n-nodes-base.code", "position": [2060, 240], "parameters": {"language": "python", "pythonCode": "import re\n\ndef clean_markdown_for_telegram(text):\n  \"\"\"\n  Removes common Markdown formatting characters from a string.\n\n  Args:\n    text: The input string.\n\n  Returns:\n    A new string with Markdown characters removed.\n  \"\"\"\n  markdown_chars = r\"[*_~`\\[\\]()#+\\-=|{}.!]\"\n  cleaned_text = re.sub(markdown_chars, \"\", text)\n  cleaned_text = \" \".join(cleaned_text.split()).strip()\n  return cleaned_text\n\n# Loop over input items and create new items with the cleaned text\noutput_items = []\nfor item in _input.all():\n  feedback_text = item.json.get(\"output\", \"\")\n  cleaned_feedback = clean_markdown_for_telegram(feedback_text)\n  output_items.append({\"json\": {\"cleanedText\": cleaned_feedback}})\n\nreturn output_items"}, "typeVersion": 2}, {"id": "82761634-7472-4ce1-806a-2b80aca985e3", "name": "Code to split output into chunks under 4000 characters", "type": "n8n-nodes-base.code", "position": [2280, 240], "parameters": {"language": "python", "pythonCode": "def split_text_for_telegram(text, max_length=4000):\n  \"\"\"\n  Splits a long text into a list of strings, each with a maximum length\n  suitable for Telegram messages.\n\n  Args:\n    text: The input string to split.\n    max_length: The maximum length of each resulting string (default: 4000).\n\n  Returns:\n    A list of strings, where each string is a chunk of the original text\n    with a maximum length of max_length.\n  \"\"\"\n  if len(text) <= max_length:\n    return [text]\n\n  chunks = []\n  start_index = 0\n  while start_index < len(text):\n    end_index = min(start_index + max_length, len(text))\n\n    split_point = end_index\n    if end_index < len(text):\n      last_sentence_end = -1\n      for i in range(start_index + max_length - 1, start_index - 1, -1):\n        if i < len(text) and text[i] in ['.', '?', '!']:\n          last_sentence_end = i + 1\n          break\n      if last_sentence_end > start_index:\n        split_point = last_sentence_end\n\n    chunks.append(text[start_index:split_point])\n    start_index = split_point\n\n  return chunks\n\noutput_items = []\nmax_length = 4000\n\nfor item in _input.all():\n  text = item.json.get(\"cleanedText\", \"\")\n  text_chunks = split_text_for_telegram(text, max_length)\n  for chunk in text_chunks:\n    output_items.append({\"json\": {\"telegramTextChunk\": chunk}})\n\nreturn output_items"}, "typeVersion": 2}, {"id": "9adbfd4c-bbb9-4c92-bf07-a3b50a92aa02", "name": "Respond to Telegram Message", "type": "n8n-nodes-base.telegram", "position": [2500, 240], "webhookId": "4c77b108-e066-4538-986a-7535143cfaac", "parameters": {"text": "={{ $json.telegramTextChunk }}", "chatId": "={{ $('Recieve Telegram Message').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "WvBkWguhZJQm5FpM", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "5bdb6ec5-339e-4e8d-a746-9cdbe4d5f12f", "name": "Wipe Conversation Memory", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [940, -20], "parameters": {"mode": "delete", "deleteMode": "all"}, "typeVersion": 1.1}, {"id": "aae703f4-e891-4681-aae4-c426ebba5146", "name": "Store Conversation Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1120, 800], "parameters": {"sessionKey": "={{ $('Recieve Telegram Message').item.json.message.from.id }}", "sessionIdType": "customKey", "contextWindowLength": 25}, "typeVersion": 1.3}, {"id": "7457f085-9b19-4a00-9ad6-af2ca8ee16d5", "name": "Set prompt to start a new speech", "type": "n8n-nodes-base.set", "position": [1340, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d5b33b03-241b-4193-915a-4eb4dfef05e9", "name": "system_prompt", "type": "string", "value": "\"I am preparing to give a speech. Your role is to act as my speech preparation assistant. Please guide me through the process of getting ready to deliver this speech effectively. Ask me relevant questions and suggest steps we should take to ensure a successful presentation.\n\nPotential areas we can work on include:\n\nDefining the core message and key takeaways.\nUnderstanding the audience's needs and expectations.\nStructuring the speech for maximum impact.\nCrafting engaging content and supporting materials.\nDeveloping effective opening and closing remarks.\nPracticing delivery and managing speaking anxiety.\nAnticipating potential questions from the audience.\nConsidering the logistics of the presentation (e.g., time limits, equipment).\nWhere should we begin?"}]}}, "typeVersion": 3.4}, {"id": "0a9f2c04-7f7e-488c-866c-717a78bf7db1", "name": "Set prompt to generate a speech based on the feedback", "type": "n8n-nodes-base.set", "position": [1340, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ddbff433-f4bb-4ac1-a954-7fb32c942a9b", "name": "system_prompt", "type": "string", "value": "I want you to act as a speech synthesizer and improvement agent. You have access to the content of several speeches I have previously provided, along with the constructive feedback I received on each and with this information your task is to generate a new speech.\n\nThis new speech should incorporate the following:\n\nKey themes and ideas that were present and well-received in my previous speeches.\nStructural elements and transitions that were identified as effective in past feedback.\nEngagement techniques that were noted as successful.\nAvoidance of areas for improvement highlighted in the feedback (e.g., rambling sections, unclear points, pacing issues).\nIncorporation of specific suggestions for improvement that were given.\nA similar tone and style to my previous speeches, while aiming for enhanced clarity and impact based on the feedback.\nPlease provide the complete text of the new speech. Feel free to ask clarifying questions if needed about the new topic, audience, or goal, or if you need a reminder of specific feedback points from my previous speeches. I am ready when you are.\""}]}}, "typeVersion": 3.4}, {"id": "f242361e-6bf4-4c4e-8cd0-72da06823842", "name": "Set prompt to provide feedback on speech", "type": "n8n-nodes-base.set", "position": [1340, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b86d5905-872a-40fa-9855-054cd8991a0d", "name": "system_prompt", "type": "string", "value": "I'd like you to act as a speech feedback agent. I will deliver a speech to you, and I want you to provide constructive criticism and insights on various aspects of my delivery and content.  Please pay attention to:  Clarity and Conciseness: Was the message easy to understand? Were there any parts that felt rambling or unnecessary? Engagement: How engaging was the speech overall? Were there moments where your attention might have drifted? Structure and Flow: Did the speech progress logically? Were the transitions smooth? Pacing and Timing: Was the speech delivered at an appropriate pace? Did it feel rushed or too slow? Vocal Delivery (if applicable): (If you are able to describe vocal elements) How was the tone, pitch, and volume? Did it enhance or detract from the message? Content and Impact: Was the content compelling and relevant? Did the speech achieve its intended purpose (as I will describe beforehand)? What was the overall impact of the message? Strengths: What were the most effective aspects of the speech? Areas for Improvement: What specific suggestions do you have to make the speech even better? Before I begin, I will briefly tell you the topic, my intended audience, and my goal for the speech. Are you ready?"}]}}, "typeVersion": 3.4}, {"id": "c1f69bc3-2c93-404e-b338-82663deb975b", "name": "Route Flow Based on Message Content", "type": "n8n-nodes-base.switch", "position": [680, 260], "parameters": {"rules": {"values": [{"outputKey": "new_speech", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8b36fb19-1a5a-4fe1-aec2-7de8b5829972", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.text }}", "rightValue": "new speech"}]}, "renameOutput": true}, {"outputKey": "generate_speech", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "708d114d-1146-4d8a-b972-cfb5a53a8d77", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.text }}", "rightValue": "generate speech"}]}, "renameOutput": true}]}, "options": {"ignoreCase": true, "fallbackOutput": "extra"}}, "typeVersion": 3.2}, {"id": "6cf41698-2345-4ff8-bd1e-9549e372b454", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1280, -300], "parameters": {"width": 220, "height": 900, "content": "## Dynamic System Prompting:\n\nThis node sets the AI's system prompt according to the user's request identified in the incoming message."}, "typeVersion": 1}, {"id": "1d967631-9300-4fb6-b488-8c09beccbb05", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2000, -300], "parameters": {"width": 440, "height": 720, "content": "## Telegram-Ready Output: Formatting and Length Management:\n\nThese code nodes perform two crucial tasks:\n1.  **Formatting:** Removing characters that could cause issues with Telegram's message parsing.\n2.  **Chunking:** Dividing messages longer than Telegram's 4000-character limit into multiple shorter messages for sequential delivery."}, "typeVersion": 1}, {"id": "c480550b-c94f-40ee-8338-3deb6bea28d8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [900, -300], "parameters": {"width": 340, "height": 420, "content": "## Clearing AI Agent Memory:\n\nThis node clears the AI agent's short-term memory. This helps to minimize the influence of past interactions on future responses, thereby reducing the likelihood of the AI generating inaccurate or irrelevant information (hallucinations)."}, "typeVersion": 1}, {"id": "bd36f9a8-30d9-4e0a-978b-a74806685adc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-320, -300], "parameters": {"width": 1160, "height": 740, "content": "## Processing Telegram Messages:\n\nThis section handles incoming messages from Telegram. It first checks if the message contains text.\n\n1.  **Text Message:** If the message includes text, it's directly routed to the analysis switch node.\n2.  **Audio Message:** If the message is an audio file:\n    * The audio file is downloaded.\n    * The audio is transcribed into text.\n    * The transcribed text is then sent to the analysis switch node.\n\nFinally, the analyzed text (whether directly from a text message or transcribed from audio) is forwarded for further processing based on the analysis results."}, "typeVersion": 1}, {"id": "cf7d8897-b963-4bb5-9b0e-3ab628e478c7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1620, -300], "parameters": {"width": 300, "height": 740, "content": "## Gemini-Powered Response and Conversation Storage:\n\nThis node utilizes the Google Gemini model to generate a response to the user's prompt and stores the ongoing conversation."}, "typeVersion": 1}, {"id": "9fffb613-536e-4b4d-8841-7b4bd3121eab", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-300, -500], "parameters": {"color": 4, "width": 2740, "height": 140, "content": "## This n8n workflow acts as your personal AI speechwriting coach, directly accessible through Telegram. It listens to your spoken or typed drafts, provides insightful feedback on clarity, engagement, structure, and content, and iteratively refines your message based on your updates. Once you're ready, it synthesizes a brand-new speech or talk incorporating all the improvements and your accumulated ideas. This tool streamlines the speechwriting process, offering on-demand AI assistance to help you craft impactful and well-structured presentations."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4aaa8457-2661-4261-a601-0a0ff<PERSON><PERSON><PERSON>ff", "connections": {"AI Agent": {"main": [[{"node": "Code to remove unwanted characters from LLM response", "type": "main", "index": 0}]]}, "If Voice Message": {"main": [[{"node": "Download Audio File", "type": "main", "index": 0}], [{"node": "Route Flow Based on Message Content", "type": "main", "index": 0}]]}, "Download Audio File": {"main": [[{"node": "Transcribe Audio File", "type": "main", "index": 0}]]}, "Transcribe Audio File": {"main": [[{"node": "Route Flow Based on Message Content", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Recieve Telegram Message": {"main": [[{"node": "Check For Text or Voice Message", "type": "main", "index": 0}]]}, "Wipe Conversation Memory": {"main": [[{"node": "Set prompt to start a new speech", "type": "main", "index": 0}]]}, "Store Conversation Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}, {"node": "Wipe Conversation Memory", "type": "ai_memory", "index": 0}]]}, "Check For Text or Voice Message": {"main": [[{"node": "If Voice Message", "type": "main", "index": 0}]]}, "Set prompt to start a new speech": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Route Flow Based on Message Content": {"main": [[{"node": "Wipe Conversation Memory", "type": "main", "index": 0}], [{"node": "Set prompt to generate a speech based on the feedback", "type": "main", "index": 0}], [{"node": "Set prompt to provide feedback on speech", "type": "main", "index": 0}]]}, "Set prompt to provide feedback on speech": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Code to remove unwanted characters from LLM response": {"main": [[{"node": "Code to split output into chunks under 4000 characters", "type": "main", "index": 0}]]}, "Set prompt to generate a speech based on the feedback": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Code to split output into chunks under 4000 characters": {"main": [[{"node": "Respond to Telegram Message", "type": "main", "index": 0}]]}}}