{"id": "IyDJ7Zgh4MV43YTh", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Convert image from jpg/png to webp", "tags": [], "nodes": [{"id": "09977b8b-e095-4419-b136-bcbadf0f5d84", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-320, -20], "parameters": {}, "typeVersion": 1}, {"id": "55c01841-9576-4663-bb24-c9e0082ecab5", "name": "Set API KEY", "type": "n8n-nodes-base.set", "position": [40, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1fa468da-3e30-46b0-a44b-a723e45c5fda", "name": "apikey", "type": "string", "value": "APY**************************************************************"}]}}, "typeVersion": 3.4}, {"id": "2d50e290-a861-4575-abbc-7f311d1934bb", "name": "Get images", "type": "n8n-nodes-base.googleSheets", "position": [380, -20], "parameters": {"options": {"returnFirstMatch": true}, "filtersUI": {"values": [{"lookupColumn": "DONE"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM/edit?usp=drivesdk", "cachedResultName": "Convert images"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "b3c5d64c-0e2a-472b-83f7-91cabd4d1646", "name": "Get extension", "type": "n8n-nodes-base.code", "position": [660, -20], "parameters": {"jsCode": "// Loop over input items and add new fields 'FILENAME' and 'EXTENSION' to the JSON of each one\nfor (const item of $input.all()) {\n  // Extract the 'FROM' field\n  const url = item.json.FROM;\n\n  const filenameWithExtension = url.split('/').pop().split(/[#?]/)[0];\n\n  const extension = filenameWithExtension.split('.').pop();\n\n  const filename = filenameWithExtension.substring(0, filenameWithExtension.length - extension.length - 1);\n\n  item.json.FILENAME = filename;\n  item.json.EXTENSION = extension;\n}\n\nreturn $input.all();\n"}, "typeVersion": 2}, {"id": "e281cd63-79d1-4ca3-88c0-81aaa7e0dbe8", "name": "JPG or PNG?", "type": "n8n-nodes-base.switch", "position": [-320, 460], "parameters": {"rules": {"values": [{"outputKey": "jpeg", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f25651ea-ee05-4e8d-91a8-fa96997e2794", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.EXTENSION }}", "rightValue": "jpg"}]}, "renameOutput": true}, {"outputKey": "jpeg", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a2dc1fd-5e5a-4015-bad1-e258dfead84f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.EXTENSION }}", "rightValue": "jpeg"}]}, "renameOutput": true}, {"outputKey": "png", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1d0e09dd-edee-4778-9b3a-9a4429a06db0", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.EXTENSION }}", "rightValue": "png"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "f3257837-88e0-4f5f-bbd5-5c63c5ba4ed1", "name": "From JPG to WEBP", "type": "n8n-nodes-base.httpRequest", "position": [-100, 320], "parameters": {"url": "=https://api.apyhub.com/convert/image/jpeg/webp/url?output=test-sample", "method": "POST", "options": {}, "jsonBody": "={\n  \"url\":\"{{ $json.FROM }}\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "api-token", "value": "={{ $('Set API KEY').item.json.apikey }}"}]}}, "typeVersion": 4.2}, {"id": "de1198c3-17a2-4b45-a334-6334b2b935c4", "name": "PNG to WEBP", "type": "n8n-nodes-base.httpRequest", "position": [-100, 580], "parameters": {"url": "=https://api.apyhub.com/convert/image/png/webp/url?output=test-sample", "method": "POST", "options": {}, "jsonBody": "={\n  \"url\":\"{{ $json.FROM }}\"\n} ", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "apy-token", "value": "={{ $('Set API KEY').item.json.apikey }}"}]}}, "typeVersion": 4.2}, {"id": "38b46480-c089-4bca-88ac-7f006c12d3b9", "name": "Update Sheet", "type": "n8n-nodes-base.googleSheets", "position": [160, 440], "parameters": {"columns": {"value": {"TO": "={{ $json.data }}", "DONE": "x", "row_number": "={{ $('Get images').item.json.row_number }}"}, "schema": [{"id": "FROM", "type": "string", "display": true, "required": false, "displayName": "FROM", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "TO", "type": "string", "display": true, "required": false, "displayName": "TO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DONE", "type": "string", "display": true, "required": false, "displayName": "DONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM/edit?usp=drivesdk", "cachedResultName": "Convert images from jpg/png to webp"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "2dc29c73-efcb-4bef-8d9a-5a1914df62ad", "name": "Get file image", "type": "n8n-nodes-base.httpRequest", "position": [420, 440], "parameters": {"url": "={{ $json.TO }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "acee7120-ceb4-472e-a941-066056da5cd6", "name": "Upload image", "type": "n8n-nodes-base.googleDrive", "position": [700, 440], "parameters": {"name": "={{ $('Get extension').item.json.FILENAME }}.webp", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1XyUSYXdNrZIw0XyZ3YpuaxGJjOaARyEJ", "cachedResultUrl": "https://drive.google.com/drive/folders/1XyUSYXdNrZIw0XyZ3YpuaxGJjOaARyEJ", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>"}}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account (n3w.it)"}}, "typeVersion": 3}, {"id": "0a491e7b-2482-429e-9901-cb2bf3d34509", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-360, -520], "parameters": {"color": 3, "width": 800, "height": 200, "content": "## Convert image from jpg/png to webp\n\nThis workflow automates the process of converting images from **JPG/PNG** format to **WEBP** using the **APYHub API**. It retrieves image URLs from a **Google Sheet**, converts the images, and uploads the converted files to **Google Drive**. \n\nThis workflow is a powerful tool for automating image conversion tasks, saving time and ensuring that images are efficiently converted and stored in the desired format."}, "typeVersion": 1}, {"id": "78a198c4-449f-4a68-96e4-20ecd044fe1f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-360, -280], "parameters": {"width": 800, "height": 120, "content": "## PRELIMINARY STEP\n- Get your FREE API KEY from [APYHub](https://apyhub.com//)\n- Clone [this sheet](https://docs.google.com/spreadsheets/d/1upj3EDLwU1N7NHWWV3DhwMuE6ty39tIK5z5lCVDWWuM/edit?usp=sharing) and insert the URL of your images (only jpg, jpeg and png format) in the column \"FROM\""}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e2fa7236-fbf9-43ec-a217-aeb43664d129", "connections": {"Get images": {"main": [[{"node": "Get extension", "type": "main", "index": 0}]]}, "JPG or PNG?": {"main": [[{"node": "From JPG to WEBP", "type": "main", "index": 0}], [{"node": "From JPG to WEBP", "type": "main", "index": 0}], [{"node": "PNG to WEBP", "type": "main", "index": 0}]]}, "PNG to WEBP": {"main": [[{"node": "Update Sheet", "type": "main", "index": 0}]]}, "Set API KEY": {"main": [[{"node": "Get images", "type": "main", "index": 0}]]}, "Update Sheet": {"main": [[{"node": "Get file image", "type": "main", "index": 0}]]}, "Get extension": {"main": [[{"node": "JPG or PNG?", "type": "main", "index": 0}]]}, "Get file image": {"main": [[{"node": "Upload image", "type": "main", "index": 0}]]}, "From JPG to WEBP": {"main": [[{"node": "Update Sheet", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set API KEY", "type": "main", "index": 0}]]}}}