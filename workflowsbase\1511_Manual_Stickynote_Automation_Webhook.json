{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "abccacce-bbdc-428e-94e0-19996c5bfe02", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1720, 160], "parameters": {"color": 7, "width": 319.5392879244982, "height": 218.888***********, "content": "### AI agent that can scrape webpages\nRemake of https://n8n.io/workflows/2006-ai-agent-that-can-scrape-webpages/\n\n**Changes**:\n* Replaces Execute Workflow Tool and Subworkflow\n* Replaces Response Formatting"}, "typeVersion": 1}, {"id": "9fc05c79-5a2d-4ac4-a4f5-32b9c1b385e1", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1340, 340], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "45c9bdaf-d51e-4026-8911-4b04c5473b06", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1720, 560], "parameters": {"color": 7, "width": 365.*************, "height": 245.**************, "content": "### Allow your AI to call an API to fetch data\nRemake of https://n8n.io/workflows/2094-allow-your-ai-to-call-an-api-to-fetch-data/\n\n**Changes**:\n* Replaces Execute Workflow Tool and Subworkflow\n* Replaces Manual Query Params Definitions\n* Replaces Response Formatting"}, "typeVersion": 1}, {"id": "bc1754e6-01f4-4561-8814-c08feb45acec", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1340, 740], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "a40230ae-6050-4bb8-b275-3a893dc3ad98", "name": "Activity Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1560, 740], "parameters": {"url": "https://bored-api.appbrewery.com/filter", "sendQuery": true, "parametersQuery": {"values": [{"name": "type"}, {"name": "participants"}]}, "toolDescription": "Call this tool to suggest an activity where:\n* the parameter \"type\" is one of \"education\", \"recreational\",\"social\",\"diy\",\"charity\",\"cooking\",\"relaxation\",\"music\",\"busywork\"\n* the parameter \"participants\" is the number of participants for the activity"}, "typeVersion": 1}, {"id": "297377e0-e149-4786-b521-82670ac390a7", "name": "Set ChatInput1", "type": "n8n-nodes-base.set", "position": [1180, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e976bf5f-8803-4129-9136-115b3d15755c", "name": "chatInput", "type": "string", "value": "Hi! Please suggest something to do. I feel like learning something new!"}]}}, "typeVersion": 3.4}, {"id": "a9128da1-4486-4a17-b9b3-64ebc402348d", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1360, 560], "parameters": {"text": "={{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "28a5e75e-e32d-4c94-bea2-7347923e6bb9", "name": "Set ChatInput", "type": "n8n-nodes-base.set", "position": [1160, 160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9695c156-c882-4e43-8a4e-70fbdc1a63de", "name": "chatInput", "type": "string", "value": "Can get the latest 10 issues from https://github.com/n8n-io/n8n/issues?"}]}}, "typeVersion": 3.4}, {"id": "d29b30fb-7edb-4665-bc6b-a511caf9db9f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [900, 400], "parameters": {}, "typeVersion": 1}, {"id": "066f9cdd-4bd3-48a1-bf9b-32eda3e28945", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1360, 160], "parameters": {"text": "={{ $json.chatInput }}", "options": {}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "fb4abae8-7e38-47b7-9595-403e523f7125", "name": "Webscraper Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1560, 340], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to fetch a webpage content.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "73d3213c-1ecb-4007-b882-1cc756a6f6e0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [420, 120], "parameters": {"width": 413.82332632615135, "height": 435.92895157500243, "content": "## Try It Out!\n\n### The HTTP tool is drastically simplifies API-enabled AI agents cutting down the number of workflow nodes by as much as 10!\n\n* Available since v1.47.0\n* Recommended for single purpose APIs which don't require much post-fetch formatting.\n* If you require a chain of API calls, you may need to implement a subworkflow instead.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Activity Tool": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Set ChatInput": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Set ChatInput1": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Webscraper Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set ChatInput", "type": "main", "index": 0}, {"node": "Set ChatInput1", "type": "main", "index": 0}]]}}}