{"id": "eF84e2NyJWTCVClW", "meta": {"instanceId": "5ce52989094be90be3b3bdd9ed9cee1d7ce1fcecaa598afaec4a50646d32e291", "templateCredsSetupCompleted": true}, "name": "Create Custom Presentations per Lead", "tags": [{"id": "fSDcaaN3w5sV5e3S", "name": "Templates", "createdAt": "2025-02-23T15:20:47.262Z", "updatedAt": "2025-02-23T15:20:47.262Z"}, {"id": "iMiiPG3ObM1yILch", "name": "SD - Sales", "createdAt": "2025-02-23T15:27:45.789Z", "updatedAt": "2025-02-23T15:28:28.561Z"}], "nodes": [{"id": "4cc04b1c-d97d-4d5a-b614-ba22b7b447bd", "name": "Download by ID", "type": "n8n-nodes-base.googleDrive", "position": [-280, 160], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "J7noNRzf26R3DpFF", "name": "<PERSON>"}}, "typeVersion": 3}, {"id": "8d550abf-a5f8-424d-8bfb-d9a9732eb93f", "name": "MoveToLeadListFolder", "type": "n8n-nodes-base.googleDrive", "position": [1060, 160], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.spreadsheetId }}"}, "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=1-oMQTyijYXNmt-Dwh748JFjlDZVCu6ii"}, "operation": "move"}, "credentials": {"googleDriveOAuth2Api": {"id": "J7noNRzf26R3DpFF", "name": "<PERSON>"}}, "typeVersion": 3}, {"id": "4872b5ec-6dda-4920-b6e2-120bdc273c00", "name": "Add Presentation ID to Lead List", "type": "n8n-nodes-base.googleSheets", "position": [1740, 160], "parameters": {"columns": {"value": {"Email": "={{ $('Get all Leads').item.json.Email }}", "PresentationID": "={{ $json.presentationId }}"}, "schema": [{"id": "First Name", "type": "string", "display": true, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Full Name", "type": "string", "display": true, "required": false, "displayName": "Full Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Title", "type": "string", "display": true, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company", "type": "string", "display": true, "required": false, "displayName": "Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Location", "type": "string", "display": true, "required": false, "displayName": "Contact Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Employees", "type": "string", "display": true, "required": false, "displayName": "Employees", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Country", "type": "string", "display": true, "required": false, "displayName": "Country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "State", "type": "string", "display": true, "required": false, "displayName": "State", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "City", "type": "string", "display": true, "required": false, "displayName": "City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PresentationID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PresentationID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Keywords", "type": "string", "display": true, "required": false, "displayName": "Keywords", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.sheets[0].properties.sheetId }}"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.spreadsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tO62CXNbmAYSCBIY", "name": "Midgard"}}, "typeVersion": 4.5}, {"id": "afe225e3-fb82-4091-8310-6645d883c54d", "name": "Get all Leads", "type": "n8n-nodes-base.googleSheets", "position": [840, 160], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.sheets[0].properties.sheetId }}"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.spreadsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tO62CXNbmAYSCBIY", "name": "Midgard"}}, "executeOnce": true, "typeVersion": 4.5}, {"id": "635c62cf-4369-442b-8fd7-be2e4caa6ebc", "name": "Create new Sheet", "type": "n8n-nodes-base.googleSheets", "position": [0, 40], "parameters": {"title": "=Leads_{{ $now.setZone('Europe/Berlin').toFormat('yyyy-dd-MM') }}", "options": {}, "resource": "spreadsheet", "sheetsUi": {"sheetValues": [{"title": "sample_data"}]}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tO62CXNbmAYSCBIY", "name": "Midgard"}}, "typeVersion": 4.5}, {"id": "d55c0f32-b74a-484a-b759-9ca1c4cf8d65", "name": "Merge Data for new Lead Document", "type": "n8n-nodes-base.googleSheets", "position": [580, 160], "parameters": {"columns": {"value": {}, "schema": [{"id": "First Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "First Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Last Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Full Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Full Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Contact Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Employees", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Employees", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Country", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "State", "type": "string", "display": true, "removed": false, "required": false, "displayName": "State", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "City", "type": "string", "display": true, "removed": false, "required": false, "displayName": "City", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Keywords", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Keywords", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {"useAppend": true}, "operation": "append", "sheetName": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.sheets[0].properties.sheetId }}"}, "documentId": {"__rl": true, "mode": "id", "value": "={{ $('Create new Sheet').first().json.spreadsheetId }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "tO62CXNbmAYSCBIY", "name": "Midgard"}}, "typeVersion": 4.5}, {"id": "9d93741a-93b2-4556-9f2e-2d40617c69ac", "name": "New Leads Arrived", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-800, 160], "parameters": {"event": "fileCreated", "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": "1GYT9Z8_BnqqY9dqsMpFWJqjeNVsq_xTY", "cachedResultUrl": "https://drive.google.com/drive/folders/1GYT9Z8_BnqqY9dqsMpFWJqjeNVsq_xTY", "cachedResultName": "__cmath"}}, "credentials": {"googleDriveOAuth2Api": {"id": "J7noNRzf26R3DpFF", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "174ff078-e06a-47c3-93c2-441667e0f8e5", "name": "File Type?", "type": "n8n-nodes-base.switch", "position": [-560, 160], "parameters": {"rules": {"values": [{"outputKey": "csv", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.mimeType }}", "rightValue": "text/csv"}]}, "renameOutput": true}, {"outputKey": "xlsx", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c2f2fb50-a750-4870-aff7-11df142a9be5", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "23d71fb1-c8f2-4144-a40e-be378aa2043a", "name": "Combine Empty New Document with CSV Data", "type": "n8n-nodes-base.merge", "position": [300, 160], "parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "typeVersion": 3}, {"id": "195d7ed6-7dc0-4f49-8256-f47b4c8d426b", "name": "Create Custom Presentation", "type": "n8n-nodes-base.googleSlides", "position": [1520, 160], "parameters": {"textUi": {"textValues": [{"text": "{COMPANYNAME}", "replaceText": "={{ $('Get all Leads').item.json.Company }}"}, {"text": "{Testdurchgestrichen}", "replaceText": "={{ $('Get all Leads').item.json['Full Name'] }}"}, {"text": "{nichtdurchgestrichen}", "replaceText": "={{ $('Get all Leads').item.json['First Name'] }}"}]}, "options": {}, "operation": "replaceText", "presentationId": "={{ $json.id }}"}, "credentials": {"googleSlidesOAuth2Api": {"id": "e9cejsZpBHAaXKv0", "name": "GSlides Midgard"}}, "typeVersion": 2}, {"id": "97d47391-20ea-4db7-bdf6-22eebc22a9dd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [980, 60], "parameters": {"color": 4, "width": 960, "height": 340, "content": "# Duplicate Template and Create Custom Presentations"}, "typeVersion": 1}, {"id": "acded411-6cdf-489a-bb14-c7c14cdaee23", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-320, -120], "parameters": {"color": 4, "width": 1300, "height": 520, "content": "# Create New Google Sheets and Insert Data from CSV file"}, "typeVersion": 1}, {"id": "9dd9e32a-5c1b-4faf-8054-63b66f5dd24e", "name": "Extract Information from CSV file", "type": "n8n-nodes-base.extractFromFile", "position": [0, 220], "parameters": {"options": {"encoding": "utf-8", "delimiter": ",", "headerRow": true}}, "typeVersion": 1}, {"id": "0329eabe-dee1-41fd-a7eb-017343523e40", "name": "Copy Presentation Template", "type": "n8n-nodes-base.googleDrive", "position": [1300, 160], "parameters": {"name": "={{ $('Get all Leads').item.json.Company }} X MYCOMPANYNAME_{{ $now.setZone('Europe/Berlin').toFormat('yyyy-dd-MM') }}", "fileId": {"__rl": true, "mode": "list", "value": "1FtMBECbZY-9gSW6L6DtioOOJv-V9LXduEjZO0pp-NmA", "cachedResultUrl": "https://docs.google.com/presentation/d/1FtMBECbZY-9gSW6L6DtioOOJv-V9LXduEjZO0pp-NmA/edit?usp=drivesdk", "cachedResultName": "Stardawn Updated"}, "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "1Yu2s72rgOJlz1-tMuzlaeN8UZezYftRT", "cachedResultUrl": "https://drive.google.com/drive/folders/1Yu2s72rgOJlz1-tMuzlaeN8UZezYftRT", "cachedResultName": "Custom Presentations"}, "operation": "copy", "sameFolder": false}, "credentials": {"googleDriveOAuth2Api": {"id": "J7noNRzf26R3DpFF", "name": "<PERSON>"}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "bd03e5b5-be73-444f-a1e6-f037db77a01b", "connections": {"File Type?": {"main": [[{"node": "Download by ID", "type": "main", "index": 0}]]}, "Get all Leads": {"main": [[{"node": "MoveToLeadListFolder", "type": "main", "index": 0}]]}, "Download by ID": {"main": [[{"node": "Extract Information from CSV file", "type": "main", "index": 0}, {"node": "Create new Sheet", "type": "main", "index": 0}]]}, "Create new Sheet": {"main": [[{"node": "Combine Empty New Document with CSV Data", "type": "main", "index": 0}]]}, "New Leads Arrived": {"main": [[{"node": "File Type?", "type": "main", "index": 0}]]}, "MoveToLeadListFolder": {"main": [[{"node": "Copy Presentation Template", "type": "main", "index": 0}]]}, "Copy Presentation Template": {"main": [[{"node": "Create Custom Presentation", "type": "main", "index": 0}]]}, "Create Custom Presentation": {"main": [[{"node": "Add Presentation ID to Lead List", "type": "main", "index": 0}]]}, "Merge Data for new Lead Document": {"main": [[{"node": "Get all Leads", "type": "main", "index": 0}]]}, "Extract Information from CSV file": {"main": [[{"node": "Combine Empty New Document with CSV Data", "type": "main", "index": 1}]]}, "Combine Empty New Document with CSV Data": {"main": [[{"node": "Merge Data for new Lead Document", "type": "main", "index": 0}]]}}}