{"id": "nkPjDxMrrkKbgHaV", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Effort<PERSON> Email Management with AI", "tags": [], "nodes": [{"id": "9d77e26f-de2b-4bd4-b0f0-9924a8f459a6", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [-2000, -180], "parameters": {"options": {}}, "credentials": {"imap": {"id": "k31W9oGddl9pMDy4", "name": "IMAP <EMAIL>"}}, "typeVersion": 2}, {"id": "cf2d020b-b125-4a20-8694-8ed0f7acf755", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [-1740, -180], "parameters": {"html": "={{ $json.textHtml }}", "options": {}}, "typeVersion": 1}, {"id": "41bfceff-0155-4643-be60-ee301e2d69e1", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [400, -320], "webhookId": "a79ae1b4-648c-4cb4-b6cd-04ea3c1d9314", "parameters": {"html": "={{ $('Edit Fields').item.json.email }}", "options": {}, "subject": "=Re: {{ $('<PERSON><PERSON>gger (IMAP)').item.json.subject }}", "toEmail": "={{ $('<PERSON><PERSON> (IMAP)').item.json.from }}", "fromEmail": "={{ $('<PERSON><PERSON> (IMAP)').item.json.to }}"}, "credentials": {"smtp": {"id": "hRjP3XbDiIQqvi7x", "name": "SMTP <EMAIL>"}}, "typeVersion": 2.1}, {"id": "2aff581a-8b64-405c-b62f-74bf189fd7b1", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-320, 600], "parameters": {"mode": "retrieve-as-tool", "options": {}, "toolName": "company_knowladge_base", "toolDescription": "Extracts information regarding the request made.", "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}, "includeDocumentMetadata": false}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "6e3f6df0-8924-47d9-855c-51205d19e86d", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-440, 800], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "37ac411b-4a74-44d1-917e-b07d1c9ca221", "name": "Email Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [-1480, -180], "parameters": {"options": {"binaryDataKey": "={{ $json.data }}", "summarizationMethodAndPrompts": {"values": {"prompt": "=Write a concise summary of the following in max 100 words:\n\n\"{{ $json.data }}\"\n\nDo not enter the total number of words used.", "combineMapPrompt": "=Write a concise summary of the following in max 100 words:\n\n\"{{ $json.data }}\"\n\nDo not enter the total number of words used."}}}, "operationMode": "nodeInputBinary"}, "typeVersion": 2}, {"id": "91edbac9-847b-4f31-a8dd-09418bd93642", "name": "Write email", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1040, -180], "parameters": {"text": "=Write the text to reply to the following email:\n\n{{ $json.response.text }}", "options": {"systemMessage": "You are an expert at answering emails. You need to answer them professionally based on the information you have. This is a business email. Be concise and never exceed 100 words. Only the body of the email, not create the subject"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "1da0e72a-db97-4216-a1a5-038cebaf7e10", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-180, 280], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "af2d6284-4c8f-4a07-b689-d0f55aaabd26", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [-300, -180], "webhookId": "d6dd2e7c-90ea-4b65-9c64-523d2541a054", "parameters": {"sendTo": "<EMAIL>", "message": "=<h3>MESSAGE</h3>\n{{ $('<PERSON><PERSON> (IMAP)').item.json.textHtml }}\n\n<h3>AI RESPONSE</h3>\n{{ $json.email }}", "options": {}, "subject": "=[Approval Required] {{ $('<PERSON><PERSON> (IMAP)').item.json.subject }}", "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"gmailOAuth2": {"id": "nyuHvSX5HuqfMPlW", "name": "Gmail account (n3w.it)"}}, "typeVersion": 2.1}, {"id": "aaccc4a6-ce53-4813-8247-65bd1a9d5639", "name": "Text Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-60, -180], "parameters": {"options": {"systemPromptTemplate": "Please classify the text provided by the user into one of the following categories: {categories}, and use the provided formatting instructions below. Don't explain, and only output the json."}, "inputText": "={{ $json.data.text }}", "categories": {"categories": [{"category": "Approved", "description": "The email has been reviewed and accepted as-is. The human explicitly or implicity express approva, indicating that no changes ar needed.\n\nExample:\n\"Ok\",\n\"Approvato\",\n\"Invia\""}, {"category": "Declined", "description": "The email has been reviewd, but the human request modifications before it sent link tweaks, removing parts, rewording etc... This could include suggested edits, rewording or major revision."}]}}, "typeVersion": 1}, {"id": "b46de5d9-1a2e-4d28-930b-e18fb1d7876e", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-580, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "35d7c303-42f4-4dd1-b41e-6eb087c23c3d", "name": "email", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "36ce51c6-8ee1-4230-84c0-40e259eafb1a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1340, -1300], "parameters": {}, "typeVersion": 1}, {"id": "21a0c991-65dc-483e-9b98-5cedaba7ae13", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-1040, -1440], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "9a048d7d-bcdf-40b7-b33a-94b811083eac", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-1040, -1180], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "db494d2d-5390-4f83-9b87-3409fef31a7d", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [-820, -1180], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "e30dbe6f-482e-47f9-b5b8-62c1113e6c8b", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [-600, -1180], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "492d48d8-4997-4f04-902b-041da3210417", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [-200, -980], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "0cf45d10-3cbf-4eb6-ab30-11f264b3aa8d", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [-240, -820], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "7d60f569-c34e-49a8-ba9a-88cf33083136", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-840, -1500], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "e86b18c4-d7e8-4e81-b520-dbd8125edf38", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -1240], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "05f65120-ef31-4c67-ac18-e68a8353909c", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-360, -1180], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "c15fd52f-b142-408e-af06-aeed10a1cf85", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-380, -980], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "3e47224f-3deb-450b-b825-f16c5f860f28", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2020, -600], "parameters": {"color": 3, "width": 580, "height": 260, "content": "# STEP 3 - MAIN FLOW\n\n\n## How it works\nThis workflow automates the handling of incoming emails, summarizes their content, generates appropriate responses using a retrieval-augmented generation (RAG) approach, and obtains approval or suggestions before sending replies. \n\nYou can quickly integrate Gmail and Outlook via the appropriate trigger nodes"}, "typeVersion": 1}, {"id": "********-58cb-4e0f-9fb6-6bf868275519", "name": "DeepSeek Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "position": [-1560, 40], "parameters": {"options": {}}, "credentials": {"deepSeekApi": {"id": "sxh1rfZxonXV83hS", "name": "DeepSeek account"}}, "typeVersion": 1}, {"id": "c86d6eeb-cf08-429f-b5b4-60b317071035", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1500, -260], "parameters": {"width": 320, "height": 240, "content": "Chain that summarizes the received email"}, "typeVersion": 1}, {"id": "4afc8b00-d1e5-473c-a71e-1299c84c546e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -260], "parameters": {"width": 340, "height": 240, "content": "Agent that retrieves business information from a vector database and processes the response"}, "typeVersion": 1}, {"id": "be1762ff-729b-4b83-9139-16f835b748f2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1800, -260], "parameters": {"height": 240, "content": "Convert email to Markdown format for better understanding of LLM models"}, "typeVersion": 1}, {"id": "f818ede7-895a-4860-91d3-f08cc32ec0e3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-380, -380], "parameters": {"color": 4, "height": 360, "content": "## IMPORTANT\n\nFor the \"Send Draft\" node, you need to send the draft email to a Gmail address because it is the only one that allows the \"Send and wait for response\" function."}, "typeVersion": 1}, {"id": "929b525a-912b-4f7b-a6e7-dfeb88a446c8", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-100, -260], "parameters": {"width": 360, "height": 240, "content": "Based on the suggestion received, the text classifier can understand whether the feedback received approves the generated email or not."}, "typeVersion": 1}, {"id": "2468e643-013f-4925-ab35-c8ef4ee6eed2", "name": "Email Reviewer", "type": "@n8n/n8n-nodes-langchain.agent", "position": [380, -40], "parameters": {"text": "=Review at the following email:\n{{ $('Edit Fields').item.json.email }}\n\nFeedback from human:\n{{ $json.data.text }}", "options": {"systemMessage": "If you are an expert in reviewing emails before sending them. You need to review and structure them in such a way that you can send them. It must be in HTML format and you can insert (if you think it is appropriate) only HTML characters such as <br>, <b>, <i>, <p> where necessary. Be concise and never exceed 100 words. Only the body of the email"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "ecd9d3f8-2e79-4e5f-a73d-48de60441376", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [340, -120], "parameters": {"width": 340, "height": 220, "content": "The Email Reviewer agent, taking inspiration from human feedback, rewrites the email"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "de11da52-1513-4797-8070-b64e84b84158", "connections": {"Gmail": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "OpenAI": {"ai_languageModel": [[{"node": "Write email", "type": "ai_languageModel", "index": 0}, {"node": "Email Reviewer", "type": "ai_languageModel", "index": 0}, {"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Markdown": {"main": [[{"node": "Email Summarization Chain", "type": "main", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Write email": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store1", "type": "main", "index": 0}]]}, "Email Reviewer": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Send Email", "type": "main", "index": 0}], [{"node": "Email Reviewer", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "Email Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store1", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store": {"ai_tool": [[{"node": "Write email", "type": "ai_tool", "index": 0}, {"node": "Email Reviewer", "type": "ai_tool", "index": 0}]]}, "Email Trigger (IMAP)": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Email Summarization Chain": {"main": [[{"node": "Write email", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}