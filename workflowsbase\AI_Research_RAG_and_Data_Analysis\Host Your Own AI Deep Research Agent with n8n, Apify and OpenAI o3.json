{
"meta": {
"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9",
"templateCredsSetupCompleted": true
},
"nodes": [
{
"id": "645ae2b1-799e-49be-8bdf-12cd1bb739e6",
"name": "Structured Output Parser",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
1680,
1140
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"learnings\": {\n \"type\": \"array\",\n \"description\": \"List of learnings, max of 3.\",\n \"items\": { \"type\": \"string\" }\n },\n \"followUpQuestions\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"string\",\n \"description\": \"List of follow-up questions to research the topic further, max of 3.\"\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "cbdb4e98-eeba-4609-91de-394c416b7904",
"name": "Set Variables",
"type": "n8n-nodes-base.set",
"position": [
-1360,
-460
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "df28b12e-7c20-4ff5-b5b8-dc773aa14d4b",
"name": "request_id",
"type": "string",
"value": "={{ $execution.id }}"
},
{
"id": "9362c1e7-717d-444a-8ea2-6b5f958c9f3f",
"name": "prompt",
"type": "string",
"value": "={{ $json['What would you like to research?'] }}"
},
{
"id": "09094be4-7844-4a9e-af82-cc8e39322398",
"name": "depth",
"type": "number",
"value": "={{ $json['Enter research depth (recommended 1-5, default 2)'] || 2 }}"
},
{
"id": "3fc30a30-7806-4013-835d-97e27ddd7ae1",
"name": "breadth",
"type": "number",
"value": "={{ $json['Enter research breadth (recommended 2-10, default 4)'] || 4 }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "c7096ab9-0b10-45b0-b178-a049bf57830b",
"name": "OpenAI Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
1500,
1140
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "d0f1bc2f-6a10-4ac7-8d35-34f48f14fad5",
"name": "OpenAI Chat Model1",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-860,
1760
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "bba3278c-0336-4305-887d-56515dfd87db",
"name": "OpenAI Chat Model2",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-1060,
-300
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "f31f2fc7-0bec-4105-9d83-5f4f9a0eb35d",
"name": "Structured Output Parser1",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
-840,
-300
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"questions\": {\n \"type\": \"array\",\n \"description\": \"Follow up questions to clarify the research direction, max of 3.\",\n \"items\": {\n \"type\": \"string\"\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "ea59c5ab-fa05-4c68-bc60-3f56e240478b",
"name": "On form submission",
"type": "n8n-nodes-base.formTrigger",
"position": [
-1760,
-460
],
"webhookId": "7ddfaa7c-a523-4d92-b033-d76cd5a313e9",
"parameters": {
"options": {
"path": "deep_research",
"ignoreBots": true,
"buttonLabel": "Next"
},
"formTitle": " DeepResearcher",
"formFields": {
"values": [
{
"fieldType": "html",
"fieldLabel": "placeholder"
}
]
},
"formDescription": "=DeepResearcher is a multi-step, recursive approach using the internet to solve complex research tasks, accomplishing in tens of minutes what a human would take many hours.\n\nTo use, provide a short summary of what the research and how \"deep\" you'd like the workflow to investigate. Note, the higher the numbers the more time and cost will occur for the research.\n\nThe workflow is designed to complete independently and when finished, a report will be saved in a designated Notion Database."
},
"typeVersion": 2.2
},
{
"id": "a8262288-a8c1-4967-9870-f728fa08b579",
"name": "Generate SERP Queries",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-1040,
820
],
"parameters": {
"text": "=Given the following prompt from the user, generate a list of SERP queries to research the topic. Return a maximum of {{ $('JobType Router').first().json.data.breadth }} queries, but feel free to return less if the original prompt is clear. Make sure each query is unique and not similar to each other: <prompt>{{ $('JobType Router').first().json.data.query.trim() }}</prompt>\n\n{{\n$('JobType Router').first().json.data.learnings.length\n ? `Here are some learnings from previous research, use them to generate more specific queries: ${$('JobType Router').first().json.data.learnings.join('\\n')}`\n : ''\n}}",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "0534be47-22b7-4c2a-956b-d085e6b9f280",
"name": "Structured Output Parser2",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
-860,
980
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"queries\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"query\": {\n \"type\": \"string\",\n \"description\": \"The SERP query\"\n },\n \"researchGoal\": {\n \"type\": \"string\",\n \"description\": \"First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions.\"\n }\n }\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "4d8aa196-986f-442d-9b56-92c043ab785d",
"name": "OpenAI Chat Model3",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-1040,
980
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "7488b037-7422-4f62-8c37-1f6a901b3299",
"name": "Set Initial Query",
"type": "n8n-nodes-base.set",
"position": [
-580,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "acb41e93-70c6-41a3-be0f-e5a74ec3ec88",
"name": "query",
"type": "string",
"value": "={{ $('JobType Router').first().json.data.query }}"
},
{
"id": "7fc54063-b610-42bc-a250-b1e8847c4d1e",
"name": "learnings",
"type": "array",
"value": "={{ $('JobType Router').first().json.data.learnings }}"
},
{
"id": "e8f1c158-56fb-41c8-8d86-96add16289bb",
"name": "breadth",
"type": "number",
"value": "={{ $('JobType Router').first().json.data.breadth }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "12ae382e-d88a-4f1b-a71f-3bd63c892b17",
"name": "SERP to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-700,
820
],
"parameters": {
"options": {},
"fieldToSplitOut": "output.queries"
},
"typeVersion": 1
},
{
"id": "********-f48a-493c-aebf-cdf175d58550",
"name": "Item Ref",
"type": "n8n-nodes-base.noOp",
"position": [
-240,
980
],
"parameters": {},
"typeVersion": 1
},
{
"id": "2cef6f1d-e244-4ee6-bf25-6dc3e8042afa",
"name": "Research Goal + Learnings",
"type": "n8n-nodes-base.set",
"position": [
1840,
1120
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "9acec2cc-64c8-4e62-bed4-c3d9ffab1379",
"name": "researchGoal",
"type": "string",
"value": "={{ $('Item Ref').first().json.researchGoal }}"
},
{
"id": "1b2d2dad-429b-4fc9-96c5-498f572a85c3",
"name": "learnings",
"type": "array",
"value": "={{ $json.output.learnings }}"
},
{
"id": "655b99f2-6045-4774-a634-49751bc9326f",
"name": "followUpQuestions",
"type": "array",
"value": "={{ $json.output.followUpQuestions }}"
},
{
"id": "c9e34ea4-5606-46d6-8d66-cb42d772a8b4",
"name": "urls",
"type": "array",
"value": "={{\n$('Page Contents')\n .all()\n .filter(item => !item.json.error && item.json.body)\n .map(item => item.json.url)\n}}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "4aebae86-2bd2-4f3d-8290-d34b9ac837c6",
"name": "Accumulate Results",
"type": "n8n-nodes-base.set",
"position": [
-200,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "db509e90-9a86-431f-8149-4094d22666cc",
"name": "should_stop",
"type": "boolean",
"value": "={{\n$runIndex >= ($('JobType Router').first().json.data.depth)\n}}"
},
{
"id": "90986e2b-8aca-4a22-a9db-ed8809d6284d",
"name": "all_learnings",
"type": "array",
"value": "={{\nArray($runIndex+1)\n .fill(0)\n .flatMap((_,idx) => {\n try {\n return $('Generate Learnings')\n .all(0,idx)\n .flatMap(item => item.json.data.flatMap(d => d.learnings))\n } catch (e) {\n return []\n }\n })\n}}"
},
{
"id": "3eade958-e8ab-4975-aac4-f4a4a983c163",
"name": "all_urls",
"type": "array",
"value": "={{\nArray($runIndex+1)\n .fill(0)\n .flatMap((_,idx) => {\n try {\n return $('Generate Learnings')\n .all(0,idx)\n .flatMap(item => item.json.data.flatMap(d => d.urls))\n } catch (e) {\n return []\n }\n })\n}}"
}
]
}
},
"executeOnce": true,
"typeVersion": 3.4
},
{
"id": "782baa36-ba07-4845-873c-c9400de6d463",
"name": "DeepResearch Results",
"type": "n8n-nodes-base.set",
"position": [
160,
360
],
"parameters": {
"mode": "raw",
"options": {},
"jsonOutput": "={{ $('Generate Learnings').item.json }}"
},
"typeVersion": 3.4
},
{
"id": "89b09898-79ec-4924-975f-e9581d3bf774",
"name": "Results to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
320,
360
],
"parameters": {
"options": {},
"fieldToSplitOut": "data"
},
"typeVersion": 1
},
{
"id": "122cd071-aade-4753-ba0a-8db4c58fa84e",
"name": "Set Next Queries",
"type": "n8n-nodes-base.set",
"position": [
480,
360
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "d88bfe95-9e73-4d25-b45c-9f164b940b0e",
"name": "query",
"type": "string",
"value": "=Previous research goal: {{ $json.researchGoal }}\nFollow-up research directions: {{ $json.followUpQuestions.map(q => `\\n${q}`).join('') }}"
},
{
"id": "4aa20690-d998-458a-b1e4-0d72e6a68e6b",
"name": "learnings",
"type": "array",
"value": "={{ $('Accumulate Results').item.json.all_learnings }}"
},
{
"id": "89acafae-b04a-4d5d-b08b-656e715654e4",
"name": "breadth",
"type": "number",
"value": "={{ $('JobType Router').first().json.data.breadth }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "9da01d8a-48d6-45b4-b8c6-9a0503b4bda6",
"name": "Web Search",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"position": [
-80,
980
],
"parameters": {
"url": "https://api.apify.com/v2/acts/serping~fast-google-search-results-scraper/run-sync-get-dataset-items",
"method": "POST",
"options": {},
"sendBody": true,
"authentication": "genericCredentialType",
"bodyParameters": {
"parameters": [
{
"name": "searchTerms",
"value": "={{\n[\n `${$json.query} -filetype:pdf`\n]\n}}"
},
{
"name": "resultsPerPage",
"value": "={{ 10 }}"
}
]
},
"genericAuthType": "httpHeaderAuth"
},
"credentials": {
"httpQueryAuth": {
"id": "cO2w8RDNOZg8DRa8",
"name": "Apify API"
},
"httpHeaderAuth": {
"id": "SV9BDKc1cRbZBeoL",
"name": "Apify.com (personal token)"
}
},
"typeVersion": 4.2
},
{
"id": "99bd2c8e-5600-43a9-ab2f-7f2911efb16c",
"name": "Top 5 Organic Results",
"type": "n8n-nodes-base.set",
"position": [
80,
980
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "29d1a759-d886-4a44-860b-9d16f9922043",
"name": "results",
"type": "array",
"value": "={{\n$json.origin_search.results\n ? $json.origin_search\n .results\n .filter(res => res.type === 'normal')\n .slice(0, 5)\n .map(res => ({\n title: res.title,\n url: res.source.link\n }))\n : []\n}}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "cb7c5a8b-5420-4fb9-b7f0-4e8e8d10034a",
"name": "Convert to Markdown",
"type": "n8n-nodes-base.markdown",
"position": [
1320,
980
],
"parameters": {
"html": "={{ $json.body }}",
"options": {
"ignore": "a,img,picture,svg,video,audio,iframe"
},
"destinationKey": "markdown"
},
"typeVersion": 1
},
{
"id": "818ccf2e-081d-492e-ba8d-de458b0c26db",
"name": "For Each Query...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
-420,
820
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "1787b562-17e8-41af-9cdc-eb2d3e630916",
"name": "Feedback to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-720,
-460
],
"parameters": {
"options": {},
"fieldToSplitOut": "output.questions"
},
"typeVersion": 1
},
{
"id": "4c695faa-74e3-456b-a1ef-aaea67e46743",
"name": "Ask Clarity Questions",
"type": "n8n-nodes-base.form",
"position": [
-360,
-380
],
"webhookId": "ab0d9b81-73f6-4baa-a3cd-ac3b31397708",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"buttonLabel": "Answer",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>\n<p style=\"text-align:left\">\nAnswer the following clarification questions to assist the DeepResearcher better under the research topic.\n</p>\n<hr style=\"display:block;margin-top:16px;margin-bottom:0\" />\n<p style=\"text-align:left;font-family:sans-serif;font-weight:700;\">\nTotal {{ $('Feedback to Items').all().length }} questions.\n</p>"
},
"formFields": {
"values": [
{
"fieldType": "textarea",
"fieldLabel": "={{ $json[\"output.questions\"] }}",
"placeholder": "=",
"requiredField": true
}
]
}
},
"typeVersion": 1
},
{
"id": "e07d8c3e-8bcd-4393-9892-f825433ab58d",
"name": "For Each Question...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
-540,
-460
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "e8d26351-52f4-40a6-ba5b-fb6bc816b734",
"name": "DeepResearch Subworkflow",
"type": "n8n-nodes-base.executeWorkflowTrigger",
"position": [
-1880,
820
],
"parameters": {
"workflowInputs": {
"values": [
{
"name": "requestId",
"type": "any"
},
{
"name": "jobType"
},
{
"name": "data",
"type": "object"
}
]
}
},
"typeVersion": 1.1
},
{
"id": "25a8055a-27aa-414f-856b-25a2e2f31974",
"name": "Sticky Note",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
-680
],
"parameters": {
"color": 7,
"width": 1000,
"height": 560,
"content": "## 2. Ask Clarifying Questions\n[Read more about form nodes](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/)\n\nTo handle the clarification questions generated by the LLM, I used the same technique found in my \"AI Interviewer\" template ([link](https://n8n.io/workflows/2566-conversational-interviews-with-ai-agents-and-n8n-forms/)).\nThis involves a looping of dynamically generated forms to collect answers from the user."
},
"typeVersion": 1
},
{
"id": "68398b92-eb35-48bf-885e-540074531cc4",
"name": "Clarifying Questions",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-1040,
-460
],
"parameters": {
"text": "=Given the following query from the user, ask some follow up questions to clarify the research direction. Return a maximum of 3 questions, but feel free to return less if the original query is clear: <query>{{ $json.prompt }}</query>`",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "65c4c293-67b8-4e64-af04-16e45e97c09a",
"name": "Sticky Note1",
"type": "n8n-nodes-base.stickyNote",
"position": [
-660,
-60
],
"parameters": {
"color": 7,
"width": 1360,
"height": 640,
"content": "## 6. Perform DeepSearch Loop\n[Learn more about the Looping in n8n](https://docs.n8n.io/flow-logic/looping/#creating-loops)\n\nThe key of the Deep Research flow is its extensive data collection capability. In this implementation, this capability is represented by a recursive web search & scrape loop which starts with the original query and extended by AI-generated subqueries. How many subqueries to generate are determined the depth and breadth parameters specified.\n\n\"Learnings\" are generated for each subquery and accumulate on each iteration of the loop. When the loop finishes when depth limit is reached, all learnings are collected and it's these learnings are what we use to generate the report."
},
"typeVersion": 1
},
{
"id": "43a5d93d-cae2-43ec-b9ae-b15d6b11b932",
"name": "End Form",
"type": "n8n-nodes-base.form",
"position": [
960,
-420
],
"webhookId": "7b531f5d-942f-4c49-ac55-8ee480889600",
"parameters": {
"options": {},
"operation": "completion",
"completionTitle": "=Thank you for using DeepResearcher.",
"completionMessage": "=You may now close this window."
},
"typeVersion": 1
},
{
"id": "9a824011-e76f-433f-8735-44b358f4ff7d",
"name": "Initiate DeepResearch",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
600,
-420
],
"parameters": {
"mode": "each",
"options": {
"waitForSubWorkflow": false
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{\n{\n \"query\": $('Get Initial Query').first().json.query,\n \"learnings\": [],\n \"depth\": $('Set Variables').first().json.depth,\n \"breadth\": $('Set Variables').first().json.breadth,\n}\n}}",
"jobType": "deepresearch_initiate",
"requestId": "={{ $('Set Variables').first().json.request_id }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"removed": false,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"removed": false,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"removed": false,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "c48ee4cd-bac1-4405-bb4c-5614e5eb25a0",
"name": "Page Contents",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"position": [
560,
980
],
"parameters": {
"url": "https://api.apify.com/v2/acts/apify~web-scraper/run-sync-get-dataset-items",
"options": {},
"jsonBody": "={\n \"startUrls\": {{ [{ url: $json.url, method: 'GET' }].toJsonString() }},\n \"breakpointLocation\": \"NONE\",\n \"browserLog\": false,\n \"closeCookieModals\": false,\n \"debugLog\": false,\n \"downloadCss\": false,\n \"downloadMedia\": false,\n \"excludes\": [\n {\n \"glob\": \"/**/*.{png,jpg,jpeg,pdf}\"\n }\n ],\n \"headless\": true,\n \"ignoreCorsAndCsp\": false,\n \"ignoreSslErrors\": false,\n \"injectJQuery\": true,\n \"keepUrlFragments\": false,\n \"linkSelector\": \"\",\n \"maxCrawlingDepth\": 1,\n \"maxPagesPerCrawl\": 1,\n \"maxRequestRetries\": 1,\n \"maxResultsPerCrawl\": 1,\n \"pageFunction\": \"// The function accepts a single argument: the \\\"context\\\" object.\\n// For a complete list of its properties and functions,\\n// see https://apify.com/apify/web-scraper#page-function \\nasync function pageFunction(context) {\\n\\n await new Promise(res => { setTimeout(res, 6000) });\\n // This statement works as a breakpoint when you're trying to debug your code. Works only with Run mode: DEVELOPMENT!\\n // debugger; \\n\\n // jQuery is handy for finding DOM elements and extracting data from them.\\n // To use it, make sure to enable the \\\"Inject jQuery\\\" option.\\n const $ = context.jQuery;\\n const title = $('title').first().text();\\n\\n // Clone the body to avoid modifying the original content\\n const bodyClone = $('body').clone();\\n bodyClone.find('iframe, img, script, style, object, embed, noscript, svg, video, audio').remove();\\n const body = bodyClone.html();\\n\\n // Return an object with the data extracted from the page.\\n // It will be stored to the resulting dataset.\\n return {\\n url: context.request.url,\\n title,\\n body\\n };\\n}\",\n \"postNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept a single argument: the \\\"crawlingContext\\\" object.\\n[\\n async (crawlingContext) => {\\n // ...\\n },\\n]\",\n \"preNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept two arguments: the \\\"crawlingContext\\\" object\\n// and \\\"gotoOptions\\\".\\n[\\n async (crawlingContext, gotoOptions) => {\\n // ...\\n },\\n]\\n\",\n \"proxyConfiguration\": {\n \"useApifyProxy\": true\n },\n \"runMode\": \"PRODUCTION\",\n \"useChrome\": false,\n \"waitUntil\": [\n \"domcontentloaded\"\n ],\n \"globs\": [],\n \"pseudoUrls\": [],\n \"proxyRotation\": \"RECOMMENDED\",\n \"maxConcurrency\": 50,\n \"pageLoadTimeoutSecs\": 60,\n \"pageFunctionTimeoutSecs\": 60,\n \"maxScrollHeightPixels\": 5000,\n \"customData\": {}\n}",
"sendBody": true,
"sendQuery": true,
"specifyBody": "json",
"authentication": "genericCredentialType",
"genericAuthType": "httpQueryAuth",
"queryParameters": {
"parameters": [
{
"name": "memory",
"value": "2048"
},
{
"name": "timeout",
"value": "90"
}
]
}
},
"credentials": {
"httpQueryAuth": {
"id": "cO2w8RDNOZg8DRa8",
"name": "Apify API"
}
},
"typeVersion": 4.2
},
{
"id": "dc9f85ff-7565-4c29-981a-5ef65bba6ca3",
"name": "Execution Data",
"type": "n8n-nodes-base.executionData",
"position": [
-1700,
820
],
"parameters": {
"dataToSave": {
"values": [
{
"key": "requestId",
"value": "={{ $json.requestId }}"
},
{
"key": "=jobType",
"value": "={{ $json.jobType }}"
}
]
}
},
"typeVersion": 1
},
{
"id": "26b33429-6d61-4758-9c76-3e998dd31fa4",
"name": "JobType Router",
"type": "n8n-nodes-base.switch",
"position": [
-1520,
820
],
"parameters": {
"rules": {
"values": [
{
"outputKey": "initiate",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"operator": {
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_initiate"
}
]
},
"renameOutput": true
},
{
"outputKey": "learnings",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "ecbfa54d-fc97-48c5-8d3d-f0538b8d727b",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_learnings"
}
]
},
"renameOutput": true
},
{
"outputKey": "report",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "392f9a98-ec22-4e57-9c8e-0e1ed6b7dafa",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_report"
}
]
},
"renameOutput": true
}
]
},
"options": {}
},
"typeVersion": 3.2
},
{
"id": "a9637952-7c09-40ae-96ec-bdf0fc63d94e",
"name": "Valid Pages",
"type": "n8n-nodes-base.filter",
"position": [
720,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "f44691e4-f753-47b0-b66a-068a723b6beb",
"operator": {
"type": "boolean",
"operation": "false",
"singleValue": true
},
"leftValue": "={{ $json['#error'] }}",
"rightValue": ""
},
{
"id": "8e05df2b-0d4a-47da-9aab-da7e8907cbca",
"operator": {
"type": "string",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json.body }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2,
"alwaysOutputData": true
},
{
"id": "204cfca2-05bb-46dd-ba96-b41866ed2cfe",
"name": "OpenAI Chat Model4",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-20,
-280
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "45bc6261-35c8-4994-bb88-ed7a0f022767",
"name": "Get Initial Query",
"type": "n8n-nodes-base.set",
"position": [
-360,
-540
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "14b77741-c3c3-4bd2-be6e-37bd09fcea2b",
"name": "query",
"type": "string",
"value": "=Initial query: {{ $('Set Variables').first().json.prompt }}\nFollow-up Questions and Answers:\n{{\n$input.all()\n .map(item => {\n const q = Object.keys(item.json)[0];\n const a = item.json[q];\n return `question: ${q}\\nanswer: ${a}`;\n })\n .join('\\n')\n}}"
}
]
}
},
"executeOnce": true,
"typeVersion": 3.4
},
{
"id": "26d26e54-ee9b-4714-ae27-4f033dc825d3",
"name": "Structured Output Parser4",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
160,
-280
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"title\": {\n \"type\": \"string\",\n \"description\":\" A short title summarising the research topic\"\n },\n \"description\": {\n \"type\": \"string\",\n \"description\": \"A short description to summarise the research topic\"\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "3842bc1d-d5f9-4879-bc06-db20fed3f55d",
"name": "Create Row",
"type": "n8n-nodes-base.notion",
"position": [
300,
-420
],
"parameters": {
"title": "={{ $json.output.title }}",
"options": {},
"resource": "databasePage",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"propertiesUi": {
"propertyValues": [
{
"key": "Description|rich_text",
"textContent": "={{ $json.output.description }}"
},
{
"key": "Status|status",
"statusValue": "Not started"
},
{
"key": "Request ID|rich_text",
"textContent": "={{ $('Set Variables').first().json.request_id }}"
},
{
"key": "Name|title",
"title": "={{ $json.output.title }}"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "bfe98996-6ed5-4f60-afdd-a947a6fa6e36",
"name": "Report Page Generator",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-20,
-420
],
"parameters": {
"text": "=Create a suitable title for the research report which will be created from the user's query.\n<query>{{ $json.query }}</query>",
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "ff05add8-94b0-4495-8f4e-3e8a10c556af",
"name": "Sticky Note2",
"type": "n8n-nodes-base.stickyNote",
"position": [
-120,
-680
],
"parameters": {
"color": 7,
"width": 600,
"height": 560,
"content": "## 3. Create Empty Report Page in Notion\n[Read more about the Notion node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion/)\n\nSome thought was given where to upload the final report and Notion was selected due to familiarity. This can be easily changed to whatever wiki tools you prefer.\n\nIf you're following along however, here's the Notion database you need to replicate - [Jim's n8n DeepResearcher Database](https://jimleuk.notion.site/19486dd60c0c80da9cb7eb1468ea9afd?v=19486dd60c0c805c8e0c000ce8c87acf)."
},
"typeVersion": 1
},
{
"id": "5bc13d62-81e1-4730-b7e6-9e5579dff174",
"name": "Sticky Note3",
"type": "n8n-nodes-base.stickyNote",
"position": [
500,
-680
],
"parameters": {
"color": 7,
"width": 640,
"height": 560,
"content": "## 4. Trigger DeepResearch Asynchronously\n[Learn more about the Execute Trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow/)\n\nn8n handles asynchronous jobs by spinning them off as separate executions. This basically means the user doesn't have to wait or keep their browser window open for our researcher to do its job.\n\nOnce we initiate the Deepresearcher job, we can close out the onboarding journey for a nice user experience."
},
"typeVersion": 1
},
{
"id": "9fea6403-b2a2-4e67-99a2-b7a2f29a1e96",
"name": "Sticky Note4",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1160,
620
],
"parameters": {
"color": 7,
"width": 620,
"height": 540,
"content": "## 7. Generate Search Queries\n[Learn more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nMuch like a human researcher, the DeepResearcher will rely on web search and content as the preferred source of information. To ensure it can cover a wide range of sources, the AI can first generate relevant research queries of which each can be explored separately."
},
"typeVersion": 1
},
{
"id": "0bccdc54-7570-4bca-93ec-cb140c5bd3a1",
"name": "Is Depth Reached?",
"type": "n8n-nodes-base.if",
"position": [
-40,
180
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "75d18d88-6ba6-43df-bef7-3e8ad99ad8bd",
"operator": {
"type": "boolean",
"operation": "true",
"singleValue": true
},
"leftValue": "={{ $json.should_stop }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "819aa5be-b71b-44a7-b062-b2a50209f290",
"name": "URLs to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
400,
980
],
"parameters": {
"options": {},
"fieldToSplitOut": "results"
},
"typeVersion": 1
},
{
"id": "4b8e9936-4b24-4bd4-8fe7-75d58244cb6d",
"name": "Get Research Results",
"type": "n8n-nodes-base.set",
"position": [
160,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "90b3da00-dcd5-4289-bd45-953146a3b0ba",
"name": "all_learnings",
"type": "array",
"value": "={{ $json.all_learnings }}"
},
{
"id": "623dbb3d-83a1-44a9-8ad3-48d92bc42811",
"name": "all_urls",
"type": "array",
"value": "={{ $json.all_urls }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "d371535a-2946-4ec5-9be6-2ee8e359ac44",
"name": "Get Existing Row",
"type": "n8n-nodes-base.notion",
"position": [
-1040,
180
],
"parameters": {
"limit": 1,
"filters": {
"conditions": [
{
"key": "Request ID|rich_text",
"condition": "equals",
"richTextValue": "={{ $json.requestId.toString() }}"
}
]
},
"options": {},
"resource": "databasePage",
"matchType": "allFilters",
"operation": "getAll",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"filterType": "manual"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "fea4c30e-1193-494d-8823-dfbec5196a0d",
"name": "Set In-Progress",
"type": "n8n-nodes-base.notion",
"position": [
-840,
180
],
"parameters": {
"pageId": {
"__rl": true,
"mode": "id",
"value": "={{ $json.id }}"
},
"options": {},
"resource": "databasePage",
"operation": "update",
"propertiesUi": {
"propertyValues": [
{
"key": "Status|status",
"statusValue": "In progress"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "37954acd-d8cb-4c74-afa8-d8973e017327",
"name": "Set Done",
"type": "n8n-nodes-base.notion",
"position": [
1680,
1600
],
"parameters": {
"pageId": {
"__rl": true,
"mode": "id",
"value": "={{ $('Get Existing Row1').first().json.id }}"
},
"options": {},
"resource": "databasePage",
"operation": "update",
"propertiesUi": {
"propertyValues": [
{
"key": "Status|status",
"statusValue": "Done"
},
{
"key": "Last Updated|date",
"date": "={{ $now.toISO() }}"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"executeOnce": true,
"typeVersion": 2.2
},
{
"id": "3db97ab8-b934-4567-a92e-92374a363df6",
"name": "Tags to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-60,
1600
],
"parameters": {
"options": {},
"fieldToSplitOut": "tag"
},
"typeVersion": 1
},
{
"id": "7f468bf7-762a-4818-86f9-54d172bb618a",
"name": "Convert to HTML",
"type": "n8n-nodes-base.markdown",
"position": [
-380,
1600
],
"parameters": {
"mode": "markdownToHtml",
"options": {
"tables": true
},
"markdown": "={{ $json.text }}"
},
"typeVersion": 1
},
{
"id": "97914ee9-0ee8-408b-b2bb-a7193b2d0454",
"name": "HTML to Array",
"type": "n8n-nodes-base.set",
"position": [
-220,
1600
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "851b8a3f-c2d3-41ad-bf60-4e0e667f6c58",
"name": "tag",
"type": "array",
"value": "={{ $json.data.match(/<table[\\s\\S]*?<\\/table>|<ul[\\s\\S]*?<\\/ul>|<[^>]+>[^<]*<\\/[^>]+>/g) }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "6ce79f16-51e3-4192-8103-738222be558b",
"name": "Notion Block Generator",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
100,
1600
],
"parameters": {
"text": "={{ $json.tag.trim() }}",
"messages": {
"messageValues": [
{
"message": "=Convert the following html into its equivalent Notion Block as per Notion's API schema.\n* Ensure the content is always included and remains the same.\n* Return only a json response.\n* Generate child-level blocks. Should not define \"parent\" or \"children\" property.\n* Strongly prefer headings, paragraphs, tables and lists type blocks.\n* available headings are heading_1, heading_2 and heading_3 - h4,h5,h6 should use heading_3 type instead. ensure headings use the rich text definition.\n* ensure lists blocks include all list items.\n\n## Examples\n\n1. headings\n```\n<h3 id=\"references\">References</h3>\n```\nwould convert to \n```\n{\"object\": \"block\", \"type\": \"heading_3\", \"heading_3\": { \"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"References\"}}]}}\n```\n\n2. lists\n```\n<ul><li>hello</li><li>world</li></ul>\n```\nwould convert to\n```\n[\n{\n \"object\": \"block\",\n \"type\": \"bulleted_list_item\",\n \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"hello\"}}]}\n},\n{\n \"object\": \"block\",\n \"type\": \"bulleted_list_item\",\n \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"world\"}}]}\n}\n]\n```\n\n3. tables\n```\n<table>\n <thead>\n <tr><th>Technology</th><th>Potential Impact</th></tr>\n </thead>\n <tbody>\n <tr>\n <td>5G Connectivity</td><td>Enables faster data speeds and advanced apps</td>\n </tr>\n </tbody>\n</table>\n```\nwould convert to\n```\n{\n \"object\": \"block\",\n \"type\": \"table\",\n \"table\": {\n \"table_width\": 2,\n \"has_column_header\": true,\n \"has_row_header\": false,\n \"children\": [\n {\n \"object\": \"block\",\n \"type\": \"table_row\",\n \"table_row\": {\n \"cells\": [\n [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Technology\",\n \"link\": null\n }\n },\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Potential Impact\",\n \"link\": null\n }\n }\n ],\n [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"5G Connectivity\",\n \"link\": null\n }\n },\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Enables faster data speeds and advanced apps\",\n \"link\": null\n }\n }\n ]\n ]\n }\n }\n ]\n }\n}\n```\n4. anchor links\nSince Notion doesn't support anchor links, just convert them to rich text blocks instead.\n```\n<a href=\"#module-0-pre-course-setup-and-learning-principles\">Module 0: Pre-Course Setup and Learning Principles</a>\n```\nconverts to\n```\n{\n \"object\": \"block\",\n \"type\": \"paragraph\",\n \"paragraph\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Module 0: Pre-Course Setup and Learning Principles\"\n }\n }\n ]\n }\n}\n```\n5. Invalid html parts\nWhen the html is not syntax valid eg. orphaned closing tags, then just skip the conversion and use an empty rich text block.\n```\n</li>\\n</ol>\n```\ncan be substituted with\n```\n{\n \"object\": \"block\",\n \"type\": \"paragraph\",\n \"paragraph\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \" \"\n }\n }\n ]\n }\n}\n```"
}
]
},
"promptType": "define"
},
"typeVersion": 1.5
},
{
"id": "e3eeb9f0-7407-41f9-a814-def6c26b2ee1",
"name": "Google Gemini Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini",
"position": [
80,
1760
],
"parameters": {
"options": {},
"modelName": "models/gemini-2.0-flash"
},
"credentials": {
"googlePalmApi": {
"id": "dSxo6ns5wn658r8N",
"name": "Google Gemini(PaLM) Api account"
}
},
"typeVersion": 1
},
{
"id": "5b0aeaca-dce5-4afd-8a8a-0ef2c18b6f06",
"name": "Parse JSON blocks",
"type": "n8n-nodes-base.set",
"onError": "continueRegularOutput",
"position": [
420,
1600
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "73fcb8a0-2672-4bd5-86de-8075e1e02baf",
"name": "=block",
"type": "array",
"value": "={{\n(function(){\n const block = $json.text\n .replace('```json', '')\n .replace('```', '')\n .trim()\n .parseJson();\n if (Array.isArray(block)) return block;\n if (block.type.startsWith('heading_')) {\n const prev = Number(block.type.split('_')[1]);\n const next = Math.max(1, prev - 1);\n if (next !== prev) {\n block.type = `heading_${next}`;\n block[`heading_${next}`] = Object.assign({}, block[`heading_${prev}`]);\n block[`heading_${prev}`] = undefined;\n }\n }\n return [block];\n})()\n}}"
}
]
}
},
"executeOnce": false,
"typeVersion": 3.4
},
{
"id": "e2a5a5bc-a3c8-42c5-9419-74ce3525f599",
"name": "Upload to Notion Page",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"maxTries": 2,
"position": [
1680,
1760
],
"parameters": {
"url": "=https://api.notion.com/v1/blocks/{{ $('Get Existing Row1').first().json.id }}/children",
"method": "PATCH",
"options": {
"timeout": "={{ 1000 * 60 }}"
},
"jsonBody": "={{\n{\n \"children\": $json.block\n}\n}}",
"sendBody": true,
"sendHeaders": true,
"specifyBody": "json",
"authentication": "predefinedCredentialType",
"headerParameters": {
"parameters": [
{
"name": "Notion-Version",
"value": "2022-06-28"
}
]
},
"nodeCredentialType": "notionApi"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"retryOnFail": true,
"typeVersion": 4.2,
"waitBetweenTries": 3000
},
{
"id": "9f18b2a5-ba74-40fc-8e35-a93ecd13507a",
"name": "Sticky Note5",
"type": "n8n-nodes-base.stickyNote",
"position": [
-520,
620
],
"parameters": {
"color": 7,
"width": 1740,
"height": 740,
"content": "## 8. Web Search and Extracting Web Page Contents using [APIFY.com](https://www.apify.com?fpr=414q6)\n[Read more about the HTTP Request node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nHere is where I deviated a little from the reference implementation. I opted not to use Firecrawl.ai due to (1) high cost of the service and (2) a regular non-ai crawler would work just as well and probably quicker.\nInstead I'm using [APIFY.com](https://www.apify.com?fpr=414q6) which is a more performant, cost-effective and reliable web scraper service. If you don't want to use Apify, feel free to swap this out with your preferred service.\n\nThis step is the most exciting in terms of improvements and optimisations eg. mix in internal data sources! Add in Perplexity.ai or Jina.ai! Possibilities are endless."
},
"typeVersion": 1
},
{
"id": "84c34a2a-d8bb-4e62-a5ea-df0a142aa2b4",
"name": "Sticky Note6",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
60
],
"parameters": {
"color": 7,
"width": 460,
"height": 360,
"content": "## 5. Set Report to In-Progress\n[Read more about the Notion node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion/)"
},
"typeVersion": 1
},
{
"id": "bd022636-873d-4aca-8929-c189f8596cc1",
"name": "Sticky Note7",
"type": "n8n-nodes-base.stickyNote",
"position": [
1240,
700
],
"parameters": {
"color": 7,
"width": 780,
"height": 660,
"content": "## 9. Compile Learnings with Reasoning Model\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nWith our gathered sources, it's now just a case of giving it to our LLM to compile a list of \"learnings\" from them. For our DeepResearcher, we'll use OpenAI's o3-mini which is the latest reasoning model at time of writing. Reasoning perform better than regular chat models due their chain-of-thought or \"thinking\" process that they perform.\n\nThe \"Learnings\" are then combined with the generated research goal to complete one loop."
},
"typeVersion": 1
},
{
"id": "b2f6e51d-cbe6-4459-9515-679f79063926",
"name": "Get Existing Row1",
"type": "n8n-nodes-base.notion",
"position": [
-1020,
1600
],
"parameters": {
"limit": 1,
"filters": {
"conditions": [
{
"key": "Request ID|rich_text",
"condition": "equals",
"richTextValue": "={{ $json.requestId.toString() }}"
}
]
},
"options": {},
"resource": "databasePage",
"matchType": "allFilters",
"operation": "getAll",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"filterType": "manual"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "7b315060-7e40-410c-ac9d-ef22acbb175a",
"name": "Sticky Note8",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
1400
],
"parameters": {
"color": 7,
"width": 660,
"height": 540,
"content": "## 10. Generate DeepSearch Report using Learnings\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nFinally! After all learnings have been gathered - which may have taken up to an hour or more on the higher settings! - they are given to our LLM to generate the final research report in markdown format. Technically, the DeepResearch ends here but for this template, we need to push the output to Notion. If you're not using Notion, feel free to ignore the last few steps."
},
"typeVersion": 1
},
{
"id": "01aaf2a8-c145-4f39-aa52-f40fc28f8767",
"name": "Sticky Note9",
"type": "n8n-nodes-base.stickyNote",
"position": [
-460,
1400
],
"parameters": {
"color": 7,
"width": 1060,
"height": 540,
"content": "## 11. Reformat Report as Notion Blocks\n[Learn more about the Markdown node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.markdown/)\n\nTo write our report to our Notion page, we'll have to convert it to Notion \"blocks\" - these are specialised json objects which are required by the Notion API. There are quite a number of ways to do this conversion not involving the use of AI but for kicks, I decided to do so anyway. In this step, we first convert to HTML as it allows us to split the report semantically and makes for easier parsing for the LLM."
},
"typeVersion": 1
},
{
"id": "700a6f44-86bf-4aab-8a42-23bf6843f681",
"name": "Sticky Note10",
"type": "n8n-nodes-base.stickyNote",
"position": [
1220,
1400
],
"parameters": {
"color": 7,
"width": 800,
"height": 580,
"content": "## 13. Update Report in Notion\n[Read more about the HTTP request node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nIn this step, we can use the Notion API to add the blocks to our page sequentially. A loop is used due to the unstable Notion API - the loop allows retries for blocks that require it."
},
"typeVersion": 1
},
{
"id": "f8536052-c851-42ec-aaf3-5fc876570f6d",
"name": "Sticky Note11",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1840,
-680
],
"parameters": {
"color": 7,
"width": 680,
"height": 560,
"content": "## 1. Let's Research!\n[Learn more about the form trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger)\n\nn8n forms are a really nice way to get our frontend up and running quickly and compared to chat, offers a superior user interface for user input. I've gone perhaps a little extra with the custom html fields but I do enjoy adding a little customisation now and then."
},
"typeVersion": 1
},
{
"id": "58aa92a2-e1fd-497d-a27e-40b733189bab",
"name": "DeepResearch Report",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-860,
1600
],
"parameters": {
"text": "=You are are an expert and insightful researcher.\n* Given the following prompt from the user, write a final report on the topic using the learnings from research.\n* Make it as as detailed as possible, aim for 3 or more pages, include ALL the learnings from research.\n* Format the report in markdown. Use headings, lists and tables only and where appropriate.\n\n<prompt>{{ $('JobType Router').first().json.data.query }}</prompt>\n\nHere are all the learnings from previous research:\n\n<learnings>\n{{\n$('JobType Router').first().json.data\n .all_learnings\n .map(item => `<learning>${item}</learning>`) \n .join('\\n')\n}}\n</learnings>",
"promptType": "define"
},
"typeVersion": 1.5
},
{
"id": "0656be83-d510-46f1-aeeb-f62a69aa3cf2",
"name": "DeepResearch Learnings",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
1500,
980
],
"parameters": {
"text": "=Given the following contents from a SERP search for the query <query>{{ $('Item Ref').first().json.query }}</query>, generate a list of learnings from the contents. Return a maximum of 3 learnings, but feel free to return less if the contents are clear. Make sure each learning is unique and not similar to each other. The learnings should be concise and to the point, as detailed and infromation dense as possible. Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any exact metrics, numbers, or dates. The learnings will be used to research the topic further.\n\n<contents>\n{{\n$('Convert to Markdown')\n .all()\n .map(item =>`<content>\\n${item.json.markdown.substr(0, 25_000)}\\n</content>`)\n .join('\\n')\n}}\n</contents>",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"executeOnce": true,
"typeVersion": 1.5
},
{
"id": "9296a787-3226-44fe-8118-f84dda8e5167",
"name": "Generate Report",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
480,
180
],
"parameters": {
"options": {
"waitForSubWorkflow": false
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{\n{\n ...Object.assign({}, $json),\n query: $('JobType Router').first().json.data.query\n}\n}}",
"jobType": "deepresearch_report",
"requestId": "={{ $('JobType Router').first().json.requestId }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "471f9b9f-f331-4652-95de-1ec7136ea692",
"name": "Generate Learnings",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
-380,
180
],
"parameters": {
"mode": "each",
"options": {
"waitForSubWorkflow": true
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{ $json }}",
"jobType": "deepresearch_learnings",
"requestId": "={{ $('JobType Router').first().json.requestId }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"removed": false,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"removed": false,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"removed": false,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "2e2fa1e6-9d5c-46ff-985c-58ada1139837",
"name": "Confirmation",
"type": "n8n-nodes-base.form",
"position": [
780,
-420
],
"webhookId": "cf41a176-5d30-4274-955e-b0d5b483d37f",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"buttonLabel": "Done",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>\n<p style=\"text-align:left\">\n<strong style=\"display:block;font-family:sans-serif;font-weight:700;font-size:16px;margin-top:12px;margin-bottom:0;\">Your Report Is On Its Way!</strong>\n<br/>\nDeepResearcher will now work independently to conduct the research and the compiled report will be uploaded to the following Notion page below when finished.\n<br/><br/>\nPlease click the \"Done\" button to complete the form.\n</p>\n<hr style=\"display:block;margin-top:16px;margin-bottom:0\" />"
},
"formFields": {
"values": [
{
"html": "=<a href=\"{{ $json.url }}\" style=\"text-decoration:none\" target=\"_blank\">\n<div style=\"display:flex;text-align:left;font-family:sans-serif;\">\n <div style=\"width:150px;height:150px;padding:12px;\">\n <img src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/cajjymprexcoesu4gg9g\" width=\"100%\" />\n </div>\n <div style=\"width:100%;padding:12px;\">\n <div style=\"font-size:14px;font-weight:700\">{{ $json.name }}</div>\n <div style=\"font-size:12px;color:#666\">\n {{ $json.property_description }}\n </div>\n </div>\n</div>\n</a>",
"fieldType": "html",
"fieldLabel": "message"
}
]
}
},
"typeVersion": 1
},
{
"id": "97ba63b7-9248-4945-9c67-1ea114b20dc5",
"name": "Research Request",
"type": "n8n-nodes-base.form",
"position": [
-1560,
-460
],
"webhookId": "d4ea875f-83cb-49a8-8992-c08b4114c9bd",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>"
},
"formFields": {
"values": [
{
"fieldType": "textarea",
"fieldLabel": "What would you like to research?",
"requiredField": true
},
{
"html": "<div class=\"form-group\" style=\"margin-bottom:16px;\">\n <label class=\"form-label\" for=\"field-1\">\n Enter research depth (Default 1)\n </label>\n <p style=\"font-size:12px;color:#666;text-align:left\">\n This value determines how many sub-queries to generate.\n </p>\n <input\n class=\"form-input\"\n type=\"range\"\n id=\"field-1\"\n name=\"field-1\"\n value=\"1\"\n step=\"1\"\n max=\"3\"\n min=\"0\"\n list=\"depth-markers\"\n >\n <datalist\n id=\"depth-markers\"\n style=\"display: flex;\n flex-direction: row;\n justify-content: space-between;\n writing-mode: horizontal-tb;\n margin-top: -10px;\n text-align: center;\n font-size: 10px;\n margin-left: 16px;\n margin-right: 16px;\"\n >\n <option style=\"padding:0\" value=\"0\" label=\"0\"></option>\n <option style=\"padding:0\" value=\"1\" label=\"1\"></option>\n <option style=\"padding:0\" value=\"2\" label=\"2\"></option>\n <option style=\"padding:0\" value=\"3\" label=\"3\"></option>\n </datalist>\n</div>",
"fieldType": "html",
"fieldLabel": "Enter research depth (recommended 1-5, default 2)"
},
{
"html": "<div class=\"form-group\" style=\"margin-bottom:16px;\">\n <label class=\"form-label\" for=\"field-2\">\n Enter research breadth (Default 2)\n </label>\n <p style=\"font-size:12px;color:#666;text-align:left\">\n This value determines how many sources to explore.\n </p>\n <input\n class=\"form-input\"\n type=\"range\"\n id=\"field-2\"\n name=\"field-2\"\n value=\"2\"\n step=\"1\"\n max=\"5\"\n min=\"1\"\n list=\"breadth-markers\"\n >\n <datalist\n id=\"breadth-markers\"\n style=\"display: flex;\n flex-direction: row;\n justify-content: space-between;\n writing-mode: horizontal-tb;\n margin-top: -10px;\n text-align: center;\n font-size: 10px;\n margin-left: 16px;\n margin-right: 16px;\"\n >\n <option value=\"1\" label=\"1\"></option>\n <option value=\"2\" label=\"2\"></option>\n <option value=\"3\" label=\"3\"></option>\n <option value=\"4\" label=\"4\"></option>\n <option value=\"5\" label=\"5\"></option>\n </datalist>\n</div>\n\n",
"fieldType": "html",
"fieldLabel": "Enter research breadth (recommended 2-10, default 4)"
},
{
"fieldType": "dropdown",
"fieldLabel": "={{ \"\" }}",
"multiselect": true,
"fieldOptions": {
"values": [
{
"option": "=I understand higher depth and breath values I've selected may incur longer wait times and higher costs. I acknowledging this and wish to proceed with the research request."
}
]
},
"requiredField": true
}
]
}
},
"typeVersion": 1
},
{
"id": "b3d11997-9c8c-4b72-b750-4fc22a2247b7",
"name": "Valid Blocks",
"type": "n8n-nodes-base.filter",
"position": [
740,
1600
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "f68cefe0-e109-4d41-9aa3-043f3bc6c449",
"operator": {
"type": "string",
"operation": "notExists",
"singleValue": true
},
"leftValue": "={{ $json.error }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "ff90a1c1-b357-4012-8964-e007bef0c9db",
"name": "Sticky Note12",
"type": "n8n-nodes-base.stickyNote",
"position": [
620,
1400
],
"parameters": {
"color": 7,
"width": 580,
"height": 580,
"content": "## 12. Append URL Sources List\n[Read more about the Code node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code)\n\nFor our source URLs, we'll manually compose the Notion blocks for them - this is because there's usually a lot of them! We'll then append to the end of the other blocks."
},
"typeVersion": 1
},
{
"id": "f6b50f06-6122-494c-bdb8-4215f473a27d",
"name": "Append Blocks",
"type": "n8n-nodes-base.merge",
"position": [
1000,
1760
],
"parameters": {},
"typeVersion": 3
},
{
"id": "ca39272a-828e-4314-80da-05dd5fd7b2e3",
"name": "URL Sources to Lists",
"type": "n8n-nodes-base.code",
"position": [
740,
1760
],
"parameters": {
"jsCode": "const urls = $('JobType Router').first().json.data.all_urls;\nconst chunksize = 50;\nconst splits = Math.max(1, Math.floor(urls.length/chunksize));\n\nconst blocks = Array(splits).fill(0)\n .map((_, idx) => {\n const block = urls\n .slice(\n idx * chunksize, \n (idx * chunksize) + chunksize - 1\n )\n .map(url => {\n return {\n object: \"block\",\n type: \"bulleted_list_item\",\n bulleted_list_item: {\n rich_text: [\n { type: \"text\", text: { content: url } }\n ]\n }\n }\n });\n return { json: { block } }\n });\n\nreturn [\n { json: {\n block:[{\n \"object\": \"block\",\n \"type\": \"heading_2\",\n \"heading_2\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Sources\"\n }\n }\n ]\n }\n }]\n } },\n ...blocks\n];"
},
"typeVersion": 2
},
{
"id": "e2e2b07a-4039-4859-b60c-f51982475282",
"name": "Has Results?",
"type": "n8n-nodes-base.if",
"position": [
240,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "9ef8d40c-1289-4654-9022-4a07f7102555",
"operator": {
"type": "array",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json.results }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "5662dbf2-8877-4e83-982c-6bc5968b8835",
"name": "Empty Response",
"type": "n8n-nodes-base.set",
"position": [
1040,
1120
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "1de40158-338b-4db3-9e22-6fd63b21f825",
"name": "ResearchGoal",
"type": "string",
"value": "={{ $('Item Ref').first().json.researchGoal }}"
},
{
"id": "9f59a2d4-5e5a-4d0b-8adf-2832ce746f0f",
"name": "learnings",
"type": "array",
"value": "={{ [] }}"
},
{
"id": "972ab5f5-0537-4755-afcb-d1db4f09ad60",
"name": "followUpQuestions",
"type": "array",
"value": "={{ [] }}"
},
{
"id": "90cef471-76b0-465d-91a4-a0e256335cd3",
"name": "urls",
"type": "array",
"value": "={{ [] }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "678b6af5-da74-4421-8c3d-0166aa52efd9",
"name": "Has Content?",
"type": "n8n-nodes-base.if",
"position": [
880,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "1ef1039a-4792-47f9-860b-d2ffcffd7129",
"operator": {
"type": "object",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "233f2e19-b4f2-4de3-8002-f79f3c01c1e7",
"name": "Sticky Note13",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1820,
-240
],
"parameters": {
"color": 5,
"width": 300,
"height": 100,
"content": "### Not using forms?\nFeel free ot swap this out for chat or even webhooks to fit your existing workflows."
},
"typeVersion": 1
},
{
"id": "453bb6eb-f2b0-4e21-b647-e095c80b7844",
"name": "Sticky Note14",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1880,
540
],
"parameters": {
"color": 5,
"width": 460,
"height": 240,
"content": "### 🚏 The Subworkflow Event Pattern \nIf you're new to n8n, this advanced technique might need some explaining but in gist, we're using subworkflows to run different parts of our DeepResearcher workflow as separate executions.\n\n* Necessary to implement the recursive loop mechanism needed to enable this workflow.\n* Negates the need to split this workflow into multiple templates.\n* Great generally for building high performance n8n workflows (a topic for a future post!)"
},
"typeVersion": 1
},
{
"id": "289cbe4c-c2e3-46b7-8799-197a7d78ab2a",
"name": "Sticky Note15",
"type": "n8n-nodes-base.stickyNote",
"position": [
720,
-60
],
"parameters": {
"color": 5,
"width": 340,
"height": 200,
"content": "### Recursive Looping\nThe recursive looping implemented for this workflow is an advanced item-linking technique. It works by specifically controlling which nodes \"execute once\" vs\" execute for each item\" because of this becareful of ermoving nodes! Always check the settings of the node you're replacing and ensure the settings match. "
},
"typeVersion": 1
},
{
"id": "b95ffdcd-c0d1-4a12-a7a9-24135db7b467",
"name": "Combine & Send back to Loop",
"type": "n8n-nodes-base.aggregate",
"position": [
-240,
820
],
"parameters": {
"options": {},
"aggregate": "aggregateAllItemData"
},
"typeVersion": 1
},
{
"id": "f0a48ab5-70b9-49dc-a153-61e573803d1e",
"name": "For Each Block...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
1440,
1600
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "2fd17fbd-005d-446e-b014-0da190cd3114",
"name": "Sticky Note16",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2420,
-920
],
"parameters": {
"width": 520,
"height": 1060,
"content": "## n8n DeepResearcher\n### This template attempts to replicate OpenAI's DeepResearch feature which, at time of writing, is only available to their pro subscribers.\n\nThough the inner workings of DeepResearch have not been made public, it is presumed the feature relies on the ability to deep search the web, scrape web content and invoking reasoning models to generate reports. All of which n8n is really good at!\n\n### How it works\n* A form is used to first capture the user's research query and how deep they'd like the researcher to go.\n* Once submitted, a blank Notion page is created which will later hold the final report and the researcher gets to work.\n* The user's query goes through a recursive series of web serches and web scraping to collect data on the research topic to generate partial learnings.\n* Once complete, all learnings are combined and given to a reasoning LLM to generate the final report.\n* The report is then written to the placeholder Notion page created earlier. \n\n### How to use\n* Duplicate this Notion database to use with this template: https://jimleuk.notion.site/19486dd60c0c80da9cb7eb1468ea9afd?v=19486dd60c0c805c8e0c000ce8c87acf\n* Sign-up for [APIFY.com](https://www.apify.com?fpr=414q6) API Key for web search and scraping services.\n* Ensure you have access to OpenAI's o3-mini model. Alternatively, switch this out for o1 series.\n* You must publish this workflow and ensure the form url is publically accessible.\n\n### On Depth & Breadth Configuration\nFor more detailed reports, increase depth and breadth but be warned the workflow will take a exponentially more time and money to complete. The defaults are usually good enough.\n\nDepth=1 & Breadth=2 - will take about 10 - 15mins.\nDepth=1 & Breadth=4 - will take about 30 - 40mins.\nDpeth=3 & Breadth=5 - will take about 2 - 5 hours!\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"
},
"typeVersion": 1
},
{
"id": "ac6f2604-7439-4524-a27e-2f031ebce089",
"name": "Sticky Note17",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2420,
-1180
],
"parameters": {
"color": 7,
"width": 520,
"height": 240,
"content": "![](https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw#full-width)"
},
"typeVersion": 1
},
{
"id": "40e3a0cf-7710-4537-b147-37ba8945fdbc",
"name": "Sticky Note18",
"type": "n8n-nodes-base.stickyNote",
"position": [
-120,
960
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE APIFY CREDENTIAL HERE!"
},
"typeVersion": 1
},
{
"id": "5a96ecc2-eeea-4c33-b299-7a7f2ca7559c",
"name": "Sticky Note19",
"type": "n8n-nodes-base.stickyNote",
"position": [
520,
960
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE APIFY CREDENTIAL HERE!"
},
"typeVersion": 1
},
{
"id": "23bca6e2-e16a-48a4-a7fc-96ce25846764",
"name": "Sticky Note20",
"type": "n8n-nodes-base.stickyNote",
"position": [
1640,
1740
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE NOTION CREDENTIAL HERE!"
},
"typeVersion": 1
}
],
"pinData": {},
"connections": {
"Item Ref": {
"main": [
[
{
"node": "Web Search",
"type": "main",
"index": 0
}
]
]
},
"Create Row": {
"main": [
[
{
"node": "Initiate DeepResearch",
"type": "main",
"index": 0
}
]
]
},
"Web Search": {
"main": [
[
{
"node": "Top 5 Organic Results",
"type": "main",
"index": 0
}
]
]
},
"Valid Pages": {
"main": [
[
{
"node": "Has Content?",
"type": "main",
"index": 0
}
]
]
},
"Confirmation": {
"main": [
[
{
"node": "End Form",
"type": "main",
"index": 0
}
]
]
},
"Has Content?": {
"main": [
[
{
"node": "Convert to Markdown",
"type": "main",
"index": 0
}
],
[
{
"node": "Empty Response",
"type": "main",
"index": 0
}
]
]
},
"Has Results?": {
"main": [
[
{
"node": "URLs to Items",
"type": "main",
"index": 0
}
],
[
{
"node": "Empty Response",
"type": "main",
"index": 0
}
]
]
},
"Valid Blocks": {
"main": [
[
{
"node": "Append Blocks",
"type": "main",
"index": 0
}
]
]
},
"Append Blocks": {
"main": [
[
{
"node": "For Each Block...",
"type": "main",
"index": 0
}
]
]
},
"HTML to Array": {
"main": [
[
{
"node": "Tags to Items",
"type": "main",
"index": 0
}
]
]
},
"Page Contents": {
"main": [
[
{
"node": "Valid Pages",
"type": "main",
"index": 0
}
],
[]
]
},
"SERP to Items": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Set Variables": {
"main": [
[
{
"node": "Clarifying Questions",
"type": "main",
"index": 0
}
]
]
},
"Tags to Items": {
"main": [
[
{
"node": "Notion Block Generator",
"type": "main",
"index": 0
}
]
]
},
"URLs to Items": {
"main": [
[
{
"node": "Page Contents",
"type": "main",
"index": 0
}
]
]
},
"Empty Response": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Execution Data": {
"main": [
[
{
"node": "JobType Router",
"type": "main",
"index": 0
}
]
]
},
"JobType Router": {
"main": [
[
{
"node": "Get Existing Row",
"type": "main",
"index": 0
}
],
[
{
"node": "Generate SERP Queries",
"type": "main",
"index": 0
}
],
[
{
"node": "Get Existing Row1",
"type": "main",
"index": 0
}
]
]
},
"Convert to HTML": {
"main": [
[
{
"node": "HTML to Array",
"type": "main",
"index": 0
}
]
]
},
"Set In-Progress": {
"main": [
[
{
"node": "Set Initial Query",
"type": "main",
"index": 0
}
]
]
},
"Get Existing Row": {
"main": [
[
{
"node": "Set In-Progress",
"type": "main",
"index": 0
}
]
]
},
"Research Request": {
"main": [
[
{
"node": "Set Variables",
"type": "main",
"index": 0
}
]
]
},
"Results to Items": {
"main": [
[
{
"node": "Set Next Queries",
"type": "main",
"index": 0
}
]
]
},
"Set Next Queries": {
"main": [
[
{
"node": "Generate Learnings",
"type": "main",
"index": 0
}
]
]
},
"Feedback to Items": {
"main": [
[
{
"node": "For Each Question...",
"type": "main",
"index": 0
}
]
]
},
"For Each Block...": {
"main": [
[
{
"node": "Set Done",
"type": "main",
"index": 0
}
],
[
{
"node": "Upload to Notion Page",
"type": "main",
"index": 0
}
]
]
},
"For Each Query...": {
"main": [
[
{
"node": "Combine & Send back to Loop",
"type": "main",
"index": 0
}
],
[
{
"node": "Item Ref",
"type": "main",
"index": 0
}
]
]
},
"Get Existing Row1": {
"main": [
[
{
"node": "DeepResearch Report",
"type": "main",
"index": 0
}
]
]
},
"Get Initial Query": {
"main": [
[
{
"node": "Report Page Generator",
"type": "main",
"index": 0
}
]
]
},
"Is Depth Reached?": {
"main": [
[
{
"node": "Get Research Results",
"type": "main",
"index": 0
}
],
[
{
"node": "DeepResearch Results",
"type": "main",
"index": 0
}
]
]
},
"OpenAI Chat Model": {
"ai_languageModel": [
[
{
"node": "DeepResearch Learnings",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Parse JSON blocks": {
"main": [
[
{
"node": "Valid Blocks",
"type": "main",
"index": 0
},
{
"node": "URL Sources to Lists",
"type": "main",
"index": 0
}
]
]
},
"Set Initial Query": {
"main": [
[
{
"node": "Generate Learnings",
"type": "main",
"index": 0
}
]
]
},
"Accumulate Results": {
"main": [
[
{
"node": "Is Depth Reached?",
"type": "main",
"index": 0
}
]
]
},
"Generate Learnings": {
"main": [
[
{
"node": "Accumulate Results",
"type": "main",
"index": 0
}
]
]
},
"On form submission": {
"main": [
[
{
"node": "Research Request",
"type": "main",
"index": 0
}
]
]
},
"OpenAI Chat Model1": {
"ai_languageModel": [
[
{
"node": "DeepResearch Report",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model2": {
"ai_languageModel": [
[
{
"node": "Clarifying Questions",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model3": {
"ai_languageModel": [
[
{
"node": "Generate SERP Queries",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model4": {
"ai_languageModel": [
[
{
"node": "Report Page Generator",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Convert to Markdown": {
"main": [
[
{
"node": "DeepResearch Learnings",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Report": {
"main": [
[
{
"node": "Convert to HTML",
"type": "main",
"index": 0
}
]
]
},
"Clarifying Questions": {
"main": [
[
{
"node": "Feedback to Items",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Results": {
"main": [
[
{
"node": "Results to Items",
"type": "main",
"index": 0
}
]
]
},
"For Each Question...": {
"main": [
[
{
"node": "Get Initial Query",
"type": "main",
"index": 0
}
],
[
{
"node": "Ask Clarity Questions",
"type": "main",
"index": 0
}
]
]
},
"Get Research Results": {
"main": [
[
{
"node": "Generate Report",
"type": "main",
"index": 0
}
]
]
},
"URL Sources to Lists": {
"main": [
[
{
"node": "Append Blocks",
"type": "main",
"index": 1
}
]
]
},
"Ask Clarity Questions": {
"main": [
[
{
"node": "For Each Question...",
"type": "main",
"index": 0
}
]
]
},
"Generate SERP Queries": {
"main": [
[
{
"node": "SERP to Items",
"type": "main",
"index": 0
}
]
]
},
"Initiate DeepResearch": {
"main": [
[
{
"node": "Confirmation",
"type": "main",
"index": 0
}
]
]
},
"Report Page Generator": {
"main": [
[
{
"node": "Create Row",
"type": "main",
"index": 0
}
]
]
},
"Top 5 Organic Results": {
"main": [
[
{
"node": "Has Results?",
"type": "main",
"index": 0
}
]
]
},
"Upload to Notion Page": {
"main": [
[
{
"node": "For Each Block...",
"type": "main",
"index": 0
}
],
[]
]
},
"DeepResearch Learnings": {
"main": [
[
{
"node": "Research Goal + Learnings",
"type": "main",
"index": 0
}
]
]
},
"Notion Block Generator": {
"main": [
[
{
"node": "Parse JSON blocks",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Subworkflow": {
"main": [
[
{
"node": "Execution Data",
"type": "main",
"index": 0
}
]
]
},
"Google Gemini Chat Model": {
"ai_languageModel": [
[
{
"node": "Notion Block Generator",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Structured Output Parser": {
"ai_outputParser": [
[
{
"node": "DeepResearch Learnings",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Research Goal + Learnings": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Structured Output Parser1": {
"ai_outputParser": [
[
{
"node": "Clarifying Questions",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Structured Output Parser2": {
"ai_outputParser": [
[
{
"node": "Generate SERP Queries",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Structured Output Parser4": {
"ai_outputParser": [
[
{
"node": "Report Page Generator",
"type": "ai_outputParser",
"index": 0
}
]
]
}
}
}{
"meta": {
"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9",
"templateCredsSetupCompleted": true
},
"nodes": [
{
"id": "645ae2b1-799e-49be-8bdf-12cd1bb739e6",
"name": "Structured Output Parser",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
1680,
1140
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"learnings\": {\n \"type\": \"array\",\n \"description\": \"List of learnings, max of 3.\",\n \"items\": { \"type\": \"string\" }\n },\n \"followUpQuestions\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"string\",\n \"description\": \"List of follow-up questions to research the topic further, max of 3.\"\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "cbdb4e98-eeba-4609-91de-394c416b7904",
"name": "Set Variables",
"type": "n8n-nodes-base.set",
"position": [
-1360,
-460
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "df28b12e-7c20-4ff5-b5b8-dc773aa14d4b",
"name": "request_id",
"type": "string",
"value": "={{ $execution.id }}"
},
{
"id": "9362c1e7-717d-444a-8ea2-6b5f958c9f3f",
"name": "prompt",
"type": "string",
"value": "={{ $json['What would you like to research?'] }}"
},
{
"id": "09094be4-7844-4a9e-af82-cc8e39322398",
"name": "depth",
"type": "number",
"value": "={{ $json['Enter research depth (recommended 1-5, default 2)'] || 2 }}"
},
{
"id": "3fc30a30-7806-4013-835d-97e27ddd7ae1",
"name": "breadth",
"type": "number",
"value": "={{ $json['Enter research breadth (recommended 2-10, default 4)'] || 4 }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "c7096ab9-0b10-45b0-b178-a049bf57830b",
"name": "OpenAI Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
1500,
1140
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "d0f1bc2f-6a10-4ac7-8d35-34f48f14fad5",
"name": "OpenAI Chat Model1",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-860,
1760
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "bba3278c-0336-4305-887d-56515dfd87db",
"name": "OpenAI Chat Model2",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-1060,
-300
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "f31f2fc7-0bec-4105-9d83-5f4f9a0eb35d",
"name": "Structured Output Parser1",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
-840,
-300
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"questions\": {\n \"type\": \"array\",\n \"description\": \"Follow up questions to clarify the research direction, max of 3.\",\n \"items\": {\n \"type\": \"string\"\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "ea59c5ab-fa05-4c68-bc60-3f56e240478b",
"name": "On form submission",
"type": "n8n-nodes-base.formTrigger",
"position": [
-1760,
-460
],
"webhookId": "7ddfaa7c-a523-4d92-b033-d76cd5a313e9",
"parameters": {
"options": {
"path": "deep_research",
"ignoreBots": true,
"buttonLabel": "Next"
},
"formTitle": " DeepResearcher",
"formFields": {
"values": [
{
"fieldType": "html",
"fieldLabel": "placeholder"
}
]
},
"formDescription": "=DeepResearcher is a multi-step, recursive approach using the internet to solve complex research tasks, accomplishing in tens of minutes what a human would take many hours.\n\nTo use, provide a short summary of what the research and how \"deep\" you'd like the workflow to investigate. Note, the higher the numbers the more time and cost will occur for the research.\n\nThe workflow is designed to complete independently and when finished, a report will be saved in a designated Notion Database."
},
"typeVersion": 2.2
},
{
"id": "a8262288-a8c1-4967-9870-f728fa08b579",
"name": "Generate SERP Queries",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-1040,
820
],
"parameters": {
"text": "=Given the following prompt from the user, generate a list of SERP queries to research the topic. Return a maximum of {{ $('JobType Router').first().json.data.breadth }} queries, but feel free to return less if the original prompt is clear. Make sure each query is unique and not similar to each other: <prompt>{{ $('JobType Router').first().json.data.query.trim() }}</prompt>\n\n{{\n$('JobType Router').first().json.data.learnings.length\n ? `Here are some learnings from previous research, use them to generate more specific queries: ${$('JobType Router').first().json.data.learnings.join('\\n')}`\n : ''\n}}",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "0534be47-22b7-4c2a-956b-d085e6b9f280",
"name": "Structured Output Parser2",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
-860,
980
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"queries\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"query\": {\n \"type\": \"string\",\n \"description\": \"The SERP query\"\n },\n \"researchGoal\": {\n \"type\": \"string\",\n \"description\": \"First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions.\"\n }\n }\n }\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "4d8aa196-986f-442d-9b56-92c043ab785d",
"name": "OpenAI Chat Model3",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-1040,
980
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "7488b037-7422-4f62-8c37-1f6a901b3299",
"name": "Set Initial Query",
"type": "n8n-nodes-base.set",
"position": [
-580,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "acb41e93-70c6-41a3-be0f-e5a74ec3ec88",
"name": "query",
"type": "string",
"value": "={{ $('JobType Router').first().json.data.query }}"
},
{
"id": "7fc54063-b610-42bc-a250-b1e8847c4d1e",
"name": "learnings",
"type": "array",
"value": "={{ $('JobType Router').first().json.data.learnings }}"
},
{
"id": "e8f1c158-56fb-41c8-8d86-96add16289bb",
"name": "breadth",
"type": "number",
"value": "={{ $('JobType Router').first().json.data.breadth }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "12ae382e-d88a-4f1b-a71f-3bd63c892b17",
"name": "SERP to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-700,
820
],
"parameters": {
"options": {},
"fieldToSplitOut": "output.queries"
},
"typeVersion": 1
},
{
"id": "********-f48a-493c-aebf-cdf175d58550",
"name": "Item Ref",
"type": "n8n-nodes-base.noOp",
"position": [
-240,
980
],
"parameters": {},
"typeVersion": 1
},
{
"id": "2cef6f1d-e244-4ee6-bf25-6dc3e8042afa",
"name": "Research Goal + Learnings",
"type": "n8n-nodes-base.set",
"position": [
1840,
1120
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "9acec2cc-64c8-4e62-bed4-c3d9ffab1379",
"name": "researchGoal",
"type": "string",
"value": "={{ $('Item Ref').first().json.researchGoal }}"
},
{
"id": "1b2d2dad-429b-4fc9-96c5-498f572a85c3",
"name": "learnings",
"type": "array",
"value": "={{ $json.output.learnings }}"
},
{
"id": "655b99f2-6045-4774-a634-49751bc9326f",
"name": "followUpQuestions",
"type": "array",
"value": "={{ $json.output.followUpQuestions }}"
},
{
"id": "c9e34ea4-5606-46d6-8d66-cb42d772a8b4",
"name": "urls",
"type": "array",
"value": "={{\n$('Page Contents')\n .all()\n .filter(item => !item.json.error && item.json.body)\n .map(item => item.json.url)\n}}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "4aebae86-2bd2-4f3d-8290-d34b9ac837c6",
"name": "Accumulate Results",
"type": "n8n-nodes-base.set",
"position": [
-200,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "db509e90-9a86-431f-8149-4094d22666cc",
"name": "should_stop",
"type": "boolean",
"value": "={{\n$runIndex >= ($('JobType Router').first().json.data.depth)\n}}"
},
{
"id": "90986e2b-8aca-4a22-a9db-ed8809d6284d",
"name": "all_learnings",
"type": "array",
"value": "={{\nArray($runIndex+1)\n .fill(0)\n .flatMap((_,idx) => {\n try {\n return $('Generate Learnings')\n .all(0,idx)\n .flatMap(item => item.json.data.flatMap(d => d.learnings))\n } catch (e) {\n return []\n }\n })\n}}"
},
{
"id": "3eade958-e8ab-4975-aac4-f4a4a983c163",
"name": "all_urls",
"type": "array",
"value": "={{\nArray($runIndex+1)\n .fill(0)\n .flatMap((_,idx) => {\n try {\n return $('Generate Learnings')\n .all(0,idx)\n .flatMap(item => item.json.data.flatMap(d => d.urls))\n } catch (e) {\n return []\n }\n })\n}}"
}
]
}
},
"executeOnce": true,
"typeVersion": 3.4
},
{
"id": "782baa36-ba07-4845-873c-c9400de6d463",
"name": "DeepResearch Results",
"type": "n8n-nodes-base.set",
"position": [
160,
360
],
"parameters": {
"mode": "raw",
"options": {},
"jsonOutput": "={{ $('Generate Learnings').item.json }}"
},
"typeVersion": 3.4
},
{
"id": "89b09898-79ec-4924-975f-e9581d3bf774",
"name": "Results to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
320,
360
],
"parameters": {
"options": {},
"fieldToSplitOut": "data"
},
"typeVersion": 1
},
{
"id": "122cd071-aade-4753-ba0a-8db4c58fa84e",
"name": "Set Next Queries",
"type": "n8n-nodes-base.set",
"position": [
480,
360
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "d88bfe95-9e73-4d25-b45c-9f164b940b0e",
"name": "query",
"type": "string",
"value": "=Previous research goal: {{ $json.researchGoal }}\nFollow-up research directions: {{ $json.followUpQuestions.map(q => `\\n${q}`).join('') }}"
},
{
"id": "4aa20690-d998-458a-b1e4-0d72e6a68e6b",
"name": "learnings",
"type": "array",
"value": "={{ $('Accumulate Results').item.json.all_learnings }}"
},
{
"id": "89acafae-b04a-4d5d-b08b-656e715654e4",
"name": "breadth",
"type": "number",
"value": "={{ $('JobType Router').first().json.data.breadth }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "9da01d8a-48d6-45b4-b8c6-9a0503b4bda6",
"name": "Web Search",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"position": [
-80,
980
],
"parameters": {
"url": "https://api.apify.com/v2/acts/serping~fast-google-search-results-scraper/run-sync-get-dataset-items",
"method": "POST",
"options": {},
"sendBody": true,
"authentication": "genericCredentialType",
"bodyParameters": {
"parameters": [
{
"name": "searchTerms",
"value": "={{\n[\n `${$json.query} -filetype:pdf`\n]\n}}"
},
{
"name": "resultsPerPage",
"value": "={{ 10 }}"
}
]
},
"genericAuthType": "httpHeaderAuth"
},
"credentials": {
"httpQueryAuth": {
"id": "cO2w8RDNOZg8DRa8",
"name": "Apify API"
},
"httpHeaderAuth": {
"id": "SV9BDKc1cRbZBeoL",
"name": "Apify.com (personal token)"
}
},
"typeVersion": 4.2
},
{
"id": "99bd2c8e-5600-43a9-ab2f-7f2911efb16c",
"name": "Top 5 Organic Results",
"type": "n8n-nodes-base.set",
"position": [
80,
980
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "29d1a759-d886-4a44-860b-9d16f9922043",
"name": "results",
"type": "array",
"value": "={{\n$json.origin_search.results\n ? $json.origin_search\n .results\n .filter(res => res.type === 'normal')\n .slice(0, 5)\n .map(res => ({\n title: res.title,\n url: res.source.link\n }))\n : []\n}}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "cb7c5a8b-5420-4fb9-b7f0-4e8e8d10034a",
"name": "Convert to Markdown",
"type": "n8n-nodes-base.markdown",
"position": [
1320,
980
],
"parameters": {
"html": "={{ $json.body }}",
"options": {
"ignore": "a,img,picture,svg,video,audio,iframe"
},
"destinationKey": "markdown"
},
"typeVersion": 1
},
{
"id": "818ccf2e-081d-492e-ba8d-de458b0c26db",
"name": "For Each Query...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
-420,
820
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "1787b562-17e8-41af-9cdc-eb2d3e630916",
"name": "Feedback to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-720,
-460
],
"parameters": {
"options": {},
"fieldToSplitOut": "output.questions"
},
"typeVersion": 1
},
{
"id": "4c695faa-74e3-456b-a1ef-aaea67e46743",
"name": "Ask Clarity Questions",
"type": "n8n-nodes-base.form",
"position": [
-360,
-380
],
"webhookId": "ab0d9b81-73f6-4baa-a3cd-ac3b31397708",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"buttonLabel": "Answer",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>\n<p style=\"text-align:left\">\nAnswer the following clarification questions to assist the DeepResearcher better under the research topic.\n</p>\n<hr style=\"display:block;margin-top:16px;margin-bottom:0\" />\n<p style=\"text-align:left;font-family:sans-serif;font-weight:700;\">\nTotal {{ $('Feedback to Items').all().length }} questions.\n</p>"
},
"formFields": {
"values": [
{
"fieldType": "textarea",
"fieldLabel": "={{ $json[\"output.questions\"] }}",
"placeholder": "=",
"requiredField": true
}
]
}
},
"typeVersion": 1
},
{
"id": "e07d8c3e-8bcd-4393-9892-f825433ab58d",
"name": "For Each Question...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
-540,
-460
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "e8d26351-52f4-40a6-ba5b-fb6bc816b734",
"name": "DeepResearch Subworkflow",
"type": "n8n-nodes-base.executeWorkflowTrigger",
"position": [
-1880,
820
],
"parameters": {
"workflowInputs": {
"values": [
{
"name": "requestId",
"type": "any"
},
{
"name": "jobType"
},
{
"name": "data",
"type": "object"
}
]
}
},
"typeVersion": 1.1
},
{
"id": "25a8055a-27aa-414f-856b-25a2e2f31974",
"name": "Sticky Note",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
-680
],
"parameters": {
"color": 7,
"width": 1000,
"height": 560,
"content": "## 2. Ask Clarifying Questions\n[Read more about form nodes](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/)\n\nTo handle the clarification questions generated by the LLM, I used the same technique found in my \"AI Interviewer\" template ([link](https://n8n.io/workflows/2566-conversational-interviews-with-ai-agents-and-n8n-forms/)).\nThis involves a looping of dynamically generated forms to collect answers from the user."
},
"typeVersion": 1
},
{
"id": "68398b92-eb35-48bf-885e-540074531cc4",
"name": "Clarifying Questions",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-1040,
-460
],
"parameters": {
"text": "=Given the following query from the user, ask some follow up questions to clarify the research direction. Return a maximum of 3 questions, but feel free to return less if the original query is clear: <query>{{ $json.prompt }}</query>`",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "65c4c293-67b8-4e64-af04-16e45e97c09a",
"name": "Sticky Note1",
"type": "n8n-nodes-base.stickyNote",
"position": [
-660,
-60
],
"parameters": {
"color": 7,
"width": 1360,
"height": 640,
"content": "## 6. Perform DeepSearch Loop\n[Learn more about the Looping in n8n](https://docs.n8n.io/flow-logic/looping/#creating-loops)\n\nThe key of the Deep Research flow is its extensive data collection capability. In this implementation, this capability is represented by a recursive web search & scrape loop which starts with the original query and extended by AI-generated subqueries. How many subqueries to generate are determined the depth and breadth parameters specified.\n\n\"Learnings\" are generated for each subquery and accumulate on each iteration of the loop. When the loop finishes when depth limit is reached, all learnings are collected and it's these learnings are what we use to generate the report."
},
"typeVersion": 1
},
{
"id": "43a5d93d-cae2-43ec-b9ae-b15d6b11b932",
"name": "End Form",
"type": "n8n-nodes-base.form",
"position": [
960,
-420
],
"webhookId": "7b531f5d-942f-4c49-ac55-8ee480889600",
"parameters": {
"options": {},
"operation": "completion",
"completionTitle": "=Thank you for using DeepResearcher.",
"completionMessage": "=You may now close this window."
},
"typeVersion": 1
},
{
"id": "9a824011-e76f-433f-8735-44b358f4ff7d",
"name": "Initiate DeepResearch",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
600,
-420
],
"parameters": {
"mode": "each",
"options": {
"waitForSubWorkflow": false
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{\n{\n \"query\": $('Get Initial Query').first().json.query,\n \"learnings\": [],\n \"depth\": $('Set Variables').first().json.depth,\n \"breadth\": $('Set Variables').first().json.breadth,\n}\n}}",
"jobType": "deepresearch_initiate",
"requestId": "={{ $('Set Variables').first().json.request_id }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"removed": false,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"removed": false,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"removed": false,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "c48ee4cd-bac1-4405-bb4c-5614e5eb25a0",
"name": "Page Contents",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"position": [
560,
980
],
"parameters": {
"url": "https://api.apify.com/v2/acts/apify~web-scraper/run-sync-get-dataset-items",
"options": {},
"jsonBody": "={\n \"startUrls\": {{ [{ url: $json.url, method: 'GET' }].toJsonString() }},\n \"breakpointLocation\": \"NONE\",\n \"browserLog\": false,\n \"closeCookieModals\": false,\n \"debugLog\": false,\n \"downloadCss\": false,\n \"downloadMedia\": false,\n \"excludes\": [\n {\n \"glob\": \"/**/*.{png,jpg,jpeg,pdf}\"\n }\n ],\n \"headless\": true,\n \"ignoreCorsAndCsp\": false,\n \"ignoreSslErrors\": false,\n \"injectJQuery\": true,\n \"keepUrlFragments\": false,\n \"linkSelector\": \"\",\n \"maxCrawlingDepth\": 1,\n \"maxPagesPerCrawl\": 1,\n \"maxRequestRetries\": 1,\n \"maxResultsPerCrawl\": 1,\n \"pageFunction\": \"// The function accepts a single argument: the \\\"context\\\" object.\\n// For a complete list of its properties and functions,\\n// see https://apify.com/apify/web-scraper#page-function \\nasync function pageFunction(context) {\\n\\n await new Promise(res => { setTimeout(res, 6000) });\\n // This statement works as a breakpoint when you're trying to debug your code. Works only with Run mode: DEVELOPMENT!\\n // debugger; \\n\\n // jQuery is handy for finding DOM elements and extracting data from them.\\n // To use it, make sure to enable the \\\"Inject jQuery\\\" option.\\n const $ = context.jQuery;\\n const title = $('title').first().text();\\n\\n // Clone the body to avoid modifying the original content\\n const bodyClone = $('body').clone();\\n bodyClone.find('iframe, img, script, style, object, embed, noscript, svg, video, audio').remove();\\n const body = bodyClone.html();\\n\\n // Return an object with the data extracted from the page.\\n // It will be stored to the resulting dataset.\\n return {\\n url: context.request.url,\\n title,\\n body\\n };\\n}\",\n \"postNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept a single argument: the \\\"crawlingContext\\\" object.\\n[\\n async (crawlingContext) => {\\n // ...\\n },\\n]\",\n \"preNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept two arguments: the \\\"crawlingContext\\\" object\\n// and \\\"gotoOptions\\\".\\n[\\n async (crawlingContext, gotoOptions) => {\\n // ...\\n },\\n]\\n\",\n \"proxyConfiguration\": {\n \"useApifyProxy\": true\n },\n \"runMode\": \"PRODUCTION\",\n \"useChrome\": false,\n \"waitUntil\": [\n \"domcontentloaded\"\n ],\n \"globs\": [],\n \"pseudoUrls\": [],\n \"proxyRotation\": \"RECOMMENDED\",\n \"maxConcurrency\": 50,\n \"pageLoadTimeoutSecs\": 60,\n \"pageFunctionTimeoutSecs\": 60,\n \"maxScrollHeightPixels\": 5000,\n \"customData\": {}\n}",
"sendBody": true,
"sendQuery": true,
"specifyBody": "json",
"authentication": "genericCredentialType",
"genericAuthType": "httpQueryAuth",
"queryParameters": {
"parameters": [
{
"name": "memory",
"value": "2048"
},
{
"name": "timeout",
"value": "90"
}
]
}
},
"credentials": {
"httpQueryAuth": {
"id": "cO2w8RDNOZg8DRa8",
"name": "Apify API"
}
},
"typeVersion": 4.2
},
{
"id": "dc9f85ff-7565-4c29-981a-5ef65bba6ca3",
"name": "Execution Data",
"type": "n8n-nodes-base.executionData",
"position": [
-1700,
820
],
"parameters": {
"dataToSave": {
"values": [
{
"key": "requestId",
"value": "={{ $json.requestId }}"
},
{
"key": "=jobType",
"value": "={{ $json.jobType }}"
}
]
}
},
"typeVersion": 1
},
{
"id": "26b33429-6d61-4758-9c76-3e998dd31fa4",
"name": "JobType Router",
"type": "n8n-nodes-base.switch",
"position": [
-1520,
820
],
"parameters": {
"rules": {
"values": [
{
"outputKey": "initiate",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"operator": {
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_initiate"
}
]
},
"renameOutput": true
},
{
"outputKey": "learnings",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "ecbfa54d-fc97-48c5-8d3d-f0538b8d727b",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_learnings"
}
]
},
"renameOutput": true
},
{
"outputKey": "report",
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "392f9a98-ec22-4e57-9c8e-0e1ed6b7dafa",
"operator": {
"name": "filter.operator.equals",
"type": "string",
"operation": "equals"
},
"leftValue": "={{ $json.jobType }}",
"rightValue": "deepresearch_report"
}
]
},
"renameOutput": true
}
]
},
"options": {}
},
"typeVersion": 3.2
},
{
"id": "a9637952-7c09-40ae-96ec-bdf0fc63d94e",
"name": "Valid Pages",
"type": "n8n-nodes-base.filter",
"position": [
720,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "f44691e4-f753-47b0-b66a-068a723b6beb",
"operator": {
"type": "boolean",
"operation": "false",
"singleValue": true
},
"leftValue": "={{ $json['#error'] }}",
"rightValue": ""
},
{
"id": "8e05df2b-0d4a-47da-9aab-da7e8907cbca",
"operator": {
"type": "string",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json.body }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2,
"alwaysOutputData": true
},
{
"id": "204cfca2-05bb-46dd-ba96-b41866ed2cfe",
"name": "OpenAI Chat Model4",
"type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
"position": [
-20,
-280
],
"parameters": {
"model": {
"__rl": true,
"mode": "id",
"value": "o3-mini"
},
"options": {}
},
"credentials": {
"openAiApi": {
"id": "8gccIjcuf3gvaoEr",
"name": "OpenAi account"
}
},
"typeVersion": 1.2
},
{
"id": "45bc6261-35c8-4994-bb88-ed7a0f022767",
"name": "Get Initial Query",
"type": "n8n-nodes-base.set",
"position": [
-360,
-540
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "14b77741-c3c3-4bd2-be6e-37bd09fcea2b",
"name": "query",
"type": "string",
"value": "=Initial query: {{ $('Set Variables').first().json.prompt }}\nFollow-up Questions and Answers:\n{{\n$input.all()\n .map(item => {\n const q = Object.keys(item.json)[0];\n const a = item.json[q];\n return `question: ${q}\\nanswer: ${a}`;\n })\n .join('\\n')\n}}"
}
]
}
},
"executeOnce": true,
"typeVersion": 3.4
},
{
"id": "26d26e54-ee9b-4714-ae27-4f033dc825d3",
"name": "Structured Output Parser4",
"type": "@n8n/n8n-nodes-langchain.outputParserStructured",
"position": [
160,
-280
],
"parameters": {
"schemaType": "manual",
"inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"title\": {\n \"type\": \"string\",\n \"description\":\" A short title summarising the research topic\"\n },\n \"description\": {\n \"type\": \"string\",\n \"description\": \"A short description to summarise the research topic\"\n }\n }\n}"
},
"typeVersion": 1.2
},
{
"id": "3842bc1d-d5f9-4879-bc06-db20fed3f55d",
"name": "Create Row",
"type": "n8n-nodes-base.notion",
"position": [
300,
-420
],
"parameters": {
"title": "={{ $json.output.title }}",
"options": {},
"resource": "databasePage",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"propertiesUi": {
"propertyValues": [
{
"key": "Description|rich_text",
"textContent": "={{ $json.output.description }}"
},
{
"key": "Status|status",
"statusValue": "Not started"
},
{
"key": "Request ID|rich_text",
"textContent": "={{ $('Set Variables').first().json.request_id }}"
},
{
"key": "Name|title",
"title": "={{ $json.output.title }}"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "bfe98996-6ed5-4f60-afdd-a947a6fa6e36",
"name": "Report Page Generator",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-20,
-420
],
"parameters": {
"text": "=Create a suitable title for the research report which will be created from the user's query.\n<query>{{ $json.query }}</query>",
"promptType": "define",
"hasOutputParser": true
},
"typeVersion": 1.5
},
{
"id": "ff05add8-94b0-4495-8f4e-3e8a10c556af",
"name": "Sticky Note2",
"type": "n8n-nodes-base.stickyNote",
"position": [
-120,
-680
],
"parameters": {
"color": 7,
"width": 600,
"height": 560,
"content": "## 3. Create Empty Report Page in Notion\n[Read more about the Notion node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion/)\n\nSome thought was given where to upload the final report and Notion was selected due to familiarity. This can be easily changed to whatever wiki tools you prefer.\n\nIf you're following along however, here's the Notion database you need to replicate - [Jim's n8n DeepResearcher Database](https://jimleuk.notion.site/19486dd60c0c80da9cb7eb1468ea9afd?v=19486dd60c0c805c8e0c000ce8c87acf)."
},
"typeVersion": 1
},
{
"id": "5bc13d62-81e1-4730-b7e6-9e5579dff174",
"name": "Sticky Note3",
"type": "n8n-nodes-base.stickyNote",
"position": [
500,
-680
],
"parameters": {
"color": 7,
"width": 640,
"height": 560,
"content": "## 4. Trigger DeepResearch Asynchronously\n[Learn more about the Execute Trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow/)\n\nn8n handles asynchronous jobs by spinning them off as separate executions. This basically means the user doesn't have to wait or keep their browser window open for our researcher to do its job.\n\nOnce we initiate the Deepresearcher job, we can close out the onboarding journey for a nice user experience."
},
"typeVersion": 1
},
{
"id": "9fea6403-b2a2-4e67-99a2-b7a2f29a1e96",
"name": "Sticky Note4",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1160,
620
],
"parameters": {
"color": 7,
"width": 620,
"height": 540,
"content": "## 7. Generate Search Queries\n[Learn more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nMuch like a human researcher, the DeepResearcher will rely on web search and content as the preferred source of information. To ensure it can cover a wide range of sources, the AI can first generate relevant research queries of which each can be explored separately."
},
"typeVersion": 1
},
{
"id": "0bccdc54-7570-4bca-93ec-cb140c5bd3a1",
"name": "Is Depth Reached?",
"type": "n8n-nodes-base.if",
"position": [
-40,
180
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "75d18d88-6ba6-43df-bef7-3e8ad99ad8bd",
"operator": {
"type": "boolean",
"operation": "true",
"singleValue": true
},
"leftValue": "={{ $json.should_stop }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "819aa5be-b71b-44a7-b062-b2a50209f290",
"name": "URLs to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
400,
980
],
"parameters": {
"options": {},
"fieldToSplitOut": "results"
},
"typeVersion": 1
},
{
"id": "4b8e9936-4b24-4bd4-8fe7-75d58244cb6d",
"name": "Get Research Results",
"type": "n8n-nodes-base.set",
"position": [
160,
180
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "90b3da00-dcd5-4289-bd45-953146a3b0ba",
"name": "all_learnings",
"type": "array",
"value": "={{ $json.all_learnings }}"
},
{
"id": "623dbb3d-83a1-44a9-8ad3-48d92bc42811",
"name": "all_urls",
"type": "array",
"value": "={{ $json.all_urls }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "d371535a-2946-4ec5-9be6-2ee8e359ac44",
"name": "Get Existing Row",
"type": "n8n-nodes-base.notion",
"position": [
-1040,
180
],
"parameters": {
"limit": 1,
"filters": {
"conditions": [
{
"key": "Request ID|rich_text",
"condition": "equals",
"richTextValue": "={{ $json.requestId.toString() }}"
}
]
},
"options": {},
"resource": "databasePage",
"matchType": "allFilters",
"operation": "getAll",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"filterType": "manual"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "fea4c30e-1193-494d-8823-dfbec5196a0d",
"name": "Set In-Progress",
"type": "n8n-nodes-base.notion",
"position": [
-840,
180
],
"parameters": {
"pageId": {
"__rl": true,
"mode": "id",
"value": "={{ $json.id }}"
},
"options": {},
"resource": "databasePage",
"operation": "update",
"propertiesUi": {
"propertyValues": [
{
"key": "Status|status",
"statusValue": "In progress"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "37954acd-d8cb-4c74-afa8-d8973e017327",
"name": "Set Done",
"type": "n8n-nodes-base.notion",
"position": [
1680,
1600
],
"parameters": {
"pageId": {
"__rl": true,
"mode": "id",
"value": "={{ $('Get Existing Row1').first().json.id }}"
},
"options": {},
"resource": "databasePage",
"operation": "update",
"propertiesUi": {
"propertyValues": [
{
"key": "Status|status",
"statusValue": "Done"
},
{
"key": "Last Updated|date",
"date": "={{ $now.toISO() }}"
}
]
}
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"executeOnce": true,
"typeVersion": 2.2
},
{
"id": "3db97ab8-b934-4567-a92e-92374a363df6",
"name": "Tags to Items",
"type": "n8n-nodes-base.splitOut",
"position": [
-60,
1600
],
"parameters": {
"options": {},
"fieldToSplitOut": "tag"
},
"typeVersion": 1
},
{
"id": "7f468bf7-762a-4818-86f9-54d172bb618a",
"name": "Convert to HTML",
"type": "n8n-nodes-base.markdown",
"position": [
-380,
1600
],
"parameters": {
"mode": "markdownToHtml",
"options": {
"tables": true
},
"markdown": "={{ $json.text }}"
},
"typeVersion": 1
},
{
"id": "97914ee9-0ee8-408b-b2bb-a7193b2d0454",
"name": "HTML to Array",
"type": "n8n-nodes-base.set",
"position": [
-220,
1600
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "851b8a3f-c2d3-41ad-bf60-4e0e667f6c58",
"name": "tag",
"type": "array",
"value": "={{ $json.data.match(/<table[\\s\\S]*?<\\/table>|<ul[\\s\\S]*?<\\/ul>|<[^>]+>[^<]*<\\/[^>]+>/g) }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "6ce79f16-51e3-4192-8103-738222be558b",
"name": "Notion Block Generator",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
100,
1600
],
"parameters": {
"text": "={{ $json.tag.trim() }}",
"messages": {
"messageValues": [
{
"message": "=Convert the following html into its equivalent Notion Block as per Notion's API schema.\n* Ensure the content is always included and remains the same.\n* Return only a json response.\n* Generate child-level blocks. Should not define \"parent\" or \"children\" property.\n* Strongly prefer headings, paragraphs, tables and lists type blocks.\n* available headings are heading_1, heading_2 and heading_3 - h4,h5,h6 should use heading_3 type instead. ensure headings use the rich text definition.\n* ensure lists blocks include all list items.\n\n## Examples\n\n1. headings\n```\n<h3 id=\"references\">References</h3>\n```\nwould convert to \n```\n{\"object\": \"block\", \"type\": \"heading_3\", \"heading_3\": { \"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"References\"}}]}}\n```\n\n2. lists\n```\n<ul><li>hello</li><li>world</li></ul>\n```\nwould convert to\n```\n[\n{\n \"object\": \"block\",\n \"type\": \"bulleted_list_item\",\n \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"hello\"}}]}\n},\n{\n \"object\": \"block\",\n \"type\": \"bulleted_list_item\",\n \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"world\"}}]}\n}\n]\n```\n\n3. tables\n```\n<table>\n <thead>\n <tr><th>Technology</th><th>Potential Impact</th></tr>\n </thead>\n <tbody>\n <tr>\n <td>5G Connectivity</td><td>Enables faster data speeds and advanced apps</td>\n </tr>\n </tbody>\n</table>\n```\nwould convert to\n```\n{\n \"object\": \"block\",\n \"type\": \"table\",\n \"table\": {\n \"table_width\": 2,\n \"has_column_header\": true,\n \"has_row_header\": false,\n \"children\": [\n {\n \"object\": \"block\",\n \"type\": \"table_row\",\n \"table_row\": {\n \"cells\": [\n [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Technology\",\n \"link\": null\n }\n },\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Potential Impact\",\n \"link\": null\n }\n }\n ],\n [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"5G Connectivity\",\n \"link\": null\n }\n },\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Enables faster data speeds and advanced apps\",\n \"link\": null\n }\n }\n ]\n ]\n }\n }\n ]\n }\n}\n```\n4. anchor links\nSince Notion doesn't support anchor links, just convert them to rich text blocks instead.\n```\n<a href=\"#module-0-pre-course-setup-and-learning-principles\">Module 0: Pre-Course Setup and Learning Principles</a>\n```\nconverts to\n```\n{\n \"object\": \"block\",\n \"type\": \"paragraph\",\n \"paragraph\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Module 0: Pre-Course Setup and Learning Principles\"\n }\n }\n ]\n }\n}\n```\n5. Invalid html parts\nWhen the html is not syntax valid eg. orphaned closing tags, then just skip the conversion and use an empty rich text block.\n```\n</li>\\n</ol>\n```\ncan be substituted with\n```\n{\n \"object\": \"block\",\n \"type\": \"paragraph\",\n \"paragraph\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \" \"\n }\n }\n ]\n }\n}\n```"
}
]
},
"promptType": "define"
},
"typeVersion": 1.5
},
{
"id": "e3eeb9f0-7407-41f9-a814-def6c26b2ee1",
"name": "Google Gemini Chat Model",
"type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini",
"position": [
80,
1760
],
"parameters": {
"options": {},
"modelName": "models/gemini-2.0-flash"
},
"credentials": {
"googlePalmApi": {
"id": "dSxo6ns5wn658r8N",
"name": "Google Gemini(PaLM) Api account"
}
},
"typeVersion": 1
},
{
"id": "5b0aeaca-dce5-4afd-8a8a-0ef2c18b6f06",
"name": "Parse JSON blocks",
"type": "n8n-nodes-base.set",
"onError": "continueRegularOutput",
"position": [
420,
1600
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "73fcb8a0-2672-4bd5-86de-8075e1e02baf",
"name": "=block",
"type": "array",
"value": "={{\n(function(){\n const block = $json.text\n .replace('```json', '')\n .replace('```', '')\n .trim()\n .parseJson();\n if (Array.isArray(block)) return block;\n if (block.type.startsWith('heading_')) {\n const prev = Number(block.type.split('_')[1]);\n const next = Math.max(1, prev - 1);\n if (next !== prev) {\n block.type = `heading_${next}`;\n block[`heading_${next}`] = Object.assign({}, block[`heading_${prev}`]);\n block[`heading_${prev}`] = undefined;\n }\n }\n return [block];\n})()\n}}"
}
]
}
},
"executeOnce": false,
"typeVersion": 3.4
},
{
"id": "e2a5a5bc-a3c8-42c5-9419-74ce3525f599",
"name": "Upload to Notion Page",
"type": "n8n-nodes-base.httpRequest",
"onError": "continueRegularOutput",
"maxTries": 2,
"position": [
1680,
1760
],
"parameters": {
"url": "=https://api.notion.com/v1/blocks/{{ $('Get Existing Row1').first().json.id }}/children",
"method": "PATCH",
"options": {
"timeout": "={{ 1000 * 60 }}"
},
"jsonBody": "={{\n{\n \"children\": $json.block\n}\n}}",
"sendBody": true,
"sendHeaders": true,
"specifyBody": "json",
"authentication": "predefinedCredentialType",
"headerParameters": {
"parameters": [
{
"name": "Notion-Version",
"value": "2022-06-28"
}
]
},
"nodeCredentialType": "notionApi"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"retryOnFail": true,
"typeVersion": 4.2,
"waitBetweenTries": 3000
},
{
"id": "9f18b2a5-ba74-40fc-8e35-a93ecd13507a",
"name": "Sticky Note5",
"type": "n8n-nodes-base.stickyNote",
"position": [
-520,
620
],
"parameters": {
"color": 7,
"width": 1740,
"height": 740,
"content": "## 8. Web Search and Extracting Web Page Contents using [APIFY.com](https://www.apify.com?fpr=414q6)\n[Read more about the HTTP Request node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nHere is where I deviated a little from the reference implementation. I opted not to use Firecrawl.ai due to (1) high cost of the service and (2) a regular non-ai crawler would work just as well and probably quicker.\nInstead I'm using [APIFY.com](https://www.apify.com?fpr=414q6) which is a more performant, cost-effective and reliable web scraper service. If you don't want to use Apify, feel free to swap this out with your preferred service.\n\nThis step is the most exciting in terms of improvements and optimisations eg. mix in internal data sources! Add in Perplexity.ai or Jina.ai! Possibilities are endless."
},
"typeVersion": 1
},
{
"id": "84c34a2a-d8bb-4e62-a5ea-df0a142aa2b4",
"name": "Sticky Note6",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
60
],
"parameters": {
"color": 7,
"width": 460,
"height": 360,
"content": "## 5. Set Report to In-Progress\n[Read more about the Notion node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion/)"
},
"typeVersion": 1
},
{
"id": "bd022636-873d-4aca-8929-c189f8596cc1",
"name": "Sticky Note7",
"type": "n8n-nodes-base.stickyNote",
"position": [
1240,
700
],
"parameters": {
"color": 7,
"width": 780,
"height": 660,
"content": "## 9. Compile Learnings with Reasoning Model\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nWith our gathered sources, it's now just a case of giving it to our LLM to compile a list of \"learnings\" from them. For our DeepResearcher, we'll use OpenAI's o3-mini which is the latest reasoning model at time of writing. Reasoning perform better than regular chat models due their chain-of-thought or \"thinking\" process that they perform.\n\nThe \"Learnings\" are then combined with the generated research goal to complete one loop."
},
"typeVersion": 1
},
{
"id": "b2f6e51d-cbe6-4459-9515-679f79063926",
"name": "Get Existing Row1",
"type": "n8n-nodes-base.notion",
"position": [
-1020,
1600
],
"parameters": {
"limit": 1,
"filters": {
"conditions": [
{
"key": "Request ID|rich_text",
"condition": "equals",
"richTextValue": "={{ $json.requestId.toString() }}"
}
]
},
"options": {},
"resource": "databasePage",
"matchType": "allFilters",
"operation": "getAll",
"databaseId": {
"__rl": true,
"mode": "list",
"value": "19486dd6-0c0c-80da-9cb7-eb1468ea9afd",
"cachedResultUrl": "https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd",
"cachedResultName": "n8n DeepResearch"
},
"filterType": "manual"
},
"credentials": {
"notionApi": {
"id": "iHBHe7ypzz4mZExM",
"name": "Notion account"
}
},
"typeVersion": 2.2
},
{
"id": "7b315060-7e40-410c-ac9d-ef22acbb175a",
"name": "Sticky Note8",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1140,
1400
],
"parameters": {
"color": 7,
"width": 660,
"height": 540,
"content": "## 10. Generate DeepSearch Report using Learnings\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/)\n\nFinally! After all learnings have been gathered - which may have taken up to an hour or more on the higher settings! - they are given to our LLM to generate the final research report in markdown format. Technically, the DeepResearch ends here but for this template, we need to push the output to Notion. If you're not using Notion, feel free to ignore the last few steps."
},
"typeVersion": 1
},
{
"id": "01aaf2a8-c145-4f39-aa52-f40fc28f8767",
"name": "Sticky Note9",
"type": "n8n-nodes-base.stickyNote",
"position": [
-460,
1400
],
"parameters": {
"color": 7,
"width": 1060,
"height": 540,
"content": "## 11. Reformat Report as Notion Blocks\n[Learn more about the Markdown node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.markdown/)\n\nTo write our report to our Notion page, we'll have to convert it to Notion \"blocks\" - these are specialised json objects which are required by the Notion API. There are quite a number of ways to do this conversion not involving the use of AI but for kicks, I decided to do so anyway. In this step, we first convert to HTML as it allows us to split the report semantically and makes for easier parsing for the LLM."
},
"typeVersion": 1
},
{
"id": "700a6f44-86bf-4aab-8a42-23bf6843f681",
"name": "Sticky Note10",
"type": "n8n-nodes-base.stickyNote",
"position": [
1220,
1400
],
"parameters": {
"color": 7,
"width": 800,
"height": 580,
"content": "## 13. Update Report in Notion\n[Read more about the HTTP request node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/)\n\nIn this step, we can use the Notion API to add the blocks to our page sequentially. A loop is used due to the unstable Notion API - the loop allows retries for blocks that require it."
},
"typeVersion": 1
},
{
"id": "f8536052-c851-42ec-aaf3-5fc876570f6d",
"name": "Sticky Note11",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1840,
-680
],
"parameters": {
"color": 7,
"width": 680,
"height": 560,
"content": "## 1. Let's Research!\n[Learn more about the form trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger)\n\nn8n forms are a really nice way to get our frontend up and running quickly and compared to chat, offers a superior user interface for user input. I've gone perhaps a little extra with the custom html fields but I do enjoy adding a little customisation now and then."
},
"typeVersion": 1
},
{
"id": "58aa92a2-e1fd-497d-a27e-40b733189bab",
"name": "DeepResearch Report",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
-860,
1600
],
"parameters": {
"text": "=You are are an expert and insightful researcher.\n* Given the following prompt from the user, write a final report on the topic using the learnings from research.\n* Make it as as detailed as possible, aim for 3 or more pages, include ALL the learnings from research.\n* Format the report in markdown. Use headings, lists and tables only and where appropriate.\n\n<prompt>{{ $('JobType Router').first().json.data.query }}</prompt>\n\nHere are all the learnings from previous research:\n\n<learnings>\n{{\n$('JobType Router').first().json.data\n .all_learnings\n .map(item => `<learning>${item}</learning>`) \n .join('\\n')\n}}\n</learnings>",
"promptType": "define"
},
"typeVersion": 1.5
},
{
"id": "0656be83-d510-46f1-aeeb-f62a69aa3cf2",
"name": "DeepResearch Learnings",
"type": "@n8n/n8n-nodes-langchain.chainLlm",
"position": [
1500,
980
],
"parameters": {
"text": "=Given the following contents from a SERP search for the query <query>{{ $('Item Ref').first().json.query }}</query>, generate a list of learnings from the contents. Return a maximum of 3 learnings, but feel free to return less if the contents are clear. Make sure each learning is unique and not similar to each other. The learnings should be concise and to the point, as detailed and infromation dense as possible. Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any exact metrics, numbers, or dates. The learnings will be used to research the topic further.\n\n<contents>\n{{\n$('Convert to Markdown')\n .all()\n .map(item =>`<content>\\n${item.json.markdown.substr(0, 25_000)}\\n</content>`)\n .join('\\n')\n}}\n</contents>",
"messages": {
"messageValues": [
{
"type": "HumanMessagePromptTemplate",
"message": "=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\n - Be highly organized.\n - Suggest solutions that I didn't think about.\n - Be proactive and anticipate my needs.\n - Treat me as an expert in all subject matter.\n - Mistakes erode my trust, so be accurate and thorough.\n - Provide detailed explanations, I'm comfortable with lots of detail.\n - Value good arguments over authorities, the source is irrelevant.\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\n - You may use high levels of speculation or prediction, just flag it for me."
}
]
},
"promptType": "define",
"hasOutputParser": true
},
"executeOnce": true,
"typeVersion": 1.5
},
{
"id": "9296a787-3226-44fe-8118-f84dda8e5167",
"name": "Generate Report",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
480,
180
],
"parameters": {
"options": {
"waitForSubWorkflow": false
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{\n{\n ...Object.assign({}, $json),\n query: $('JobType Router').first().json.data.query\n}\n}}",
"jobType": "deepresearch_report",
"requestId": "={{ $('JobType Router').first().json.requestId }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "471f9b9f-f331-4652-95de-1ec7136ea692",
"name": "Generate Learnings",
"type": "n8n-nodes-base.executeWorkflow",
"position": [
-380,
180
],
"parameters": {
"mode": "each",
"options": {
"waitForSubWorkflow": true
},
"workflowId": {
"__rl": true,
"mode": "id",
"value": "={{ $workflow.id }}"
},
"workflowInputs": {
"value": {
"data": "={{ $json }}",
"jobType": "deepresearch_learnings",
"requestId": "={{ $('JobType Router').first().json.requestId }}"
},
"schema": [
{
"id": "requestId",
"display": true,
"removed": false,
"required": false,
"displayName": "requestId",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "jobType",
"type": "string",
"display": true,
"removed": false,
"required": false,
"displayName": "jobType",
"defaultMatch": false,
"canBeUsedToMatch": true
},
{
"id": "data",
"type": "object",
"display": true,
"removed": false,
"required": false,
"displayName": "data",
"defaultMatch": false,
"canBeUsedToMatch": true
}
],
"mappingMode": "defineBelow",
"matchingColumns": [],
"attemptToConvertTypes": false,
"convertFieldsToString": true
}
},
"typeVersion": 1.2
},
{
"id": "2e2fa1e6-9d5c-46ff-985c-58ada1139837",
"name": "Confirmation",
"type": "n8n-nodes-base.form",
"position": [
780,
-420
],
"webhookId": "cf41a176-5d30-4274-955e-b0d5b483d37f",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"buttonLabel": "Done",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>\n<p style=\"text-align:left\">\n<strong style=\"display:block;font-family:sans-serif;font-weight:700;font-size:16px;margin-top:12px;margin-bottom:0;\">Your Report Is On Its Way!</strong>\n<br/>\nDeepResearcher will now work independently to conduct the research and the compiled report will be uploaded to the following Notion page below when finished.\n<br/><br/>\nPlease click the \"Done\" button to complete the form.\n</p>\n<hr style=\"display:block;margin-top:16px;margin-bottom:0\" />"
},
"formFields": {
"values": [
{
"html": "=<a href=\"{{ $json.url }}\" style=\"text-decoration:none\" target=\"_blank\">\n<div style=\"display:flex;text-align:left;font-family:sans-serif;\">\n <div style=\"width:150px;height:150px;padding:12px;\">\n <img src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/cajjymprexcoesu4gg9g\" width=\"100%\" />\n </div>\n <div style=\"width:100%;padding:12px;\">\n <div style=\"font-size:14px;font-weight:700\">{{ $json.name }}</div>\n <div style=\"font-size:12px;color:#666\">\n {{ $json.property_description }}\n </div>\n </div>\n</div>\n</a>",
"fieldType": "html",
"fieldLabel": "message"
}
]
}
},
"typeVersion": 1
},
{
"id": "97ba63b7-9248-4945-9c67-1ea114b20dc5",
"name": "Research Request",
"type": "n8n-nodes-base.form",
"position": [
-1560,
-460
],
"webhookId": "d4ea875f-83cb-49a8-8992-c08b4114c9bd",
"parameters": {
"options": {
"formTitle": "DeepResearcher",
"formDescription": "=<img\n src=\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\"\n width=\"100%\"\n style=\"border:1px solid #ccc\"\n/>"
},
"formFields": {
"values": [
{
"fieldType": "textarea",
"fieldLabel": "What would you like to research?",
"requiredField": true
},
{
"html": "<div class=\"form-group\" style=\"margin-bottom:16px;\">\n <label class=\"form-label\" for=\"field-1\">\n Enter research depth (Default 1)\n </label>\n <p style=\"font-size:12px;color:#666;text-align:left\">\n This value determines how many sub-queries to generate.\n </p>\n <input\n class=\"form-input\"\n type=\"range\"\n id=\"field-1\"\n name=\"field-1\"\n value=\"1\"\n step=\"1\"\n max=\"3\"\n min=\"0\"\n list=\"depth-markers\"\n >\n <datalist\n id=\"depth-markers\"\n style=\"display: flex;\n flex-direction: row;\n justify-content: space-between;\n writing-mode: horizontal-tb;\n margin-top: -10px;\n text-align: center;\n font-size: 10px;\n margin-left: 16px;\n margin-right: 16px;\"\n >\n <option style=\"padding:0\" value=\"0\" label=\"0\"></option>\n <option style=\"padding:0\" value=\"1\" label=\"1\"></option>\n <option style=\"padding:0\" value=\"2\" label=\"2\"></option>\n <option style=\"padding:0\" value=\"3\" label=\"3\"></option>\n </datalist>\n</div>",
"fieldType": "html",
"fieldLabel": "Enter research depth (recommended 1-5, default 2)"
},
{
"html": "<div class=\"form-group\" style=\"margin-bottom:16px;\">\n <label class=\"form-label\" for=\"field-2\">\n Enter research breadth (Default 2)\n </label>\n <p style=\"font-size:12px;color:#666;text-align:left\">\n This value determines how many sources to explore.\n </p>\n <input\n class=\"form-input\"\n type=\"range\"\n id=\"field-2\"\n name=\"field-2\"\n value=\"2\"\n step=\"1\"\n max=\"5\"\n min=\"1\"\n list=\"breadth-markers\"\n >\n <datalist\n id=\"breadth-markers\"\n style=\"display: flex;\n flex-direction: row;\n justify-content: space-between;\n writing-mode: horizontal-tb;\n margin-top: -10px;\n text-align: center;\n font-size: 10px;\n margin-left: 16px;\n margin-right: 16px;\"\n >\n <option value=\"1\" label=\"1\"></option>\n <option value=\"2\" label=\"2\"></option>\n <option value=\"3\" label=\"3\"></option>\n <option value=\"4\" label=\"4\"></option>\n <option value=\"5\" label=\"5\"></option>\n </datalist>\n</div>\n\n",
"fieldType": "html",
"fieldLabel": "Enter research breadth (recommended 2-10, default 4)"
},
{
"fieldType": "dropdown",
"fieldLabel": "={{ \"\" }}",
"multiselect": true,
"fieldOptions": {
"values": [
{
"option": "=I understand higher depth and breath values I've selected may incur longer wait times and higher costs. I acknowledging this and wish to proceed with the research request."
}
]
},
"requiredField": true
}
]
}
},
"typeVersion": 1
},
{
"id": "b3d11997-9c8c-4b72-b750-4fc22a2247b7",
"name": "Valid Blocks",
"type": "n8n-nodes-base.filter",
"position": [
740,
1600
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "f68cefe0-e109-4d41-9aa3-043f3bc6c449",
"operator": {
"type": "string",
"operation": "notExists",
"singleValue": true
},
"leftValue": "={{ $json.error }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "ff90a1c1-b357-4012-8964-e007bef0c9db",
"name": "Sticky Note12",
"type": "n8n-nodes-base.stickyNote",
"position": [
620,
1400
],
"parameters": {
"color": 7,
"width": 580,
"height": 580,
"content": "## 12. Append URL Sources List\n[Read more about the Code node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code)\n\nFor our source URLs, we'll manually compose the Notion blocks for them - this is because there's usually a lot of them! We'll then append to the end of the other blocks."
},
"typeVersion": 1
},
{
"id": "f6b50f06-6122-494c-bdb8-4215f473a27d",
"name": "Append Blocks",
"type": "n8n-nodes-base.merge",
"position": [
1000,
1760
],
"parameters": {},
"typeVersion": 3
},
{
"id": "ca39272a-828e-4314-80da-05dd5fd7b2e3",
"name": "URL Sources to Lists",
"type": "n8n-nodes-base.code",
"position": [
740,
1760
],
"parameters": {
"jsCode": "const urls = $('JobType Router').first().json.data.all_urls;\nconst chunksize = 50;\nconst splits = Math.max(1, Math.floor(urls.length/chunksize));\n\nconst blocks = Array(splits).fill(0)\n .map((_, idx) => {\n const block = urls\n .slice(\n idx * chunksize, \n (idx * chunksize) + chunksize - 1\n )\n .map(url => {\n return {\n object: \"block\",\n type: \"bulleted_list_item\",\n bulleted_list_item: {\n rich_text: [\n { type: \"text\", text: { content: url } }\n ]\n }\n }\n });\n return { json: { block } }\n });\n\nreturn [\n { json: {\n block:[{\n \"object\": \"block\",\n \"type\": \"heading_2\",\n \"heading_2\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": \"Sources\"\n }\n }\n ]\n }\n }]\n } },\n ...blocks\n];"
},
"typeVersion": 2
},
{
"id": "e2e2b07a-4039-4859-b60c-f51982475282",
"name": "Has Results?",
"type": "n8n-nodes-base.if",
"position": [
240,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "9ef8d40c-1289-4654-9022-4a07f7102555",
"operator": {
"type": "array",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json.results }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "5662dbf2-8877-4e83-982c-6bc5968b8835",
"name": "Empty Response",
"type": "n8n-nodes-base.set",
"position": [
1040,
1120
],
"parameters": {
"options": {},
"assignments": {
"assignments": [
{
"id": "1de40158-338b-4db3-9e22-6fd63b21f825",
"name": "ResearchGoal",
"type": "string",
"value": "={{ $('Item Ref').first().json.researchGoal }}"
},
{
"id": "9f59a2d4-5e5a-4d0b-8adf-2832ce746f0f",
"name": "learnings",
"type": "array",
"value": "={{ [] }}"
},
{
"id": "972ab5f5-0537-4755-afcb-d1db4f09ad60",
"name": "followUpQuestions",
"type": "array",
"value": "={{ [] }}"
},
{
"id": "90cef471-76b0-465d-91a4-a0e256335cd3",
"name": "urls",
"type": "array",
"value": "={{ [] }}"
}
]
}
},
"typeVersion": 3.4
},
{
"id": "678b6af5-da74-4421-8c3d-0166aa52efd9",
"name": "Has Content?",
"type": "n8n-nodes-base.if",
"position": [
880,
980
],
"parameters": {
"options": {},
"conditions": {
"options": {
"version": 2,
"leftValue": "",
"caseSensitive": true,
"typeValidation": "strict"
},
"combinator": "and",
"conditions": [
{
"id": "1ef1039a-4792-47f9-860b-d2ffcffd7129",
"operator": {
"type": "object",
"operation": "notEmpty",
"singleValue": true
},
"leftValue": "={{ $json }}",
"rightValue": ""
}
]
}
},
"typeVersion": 2.2
},
{
"id": "233f2e19-b4f2-4de3-8002-f79f3c01c1e7",
"name": "Sticky Note13",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1820,
-240
],
"parameters": {
"color": 5,
"width": 300,
"height": 100,
"content": "### Not using forms?\nFeel free ot swap this out for chat or even webhooks to fit your existing workflows."
},
"typeVersion": 1
},
{
"id": "453bb6eb-f2b0-4e21-b647-e095c80b7844",
"name": "Sticky Note14",
"type": "n8n-nodes-base.stickyNote",
"position": [
-1880,
540
],
"parameters": {
"color": 5,
"width": 460,
"height": 240,
"content": "### 🚏 The Subworkflow Event Pattern \nIf you're new to n8n, this advanced technique might need some explaining but in gist, we're using subworkflows to run different parts of our DeepResearcher workflow as separate executions.\n\n* Necessary to implement the recursive loop mechanism needed to enable this workflow.\n* Negates the need to split this workflow into multiple templates.\n* Great generally for building high performance n8n workflows (a topic for a future post!)"
},
"typeVersion": 1
},
{
"id": "289cbe4c-c2e3-46b7-8799-197a7d78ab2a",
"name": "Sticky Note15",
"type": "n8n-nodes-base.stickyNote",
"position": [
720,
-60
],
"parameters": {
"color": 5,
"width": 340,
"height": 200,
"content": "### Recursive Looping\nThe recursive looping implemented for this workflow is an advanced item-linking technique. It works by specifically controlling which nodes \"execute once\" vs\" execute for each item\" because of this becareful of ermoving nodes! Always check the settings of the node you're replacing and ensure the settings match. "
},
"typeVersion": 1
},
{
"id": "b95ffdcd-c0d1-4a12-a7a9-24135db7b467",
"name": "Combine & Send back to Loop",
"type": "n8n-nodes-base.aggregate",
"position": [
-240,
820
],
"parameters": {
"options": {},
"aggregate": "aggregateAllItemData"
},
"typeVersion": 1
},
{
"id": "f0a48ab5-70b9-49dc-a153-61e573803d1e",
"name": "For Each Block...",
"type": "n8n-nodes-base.splitInBatches",
"position": [
1440,
1600
],
"parameters": {
"options": {}
},
"typeVersion": 3
},
{
"id": "2fd17fbd-005d-446e-b014-0da190cd3114",
"name": "Sticky Note16",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2420,
-920
],
"parameters": {
"width": 520,
"height": 1060,
"content": "## n8n DeepResearcher\n### This template attempts to replicate OpenAI's DeepResearch feature which, at time of writing, is only available to their pro subscribers.\n\nThough the inner workings of DeepResearch have not been made public, it is presumed the feature relies on the ability to deep search the web, scrape web content and invoking reasoning models to generate reports. All of which n8n is really good at!\n\n### How it works\n* A form is used to first capture the user's research query and how deep they'd like the researcher to go.\n* Once submitted, a blank Notion page is created which will later hold the final report and the researcher gets to work.\n* The user's query goes through a recursive series of web serches and web scraping to collect data on the research topic to generate partial learnings.\n* Once complete, all learnings are combined and given to a reasoning LLM to generate the final report.\n* The report is then written to the placeholder Notion page created earlier. \n\n### How to use\n* Duplicate this Notion database to use with this template: https://jimleuk.notion.site/19486dd60c0c80da9cb7eb1468ea9afd?v=19486dd60c0c805c8e0c000ce8c87acf\n* Sign-up for [APIFY.com](https://www.apify.com?fpr=414q6) API Key for web search and scraping services.\n* Ensure you have access to OpenAI's o3-mini model. Alternatively, switch this out for o1 series.\n* You must publish this workflow and ensure the form url is publically accessible.\n\n### On Depth & Breadth Configuration\nFor more detailed reports, increase depth and breadth but be warned the workflow will take a exponentially more time and money to complete. The defaults are usually good enough.\n\nDepth=1 & Breadth=2 - will take about 10 - 15mins.\nDepth=1 & Breadth=4 - will take about 30 - 40mins.\nDpeth=3 & Breadth=5 - will take about 2 - 5 hours!\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"
},
"typeVersion": 1
},
{
"id": "ac6f2604-7439-4524-a27e-2f031ebce089",
"name": "Sticky Note17",
"type": "n8n-nodes-base.stickyNote",
"position": [
-2420,
-1180
],
"parameters": {
"color": 7,
"width": 520,
"height": 240,
"content": "![](https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw#full-width)"
},
"typeVersion": 1
},
{
"id": "40e3a0cf-7710-4537-b147-37ba8945fdbc",
"name": "Sticky Note18",
"type": "n8n-nodes-base.stickyNote",
"position": [
-120,
960
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE APIFY CREDENTIAL HERE!"
},
"typeVersion": 1
},
{
"id": "5a96ecc2-eeea-4c33-b299-7a7f2ca7559c",
"name": "Sticky Note19",
"type": "n8n-nodes-base.stickyNote",
"position": [
520,
960
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE APIFY CREDENTIAL HERE!"
},
"typeVersion": 1
},
{
"id": "23bca6e2-e16a-48a4-a7fc-96ce25846764",
"name": "Sticky Note20",
"type": "n8n-nodes-base.stickyNote",
"position": [
1640,
1740
],
"parameters": {
"width": 180,
"height": 260,
"content": "\n\n\n\n\n\n\n\n\n\n\n\n### UPDATE NOTION CREDENTIAL HERE!"
},
"typeVersion": 1
}
],
"pinData": {},
"connections": {
"Item Ref": {
"main": [
[
{
"node": "Web Search",
"type": "main",
"index": 0
}
]
]
},
"Create Row": {
"main": [
[
{
"node": "Initiate DeepResearch",
"type": "main",
"index": 0
}
]
]
},
"Web Search": {
"main": [
[
{
"node": "Top 5 Organic Results",
"type": "main",
"index": 0
}
]
]
},
"Valid Pages": {
"main": [
[
{
"node": "Has Content?",
"type": "main",
"index": 0
}
]
]
},
"Confirmation": {
"main": [
[
{
"node": "End Form",
"type": "main",
"index": 0
}
]
]
},
"Has Content?": {
"main": [
[
{
"node": "Convert to Markdown",
"type": "main",
"index": 0
}
],
[
{
"node": "Empty Response",
"type": "main",
"index": 0
}
]
]
},
"Has Results?": {
"main": [
[
{
"node": "URLs to Items",
"type": "main",
"index": 0
}
],
[
{
"node": "Empty Response",
"type": "main",
"index": 0
}
]
]
},
"Valid Blocks": {
"main": [
[
{
"node": "Append Blocks",
"type": "main",
"index": 0
}
]
]
},
"Append Blocks": {
"main": [
[
{
"node": "For Each Block...",
"type": "main",
"index": 0
}
]
]
},
"HTML to Array": {
"main": [
[
{
"node": "Tags to Items",
"type": "main",
"index": 0
}
]
]
},
"Page Contents": {
"main": [
[
{
"node": "Valid Pages",
"type": "main",
"index": 0
}
],
[]
]
},
"SERP to Items": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Set Variables": {
"main": [
[
{
"node": "Clarifying Questions",
"type": "main",
"index": 0
}
]
]
},
"Tags to Items": {
"main": [
[
{
"node": "Notion Block Generator",
"type": "main",
"index": 0
}
]
]
},
"URLs to Items": {
"main": [
[
{
"node": "Page Contents",
"type": "main",
"index": 0
}
]
]
},
"Empty Response": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Execution Data": {
"main": [
[
{
"node": "JobType Router",
"type": "main",
"index": 0
}
]
]
},
"JobType Router": {
"main": [
[
{
"node": "Get Existing Row",
"type": "main",
"index": 0
}
],
[
{
"node": "Generate SERP Queries",
"type": "main",
"index": 0
}
],
[
{
"node": "Get Existing Row1",
"type": "main",
"index": 0
}
]
]
},
"Convert to HTML": {
"main": [
[
{
"node": "HTML to Array",
"type": "main",
"index": 0
}
]
]
},
"Set In-Progress": {
"main": [
[
{
"node": "Set Initial Query",
"type": "main",
"index": 0
}
]
]
},
"Get Existing Row": {
"main": [
[
{
"node": "Set In-Progress",
"type": "main",
"index": 0
}
]
]
},
"Research Request": {
"main": [
[
{
"node": "Set Variables",
"type": "main",
"index": 0
}
]
]
},
"Results to Items": {
"main": [
[
{
"node": "Set Next Queries",
"type": "main",
"index": 0
}
]
]
},
"Set Next Queries": {
"main": [
[
{
"node": "Generate Learnings",
"type": "main",
"index": 0
}
]
]
},
"Feedback to Items": {
"main": [
[
{
"node": "For Each Question...",
"type": "main",
"index": 0
}
]
]
},
"For Each Block...": {
"main": [
[
{
"node": "Set Done",
"type": "main",
"index": 0
}
],
[
{
"node": "Upload to Notion Page",
"type": "main",
"index": 0
}
]
]
},
"For Each Query...": {
"main": [
[
{
"node": "Combine & Send back to Loop",
"type": "main",
"index": 0
}
],
[
{
"node": "Item Ref",
"type": "main",
"index": 0
}
]
]
},
"Get Existing Row1": {
"main": [
[
{
"node": "DeepResearch Report",
"type": "main",
"index": 0
}
]
]
},
"Get Initial Query": {
"main": [
[
{
"node": "Report Page Generator",
"type": "main",
"index": 0
}
]
]
},
"Is Depth Reached?": {
"main": [
[
{
"node": "Get Research Results",
"type": "main",
"index": 0
}
],
[
{
"node": "DeepResearch Results",
"type": "main",
"index": 0
}
]
]
},
"OpenAI Chat Model": {
"ai_languageModel": [
[
{
"node": "DeepResearch Learnings",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Parse JSON blocks": {
"main": [
[
{
"node": "Valid Blocks",
"type": "main",
"index": 0
},
{
"node": "URL Sources to Lists",
"type": "main",
"index": 0
}
]
]
},
"Set Initial Query": {
"main": [
[
{
"node": "Generate Learnings",
"type": "main",
"index": 0
}
]
]
},
"Accumulate Results": {
"main": [
[
{
"node": "Is Depth Reached?",
"type": "main",
"index": 0
}
]
]
},
"Generate Learnings": {
"main": [
[
{
"node": "Accumulate Results",
"type": "main",
"index": 0
}
]
]
},
"On form submission": {
"main": [
[
{
"node": "Research Request",
"type": "main",
"index": 0
}
]
]
},
"OpenAI Chat Model1": {
"ai_languageModel": [
[
{
"node": "DeepResearch Report",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model2": {
"ai_languageModel": [
[
{
"node": "Clarifying Questions",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model3": {
"ai_languageModel": [
[
{
"node": "Generate SERP Queries",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"OpenAI Chat Model4": {
"ai_languageModel": [
[
{
"node": "Report Page Generator",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Convert to Markdown": {
"main": [
[
{
"node": "DeepResearch Learnings",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Report": {
"main": [
[
{
"node": "Convert to HTML",
"type": "main",
"index": 0
}
]
]
},
"Clarifying Questions": {
"main": [
[
{
"node": "Feedback to Items",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Results": {
"main": [
[
{
"node": "Results to Items",
"type": "main",
"index": 0
}
]
]
},
"For Each Question...": {
"main": [
[
{
"node": "Get Initial Query",
"type": "main",
"index": 0
}
],
[
{
"node": "Ask Clarity Questions",
"type": "main",
"index": 0
}
]
]
},
"Get Research Results": {
"main": [
[
{
"node": "Generate Report",
"type": "main",
"index": 0
}
]
]
},
"URL Sources to Lists": {
"main": [
[
{
"node": "Append Blocks",
"type": "main",
"index": 1
}
]
]
},
"Ask Clarity Questions": {
"main": [
[
{
"node": "For Each Question...",
"type": "main",
"index": 0
}
]
]
},
"Generate SERP Queries": {
"main": [
[
{
"node": "SERP to Items",
"type": "main",
"index": 0
}
]
]
},
"Initiate DeepResearch": {
"main": [
[
{
"node": "Confirmation",
"type": "main",
"index": 0
}
]
]
},
"Report Page Generator": {
"main": [
[
{
"node": "Create Row",
"type": "main",
"index": 0
}
]
]
},
"Top 5 Organic Results": {
"main": [
[
{
"node": "Has Results?",
"type": "main",
"index": 0
}
]
]
},
"Upload to Notion Page": {
"main": [
[
{
"node": "For Each Block...",
"type": "main",
"index": 0
}
],
[]
]
},
"DeepResearch Learnings": {
"main": [
[
{
"node": "Research Goal + Learnings",
"type": "main",
"index": 0
}
]
]
},
"Notion Block Generator": {
"main": [
[
{
"node": "Parse JSON blocks",
"type": "main",
"index": 0
}
]
]
},
"DeepResearch Subworkflow": {
"main": [
[
{
"node": "Execution Data",
"type": "main",
"index": 0
}
]
]
},
"Google Gemini Chat Model": {
"ai_languageModel": [
[
{
"node": "Notion Block Generator",
"type": "ai_languageModel",
"index": 0
}
]
]
},
"Structured Output Parser": {
"ai_outputParser": [
[
{
"node": "DeepResearch Learnings",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Research Goal + Learnings": {
"main": [
[
{
"node": "For Each Query...",
"type": "main",
"index": 0
}
]
]
},
"Structured Output Parser1": {
"ai_outputParser": [
[
{
"node": "Clarifying Questions",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Structured Output Parser2": {
"ai_outputParser": [
[
{
"node": "Generate SERP Queries",
"type": "ai_outputParser",
"index": 0
}
]
]
},
"Structured Output Parser4": {
"ai_outputParser": [
[
{
"node": "Report Page Generator",
"type": "ai_outputParser",
"index": 0
}
]
]
}
}
}