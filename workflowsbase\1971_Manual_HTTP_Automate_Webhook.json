{"id": "rLoXUoKSZ4a9XUAv", "meta": {"instanceId": "7599ed929ea25767a019b87ecbc83b90e16a268cb51892887b450656ac4518a2"}, "name": "My workflow 6", "tags": [], "nodes": [{"id": "dad32e79-af45-4255-90d9-845a5357395a", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-580, 920], "parameters": {}, "typeVersion": 1}, {"id": "1cadea33-7c6a-4282-be84-e127fc7437c2", "name": "Extract Pages From PDF1", "type": "@custom-js/n8n-nodes-pdf-toolkit.ExtractPages", "position": [-140, 920], "parameters": {"pageRange": "2-3", "field_name": "=data"}, "credentials": {"customJsApi": {"id": "h29wo2anYKdANAzm", "name": "CustomJS account"}}, "typeVersion": 1}, {"id": "a8554e6b-6e2d-4a26-909c-25e74e618480", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-360, 920], "parameters": {"url": "https://www.sldttc.org/allpdf/***********.pdf", "options": {}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "82cbb24e-e907-419a-97d6-bdb577269927", "connections": {"HTTP Request": {"main": [[{"node": "Extract Pages From PDF1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}