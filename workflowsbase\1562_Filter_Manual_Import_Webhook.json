{"id": "NLVfecejH0cTtcdd", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "Import CSV from URL to GoogleSheet", "tags": [], "nodes": [{"id": "90cced3d-b03b-4b51-b1f7-4cbd2dac25eb", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [860, 380], "parameters": {}, "typeVersion": 1}, {"id": "df9519b6-937e-4a9e-bdb9-86fb722ca3c1", "name": "Upload to spreadsheet", "type": "n8n-nodes-base.googleSheets", "position": [1880, 380], "parameters": {"columns": {"value": {}, "schema": [{"id": "unique_key", "type": "string", "display": true, "removed": false, "required": false, "displayName": "unique_key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "﻿country", "type": "string", "display": true, "required": false, "displayName": "﻿country", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "country_code", "type": "string", "display": true, "required": false, "displayName": "country_code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "year_week", "type": "string", "display": true, "required": false, "displayName": "year_week", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "level", "type": "string", "display": true, "required": false, "displayName": "level", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "region", "type": "string", "display": true, "required": false, "displayName": "region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "region_name", "type": "string", "display": true, "required": false, "displayName": "region_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "new_cases", "type": "string", "display": true, "required": false, "displayName": "new_cases", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tests_done", "type": "string", "display": true, "required": false, "displayName": "tests_done", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "population", "type": "string", "display": true, "required": false, "displayName": "population", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "testing_rate", "type": "string", "display": true, "required": false, "displayName": "testing_rate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "positivity_rate", "type": "string", "display": true, "required": false, "displayName": "positivity_rate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "testing_data_source", "type": "string", "display": true, "required": false, "displayName": "testing_data_source", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["unique_key"]}, "options": {"cellFormat": "USER_ENTERED"}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/13YYuEJ1cDf-t8P2MSTFWnnNHCreQ6Zo8oPSp7WeNnbY/edit#gid=*********", "cachedResultName": "COVID-weekly"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/13YYuEJ1cDf-t8P2MSTFWnnNHCreQ6Zo8oPSp7WeNnbY"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "54", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "8298b29e-8784-4e15-902f-dc073fa73668", "name": "Add unique field", "type": "n8n-nodes-base.set", "position": [1460, 380], "parameters": {"fields": {"values": [{"name": "unique_key", "stringValue": "={{ $json.country_code }}-{{ $json.year_week }}"}]}, "options": {}}, "typeVersion": 3}, {"id": "b71bb998-4df2-4311-ae98-42c3e5e41d58", "name": "Import CSV", "type": "n8n-nodes-base.spreadsheetFile", "position": [1260, 380], "parameters": {"options": {"headerRow": true}, "fileFormat": "csv"}, "typeVersion": 2}, {"id": "********-3995-46d4-ac8f-3408cbaed657", "name": "Download CSV", "type": "n8n-nodes-base.httpRequest", "position": [1060, 380], "parameters": {"url": "https://opendata.ecdc.europa.eu/covid19/testing/csv/data.csv", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.1}, {"id": "b1a78d2e-1a8b-4d98-b130-080b3017192d", "name": "Keep only DACH in 2023", "type": "n8n-nodes-base.filter", "position": [1680, 380], "parameters": {"conditions": {"string": [{"value1": "={{ $json.year_week }}", "value2": "2023", "operation": "startsWith"}], "boolean": [{"value1": "={{ ['DE', 'AT', 'CH'].includes($json.country_code )}}", "value2": true}]}}, "typeVersion": 1}, {"id": "c5a3af9b-30a0-4337-bbb7-cff54007b22f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1620, 241], "parameters": {"width": 460, "height": 293, "content": "### Google API has rate-limits for read and write operations, that's why we take only a subset of the data\n\nTo import the whole dataset please add Split In Batches and a Wait node with a sufficient delay."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d49a3f54-a422-4e76-b410-f8c12b4dd78b", "connections": {"Import CSV": {"main": [[{"node": "Add unique field", "type": "main", "index": 0}]]}, "Download CSV": {"main": [[{"node": "Import CSV", "type": "main", "index": 0}]]}, "Add unique field": {"main": [[{"node": "Keep only DACH in 2023", "type": "main", "index": 0}]]}, "Keep only DACH in 2023": {"main": [[{"node": "Upload to spreadsheet", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Download CSV", "type": "main", "index": 0}]]}}}