{"id": "LMMle9xFHQxXUWQy", "meta": {"instanceId": "7d362a334cd7fabe145eb8ec1b9c6b483cd4fa9315ab54f45d181e73340a0ebc", "templateCredsSetupCompleted": true}, "name": "Airtable markdown to html", "tags": [], "nodes": [{"id": "ae8fad3b-7108-4a7a-a6d6-13e249c41ad7", "name": "Check if it's 1 record or all records - Airtable", "type": "n8n-nodes-base.if", "position": [1460, 760], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "985d1b48-78d1-4cd8-bd89-c4c2cb8f1e94", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.query.recordId }}", "rightValue": "all"}]}}, "typeVersion": 2}, {"id": "c2a99d03-3570-4956-8e1e-06d6d72af7cd", "name": "Get single record from airtable", "type": "n8n-nodes-base.airtable", "position": [1660, 740], "parameters": {"id": "={{ $json.query.recordId }}", "base": {"__rl": true, "mode": "list", "value": "appk2iZaWQLO3Tqvx", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx", "cachedResultName": "360Creators"}, "table": {"__rl": true, "mode": "list", "value": "tblbYRGFgCo9u2VpB", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx/tblbYRGFgCo9u2VpB", "cachedResultName": "📺 Videos"}, "options": {}, "operation": "get"}, "credentials": {"airtableTokenApi": {"id": "1fljBv63tzQeX9rd", "name": "🏰 360Creators"}}, "typeVersion": 2}, {"id": "73c07061-d6e5-4cb1-9aaf-8a906bc6fd28", "name": "Convert markdown to HTML1", "type": "n8n-nodes-base.markdown", "position": [1860, 740], "parameters": {"mode": "markdownToHtml", "options": {"simplifiedAutoLink": true}, "markdown": "={{ $json['📥 Video Description'] }}"}, "typeVersion": 1}, {"id": "ce9751b6-484d-4b31-9357-002059d7348a", "name": "Convert markdown to HTML2", "type": "n8n-nodes-base.markdown", "position": [1860, 960], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json[\"📥 Video Description\"] }}"}, "typeVersion": 1}, {"id": "d713dd7d-8381-441b-8a4f-a0ce184abc31", "name": "Update single record in airtable", "type": "n8n-nodes-base.airtable", "position": [2080, 740], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appk2iZaWQLO3Tqvx", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx", "cachedResultName": "360Creators"}, "table": {"__rl": true, "mode": "list", "value": "tblbYRGFgCo9u2VpB", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx/tblbYRGFgCo9u2VpB", "cachedResultName": "📺 Videos"}, "columns": {"value": {"id": "={{ $json.id }}", "Video description HTML": "={{ $json.data }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "📥 Video Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Video Thumbnail", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Thumbnail", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Video Description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Meta Description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Meta Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Publish Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚡️ Publish Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Color [low priority]", "type": "options", "display": true, "options": [{"name": "#f6a31b", "value": "#f6a31b"}, {"name": "#B3212C", "value": "#B3212C"}], "removed": false, "readOnly": false, "required": false, "displayName": "📥 Color [low priority]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Youtube url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚡️ Youtube url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Vimeo url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Vimeo url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Calculation", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Calculation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Youtube url, autoplay", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Youtube url, autoplay", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 videos.360creators.com url [backup]", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 videos.360creators.com url [backup]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Webflow url", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Webflow url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Slug", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Slug", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🏘️ Creator", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🏘️ Creator", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tools with url | video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Tools with url | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Video Description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Timestamps | video description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Timestamps | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🛠 Tools", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🛠 Tools", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #Tools | video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ #Tools | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tool Categories | video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Tool Categories | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Created", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "💬 Channels", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "💬 Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Slug", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Slug", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🚀 Publish", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "🚀 Publish", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Channels", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tools Categories | Video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Tools Categories | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #Tool | Video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ #Tool | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #ToolCategory | Video Description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ #ToolCategory | Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Unpublished", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Unpublished", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚖️ Market", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚖️ Market", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market company | Video Description lookup", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Market company | Video Description lookup", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market company | Video description arrayflatten", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Market company | Video description arrayflatten", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market listing arrayjoin | Video description", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "⚡️ Market listing arrayjoin | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description | formula v3", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Description | formula v3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description referral formula", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Description referral formula", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📺 Video description market listing lookup", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "📺 Video description market listing lookup", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📺 Video description market listing formula", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "📺 Video description market listing formula", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name Short [backup]", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name Short [backup]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🗃️ File Resources", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🗃️ File Resources", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Calculation 2", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Calculation 2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video description HTML", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Video description HTML", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Convert video description to HTML", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "Convert video description to HTML", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "1fljBv63tzQeX9rd", "name": "🏰 360Creators"}}, "typeVersion": 2}, {"id": "1b836b94-cedb-4a1b-9e7c-5ef0978578a5", "name": "Update all records in airtable", "type": "n8n-nodes-base.airtable", "position": [2080, 960], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appk2iZaWQLO3Tqvx", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx", "cachedResultName": "360Creators"}, "table": {"__rl": true, "mode": "list", "value": "tblbYRGFgCo9u2VpB", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx/tblbYRGFgCo9u2VpB", "cachedResultName": "📺 Videos"}, "columns": {"value": {"id": "={{ $json.id }}", "Unpublished": false, "Video description HTML": "={{ $json.data }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "📥 Video Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Video Thumbnail", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Thumbnail", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Video Description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Meta Description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Meta Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Publish Date", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚡️ Publish Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Color [low priority]", "type": "options", "display": true, "options": [{"name": "#f6a31b", "value": "#f6a31b"}, {"name": "#B3212C", "value": "#B3212C"}], "removed": false, "readOnly": false, "required": false, "displayName": "📥 Color [low priority]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Youtube url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚡️ Youtube url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Vimeo url", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Vimeo url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Calculation", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Calculation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Youtube url, autoplay", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Youtube url, autoplay", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 videos.360creators.com url [backup]", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 videos.360creators.com url [backup]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Webflow url", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Webflow url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Slug", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Slug", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🏘️ Creator", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🏘️ Creator", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tools with url | video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Tools with url | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Video Description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📥 Timestamps | video description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "📥 Timestamps | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🛠 Tools", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🛠 Tools", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #Tools | video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ #Tools | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tool Categories | video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Tool Categories | video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "💬 Channels", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "💬 Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Slug", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Slug", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🚀 Publish", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "🚀 Publish", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Channels", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Channels", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Tools Categories | Video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Tools Categories | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #Tool | Video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ #Tool | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ #ToolCategory | Video Description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ #ToolCategory | Video Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Unpublished", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Unpublished", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚖️ Market", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "⚖️ Market", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market company | Video Description lookup", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Market company | Video Description lookup", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market company | Video description arrayflatten", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Market company | Video description arrayflatten", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "⚡️ Market listing arrayjoin | Video description", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "⚡️ Market listing arrayjoin | Video description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description | formula v3", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Description | formula v3", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description referral formula", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Description referral formula", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📺 Video description market listing lookup", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "📺 Video description market listing lookup", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "📺 Video description market listing formula", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "📺 Video description market listing formula", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Name Short [backup]", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name Short [backup]", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "🗃️ File Resources", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "🗃️ File Resources", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Calculation 2", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Calculation 2", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Video description HTML", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Video description HTML", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Convert video description to HTML", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Convert video description to HTML", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "1fljBv63tzQeX9rd", "name": "🏰 360Creators"}}, "typeVersion": 2}, {"id": "1167e05e-998d-44ca-bd28-09c1a74d18b3", "name": "Get all records from airtable", "type": "n8n-nodes-base.airtable", "position": [1660, 960], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appk2iZaWQLO3Tqvx", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx", "cachedResultName": "360Creators"}, "limit": 3, "table": {"__rl": true, "mode": "list", "value": "tblbYRGFgCo9u2VpB", "cachedResultUrl": "https://airtable.com/appk2iZaWQLO3Tqvx/tblbYRGFgCo9u2VpB", "cachedResultName": "📺 Videos"}, "options": {}, "operation": "search", "returnAll": false}, "credentials": {"airtableTokenApi": {"id": "1fljBv63tzQeX9rd", "name": "🏰 360Creators"}}, "typeVersion": 2}, {"id": "5cffbbdb-936c-4aa9-8e2d-9b8868f7db03", "name": "Airtable sync video description", "type": "n8n-nodes-base.webhook", "position": [1260, 760], "webhookId": "848644e5-6b1d-42b3-9259-5828c29780a8", "parameters": {"path": "848644e5-6b1d-42b3-9259-5828c29780a8", "options": {}}, "typeVersion": 2}, {"id": "6a084927-2cd8-40f9-8072-093a4847af6a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1400, 540], "parameters": {"content": "# Tutorial\n[Youtube video](https://www.youtube.com/watch?v=PAoxZjICd7o)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5dd4fedb-7108-4641-890b-8f7f76d7684a", "connections": {"Convert markdown to HTML1": {"main": [[{"node": "Update single record in airtable", "type": "main", "index": 0}]]}, "Convert markdown to HTML2": {"main": [[{"node": "Update all records in airtable", "type": "main", "index": 0}]]}, "Get all records from airtable": {"main": [[{"node": "Convert markdown to HTML2", "type": "main", "index": 0}]]}, "Airtable sync video description": {"main": [[{"node": "Check if it's 1 record or all records - Airtable", "type": "main", "index": 0}]]}, "Get single record from airtable": {"main": [[{"node": "Convert markdown to HTML1", "type": "main", "index": 0}]]}, "Check if it's 1 record or all records - Airtable": {"main": [[{"node": "Get single record from airtable", "type": "main", "index": 0}], [{"node": "Get all records from airtable", "type": "main", "index": 0}]]}}}