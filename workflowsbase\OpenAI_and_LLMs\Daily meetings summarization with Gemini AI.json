{"id": "jAML9xW28lOdsObH", "meta": {"instanceId": "be04c66ddabda64dad2c5d4c4611c3879370cfcff746359dfed22dbbfaacfc1a", "templateCredsSetupCompleted": true}, "name": "Daily meetings summarization with Gemini AI", "tags": [], "nodes": [{"id": "2f5c6f8b-023a-4fc0-8684-66d7f743af0a", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [100, 380], "parameters": {"color": 4, "width": 217.47708894878716, "height": 233, "content": "### Gemini Flash model a base"}, "typeVersion": 1}, {"id": "8c159251-d78c-4f18-a886-b930194e6459", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [600, 40], "parameters": {"color": 4, "width": 223.7196765498655, "height": 236.66152029520293, "content": "### Send the response from AI back to slack channel\n"}, "typeVersion": 1}, {"id": "ee7164d8-f257-4e47-9867-239440153fd4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, -20], "parameters": {"color": 4, "width": 561, "height": 360, "content": "## Trigger the task daily, receive the meetings data, process the data and return response for sending\n\n\n\n\n\n\n\n\n\n\n\nNo memory assigned to the model since the model is running one task and doesn't need a followup, then send the data to the user."}, "typeVersion": 1}, {"id": "30ac78b7-08ba-4df9-a67c-e6825a9de380", "name": "Send response back to slack channel", "type": "n8n-nodes-base.slack", "position": [660, 100], "webhookId": "636ae330-cc22-408b-b6a5-caf02e48897f", "parameters": {"text": "=Gemini : {{ $json.output.removeMarkdown() }} ", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C07QMTJHR0A", "cachedResultName": "ai-chat-gemini"}, "otherOptions": {"mrkdwn": true, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "DFQMzAsWKIdZFCR4", "name": "Slack account - iKemo"}}, "typeVersion": 2.1}, {"id": "938738d6-1e2e-4e93-a5bf-70d11fd4fd32", "name": "Google Calendar - Get Events", "type": "n8n-nodes-base.googleCalendarTool", "position": [400, 460], "parameters": {"options": {"timeMax": "={{ $fromAI('end_date') }}", "timeMin": "={{ $fromAI('start_date') }}"}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll", "descriptionType": "manual", "toolDescription": "Use this tool when you’re asked to retrieve events data."}, "credentials": {"googleCalendarOAuth2Api": {"id": "R2W7XHvEyQgyykI0", "name": "Google Calendar - <PERSON>"}}, "typeVersion": 1.2}, {"id": "2290c30e-9e9f-471a-a882-df6856a1dd9d", "name": "Calendar AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [240, 100], "parameters": {"text": "=summarize today's meetings.\nstartdate = {{ $now.format('yyyy-MM-dd 00:00:00') }}\nenddate = {{ $now.format('yyyy-MM-dd 23:59:59') }}", "options": {"systemMessage": "=You are a Google Calendar assistant.\nYour primary goal is to assist the user in managing their calendar effectively using Event Retrieval tool. \nAlways base your responses on the current date: \n{{ DateTime.local().toFormat('cccc d LLLL yyyy') }}.\nGeneral Guidelines:\nAlways mention all meetings attendees\nTool: Event Retrieval\nFormat the date range:\nstart_date: Start date and time in YYYY-MM-DD HH:mm:ss.\nend_date: End date and time in YYYY-MM-DD HH:mm:ss.\n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "dd63bab9-0f95-4b84-8bbd-26a1f91fe635", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [20, 100], "parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "typeVersion": 1.2}, {"id": "06b9ecd2-83e0-498f-ad79-fbc89242a6f0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [340, 380], "parameters": {"color": 4, "width": 221.**************, "height": 233, "content": "### Access Google Calendar and fetch all the data"}, "typeVersion": 1}, {"id": "********-2af8-4507-80a9-fc0aad171169", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [160, 480], "parameters": {"options": {}, "modelName": "models/gemini-1.5-flash-latest"}, "credentials": {"googlePalmApi": {"id": "3BBJHhMKD8W8VfL4", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e517b214-b0e5-4119-8aaf-77ee0655dd78", "connections": {"Schedule Trigger": {"main": [[{"node": "Calendar AI Agent", "type": "main", "index": 0}]]}, "Calendar AI Agent": {"main": [[{"node": "Send response back to slack channel", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Calendar AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Calendar - Get Events": {"ai_tool": [[{"node": "Calendar AI Agent", "type": "ai_tool", "index": 0}]]}}}