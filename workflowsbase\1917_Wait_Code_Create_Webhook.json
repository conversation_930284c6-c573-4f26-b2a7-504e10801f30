{"id": "mN7jDJoWHtJuyKpS", "meta": {"instanceId": "1e003a7ea4715b6b35e9947791386a7d07edf3b5bf8d4c9b7ee4fdcbec0447d7"}, "name": "Generate Graphic Wallpaper with Midjourney, GPT-4o-mini and Canvas APIs", "tags": [], "nodes": [{"id": "11cef766-dd10-46ea-98cf-11eb8d95e157", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [280, 80], "parameters": {"width": 520, "height": 200, "content": "## Generate Graphic Wallpaper with Midjourney, GPT-4o-mini and Canvas APIs\nWe design this workflow with PiAPI APIs and Canvas API with the purpose to  produce a visually compelling image with resonant copy to spark emotional connection. 🙌 \nWish you make a fantastic generation with our workflow! "}, "typeVersion": 1}, {"id": "ba7143d7-442d-4153-9cfd-bb36448d4c91", "name": "Midjourney Generator", "type": "n8n-nodes-base.httpRequest", "position": [1200, 320], "parameters": {"url": "https://api.piapi.ai/api/v1/task", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $json.prompt }}\",\n    \"aspect_ratio\": \"1:1\",\n    \"process_mode\": \"turbo\",\n    \"skip_prompt_check\": false\n  }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "117b5929-e98c-456a-9bfd-fe1deee77abc", "name": "When clicking Test workflow", "type": "n8n-nodes-base.manualTrigger", "position": [300, 320], "parameters": {}, "typeVersion": 1}, {"id": "dfcf5c57-536c-4fb4-967b-24fd375db57c", "name": "Get Prompt", "type": "n8n-nodes-base.code", "position": [960, 320], "parameters": {"jsCode": "const image_prompt=$('Basic Params').first().json.image_prompt;\nconst show_prompt =$input.first().json.choices[0].message.content;\n\nconst prompt = image_prompt.replace(/'xxx'/, `'${show_prompt}'`)\nreturn {show_prompt,prompt};"}, "typeVersion": 2}, {"id": "1c641437-de26-4e55-9b34-0cb13d8d1cd3", "name": "Get Midjourney Task", "type": "n8n-nodes-base.httpRequest", "position": [1140, 580], "parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "={{ $('Basic Params').item.json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "38fda20c-fef6-484c-ac75-c8f2fbaaca15", "name": "Wait for Midjourney Generation", "type": "n8n-nodes-base.wait", "position": [940, 580], "webhookId": "af79053d-1291-4dd2-889e-4593dbbb2512", "parameters": {}, "typeVersion": 1.1}, {"id": "1d5eaf9a-caf8-4b08-a35c-281b400c9198", "name": "Determine Whether the Image URL was Fetched", "type": "n8n-nodes-base.if", "position": [1340, 580], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "e97a02cc-8d1d-4500-bce5-0a296c792b76", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}, {"id": "50b63a7a-52b5-4766-a859-96ac1ff949ec", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "failed"}]}}, "typeVersion": 2.2}, {"id": "983acf91-c5ba-4335-b43e-d7a8a1a6b918", "name": "Check Image  Generation Status", "type": "n8n-nodes-base.switch", "position": [1520, 320], "parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f61ee56-4ebe-411f-95e6-b47d9741e7a2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "completed"}]}}]}, "options": {}}, "typeVersion": 3.2}, {"id": "50455a13-5914-4f96-b977-d1c6461807bc", "name": "Design in Canvas", "type": "n8n-nodes-base.httpRequest", "position": [1920, 320], "parameters": {"url": "https://api.canvas.switchboard.ai", "method": "POST", "options": {}, "jsonBody": "={\n    \"template\": \"social-3-1\",\n    \"sizes\": [\n        {\n         \"width\": 1000,\n         \"height\": 1500\n        }\n    ],\n    \"elements\": {\n        \"text1\": {\n            \"text\": \"{{ $('Get Prompt').item.json.show_prompt.replace(/;/g, \";\\\\n \")}}\"\n        },\n         \"rectangle1\": {\n                 \"fillColor\": \"#fff\"\n             },\n        \"image1\": {\n            \"url\": \"{{ $json.data.output.temporary_image_urls[0] }}\"\n        }\n    }\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "45ba3916-2f10-497d-815b-7ffc9b69001f"}]}}, "typeVersion": 4.2}, {"id": "0fd5d40c-aebc-4ec3-b9b5-027126b39452", "name": "Get Image Url", "type": "n8n-nodes-base.set", "position": [1720, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d52d19d1-3a37-47bb-ad23-e809323c0c54", "name": "data.output.temporary_image_urls", "type": "array", "value": "={{ $json.data.output.temporary_image_urls }}"}, {"id": "49bed53e-675d-4ea0-947c-ffcbfae0ee97", "name": "data.output.image_url", "type": "string", "value": "={{ $json.data.output.image_url }}"}]}}, "typeVersion": 3.4}, {"id": "e36d4bd6-2d34-447c-984e-515c48f3632e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [280, 500], "parameters": {"width": 580, "height": 280, "content": "## Basic Params\nIn **Basic Params** node, you need to fill in your PiAPI key which you could check in [My Account](https://piapi.ai/workspace/my-account) on [PiAPI](https://piapi.ai). Other information you need to provide is listed as follow📝: \n1. Theme. The theme refers to the topic that you want to talk about when you start your generation.\n2. Scenario. The scenario text usually describe your status about your feeling.\n3. Style. The style of the image.\n4. Example. The text example shown to LLM to describe a style t you want to generate.\n5. Image prompt. Image prompt is usually about the context of the image that you want to generate."}, "typeVersion": 1}, {"id": "1e04fe5d-a1f4-4fe9-a3d2-9c6aeaf05d96", "name": "Gpt-4o-mini API", "type": "n8n-nodes-base.httpRequest", "position": [740, 320], "parameters": {"url": "https://api.piapi.ai/v1/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n    \"model\": \"gpt-4o-mini\",\n    \"messages\": [\n       {\n          \"role\": \"system\",\n          \"content\": \"You are a helpful assistant.\"\n        },\n        {\n          \"role\": \"user\",\n          \"content\": \"Please {{ $json.style }}, based on the theme of {{ $json.theme }} and the scenario of {{ $json.scenario }}, according to the output case and language context. Examples are  {{ $json.example }},Return a sentence directly, nothing else,Don't add a serial number, just a prompt that can be used for input,Do not add any quotation marks.\"}\n      ]\n  }", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Basic Params').first().json['x-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "4eaf0f57-9458-42cb-b736-c737f134320b", "name": "Basic Params", "type": "n8n-nodes-base.set", "position": [540, 320], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n  \"x-api-key\":\"\",\n  \"theme\": \"Hope\",\n  \"scenario\": \"Don't know about the future, confused and feel lost about AI agent\",\n  \"style\":\"Cinematic Grandeur,Sci-Tech Aesthetic, 3D style\",\n  \"example\":\"1. March. Because of your faith, it will happen.2. Something in me will save me.3. To everyone carrying a heavy heart in silence. You are going to be okay.4. Tomorrow will be better.\",\n  \"image_prompt\":\"A cinematic sci-fi metropolis where Deep Neural Nets control a hyper-connected society. Holographic interfaces glow in the air as robotic agents move among humans, symbolizing Industry 4.0. The scene contrasts organic human emotion with cold machine precision, rendered in a hyper-realistic 3D style with futuristic lighting. Epic wide shots showcase the grandeur of this civilization’s industrial evolution.\"\n}\n"}, "typeVersion": 3.4}, {"id": "6750b606-4222-4cda-bcaa-5d4f2b4f9ec8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1680, 560], "parameters": {"width": 380, "height": 200, "content": "## Design in Canvas API Node\nWe make a final design with Canvas API. \nYou could check the node code to make a template design more efficiently in Canvas.\nAlso you could make various artworks with template library in Canvas. \nYou could modify node parameters to or add more nodes to make more artworks in one role."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "09b5a8e6-bdc8-47bb-a9e3-95b090ff3f13", "connections": {"Get Prompt": {"main": [[{"node": "Midjourney Generator", "type": "main", "index": 0}]]}, "Basic Params": {"main": [[{"node": "Gpt-4o-mini API", "type": "main", "index": 0}]]}, "Get Image Url": {"main": [[{"node": "Design in Canvas", "type": "main", "index": 0}]]}, "Gpt-4o-mini API": {"main": [[{"node": "Get Prompt", "type": "main", "index": 0}]]}, "Get Midjourney Task": {"main": [[{"node": "Determine Whether the Image URL was Fetched", "type": "main", "index": 0}]]}, "Midjourney Generator": {"main": [[{"node": "Wait for Midjourney Generation", "type": "main", "index": 0}]]}, "When clicking Test workflow": {"main": [[{"node": "Basic Params", "type": "main", "index": 0}]]}, "Check Image  Generation Status": {"main": [[{"node": "Get Image Url", "type": "main", "index": 0}]]}, "Wait for Midjourney Generation": {"main": [[{"node": "Get Midjourney Task", "type": "main", "index": 0}]]}, "Determine Whether the Image URL was Fetched": {"main": [[{"node": "Check Image  Generation Status", "type": "main", "index": 0}], [{"node": "Wait for Midjourney Generation", "type": "main", "index": 0}]]}}}