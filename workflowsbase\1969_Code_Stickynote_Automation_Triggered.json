{"id": "r3qHlCVCczqTw3pP", "meta": {"instanceId": "1bc0f4fa5e7d17ac362404cbb49337e51e5061e019cfa24022a8667c1f1ce287"}, "name": "Zip multiple files", "tags": [], "nodes": [{"id": "8de50ed2-b298-4701-8747-f6c7158fa15f", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 0], "parameters": {}, "typeVersion": 1}, {"id": "5e03dfdd-696e-4a04-99e9-4094ae4371ac", "name": "Compression", "type": "n8n-nodes-base.compression", "position": [460, 0], "parameters": {"fileName": "=data{{$now.format('yyyy-MM-dd-tt')}}.zip", "operation": "compress", "binaryPropertyName": "={{ $json.binary_keys }}"}, "typeVersion": 1.1}, {"id": "e25abf55-fb05-47d0-ba65-9b4e2f08d856", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -100], "parameters": {"height": 360, "content": "## About\nUse me as modular workflow. Instead of building me fixed in your workflow. Just call me when you need me.\n\n\n## Input\nInput can be multiple files \n-imgaes\n-pdfs\n-xlsx,csv....\n\n## Output\nSingle zip file"}, "typeVersion": 1}, {"id": "db7b6475-25b5-44de-b37e-70b75c91455e", "name": "Prepare Output", "type": "n8n-nodes-base.set", "position": [680, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b0c3c3db-584a-48c9-b0ca-7f527d35f5a4", "name": "fileName", "type": "string", "value": "={{ $binary.data.fileName.replaceAll(\" \",\"\") }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "6cf6b9ba-e5a3-4d5f-a539-e84d839f7e99", "name": "Code Magic", "type": "n8n-nodes-base.code", "position": [240, 0], "parameters": {"jsCode": "let binaries = {}, binary_keys = [];\n\nfor (const [index, inputItem] of Object.entries($input.all())) {\n  binaries[`data_${index}`] = inputItem.binary.data;\n  binary_keys.push(`data_${index}`);\n}\n\nreturn [{\n    json: {\n        binary_keys: binary_keys.join(',')\n    },\n    binary: binaries\n}];\n"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "16f64706-0a2a-4f9f-a96f-f149a4874ae4", "connections": {"Code Magic": {"main": [[{"node": "Compression", "type": "main", "index": 0}]]}, "Compression": {"main": [[{"node": "Prepare Output", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Code Magic", "type": "main", "index": 0}]]}}}