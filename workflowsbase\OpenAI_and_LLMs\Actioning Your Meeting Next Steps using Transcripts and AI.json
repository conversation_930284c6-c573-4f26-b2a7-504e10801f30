{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "bec5c6c1-52d4-4665-b814-56a6bb82ea6b", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [800, 660], "parameters": {"options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "d3e057d1-df44-4ac3-ac46-fc2b04e3de78", "name": "Get Meeting ConferenceRecords", "type": "n8n-nodes-base.httpRequest", "position": [20, 580], "parameters": {"url": "https://meet.googleapis.com/v2/conferenceRecords", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "filter", "value": "=space.meeting_code={{ $json.conferenceData.conferenceId }}"}]}, "nodeCredentialType": "googleOAuth2Api"}, "credentials": {"googleOAuth2Api": {"id": "kgVOfvlBIWTWXthG", "name": "Google Meets Oauth2 API"}}, "typeVersion": 4.2}, {"id": "831668fd-04ab-4144-bec0-c733902f2a13", "name": "Get Meeting Transcript Location", "type": "n8n-nodes-base.httpRequest", "position": [200, 580], "parameters": {"url": "=https://meet.googleapis.com/v2/{{ $json.conferenceRecords[0].name }}/transcripts", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "googleOAuth2Api"}, "credentials": {"googleOAuth2Api": {"id": "kgVOfvlBIWTWXthG", "name": "Google Meets Oauth2 API"}}, "typeVersion": 4.2}, {"id": "0a1c3386-1456-4abd-a67c-4f2084efb1f1", "name": "Get Transcript File", "type": "n8n-nodes-base.googleDrive", "position": [380, 580], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $json.docsDestination.document }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "application/pdf"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "yOwz41gMQclOadgu", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "40d1e969-3a04-4fb0-98c3-59865f317e07", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-480, 540], "parameters": {}, "typeVersion": 1}, {"id": "1d277cc0-9f51-43a2-9d17-17d535b4dd53", "name": "PDF Loader", "type": "n8n-nodes-base.extractFromFile", "position": [660, 520], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "08b2d0ce-0f59-45d8-b010-53910a1bc746", "name": "Get Calendar Event", "type": "n8n-nodes-base.googleCalendar", "position": [-280, 540], "parameters": {"eventId": "abc123", "options": {}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "n8n-events"}, "operation": "get"}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.1}, {"id": "35a68444-15da-4b6e-a3c8-d296971b0fc0", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1040, 660], "parameters": {"jsonSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"summary\": { \"type\": \"string\" },\n \"highlights\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"attendee\": { \"type\": \"string\" },\n \"message\": { \"type\": \"string\" }\n }\n }\n },\n \"next_steps\": {\n \"type\": \"array\",\n \"items:\": {\n \"type\": \"string\"\n }\n },\n \"meetings_created\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"event_title\": { \"type\": \"string\" },\n \"event_invite_url\": { \"type\" : \"string\" }\n }\n }\n }\n }\n}"}, "typeVersion": 1.1}, {"id": "e73ab051-1763-4130-bf44-f1461886e5f4", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [640, 1200], "parameters": {}, "typeVersion": 1}, {"id": "c940c9e1-8236-45b8-bdb2-39a326004680", "name": "Response", "type": "n8n-nodes-base.set", "position": [1780, 1080], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3c12dc11-0ff3-4c6a-9d67-1454d7b0d16d", "name": "response", "type": "string", "value": "={{ JSON.stringify($('Create Calendar Event1').item.json) }}"}]}}, "typeVersion": 3.3}, {"id": "daa3e96f-bcc1-4f99-a050-c09189041ce5", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [800, 1200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7263764b-8409-4cea-8db3-3278dd7ef9d8", "name": "=route", "type": "string", "value": "={{ $json.route }}"}, {"id": "55c3b207-2e98-4137-8413-f72cbff17986", "name": "query", "type": "object", "value": "={{ $json.query.parseJson() }}"}]}}, "typeVersion": 3.3}, {"id": "4e492c9f-6be3-4b7c-a8f7-e18dd94cd158", "name": "Fallback Response", "type": "n8n-nodes-base.set", "position": [960, 1340], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "{\n \"response\": {\n \"ok\": false,\n \"error\": \"The requested tool was not found or the service may be unavailable. Do not retry.\"\n }\n}\n"}, "typeVersion": 3.3}, {"id": "7af68b6d-75ef-4332-8193-eb810179ec90", "name": "Actions Router", "type": "n8n-nodes-base.switch", "position": [960, 1200], "parameters": {"rules": {"values": [{"outputKey": "meetings.create", "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "meetings.create"}]}, "renameOutput": true}]}, "options": {"fallbackOutput": "extra"}}, "typeVersion": 3}, {"id": "8cc6b737-2867-4fca-93d1-8973f14a9f00", "name": "Get Attendees", "type": "n8n-nodes-base.set", "position": [1440, 1080], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "521823f4-cee1-4f69-82e7-cea9be0dbc41", "name": "attendees", "type": "array", "value": "={{ $('Actions Router').item.json.query.attendees }}"}]}}, "typeVersion": 3.3}, {"id": "1b3bb8f7-3775-48be-8b73-5c9f0db37ebf", "name": "Attendees List", "type": "n8n-nodes-base.splitOut", "position": [1444, 1212], "parameters": {"options": {}, "fieldToSplitOut": "attendees"}, "typeVersion": 1}, {"id": "c285a0fa-4b0b-4775-83bb-5acb597dd9a8", "name": "Add <PERSON> to Invite", "type": "n8n-nodes-base.googleCalendar", "position": [1620, 1080], "parameters": {"eventId": "={{ $('Create Calendar Event1').item.json.id }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "n8n-events"}, "operation": "update", "updateFields": {"attendees": ["={{ $json.name }} <{{ $json.email }}>"]}}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.1}, {"id": "006c2b05-4526-4e7d-b303-0cd72b36b9e8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1180, 940], "parameters": {"color": 7, "width": 756.*************, "height": 445.**************, "content": "## 4. This Tool Creates Calendar Events\nThis tool, given event details and a list of attendees, will create a new Google calendar event and add the attendees to it."}, "typeVersion": 1}, {"id": "512dfd7d-ba06-48e5-b97f-3dfbbfb0023f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-56.**************, 391.**************], "parameters": {"color": 7, "width": 586.*************, "height": 405.*************, "content": "## 1. Retrieve Meeting Transcript\n[Read more about working with HTTP node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest)\n\nThere's no built-in support for Google Meets transcript API however, we can solve this problem with the HTTP node. Note you may also need to setup a separate Google OAuth API Credential to obtain the required scopes."}, "typeVersion": 1}, {"id": "91c5b898-b491-4359-90b4-2b7458cc03c8", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [560, 323.25204909069373], "parameters": {"color": 7, "width": 681.4281346810014, "height": 588.2833041602365, "content": "## 2. Let AI Agent Carry Out Follow-Up Actions\n[Read more about working with AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThe big difference between Basic LLM chains and AI Agents is that AI agents are given the automony to perform actions. Provided the right tool exists, AI Agents can send emails, book flights and even order pizza! Here we're leaving it up to our agent to book any follow-up meetings after the call and invite all interested parties."}, "typeVersion": 1}, {"id": "7df4412d-b82b-4623-8ff5-89f3bd9356d8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [560, 940], "parameters": {"color": 7, "width": 591.4907024073684, "height": 579.2725119898125, "content": "## 3: Using the Custom Workflow Tool\n[Read more about Workflow Triggers](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflowtrigger)\n\nOne common implementation of tool use is to set them up as workflows which are intended triggered via other workflows. With this, we can either build a tool per workflow or for efficiency, take an API approach where multiple tools can exist behind a router (in this case our \"switch\" node).\n\nOur AI agent will therefore only passing through the parameters of the request and won't have to learn/know how to intereact directly with the tools and services."}, "typeVersion": 1}, {"id": "06b0b3ae-344a-4150-9fa1-bdbcfe80b000", "name": "Create Calendar Event1", "type": "n8n-nodes-base.googleCalendar", "position": [1240, 1080], "parameters": {"end": "={{ $json.query.end_date }} {{ $json.query.end_time }}", "start": "={{ $json.query.start_date }} {{ $json.query.start_time }}", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "n8n-events"}, "additionalFields": {"summary": "={{ $json.query.title }}", "attendees": [], "description": "={{ $json.query.description }}"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "kWMxmDbMDDJoYFVK", "name": "Google Calendar account"}}, "typeVersion": 1.1}, {"id": "2e2eec66-a737-48b9-b1ab-264182163dae", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-940, 320], "parameters": {"width": 359.*************, "height": 385.************, "content": "## Try It Out!\n### This workflow does the following:\n* Retrieves a meeting transcript\n* Sends transcript to an AI Agent to parse and carry out follow up actions if necessary.\n* If transcript mentions a follow up meeting is required, the AI Agent will call a tool to create the meeting.\n* Additionally if able, the AI Agent will also assign attendees it thinks should attend the meeting. \n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "3833bb1c-1145-4abd-a371-bce4c0543fb6", "name": "Schedule Meeting", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [920, 740], "parameters": {"name": "create_calendar_event", "fields": {"values": [{"name": "route", "stringValue": "meetings.create"}]}, "workflowId": "={{ $workflow.id }}", "description": "Call this tool to create an calendar event. This tool requires the following object request body.\n```\n{\n \"type\": \"object\",\n \"properties\": {\n \"title\": { \"type\": \"string\" },\n \"description\": { \"type\": \"string\" },\n \"start_date\": { \"type\": \"string\" },\n \"start_time\": { \"type\": \"string\" },\n \"end_date\": { \"type\": \"string\" },\n \"end_time\": { \"type\": \"string\" },\n \"attendees\": {\n \"type\": \"array\",\n \"items\": {\n \"type\": \"object\",\n \"properties\": {\n \"name\": { \"type\": \"string\" },\n \"email\": { \"type\": \"string\" }\n }\n }\n }\n }\n}\n```\nNote that dates are in the format yyyy-MM-dd and times are in the format HH:mm:ss."}, "typeVersion": 1.1}, {"id": "ac955f91-9aa1-4ce8-9a5a-740c4d48dd18", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [820, 520], "parameters": {"text": "=system: your role is to help people get the most out of their meetings. You achieve this by helpfully summarising the meeting transcript to pull out useful information and key points of interest and delivery this in note form. You also help carry out any follow-up actions on behalf of the meeting attendees.\n1. Summarise the meeting and highlight any key goals of the meeting.\n2. Identify and list important points mentioned by each attendee. If non-applicable for an attendee, skip and proceed to the next attendee.\n3. Identify and list all next steps agreed by the attendees. If there are none, make a maximum of 3 suggestions based on the transcript instead. Please list the steps even if they've already been actioned.\n4. identify and perform follow-up actions based on a transcript of a meeting. These actions which are allowed are: creating follow-up calendar events if suggested by the attendees.\n\nThe meeting details were as follows:\n* The creator of the meeting was {{ $('Get Calendar Event').item.json[\"creator\"][\"displayName\"] }} <{{ $('Get Calendar Event').item.json[\"creator\"][\"email\"]}}>\n* The attendees were {{ $('Get Calendar Event').item.json[\"attendees\"].map(attendee => `${attendee.display_name} <${attendee.email}>`).join(', ') }}\n* The meeting was scheduled for {{ $('Get Calendar Event').item.json[\"start\"][\"dateTime\"] }}\n\nThe meeting transcript as follows:\n```\n{{ $json[\"text\"] }}\n```", "agent": "openAiFunctionsAgent", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "b6d24f80-9f47-4c54-b84e-23d5de76f027", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-560, 303.2560786071914], "parameters": {"color": 7, "width": 464.50696860436165, "height": 446.9122178333584, "content": "## 1. Get Calendar Event\n[Read more about working with Google Calendar](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecalendar)\n\nIn this demo, we've decided to go with google meet as transcripts are stored in the user google drive. First, we'll need to get the calendar event of which the google meet was attached.\nIf the meet was not arranged through Google calendar, you may need to skip this step and just reference the transcripts in google drive directly."}, "typeVersion": 1}, {"id": "b28e2c8f-7a4e-4ae8-b298-9a78747b81e5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-320, 520], "parameters": {"width": 184.0677386144551, "height": 299.3566512487305, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Set your calendar event ID here."}, "typeVersion": 1}, {"id": "5ffb49d4-6bfd-420e-9c0f-ed73a955bd46", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [180, 820], "parameters": {"color": 5, "width": 349.91944442094535, "height": 80, "content": "### 💡 Can't find your transcript?\nOnly meetings which own and were recorded and had transcription enabled will be available.\n"}, "typeVersion": 1}, {"id": "241ccec3-d8a0-4ca6-9267-31fe6f27aed6", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1200, 1060], "parameters": {"width": 184.0677386144551, "height": 299.3566512487305, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* Set your calendar ID here."}, "typeVersion": 1}], "pinData": {}, "connections": {"PDF Loader": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Actions Router", "type": "main", "index": 0}]]}, "Get Attendees": {"main": [[{"node": "Attendees List", "type": "main", "index": 0}]]}, "Actions Router": {"main": [[{"node": "Create Calendar Event1", "type": "main", "index": 0}], [{"node": "Fallback Response", "type": "main", "index": 0}]]}, "Attendees List": {"main": [[{"node": "Add <PERSON> to Invite", "type": "main", "index": 0}]]}, "Schedule Meeting": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Calendar Event": {"main": [[{"node": "Get Meeting ConferenceRecords", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Transcript File": {"main": [[{"node": "PDF Loader", "type": "main", "index": 0}]]}, "Add Attendee to Invite": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Create Calendar Event1": {"main": [[{"node": "Get Attendees", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Get Meeting ConferenceRecords": {"main": [[{"node": "Get Meeting Transcript Location", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Get Calendar Event", "type": "main", "index": 0}]]}, "Get Meeting Transcript Location": {"main": [[{"node": "Get Transcript File", "type": "main", "index": 0}]]}}}