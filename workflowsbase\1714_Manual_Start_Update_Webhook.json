{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Start", "type": "n8n-nodes-base.start", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [450, 300], "parameters": {"url": "https://unsplash.com/photos/lUDMZUWFUXE/download?ixid=***********************************************&force=true", "options": {}, "responseFormat": "file", "headerParametersUi": {"parameter": []}}, "typeVersion": 1}, {"name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [650, 300], "parameters": {"url": "https://api.twitter.com/1.1/account/update_profile_banner.json", "options": {}, "requestMethod": "POST", "authentication": "oAuth1", "jsonParameters": true, "sendBinaryData": true, "binaryPropertyName": "banner:data"}, "credentials": {"oAuth1Api": {"id": "300", "name": "Unnamed credential"}}, "typeVersion": 1}], "connections": {"HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}}