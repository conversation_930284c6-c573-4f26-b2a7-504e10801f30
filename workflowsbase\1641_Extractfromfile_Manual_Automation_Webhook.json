{"id": "SCUbdpVPX4USbQmr", "meta": {"instanceId": "7c617982c5622c49e1ea217f3ee01da25b7fb42fb9e969ce6e4e1b6c269ad0e5", "templateCredsSetupCompleted": true}, "name": "youtube chapter generator", "tags": [{"id": "637Ga13eORejFbTG", "name": "youtube", "createdAt": "2025-04-06T16:41:11.086Z", "updatedAt": "2025-04-06T16:41:11.086Z"}, {"id": "tfcUyZ2pGsRZFcje", "name": "chapters", "createdAt": "2025-04-06T16:41:28.633Z", "updatedAt": "2025-04-06T16:41:28.633Z"}], "nodes": [{"id": "104fa4ce-cd86-4fff-b31c-0ef37fba6d93", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-800, -120], "parameters": {}, "typeVersion": 1}, {"id": "c3b45480-3098-40f9-a77f-ada54481b590", "name": "Get Caption ID", "type": "n8n-nodes-base.httpRequest", "position": [-200, -120], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId={{ $json.id }}", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api"}, "credentials": {"youTubeOAuth2Api": {"id": "1TkjUqPfFCQ6NzL7", "name": "YouTube account"}}, "typeVersion": 4.2}, {"id": "fe08adc4-e6ef-47ae-a946-1e6d5a85e10e", "name": "Get Captions", "type": "n8n-nodes-base.httpRequest", "position": [20, -120], "parameters": {"url": "=https://www.googleapis.com/youtube/v3/captions/{{ $json.items[0].id }}?tfmt=srt", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api"}, "credentials": {"youTubeOAuth2Api": {"id": "1TkjUqPfFCQ6NzL7", "name": "YouTube account"}}, "typeVersion": 4.2}, {"id": "0e15f334-9ff8-4a7e-85a9-4cf8cf10ea55", "name": "Extract Captions", "type": "n8n-nodes-base.extractFromFile", "position": [240, -120], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "af99a919-7ebc-4a6c-80be-83e2ffa68d05", "name": "Structured Captions", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [640, 100], "parameters": {"jsonSchemaExample": "{\n\t\"description\": \"California\"\n\t\n}"}, "typeVersion": 1.2}, {"id": "414a41a2-0715-4a57-a606-9f3678b2472a", "name": "Get Video Meta Data", "type": "n8n-nodes-base.youTube", "position": [-420, -120], "parameters": {"options": {}, "videoId": "={{ $json.video_id }}", "resource": "video", "operation": "get"}, "credentials": {"youTubeOAuth2Api": {"id": "1TkjUqPfFCQ6NzL7", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "7304d9b1-5956-41c3-b78a-2c409d0aa726", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [460, 100], "parameters": {"options": {}, "modelName": "models/gemini-1.5-flash-8b-exp-0924"}, "credentials": {"googlePalmApi": {"id": "FshILEOmCAPVoGfW", "name": "Google Gemini(PaLM) Api account 2"}}, "typeVersion": 1}, {"id": "867a6ad6-0712-4fbf-97fd-ab054b783172", "name": "Set Video ID", "type": "n8n-nodes-base.set", "position": [-640, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "568762f7-e496-4550-8567-d49e2ce1676d", "name": "video_id", "type": "string", "value": "r1wqsrW2vmE"}]}}, "typeVersion": 3.4}, {"id": "dcd0c9d7-1a69-45e8-98e9-b7cf7d12734e", "name": "Update Chapters", "type": "n8n-nodes-base.youTube", "position": [940, -120], "parameters": {"title": "={{ $('Get Video Meta Data').item.json.snippet.title }}", "videoId": "={{ $('Get Captions').item.json.items[0].snippet.videoId }}", "resource": "video", "operation": "update", "categoryId": "22", "regionCode": "US", "updateFields": {"description": "={{ $json.output.description }}\nChapters\n{{ $json.output.description }}"}}, "credentials": {"youTubeOAuth2Api": {"id": "1TkjUqPfFCQ6NzL7", "name": "YouTube account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "916629c4-6e49-4432-88e8-626748cb3d24", "name": "Tag Chapters in Description", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [460, -120], "parameters": {"text": "=This is an srt format data. please classify this data into chapters\nbased upon this transcript \n{{ $json.data }}\n{\n\"description\":\"00:00 Introduction\n02:15 Topic One\n05:30 Topic Two\n10:45 Conclusion\"\n}\n", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "b0f56d68-b787-4ccc-8bb5-bdb5b04c3ae4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-680, -200], "parameters": {"width": 1040, "height": 440, "content": "\n## Get Captions"}, "typeVersion": 1}, {"id": "0bcee6b5-0e8b-4f85-8f83-c829e785467a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [378, -200], "parameters": {"color": 4, "width": 420, "height": 440, "content": "## Generate Chapters\n"}, "typeVersion": 1}, {"id": "0f90f6ec-2154-4945-b262-6531fef2334f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, -200], "parameters": {"color": 6, "width": 440, "height": 440, "content": "## Update Description\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "27125160-7c64-4431-b243-832c1ae29d29", "connections": {"Get Captions": {"main": [[{"node": "Extract Captions", "type": "main", "index": 0}]]}, "Set Video ID": {"main": [[{"node": "Get Video Meta Data", "type": "main", "index": 0}]]}, "Get Caption ID": {"main": [[{"node": "Get Captions", "type": "main", "index": 0}]]}, "Extract Captions": {"main": [[{"node": "Tag Chapters in Description", "type": "main", "index": 0}]]}, "Get Video Meta Data": {"main": [[{"node": "Get Caption ID", "type": "main", "index": 0}]]}, "Structured Captions": {"ai_outputParser": [[{"node": "Tag Chapters in Description", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Tag Chapters in Description", "type": "ai_languageModel", "index": 0}]]}, "Tag Chapters in Description": {"main": [[{"node": "Update Chapters", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Video ID", "type": "main", "index": 0}]]}}}