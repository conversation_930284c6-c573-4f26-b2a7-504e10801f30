{"meta": {"instanceId": "67d4d33d8b0ad4e5e12f051d8ad92fc35893d7f48d7f801bc6da4f39967b3592"}, "nodes": [{"id": "30f5203b-469d-4f0c-8493-e8f08e14e4fe", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-560, 440], "parameters": {}, "typeVersion": 1}, {"id": "d16f59dd-f54e-487b-9aac-67f109ba9869", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1000, -280], "parameters": {"color": 7, "width": 727.9032097745135, "height": 110.58643966444157, "content": "# Auto Categorise Outlook Emails with AI\nBuilt by [<PERSON>](https://www.linkedin.com/in/simpsonwayne/) at [nocodecreative.io](https://nocodecreative.io)"}, "typeVersion": 1}, {"id": "4e110412-8530-4322-bc5c-7f9df2b63bcb", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1100, -120], "parameters": {"color": 7, "width": 506.8102696237577, "height": 337.24177957113216, "content": "### Watch Set Up Video 👇\n[![Auto Categorise Outlook Emails with AI](https://vdyfnvnstovfxpabhdjc.supabase.co/storage/v1/object/public/images/Thumbnails/auto-categories-emails.png?t=2024-10-11T09%3A56%3A37.961Z#full-width)](https://www.youtube.com/watch?v=EhRBkkjv_3c)\n\n"}, "typeVersion": 1}, {"id": "9d79875f-148e-46ef-967a-95c07298456d", "name": "Ollama Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "position": [1129, 684], "parameters": {"model": "qwen2.5:14b", "options": {"temperature": 0.2}}, "typeVersion": 1}, {"id": "bcf92a71-ff5f-46a7-bec3-cedb5be2bf98", "name": "Microsoft Outlook10", "type": "n8n-nodes-base.microsoftOutlook", "position": [3020, 8], "parameters": {"folderId": {"__rl": true, "mode": "list", "value": "AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAAAAgFJAAAA", "cachedResultUrl": "https://outlook.office365.com/mail/AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj%2FaQEAnz%2FIOcWBGE2lrVuQgAF6zAAAAgFJAAAA", "cachedResultName": "Junk Email"}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "move"}, "typeVersion": 2}, {"id": "100db1cb-3819-43c7-a74b-5c087ad4f2da", "name": "Microsoft Outlook12", "type": "n8n-nodes-base.microsoftOutlook", "position": [2700, 8], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "d4969259-a3ae-473d-82ef-0c9f7933c899", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "position": [160, 448], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "524f6be3-7708-4aae-b9ab-e0ef8180a627", "name": "Microsoft Outlook13", "type": "n8n-nodes-base.microsoftOutlook", "position": [2700, 188], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "72cb54f3-4e4e-4ad2-8845-11a38fc29f1a", "name": "Microsoft Outlook15", "type": "n8n-nodes-base.microsoftOutlook", "position": [3020, 188], "parameters": {"folderId": {"__rl": true, "mode": "list", "value": "AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAADLJmrBwAAAA==", "cachedResultUrl": "https://outlook.office365.com/mail/AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj%2FaQEAnz%2FIOcWBGE2lrVuQgAF6zAADLJmrBwAAAA%3D%3D", "cachedResultName": "Receipt"}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "move"}, "typeVersion": 2}, {"id": "e4446e84-c05e-4d04-b415-7608e39024ee", "name": "Microsoft Outlook16", "type": "n8n-nodes-base.microsoftOutlook", "position": [2709, 504], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "3ee05cfe-a528-472e-aa3d-c890fd88b6c4", "name": "Microsoft Outlook17", "type": "n8n-nodes-base.microsoftOutlook", "position": [3020, 508], "parameters": {"folderId": {"__rl": true, "mode": "list", "value": "AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAADLJmrCAAAAA==", "cachedResultUrl": "https://outlook.office365.com/mail/AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj%2FaQEAnz%2FIOcWBGE2lrVuQgAF6zAADLJmrCAAAAA%3D%3D", "cachedResultName": "Community"}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "move"}, "typeVersion": 2}, {"id": "2fcecd9e-95cc-489a-b874-699c54518e44", "name": "Microsoft Outlook18", "type": "n8n-nodes-base.microsoftOutlook", "position": [2709, 344], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "41a39309-1a94-461f-9308-63dd5b9a94a7", "name": "Microsoft Outlook19", "type": "n8n-nodes-base.microsoftOutlook", "position": [3020, 348], "parameters": {"folderId": {"__rl": true, "mode": "list", "value": "AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAADLJmrCQAAAA==", "cachedResultUrl": "https://outlook.office365.com/mail/AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj%2FaQEAnz%2FIOcWBGE2lrVuQgAF6zAADLJmrCQAAAA%3D%3D", "cachedResultName": "SaaS"}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "move"}, "typeVersion": 2}, {"id": "ebf606f9-099c-4218-b23b-66e2487262d0", "name": "Markdown1", "type": "n8n-nodes-base.markdown", "notes": "Converts the body of the email to markdown", "position": [420, 468], "parameters": {"html": "={{ $('Loop Over Items1').item.json.body.content }}", "options": {}}, "notesInFlow": true, "typeVersion": 1}, {"id": "ff447dd5-3ef6-4a02-8453-3489af8bf6b5", "name": "varEmal1", "type": "n8n-nodes-base.set", "notes": "Set email fields", "position": [620, 468], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "edb304e1-3e9f-4a77-918c-25646addbc53", "name": "subject", "type": "string", "value": "={{ $json.subject }}"}, {"id": "57a3ef3a-2701-40d9-882f-f43a7219f148", "name": "importance", "type": "string", "value": "={{ $json.importance }}"}, {"id": "d8317f4f-aa0e-4196-89af-cb016765490a", "name": "sender", "type": "object", "value": "={{ $json.sender.emailAddress }}"}, {"id": "908716c8-9ff7-4bdc-a1a3-64227559635e", "name": "from", "type": "object", "value": "={{ $json.from.emailAddress }}"}, {"id": "ce007329-e221-4c5a-8130-2f8e9130160f", "name": "body", "type": "string", "value": "={{ $json.data\n .replace(/<[^>]*>/g, '') // Remove HTML tags\n .replace(/\\[(.*?)\\]\\((.*?)\\)/g, '') // Remove Markdown links like [text](link)\n .replace(/!\\[.*?\\]\\(.*?\\)/g, '') // Remove Markdown images like ![alt](image-link)\n .replace(/\\|/g, '') // Remove table separators \"|\"\n .replace(/-{3,}/g, '') // Remove horizontal rule \"---\"\n .replace(/\\n+/g, ' ') // Remove multiple newlines\n .replace(/([^\\w\\s.,!?@])/g, '') // Remove special characters except essential ones\n .replace(/\\s{2,}/g, ' ') // Replace multiple spaces with a single space\n .trim() // Trim leading/trailing whitespace\n}}\n"}]}}, "typeVersion": 3.4}, {"id": "198524cb-c9f0-4261-8c38-7c878efe7457", "name": "Microsoft Outlook20", "type": "n8n-nodes-base.microsoftOutlook", "position": [2700, 668], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "ec73629c-59ac-4f0e-a432-2c06934952ab", "name": "Microsoft Outlook21", "type": "n8n-nodes-base.microsoftOutlook", "position": [2709, 1044], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "0a19d15c-0cd3-4f26-9be2-4914522751fb", "name": "Filter1", "type": "n8n-nodes-base.filter", "position": [-100, 448], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c8cd6917-f94e-4fb7-8601-b8ed8f1aa8bf", "operator": {"type": "array", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.categories }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "96e6e31c-6306-44a8-a57a-2b5216636b00", "name": "If1", "type": "n8n-nodes-base.if", "notes": "Checks if the email has been read", "position": [3320, 668], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f8cf2a56-cea8-4150-b7a0-048dbda20f2f", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.isRead }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "8a6e0118-abe3-45e2-aefc-94640348b2ec", "name": "Microsoft Outlook22", "type": "n8n-nodes-base.microsoftOutlook", "position": [2709, 864], "parameters": {"messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "update", "updateFields": {"categories": "={{ \n [$('varJSON1').first().json.output.category, $('varJSON1').first().json.output.subCategory]\n .filter(item => item && item.trim() !== \"\")\n .map(item => item.charAt(0).toUpperCase() + item.slice(1))\n}}"}}, "typeVersion": 2}, {"id": "e2d8e7b5-4447-4327-9f4e-b8d52765667e", "name": "Catch Errors1", "type": "n8n-nodes-base.set", "position": [1760, 608], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0dc6d439-60fb-49f6-b4d5-f5cce6f030ad", "name": "error", "type": "string", "value": "={{ $json }}"}]}}, "typeVersion": 3.4}, {"id": "17f6ac43-51e4-4bee-b0d8-13deb3bf3cc9", "name": "varJSON1", "type": "n8n-nodes-base.set", "onError": "continueErrorOutput", "position": [1540, 468], "parameters": {"options": {"ignoreConversionErrors": true}, "assignments": {"assignments": [{"id": "0c52f57f-74eb-4385-ac6b-f3e5f4f50e73", "name": "output", "type": "object", "value": "={{ $json.output.replace(/^.*?({.*}).*$/s, '$1') }}"}]}}, "typeVersion": 3.4}, {"id": "82dd9631-a34b-4d54-be28-6f8dcc3548f0", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-360, 220], "parameters": {"width": 411.91693012378937, "height": 401.49417117683515, "content": "## Outlook Business with filters\nFilters:\n```\nflag/flagStatus eq 'notFlagged' and not categories/any()\n```\n\nThese filters ensure we do not process flagged emails or email that already have a category set."}, "typeVersion": 1}, {"id": "0583e196-37a5-43db-8c0a-aa624029c926", "name": "Microsoft Outlook23", "type": "n8n-nodes-base.microsoftOutlook", "position": [-300, 448], "parameters": {"limit": 1, "fields": ["flag", "from", "importance", "replyTo", "sender", "subject", "toRecipients", "body", "categories", "isRead"], "output": "fields", "options": {}, "filtersUI": {"values": {"filters": {"custom": "flag/flagStatus eq 'notFlagged' and not categories/any()", "foldersToInclude": ["AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAAAAgEMAAAA"]}}}, "operation": "getAll"}, "typeVersion": 2}, {"id": "a9540e6b-929b-4460-8972-93e4d19cd934", "name": "varID & Category1", "type": "n8n-nodes-base.set", "position": [900, 468], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "de2ad4f2-7381-4715-a3f4-59611e161b74", "name": "id", "type": "string", "value": "={{ $('Microsoft Outlook23').item.json.id }}"}, {"id": "458c7a89-e4a3-46d0-8b38-72d87748e306", "name": "category", "type": "string", "value": "\"action\", \"junk\", \"receipt\", \"SaaS\", \"community\", \"business\" or \"other\""}]}}, "typeVersion": 3.4}, {"id": "e6b3b41e-d7d3-4c9b-8189-a005c748ff18", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [360, 348], "parameters": {"color": 6, "width": 418.7820408163265, "height": 301.40952380952365, "content": "## Sanitise Email \nRemoves HTML and useless information in preparation for the AI Agent"}, "typeVersion": 1}, {"id": "f9787a75-526c-4ef1-b0a7-0db7d890ab3f", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [820, 348], "parameters": {"color": 6, "width": 256.16108843537415, "height": 298.37931972789124, "content": "## Modify Categories \nEdit this to customise category selection"}, "typeVersion": 1}, {"id": "50223a01-34cf-4191-9dd7-3dac02a9e945", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [1480, 328], "parameters": {"color": 5, "width": 441.003537414966, "height": 463.0204081632651, "content": "## Convert to JSON\n* Ensures the Agent output to converted to JSON\n* Catches any errors and continues processing"}, "typeVersion": 1}, {"id": "4580c532-96a6-46b4-8922-d79316d1cc01", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2120, 328], "parameters": {"color": 5, "width": 311.71482993197264, "height": 454.93986394557805, "content": "## Switch Categories\nEnsure your categories match the **varID & Category** Edit Fields node"}, "typeVersion": 1}, {"id": "b51a7c34-2a5e-4670-81a4-d1582711c69a", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2629, -76], "parameters": {"color": 4, "width": 251.3480889735252, "height": 1289.0156245602684, "content": "## Set Categories\n"}, "typeVersion": 1}, {"id": "3a7ede7b-539b-49d2-8803-153ca6c9eb69", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [2949, -76], "parameters": {"color": 4, "width": 251.3480889735252, "height": 770.995811762121, "content": "## Move to Folders\n"}, "typeVersion": 1}, {"id": "ee9a9d78-8c07-470a-9d1b-ceddfc8875ca", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [3260, 553], "parameters": {"color": 4, "height": 293.65527013262994, "content": "## Check if email has been read\n\n"}, "typeVersion": 1}, {"id": "c75b9d38-79a7-4be2-a90b-a99da1bbd745", "name": "Microsoft Outlook Move Message1", "type": "n8n-nodes-base.microsoftOutlook", "position": [3609, 604], "parameters": {"folderId": {"__rl": true, "mode": "list", "value": "AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj-aQEAnz-IOcWBGE2lrVuQgAF6zAADLJmrCwAAAA==", "cachedResultUrl": "https://outlook.office365.com/mail/AQMkAGE3ZTU5MGMzLTFkNGItNGQ5Zi04MDQ1LThmNGFlMTVhYjMwYgAuAAAD8UhruVwm402lgPBG2Tj%2FaQEAnz%2FIOcWBGE2lrVuQgAF6zAADLJmrCwAAAA%3D%3D", "cachedResultName": "Actioned"}, "messageId": {"__rl": true, "mode": "id", "value": "={{ $('varID & Category1').item.json.id }}"}, "operation": "move"}, "typeVersion": 2}, {"id": "85ff0348-16dc-46e6-bf70-48a10fe0ded8", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1160, 468], "parameters": {"text": "=Categorise the following email\n<email>\n{{ $('varEmal1').first().json.toJsonString() }}\n</email>\n\nEnsure your final output is valid JSON with no additional text or token in the following format:\n\n{\n \"subject\": \"SUBJECT_LINE\",1\n \"category\": \"CATEGORY\",\n \"subCategory\": \"SUBCATEGORY\", //use sparingly\n \"analysis\": \"ANALYSIS_REASONING\"\n}\n\nRemember you can only use ONE of the following categories {{ $json.category }}. No other categories can be used. Use the subcategory for additional context, for example, if a SaaS email requires action, or if a business email requires action. Do not create any additional subcategories, you can only use ONE of the following {{ $json.category }}.", "options": {"systemMessage": "=You're an AI assistant for a freelance developer, categorizing emails as {{ $json.category }}. Email info is in <email> tags.\n\nCategorization priority:\n\nAction: Needs response or action (includes some SaaS emails), avoid sales email but include enquires.\nJunk: Ads, sales, newsletters, promotions, daily digests, (emojis often indicate junk), phishing, scams, discounts etc.\nReceipt: Any purchase confirmation.\nSaaS: Account/security updates, unless action required, generic SaaS information, usually from a non-personal email address.\nCommunity: Updates, events, forums, everything related to \"community\".\nBusiness: Any communication related to freelance work, usually from a humans email address\nOther: Doesn't fit into any other category.\n\nKey points:\n\nSaaS emails needing action are \"SaaS\" and subcategory \"action\".\nAnalyze the subject, body, email addresses and other data.\nLook for specific keywords and phrases for each category.\nEmail can have 2 categories, primary and sub, for example, \"action\" and \"SaaS\" or \"action\" and \"business\".\nEmails from business development executives are often junk.\n\n\nOutput in valid JSON format:\n{\n\"subject\": \"SUBJECT_LINE\",\n\"category\": \"PRIMARY CATEGORY\",\n\"subCategory\": \"SUBCATEGORY\", //use sparingly\n\"analysis\": \"Brief 1-2 sentence explanation of category choice\"\n}\nNo additional text or tokens outside the JSON.\n\nYou may only use the following categories and subcategories, do not create any more categories or subcategories: {{ $json.category }}"}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "93e7be79-9035-4b58-9a83-b9182a0515f8", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [3989, 564], "parameters": {"numberInputs": 7}, "typeVersion": 3}, {"id": "cbaeaed1-cb09-4614-93f1-3fe349cd0e4e", "name": "Switch1", "type": "n8n-nodes-base.switch", "position": [2220, 488], "parameters": {"rules": {"values": [{"outputKey": "junk", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "junk"}]}, "renameOutput": true}, {"outputKey": "receipt", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0c61c7a8-e8b4-49c5-a96c-402d5eae7089", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "receipt"}]}, "renameOutput": true}, {"outputKey": "SaaS", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "703f65c8-cf4a-47fe-ad1a-a5f6e0412ae7", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "SaaS"}]}, "renameOutput": true}, {"outputKey": "community", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b074d5cd-9215-40df-8877-5df904edc000", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "community"}]}, "renameOutput": true}, {"outputKey": "action", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bece338a-e0c5-43b5-b8cc-41229a374213", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "action"}]}, "renameOutput": true}, {"outputKey": "business", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d6c9751f-0ffa-4041-a579-6957bb9c9296", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output.category }}", "rightValue": "business"}]}, "renameOutput": true}]}, "options": {"ignoreCase": true, "fallbackOutput": "extra"}}, "typeVersion": 3.2}], "pinData": {}, "connections": {"If1": {"main": [[{"node": "Microsoft Outlook Move Message1", "type": "main", "index": 0}], [{"node": "Merge1", "type": "main", "index": 5}]]}, "Merge1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Microsoft Outlook12", "type": "main", "index": 0}], [{"node": "Microsoft Outlook13", "type": "main", "index": 0}], [{"node": "Microsoft Outlook18", "type": "main", "index": 0}], [{"node": "Microsoft Outlook16", "type": "main", "index": 0}], [{"node": "Microsoft Outlook20", "type": "main", "index": 0}], [{"node": "Microsoft Outlook22", "type": "main", "index": 0}], [{"node": "Microsoft Outlook21", "type": "main", "index": 0}]]}, "varEmal1": {"main": [[{"node": "varID & Category1", "type": "main", "index": 0}]]}, "varJSON1": {"main": [[{"node": "Switch1", "type": "main", "index": 0}], [{"node": "Catch Errors1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "varJSON1", "type": "main", "index": 0}]]}, "Markdown1": {"main": [[{"node": "varEmal1", "type": "main", "index": 0}]]}, "Catch Errors1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [null, [{"node": "Markdown1", "type": "main", "index": 0}]]}, "varID & Category1": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Ollama Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Microsoft Outlook10": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Microsoft Outlook12": {"main": [[{"node": "Microsoft Outlook10", "type": "main", "index": 0}]]}, "Microsoft Outlook13": {"main": [[{"node": "Microsoft Outlook15", "type": "main", "index": 0}]]}, "Microsoft Outlook15": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Microsoft Outlook16": {"main": [[{"node": "Microsoft Outlook17", "type": "main", "index": 0}]]}, "Microsoft Outlook17": {"main": [[{"node": "Merge1", "type": "main", "index": 3}]]}, "Microsoft Outlook18": {"main": [[{"node": "Microsoft Outlook19", "type": "main", "index": 0}]]}, "Microsoft Outlook19": {"main": [[{"node": "Merge1", "type": "main", "index": 2}]]}, "Microsoft Outlook20": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Microsoft Outlook21": {"main": [[{"node": "Merge1", "type": "main", "index": 6}]]}, "Microsoft Outlook22": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Microsoft Outlook23": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Microsoft Outlook Move Message1": {"main": [[{"node": "Merge1", "type": "main", "index": 4}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Microsoft Outlook23", "type": "main", "index": 0}]]}}}