{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9"}, "nodes": [{"id": "********-4a1b-439a-a188-c6ee7990bb63", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [860, 260], "parameters": {"options": {}, "operation": "pdf", "binaryPropertyName": "File_Upload"}, "typeVersion": 1}, {"id": "583aff4b-d9f5-44e7-8e91-4938592b5630", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1740, 380], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "3a09afd0-0dce-41fd-bec3-783fcb3d01fc", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1920, 380], "parameters": {"schemaType": "manual", "inputSchema": "{\n \"type\": \"object\",\n \"properties\": {\n \"Name\": { \"type\": \"string\" },\n \"Address\": { \"type\": \"string\" },\n \"Email\": { \"type\": \"string\" },\n \"Telephone\": { \"type\": \"string\" },\n \"Education\": { \"type\": \"string\" },\n \"Skills & Technologies\": { \"type\": \"string\" },\n \"Years of Experience\": { \"type\": \"string\" },\n \"Cover Letter\": { \"type\": \"string\" }\n }\n}"}, "typeVersion": 1.2}, {"id": "541a00d0-1635-48ad-b69e-83b28e178d6e", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1020, 420], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "19e4ad5b-2f96-491c-bcb3-52cca526ff82", "name": "Step 1 of 2 - Upload CV", "type": "n8n-nodes-base.formTrigger", "position": [460, 220], "webhookId": "4cf0f3b7-6282-47af-a7f1-3dfb00a1311d", "parameters": {"options": {"path": "job-application-step1of2", "ignoreBots": true, "buttonLabel": "Submit", "useWorkflowTimezone": true}, "formTitle": "Step 1 of 2: Submit Your CV", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "<PERSON><PERSON>. <PERSON>", "requiredField": true}, {"fieldType": "file", "fieldLabel": "File Upload", "multipleFiles": false, "requiredField": true, "acceptFileTypes": "pdf"}, {"fieldType": "dropdown", "fieldLabel": "Acknowledgement of Terms", "multiselect": true, "fieldOptions": {"values": [{"option": "I agree to the terms & conditions"}]}, "requiredField": true}]}, "responseMode": "lastNode", "formDescription": "Thank you for your interest in applying for Acme Inc. To ensure a speedy process, please ensure you following all instructions and fill out all required inputs.\n\nThis step requires you upload your CV in a password-free PDF document. Any document that is not a CV will be rejected."}, "typeVersion": 2.2}, {"id": "ec54096b-5f9f-444e-87b1-db99197731f1", "name": "Save to Airtable", "type": "n8n-nodes-base.airtable", "position": [2340, 320], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appQ6mE9KSzlvaGDT", "cachedResultUrl": "https://airtable.com/appQ6mE9KSzlvaGDT", "cachedResultName": "Job Applications with AI & Forms"}, "table": {"__rl": true, "mode": "list", "value": "tblUwwRXGnNzesNgr", "cachedResultUrl": "https://airtable.com/appQ6mE9KSzlvaGDT/tblUwwRXGnNzesNgr", "cachedResultName": "Table 1"}, "columns": {"value": {"Name": "={{ $json.output.Name }}", "Email": "={{ $json.output.Email }}", "Address": "={{ $json.output.Address }}", "Education": "={{ $json.output.Education }}", "Telephone": "={{ $json.output.Telephone }}", "Cover Letter": "={{ $json.output['Cover Letter'] }}", "Submitted By": "={{ $('Step 1 of 2 - Upload CV').first().json.Name }}", "Years of Experience": "={{ $json.output['Years of Experience'] }}", "Skills & Technologies": "={{ $json.output['Skills & Technologies'] }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "File", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "File", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Cover Letter", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Cover Letter", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Telephone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Telephone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Education", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Education", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Skills & Technologies", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Skills & Technologies", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Years of Experience", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Years of Experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Submitted By", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Submitted By", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "127965b3-a2c6-443b-942d-8691b5bcb25d", "name": "Classify Document", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [1020, 260], "parameters": {"options": {"fallback": "other"}, "inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "CV or Resume", "description": "This document is a CV or Resume"}]}}, "typeVersion": 1}, {"id": "b82476c8-b285-467f-b344-e1f667f42479", "name": "Upload File to Record", "type": "n8n-nodes-base.httpRequest", "position": [2540, 320], "parameters": {"url": "=https://content.airtable.com/v0/{{ $('Save to Airtable').params.base.value }}/{{ $json.id }}/File/uploadAttachment", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "contentType", "value": "application/pdf"}, {"name": "filename", "value": "={{ $workflow.id }}-{{ $execution.id }}.pdf"}, {"name": "file", "value": "={{ $('Step 1 of 2 - Upload CV').first().binary.File_Upload.data }}"}]}, "nodeCredentialType": "airtableTokenApi"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 4.2}, {"id": "ee6f59ee-781f-4ed4-8cec-b7de70a82dac", "name": "Form Success", "type": "n8n-nodes-base.form", "position": [3900, 320], "webhookId": "4b154ccc-ad54-4cc2-a239-cf8354fc91bf", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Application Success", "completionMessage": "Thank you for completing the application process.\nYour informaion is filed securely and will be reviewed by our team.\n\nWe will be in touch shortly."}, "typeVersion": 1}, {"id": "43d46474-b9f8-4adf-89f8-d4c993641448", "name": "Save to Airtable1", "type": "n8n-nodes-base.airtable", "onError": "continueErrorOutput", "position": [3720, 320], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appQ6mE9KSzlvaGDT", "cachedResultUrl": "https://airtable.com/appQ6mE9KSzlvaGDT", "cachedResultName": "Job Applications with AI & Forms"}, "table": {"__rl": true, "mode": "list", "value": "tblUwwRXGnNzesNgr", "cachedResultUrl": "https://airtable.com/appQ6mE9KSzlvaGDT/tblUwwRXGnNzesNgr", "cachedResultName": "Table 1"}, "columns": {"value": {"Name": "={{ $json.Name }}", "Email": "={{ $json.Email }}", "Address": "={{ $json.Address }}", "Education": "={{ $json.Education }}", "Telephone": "={{ $json.Telephone }}", "Cover Letter": "={{ $json.output['Cover Letter'] }}", "Years of Experience": "={{ $json['Years of Experience'] }}", "Skills & Technologies": "={{ $json['Skills & Technologies'] }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "File", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "File", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Cover Letter", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Cover Letter", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Telephone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Telephone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Education", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Education", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Skills & Technologies", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Skills & Technologies", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Years of Experience", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Years of Experience", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Submitted By", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Submitted By", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Email", "Name"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "Und0frCQ6SNVX3VV", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "********-824c-4354-917c-b18e93178f87", "name": "Step 2 of 2 - Application Form", "type": "n8n-nodes-base.formTrigger", "position": [3520, 320], "webhookId": "db923d6c-ea24-4679-b4ba-d3b142ef8338", "parameters": {"options": {"path": "job-application-step2of2", "ignoreBots": true, "useWorkflowTimezone": true}, "formTitle": "Step 2 of 2: Application Form", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "<PERSON><PERSON>. <PERSON>", "requiredField": true}, {"fieldLabel": "Address", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "requiredField": true}, {"fieldLabel": "Telephone", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Education", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Skills & Technologies", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Years of Experience", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Cover Letter", "requiredField": true}, {"fieldType": "dropdown", "fieldLabel": "Acknowledgement of Terms", "multiselect": true, "fieldOptions": {"values": [{"option": "I agree to consent to the terms and conditions"}]}, "requiredField": true}]}, "formDescription": "This application form prefills using the CV you submitted. Please make any amendments as required and once satisfied, please submit the form to complete the application process."}, "typeVersion": 2.2}, {"id": "1171540b-ebb2-41cb-b9f1-2da335caaece", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [300, 20], "parameters": {"color": 7, "width": 430, "height": 381, "content": "## 1. Application Form To Upload CV\n[Learn more the Form Trigger node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger/)\n\nOur application process starts with a simple file upload to get the applicant's CV for processing."}, "typeVersion": 1}, {"id": "4791901b-31a6-44c3-a1da-9c32b78cf305", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [760, 17.5], "parameters": {"color": 7, "width": 774, "height": 593, "content": "## 2. Document Classifier and ReUpload Form\n[Read more about the Text Classifier](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.text-classifier/)\n\nForm validation remains a critical step and before the introduction of LLMs, classifying document types was a relatively troublesome process. Today, n8n's text classifier node does an excellent job at this task.\n\nContextual validation powered by AI means invalid, incomplete or poorly created applicant CVs can be rejected as a quality check. When this happens in our workflow, we present the user again with the file upload form to retry."}, "typeVersion": 1}, {"id": "4dc1a316-15b7-4568-9910-79b4a7989dcb", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1560, -20], "parameters": {"color": 7, "width": 648, "height": 584, "content": "## 3. Smarter Application Pre-fill with Job Role Context\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nInformation extraction is a logical next step once we have our PDF contents but we can extend further by only extracting data which is relevant to our job post. This ensure the information we extract is always relevant which saves time for the hiring team.\n\nTo achieve this for this demo, I've included the job post in the prompt for the LLM to compare the CV against. The provides the AI enough context to complete the task successfully."}, "typeVersion": 1}, {"id": "76006a7b-32ce-4606-be98-9a7b7b451215", "name": "Application Suitability Agent", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1740, 220], "parameters": {"text": "=Here is the candidate's CV:\n{{ $json.text }}", "messages": {"messageValues": [{"message": "=Extract information from the applicant's CV which is relevant to the job post.\nWhen writing the cover letter, use no more than a few paragraphs. No need to address the hiring company or personnel as this text will be input into an online form.\nUse a formal and professional tone.\nThis is the job post which the cover letter should address:\n\n```\nJob Post: General Operations Manager – Manufacturing Industry\nJob Type: Full-time\nExperience Level: Mid to Senior\n\nAbout Us:\nWe are a forward-thinking manufacturing company committed to innovation, quality, and sustainability. We strive to improve operations, enhance product quality, and implement eco-friendly practices, fostering a productive and collaborative work environment.\n\nJob Description:\nWe are seeking an experienced and dynamic General Operations Manager to lead and optimize our manufacturing processes. The successful candidate will oversee production, enhance efficiency, and implement effective strategies to support our mission. This role is ideal for a seasoned professional with a strong background in operational management and a knack for process improvement.\n\nKey Responsibilities:\n\nOversee and manage production and sales teams across multiple shifts, ensuring seamless 24/6 operations.\nDevelop and implement cost-effective quality control and accountability measures to maintain high manufacturing standards.\nManage inventory and procurement, strategically timing raw material purchases to maximize cost efficiency.\nLead ERP system upgrades or similar digital transformation projects, ensuring timely and budget-friendly execution.\nOptimize credit control and payment terms to improve cash flow while maintaining client relationships.\nAdvocate for sustainable practices, including integrating recycled materials into production processes.\nQualifications:\n\nBachelor's degree in Business Administration or a related field; a Master's in Financial Economics is a plus.\nProven experience in a leadership role within the manufacturing industry.\nExpertise in managing teams, production cycles, and quality assurance.\nProficiency in ERP systems and software such as Stata, Bloomberg Professional, and Thomson Reuters DataStream.\nStrong analytical, decision-making, and organizational skills.\nFamiliarity with capital markets, private equity, or strategic management consulting is a plus.\nPreferred Skills:\n\nAdvanced knowledge of plastics manufacturing, including polyethylene and polypropylene applications.\nExperience in implementing sustainability initiatives and green business practices.\nExcellent communication skills, with a history of collaboration and team-building.\nWhat We Offer:\n\nCompetitive salary and benefits package.\nOpportunities for professional growth and development.\nA collaborative and innovative work environment.\nHow to Apply:\nPlease send your resume and a cover letter highlighting your experience and achievements to [HR Email]. Applications will be reviewed on a rolling basis.\n\nJoin us and drive operational excellence in manufacturing!\n```"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "cfc6a1a1-d42c-49b1-a93b-4a04e7e88521", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2240, 40], "parameters": {"color": 7, "width": 528, "height": 524, "content": "## 4. Save to Applicant Tracking System\n[Read more about the Airtable node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable/)\n\nNext, we can complete our simple data capture by integrating and pushing data to our Applicant Tracking System.\n\nHere, we're using Airtable because we can also store PDF files in our rows.\n\nSee our example Airtable here: [https://airtable.com/appQ6mE9KSzlvaGDT/shrIivfe9qH6YEYAs](https://airtable.com/appQ6mE9KSzlvaGDT/shrIivfe9qH6YEYAs)"}, "typeVersion": 1}, {"id": "8f21067f-a851-4480-84b8-bb37eddfd7d6", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2780, 40], "parameters": {"color": 7, "width": 575.8190139534884, "height": 524, "content": "## 5. Redirect to Application Form\n[Learn more about Form Ending](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/#form-ending)\n\nFinally to complete the form flow for step 1 of 2, we'll use a form ending node to redirect the user to step 2 of 2.\n\nHere, we using query params as part of our redirect as this will pre-fill the form fields in step 2 of 2."}, "typeVersion": 1}, {"id": "2ba9cea6-173f-45be-bdda-a6ef061d91f5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [3380, 40], "parameters": {"color": 7, "width": 788, "height": 524, "content": "## 6. Application Form to Amend Details\n[Learn more about Forms](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form)\n\nIn the second part of the application process, applicants are presented with a form containing multiple fields to complete. This step has often been a source of frustration for many, as they end up duplicating information that’s already in their CV.\n\nIf our redirection with prefilled data works as intended, this issue will be resolved, as the fields will be automatically populated by our LLM during step 1 of 2. This also allows candidates the opportunity to review and refine the application fields before submitting."}, "typeVersion": 1}, {"id": "5add63c3-19d4-4035-a718-b1c125a03c67", "name": "File Upload Retry", "type": "n8n-nodes-base.form", "position": [1340, 380], "webhookId": "c3e8dc74-c6e0-4d0b-acf3-8bbc2f7c9ae2", "parameters": {"options": {"formTitle": "Please upload a CV", "formDescription": "Unfortunately, we were unable to process your previous file upload.\n\nTo continue, you must upload a valid CV in PDF format. "}, "formFields": {"values": [{"fieldType": "file", "fieldLabel": "File Upload", "multipleFiles": false, "requiredField": true, "acceptFileTypes": "pdf"}]}}, "typeVersion": 1}, {"id": "cc27b37f-26f5-47c3-9ac2-4412352070e5", "name": "Redirect To Step 2 of 2", "type": "n8n-nodes-base.form", "position": [3120, 280], "webhookId": "1b6e2375-e21d-4e4f-a44e-3ef0de95320e", "parameters": {"operation": "completion", "redirectUrl": "=https://<HOST>/form/job-application-step2of2?{{ $('Application Suitability Agent').first().json.output.urlEncode() }}", "respondWith": "redirect"}, "typeVersion": 1}, {"id": "1cba63a9-57cb-4e17-a601-2bd64fb50dbf", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-140, -240], "parameters": {"width": 420, "height": 640, "content": "## Try It Out!\n\n### This n8n template combines form file uploads with AI components to create a simple but effective job application submission flow.\nIt's a perfect low-cost solution without the bells and whistles of the surface yet is highly advanced with its use of AI.\n\n### How it works\n* The application submission process starts with an n8n form trigger to accept CV files in the form of PDFs.\n* The PDF is validated using the text classifier node to determine if it is a valid CV.\n* A basic LLM node is used to extract relevant information from the CV as data capture. A copy of the original job post is included to ensure relevancy.\n* Applicant's data is then sent to an ATS for processing. For our demo, we used airtable because we could attach PDFs to rows.\n* Finally, a second form trigger is used to allow the applicant to amend any of the generated application fields.\n\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!\n"}, "typeVersion": 1}, {"id": "4289f9f2-2286-4bc7-9045-c645ff292341", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3060, 460], "parameters": {"height": 120, "content": "### 🚨 Change Base URL here!\nThis redirect requires the full base URL, change it to the host of your n8n instance."}, "typeVersion": 1}, {"id": "fca5b2ab-291f-4ac3-b4e1-13911666359f", "name": "Submission Success", "type": "n8n-nodes-base.form", "position": [2900, 280], "webhookId": "f3b12dd4-dd5d-47a9-8bc1-727ba7eb5d15", "parameters": {"options": {"formTitle": "CV Submission Successful!", "buttonLabel": "Continue", "formDescription": "We'll now redirect you to step 2 of 2 - our Application form. Please note, some fields will be prefilled with information from your CV. Feel free to amend this information as needed."}, "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Acknowledgement", "multiselect": true, "fieldOptions": {"values": [{"option": "I understand my CV will be held soley for purpose of application and for no more than 90 days."}]}, "requiredField": true}]}}, "typeVersion": 1}], "pinData": {}, "connections": {"Save to Airtable": {"main": [[{"node": "Upload File to Record", "type": "main", "index": 0}]]}, "Classify Document": {"main": [[{"node": "Application Suitability Agent", "type": "main", "index": 0}], [{"node": "File Upload Retry", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Classify Document", "type": "main", "index": 0}]]}, "File Upload Retry": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Save to Airtable1": {"main": [[{"node": "Form Success", "type": "main", "index": 0}], [{"node": "Form Success", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Application Suitability Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Classify Document", "type": "ai_languageModel", "index": 0}]]}, "Submission Success": {"main": [[{"node": "Redirect To Step 2 of 2", "type": "main", "index": 0}]]}, "Upload File to Record": {"main": [[{"node": "Submission Success", "type": "main", "index": 0}]]}, "Step 1 of 2 - Upload CV": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Application Suitability Agent", "type": "ai_outputParser", "index": 0}]]}, "Application Suitability Agent": {"main": [[{"node": "Save to Airtable", "type": "main", "index": 0}]]}, "Step 2 of 2 - Application Form": {"main": [[{"node": "Save to Airtable1", "type": "main", "index": 0}]]}}}