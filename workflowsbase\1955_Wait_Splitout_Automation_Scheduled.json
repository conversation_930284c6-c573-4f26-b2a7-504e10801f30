{"id": "piapgd2e6zmzFxAq", "meta": {"instanceId": "81e51e738261a40c0e64dd3936c24cbb416bdb576f8302713e8725b56c039235", "templateCredsSetupCompleted": true}, "name": "HDW Lead Geländewagen", "tags": [], "nodes": [{"id": "8fab1dcf-f454-4155-be32-a4e007a79ab0", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-540, 280], "webhookId": "77186e98-b103-4ac7-962b-fcae8180f982", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "c4791561-6919-46a9-8ee0-4328aac9bfa0", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-260, 520], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "c82903df-8b58-4789-987a-116c11c78215", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-120, 520], "parameters": {}, "typeVersion": 1.3}, {"id": "c65f5035-804f-4e50-948e-ab863e800393", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "notes": "For changing limits use \"count\"", "position": [20, 520], "parameters": {"jsonSchemaExample": "[\n  {\n    \"salesNavigatorParams\": {\n      \"keywords\": \"additional keywords important to search but not mentioned in other fields\",\n      \"current_titles\": \"title1, title2\",\n      \"current_companies\": \"company1, company2\",\n      \"location\": \"location1\",\n      \"industry\": \"industry1\",\n      \"company_sizes\": [\"Self-employed\", \"1-10\", \"11-50\", \"51-200\", \"201-500\", \"501-1,000\", \"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n    },\n    \"count\": 50\n  },\n  {\n    \"salesNavigatorParams\": {\n      \"keywords\": \"additional keywords important to search but not mentioned in other fields\",\n      \"current_titles\": \"title1, title2\",\n      \"current_companies\": \"company1, company2\",\n      \"location\": \"location2\",\n      \"industry\": \"industry2\",\n      \"company_sizes\": [\"Self-employed\", \"1-10\", \"11-50\", \"51-200\", \"201-500\", \"501-1,000\", \"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n    },\n    \"count\": 50\n  }\n]"}, "notesInFlow": true, "typeVersion": 1.2}, {"id": "fd939b62-927d-4532-96cd-36e91fdc2835", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [680, 140], "parameters": {"columns": {"value": {"URL": "={{ $json.url }}", "URN": "={{ $json.urn.type }}:{{ $json.urn.value }}", "img": "={{ $json.image }}", "Date": "={{ $json.current_companies[0].joined }}", "Name": "={{ $json.name }}", "Headline": "={{ $json.headline }}", "Industry": "={{ $json.current_companies[0].company.industry }}", "Position": "={{ $json.current_companies[0].position }}", "location": "={{ $json.location }}", "is premium": "={{ $json.is_premium }}", "Company URN": "={{ $json.current_companies[0].company.urn.type }}:{{ $json.current_companies[0].company.urn.value }}", "Description": "={{ $json.current_companies[0].description }}", "Current company": "={{ $json.current_companies[0].company.name }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": false, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": false, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "c98a9320-ca25-4a8d-a1a0-e7fd421976b7", "name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "maxTries": 5, "position": [980, 260], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "073214f0-b41c-41b0-af0f-136688bbbf24", "name": "Google Sheets2", "type": "n8n-nodes-base.googleSheets", "position": [1100, 1580], "parameters": {"columns": {"value": {"URN": "={{ $('Google Sheets1').item.json.URN }}", "Website": "={{ $json.website }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lead Score", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lead Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Request", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Contact Request", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Connected", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Connected", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Message Sent", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Message Sent", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "307cc492-0b47-4824-95da-8f4b531626b7", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [780, 1300], "parameters": {"options": {"reset": false}}, "retryOnFail": true, "typeVersion": 3}, {"id": "768f5782-f5fa-48ac-ad21-d8ec4912cce0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-520, -240], "parameters": {"color": 4, "width": 1320, "content": "Find leads in LinkedIn"}, "typeVersion": 1}, {"id": "3dff0b52-fea3-486f-a605-f02b2e5e7b0b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [860, -240], "parameters": {"color": 4, "width": 980, "content": "Get company website "}, "typeVersion": 1}, {"id": "867138af-ad26-4fab-9c41-b3f99d8b70dc", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1880, -780], "parameters": {"color": 4, "width": 1520, "content": "Research company website"}, "typeVersion": 1}, {"id": "f536cefc-27d3-474f-97d8-f3ae4ec55742", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3440, -240], "parameters": {"color": 4, "width": 980, "content": "Score data"}, "typeVersion": 1}, {"id": "9aefe55c-4e03-46ae-98bd-628ca6d18694", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [4460, -240], "parameters": {"color": 4, "width": 1860, "content": "Communicate with leads"}, "typeVersion": 1}, {"id": "7a1e6bbc-3b1a-46b5-a9b1-a2f381bf787c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1880, -600], "parameters": {"color": 4, "width": 1520, "content": "Research lead L<PERSON> post"}, "typeVersion": 1}, {"id": "807e12e1-2cc2-46a6-bdac-2cc2e6ebe762", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1880, -240], "parameters": {"color": 4, "width": 1520, "content": "Research company LN post"}, "typeVersion": 1}, {"id": "068b0826-d343-4882-b8c9-0666d0ac5b4b", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1880, -420], "parameters": {"color": 4, "width": 1520, "content": "Research company News"}, "typeVersion": 1}, {"id": "fbd1394d-194c-47bd-81f4-2d7ff4a7dcb6", "name": "HDW LinkedIn SN", "type": "n8n-nodes-hdw.hdwLinkedin", "onError": "continueRegularOutput", "position": [680, 340], "parameters": {"count": "={{ $json.output.count }}", "keywords": "={{ $json.output.salesNavigatorParams.keywords }}", "resource": "search", "additionalFilters": {"industry": "={{ $json.output.salesNavigatorParams.industry }}", "location": "={{ $json.output.salesNavigatorParams.location }}", "company_sizes": "={{ $json.output.salesNavigatorParams.company_sizes }}", "current_titles": "={{ $json.output.salesNavigatorParams.current_titles }}", "current_companies": "={{ $json.output.salesNavigatorParams.current_companies }}"}}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1}, {"id": "a9bca5e9-0fd0-4cd0-b24a-0114d958ba7f", "name": "HDW Get Company Website", "type": "n8n-nodes-hdw.hdwLinkedin", "onError": "continueRegularOutput", "position": [940, 1480], "parameters": {"company": "={{ $json[\"Company URN\"] }}", "resource": "company"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "99bc33ab-ef1c-4c61-a2b9-7306b104aacc", "name": "Google Sheets3", "type": "n8n-nodes-base.googleSheets", "position": [1900, 100], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "23a65054-34f2-4935-a764-a1c8cc530cc9", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "position": [2420, 80], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "f268ace9-4444-46ff-ad8a-0ceb453fff6f", "name": "Google Sheets4", "type": "n8n-nodes-base.googleSheets", "position": [1920, 640], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "6ddc37b0-061f-4307-be42-83d690897602", "name": "HDW Get User Posts", "type": "n8n-nodes-hdw.hdwLinkedin", "position": [2560, 680], "parameters": {"urn": "={{ $('Post summary is empty').item.json.URN }}", "operation": "getPosts"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "08f9365a-9efb-4927-84a1-7a5e8e9a0c49", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [2740, 680], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "text"}, {"renameField": true, "outputFieldName": "repost", "fieldToAggregate": "repost.text"}]}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "5ca5128c-4313-4ea0-be08-347627ccd9bb", "name": "Loop Over Items2", "type": "n8n-nodes-base.splitInBatches", "position": [2400, 520], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "828b2f34-aa9d-42e7-ac2e-9760da6aadc7", "name": "Google Sheets5", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [3240, 680], "parameters": {"columns": {"value": {"URN": "={{ $('Post summary is empty').item.json.URN }}", "Posts summary": "={{ $json.message.content }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "4d5f6214-f970-45b2-9c98-c1af5456f534", "name": "Google Sheets6", "type": "n8n-nodes-base.googleSheets", "position": [1920, 1100], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "9f3f1a52-01c0-4a87-808d-a268d7a6a7d0", "name": "Aggregate1", "type": "n8n-nodes-base.aggregate", "position": [2740, 1100], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "description"}]}}, "typeVersion": 1}, {"id": "805cfdc4-2413-45a1-b93f-b38cd84652b8", "name": "Loop Over Items3", "type": "n8n-nodes-base.splitInBatches", "position": [2400, 940], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "7c19dbc2-8367-40d1-b8ea-b957cc9bf17d", "name": "Google Sheets7", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [3260, 1100], "parameters": {"columns": {"value": {"URN": "={{ $('Company news is empty').item.json.URN }}", "Company News": "={{ $json.message.content }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "65d72d34-1908-4605-84ae-2047378ebab9", "name": "Google Sheets8", "type": "n8n-nodes-base.googleSheets", "position": [1920, 1600], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "********-b52e-400c-971c-d67da39825f9", "name": "Aggregate2", "type": "n8n-nodes-base.aggregate", "position": [2740, 1600], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "text"}]}}, "typeVersion": 1}, {"id": "4daa3a70-cb04-4b82-a424-cd726d196b05", "name": "Loop Over Items4", "type": "n8n-nodes-base.splitInBatches", "position": [2400, 1460], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "c40d6767-46ae-48ba-9850-bb0c75c83279", "name": "Google Sheets9", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [3260, 1600], "parameters": {"columns": {"value": {"URN": "={{ $('Company post is empty').item.json.URN }}", "Company post summary": "={{ $('Summarise company posts').item.json.message.content }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "04175e5f-dba4-4462-9dcc-a07ec87b13f8", "name": "HDW Get Company News", "type": "n8n-nodes-hdw.hdwLinkedin", "position": [2560, 1100], "parameters": {"query": "={{ $('Company news is empty').item.json[\"Current company\"] }} (news OR press OR announcement OR update)", "resource": "google", "operation": "googleSearch"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "4fe3c8cf-4376-4530-8bb3-9a3b63790bd4", "name": "HDW Get Company Posts", "type": "n8n-nodes-hdw.hdwLinkedin", "onError": "continueRegularOutput", "position": [2560, 1600], "parameters": {"urn": "={{ $('Google Sheets8').item.json[\"Company URN\"] }}", "resource": "company", "operation": "getCompanyPosts"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1, "alwaysOutputData": true}, {"id": "34fd2f1e-f552-4680-960b-9d0a3708095e", "name": "Loop Over Items5", "type": "n8n-nodes-base.splitInBatches", "position": [460, 280], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "e8fcd801-4036-478d-8190-5c7a64a3b53f", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [240, 280], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "output"}, "retryOnFail": true, "typeVersion": 1}, {"id": "26442be2-04bf-4c93-9b9f-8447ed0d8ca5", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2620, 340], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5b36ec97-5b22-49bd-973f-6e42a3515f89", "name": "Google Sheets10", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [3220, 80], "parameters": {"columns": {"value": {"URN": "={{ $('Website is not empty').item.json.URN }}", "Product Summary": "={{ $json.output }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "e6501f53-8ca7-405a-8842-7efdb20b7007", "name": "HDW Site-map", "type": "n8n-nodes-hdw.hdwWebParserTool", "position": [2940, 340], "parameters": {"url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('URL', `use company url to get a site-map`, 'string') }}", "operation": "map", "descriptionType": "manual", "toolDescription": "Get sitemap by url"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "typeVersion": 1}, {"id": "524e7c59-dfc0-4938-b214-d1f224649964", "name": "HDW Parser", "type": "n8n-nodes-hdw.hdwWebParserTool", "position": [3100, 340], "parameters": {"url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('URL', ``, 'string') }}", "descriptionType": "manual", "toolDescription": "Parse info from website by url"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "typeVersion": 1}, {"id": "9f6137ff-3edc-4b18-b89c-ad0c74c15d5b", "name": "Summarise user posts", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2900, 680], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Make summary of this text:\n {{ $json.text }}\n{{ $json.repost }}"}]}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "retryOnFail": true, "typeVersion": 1.8}, {"id": "995c5df3-ddf6-4350-b6f7-531acc30e289", "name": "Summarise company news", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2920, 1100], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Make summary of this news:\n{{ $json.description }}"}]}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "retryOnFail": true, "typeVersion": 1.8}, {"id": "b53615f8-35f2-4f1c-9478-ab4220a44ecd", "name": "Summarise company posts", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2920, 1600], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "=Make summary of this company posts:\n{{ $json.text }}"}]}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "retryOnFail": true, "typeVersion": 1.8}, {"id": "bdfd86aa-1456-4f5f-820c-cb578b17736d", "name": "Summarise company website", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2700, 80], "parameters": {"text": "=Research company website {{ $json.Website }} and get summary about business and products\n1. Get site-map with the tool\n2. Define what links may contain data about company business and products\n3. Scrape data for every links\n4. Summarize info from website ", "options": {}, "promptType": "define"}, "retryOnFail": true, "typeVersion": 1.8}, {"id": "f403c04d-0c9e-45ba-92fb-5c597e571dde", "name": "Google Sheets11", "type": "n8n-nodes-base.googleSheets", "position": [3640, 920], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "239304d7-531e-4185-b6ba-32c2c359796f", "name": "Loop Over Items6", "type": "n8n-nodes-base.splitInBatches", "position": [3980, 800], "parameters": {"options": {"reset": false}}, "retryOnFail": true, "typeVersion": 3}, {"id": "6edb5957-4534-41b0-96aa-ee0e2c516b4b", "name": "Google Sheets12", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [4480, 920], "parameters": {"columns": {"value": {"URN": "={{ $('Google Sheets11').item.json.URN }}", "Lead Score": "={{ $json.message.content }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lead Score", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Lead Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "a839c825-6d9b-4a25-a0a7-1332a2792ca5", "name": "Google Sheets13", "type": "n8n-nodes-base.googleSheets", "position": [4740, 340], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "6428bc44-c58a-4ad2-abe5-b8f5528775d8", "name": "Sort", "type": "n8n-nodes-base.sort", "position": [4960, 340], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "Lead Score"}]}}, "typeVersion": 1}, {"id": "a070ee7c-918b-4ad3-a01f-55ef37203359", "name": "If2", "type": "n8n-nodes-base.if", "position": [5160, 340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "140d284f-34c0-4bf4-bb88-b0a9bdbc80f7", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Contact Request\"] }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "4f692096-357e-4a29-bc5c-bee54dd2ea54", "name": "Limit", "type": "n8n-nodes-base.limit", "notes": "max 200 per week", "position": [5380, 240], "parameters": {"maxItems": 20}, "notesInFlow": true, "typeVersion": 1}, {"id": "14bb360b-49e5-4522-9c8d-78766460ec94", "name": "Loop Over Items7", "type": "n8n-nodes-base.splitInBatches", "position": [5520, 460], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "6705e666-7dca-4268-ae38-d94734758c79", "name": "Google Sheets14", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [6060, 480], "parameters": {"columns": {"value": {"URN": "={{ $('Google Sheets13').item.json.URN }}", "Contact Request": "TRUE"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lead Score", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lead Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Request", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Contact Request", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Connected", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Connected", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "d371bb18-d756-487c-a307-2f6bb08ebc86", "name": "Google Sheets15", "type": "n8n-nodes-base.googleSheets", "maxTries": 5, "position": [5160, 1420], "parameters": {"columns": {"value": {"URN": "={{ $json.urn.type }}:{{ $json.urn.value }}", "Connected": "TRUE"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lead Score", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lead Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Request", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Contact Request", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Connected", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Connected", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Message Sent", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Message Sent", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "notesInFlow": false, "retryOnFail": true, "typeVersion": 4.5, "alwaysOutputData": false, "waitBetweenTries": 5000}, {"id": "86f77be8-7c58-4863-a287-4c7c75110fde", "name": "Google Sheets16", "type": "n8n-nodes-base.googleSheets", "maxTries": 5, "position": [5400, 1420], "parameters": {"options": {}, "filtersUI": {"values": [{"lookupValue": "true", "lookupColumn": "Connected"}, {"lookupColumn": "Message Sent"}, {"lookupValue": "={{ $json.URN }}", "lookupColumn": "URN"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "63a3d60f-b7e7-4cca-a1f4-e3d0d93af92d", "name": "Google Sheets17", "type": "n8n-nodes-base.googleSheets", "onError": "continueRegularOutput", "position": [6440, 1420], "parameters": {"columns": {"value": {"URN": "={{ $('Google Sheets16').item.json.URN }}", "Message Sent": "TRUE"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Posts summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Posts summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Product Summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Product Summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company News", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company News", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company post summary", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company post summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Lead Score", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Lead Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Contact Request", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Contact Request", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Connected", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Connected", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Message Sent", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Message Sent", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5}, {"id": "a8a62da2-ecfc-423c-98d6-381463f02a41", "name": "Loop Over Items8", "type": "n8n-nodes-base.splitInBatches", "position": [5660, 1240], "parameters": {"options": {}}, "retryOnFail": true, "typeVersion": 3}, {"id": "6e76d190-3cd9-44a5-8167-230b13a8df4a", "name": "HDW LinkedIn Send Message", "type": "n8n-nodes-hdw.hdwLinkedinManagement", "position": [6140, 1360], "parameters": {"text": "Hello", "user": "={{ $json.URN }}", "resource": "chat"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1}, {"id": "ccf223a1-155a-4db6-a5f8-c2514c508fe7", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [4520, 340], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "a0c6bdc7-8eba-4e86-afc5-86ece19501aa", "name": "Schedule Trigger1", "type": "n8n-nodes-base.scheduleTrigger", "position": [4460, 1420], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "ef631ea6-876c-4a06-a967-f42945ee65e4", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1400, 20], "parameters": {"color": 7, "width": 620, "height": 640, "content": "# LinkedIn Lead Automation Flow\n\nThis flow is designed to automate working with leads on LinkedIn from the process of finding leads according to ICP (Ideal Customer Profile) to sending messages in chat.\n\n## Stages:\n1. Building search filters based on your ICP\n2. Searching LinkedIn using these filters\n3. Saving the list of leads in Google Spreadsheet\n4. Data enrichment\n  • Company website\n  • Intent search in lead's posts\n  • Intent search in company posts\n  • Analysis of company news\n  • Analysis of company website\n5. Scoring information about the lead to prioritize interaction\n6. Adding the lead to LinkedIn connections\n7. Checking for reciprocal connection\n8. Writing messages\n\n## Requirements:\n1. Access to LLM (the example uses OpenAI API)\n2. Access to Google Drive\n3. API key for horizondatawave.ai\n4. LinkedIn account"}, "typeVersion": 1}, {"id": "e640085a-70a8-4a6c-9dde-45af5dc96ed9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-200, 140], "parameters": {"color": 7, "width": 300, "height": 80, "content": "This node - an AI agent transforms your ICP description into filters for querying data in LinkedIn"}, "typeVersion": 1}, {"id": "f0520d0c-fedc-478b-a9db-3322be01bde6", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [500, 580], "parameters": {"color": 7, "width": 320, "height": 80, "content": "Data is requested through the HDW API for each filter and saved in Google Sheets."}, "typeVersion": 1}, {"id": "ca06282d-6560-42cd-aebf-9c6a47f7fde4", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1240, 80], "parameters": {"color": 7, "width": 320, "height": 120, "content": "At this stage, the presence of the company name in the lead's profile is verified, and a website search is performed for each company"}, "typeVersion": 1}, {"id": "34010f44-36ee-4023-98b9-2e26d0d72f79", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2660, -40], "parameters": {"color": 5, "width": 360, "height": 100, "content": "You can modify the prompt here so that at this stage the search corresponds to your product. \nFor example, you can explicitly mark mentions of certain topics, locations, or other entities."}, "typeVersion": 1}, {"id": "4068eb68-422e-46ef-8202-e01f38f88cb1", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2840, 540], "parameters": {"color": 5, "width": 360, "height": 100, "content": "You can modify the prompt here so that at this stage the search corresponds to your product. \nFor example, you can explicitly mark mentions of certain topics, locations, or other entities."}, "typeVersion": 1}, {"id": "0f1a3d57-b167-41c4-a0ef-4d9916dc2334", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2860, 980], "parameters": {"color": 5, "width": 360, "height": 100, "content": "You can modify the prompt here so that at this stage the search corresponds to your product. \nFor example, you can explicitly mark mentions of certain topics, locations, or other entities."}, "typeVersion": 1}, {"id": "c6290c23-9141-402a-8535-6816867cfd74", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2860, 1460], "parameters": {"color": 5, "width": 360, "height": 100, "content": "You can modify the prompt here so that at this stage the search corresponds to your product. \nFor example, you can explicitly mark mentions of certain topics, locations, or other entities."}, "typeVersion": 1}, {"id": "8aabae76-7833-43e8-aedf-b221058f47a3", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [4140, 800], "parameters": {"color": 5, "width": 360, "height": 100, "content": "You can also change the scoring criteria to assess the probability of need based on your product or business by adjusting the prompt in this node"}, "typeVersion": 1}, {"id": "cc7f165a-995f-4798-b499-c6e3f5ed5c30", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [5260, 100], "parameters": {"color": 5, "width": 360, "height": 100, "content": "Here you can change the number of connection requests. It is not recommended to add more than 200 connections per week."}, "typeVersion": 1}, {"id": "1ae9943e-67f4-44ba-bd0c-e99706520558", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [5900, 1040], "parameters": {"color": 3, "width": 360, "height": 100, "content": "In this node, you need to modify the message that will be automatically sent to the user after the connection is confirmed."}, "typeVersion": 1}, {"id": "015a32a7-1a78-4205-b5de-2a1906ceb52e", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [4500, 220], "parameters": {"color": 3, "width": 180, "height": 80, "content": "Change the schedule for when connection requests will be sent."}, "typeVersion": 1}, {"id": "b2ab2460-b063-43bd-b713-b7b04dfc8b00", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [4420, 1300], "parameters": {"color": 3, "width": 200, "height": 100, "content": "Change the schedule for when connection request responses will be checked and messages will be sent."}, "typeVersion": 1}, {"id": "280fb540-93c2-42a3-be4c-27e1a1c02ccf", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [3620, 400], "parameters": {}, "typeVersion": 1}, {"id": "5f66e03a-9eca-4aa6-aced-d5a8730d6740", "name": "HDW Get LinkedIn Profile Connections", "type": "n8n-nodes-hdw.hdwLinkedinManagement", "maxTries": 5, "position": [4700, 1420], "parameters": {"count": 30, "operation": "getConnections", "connectedAfter": 0}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "9b2502c5-d7c7-4df6-9929-726f3ea008de", "name": "HDW Send LinkedIn Connection", "type": "n8n-nodes-hdw.hdwLinkedinManagement", "onError": "continueRegularOutput", "maxTries": 5, "position": [5800, 420], "parameters": {"user": "={{ $('Google Sheets13').item.json.URN }}"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "5b7cd96c-091e-45af-b1dc-250b343a0b45", "name": "AI Agent: ICP -> LinkedIn search filters", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-180, 280], "parameters": {"options": {"systemMessage": "Your task is to analyze the given ICP (Ideal Customer Profile) description and convert it into LinkedIn Sales Navigator search parameters compatible with the HDW LinkedIn node used in n8n.\n\nInstructions:\n\t•\tUse keywords parameter ONLY for additional info, never duplicate titles, industries, companies, or locations from the request.\n\t•\tIf you need to specify multiple values for the parameters location or industry, return an array of separate JSON objects, each containing unique values.\n\t•\tIf the company size is not specified, omit the company_sizes parameter entirely.\n\t•\tIf a parameter has no value, exclude it from the JSON entirely.\n• If you need industry name use one from this list closer to the user request:\nChiropractors \nDefense and Space Manufacturing\nOptometrists\nComputer Hardware Manufacturing\nSoftware Development\nComputer Networking Products\nTransportation Equipment Manufacturing\nTechnology, Information and Internet\nPhysical, Occupational and Speech Therapists\nSemiconductor Manufacturing\nTelecommunications\nLaw Practice\nHousing Programs\nLegal Services\nBusiness Consulting and Services\nBiotechnology Research\nFamily Planning Centers\nMedical Practices\nTransportation Programs\nHospitals and Health Care\nUtilities Administration\nPharmaceutical Manufacturing\nOutpatient Care Centers\nVeterinary Services\nMedical Equipment Manufacturing\nSpace Research and Technology\nMotor Vehicle Parts Manufacturing\nPersonal Care Product Manufacturing\nRetail Apparel and Fashion\nSporting Goods Manufacturing\nTobacco Manufacturing\nMedical and Diagnostic Laboratories\nRetail Groceries\nFood and Beverage Manufacturing\nOil Extraction\nComputers and Electronics Manufacturing\nNatural Gas Extraction\nManufacturing\nFurniture and Home Furnishings Manufacturing\nHome Health Care Services\nRetail\nEmbedded Software Products\nEntertainment Providers\nMobile Computing Software Products\nGambling Facilities and Casinos\nAmbulance Services\nDesktop Computing Software Products\nIT System Custom Software Development\nTravel Arrangements\nIT System Operations and Maintenance\nHospitality\nIT System Installation and Disposal\nRestaurants\nIT System Training and Support\nHospitals\nSpectator Sports\nIT System Data Services\nFood and Beverage Services\nIT System Testing and Evaluation\nMovies, Videos, and Sound\nBroadcast Media Production and Distribution\nMuseums, Historical Sites, and Zoos\nArtists and Writers\nPerforming Arts\nRecreational Facilities\nBanking\nInsurance\nNursing Homes and Residential Care Facilities\nFinancial Services\nReal Estate\nInvestment Banking\nInvestment Management\nAccounting\nConstruction\nWholesale Building Materials\nArchitecture and Planning\nCivil Engineering\nInternet News\nAviation and Aerospace Component Manufacturing\nBlogs\nMotor Vehicle Manufacturing\nInterior Design\nChemical Manufacturing\nSocial Networking Platforms\nMachinery Manufacturing\nHousehold and Institutional Furniture Manufacturing\nBusiness Intelligence Platforms\nMining\nBusiness Content\nOil and Gas\nData Security Software Products\nShipbuilding\nUtilities\nMobile Gaming Apps\nTextile Manufacturing\nInternet Publishing\nPaper and Forest Product Manufacturing\nMedia and Telecommunications\nRailroad Equipment Manufacturing\nBlockchain Services\nFarming\nServices for the Elderly and Disabled\nRanching\nDairy Product Manufacturing\nOffice Furniture and Fixtures Manufacturing\nFisheries\nCommunity Services\nPrimary and Secondary Education\nHigher Education\nEducation Administration Programs\nResearch Services\nMattress and Blinds Manufacturing\nArmed Forces\nLegislative Offices\nAdministration of Justice\nInternational Affairs\nEmergency and Relief Services\nGovernment Administration\nExecutive Offices\nLaw Enforcement\nVocational Rehabilitation Services\nPublic Safety\nPublic Policy Offices\nAdvertising Services\nChild Day Care Services\nNewspaper Publishing\nPerforming Arts and Spectator Sports\nBook and Periodical Publishing\nPrinting Services\nInformation Services\nLibraries\nTheater Companies\nEnvironmental Services\nFreight and Package Transportation\nDance Companies\nIndividual and Family Services\nReligious Institutions\nCivic and Social Organizations\nConsumer Services\nCircuses and Magic Shows\nTruck Transportation\nWarehousing and Storage\nSports Teams and Clubs\nAirlines and Aviation\nMaritime Transportation\nRacetracks\nIT Services and IT Consulting\nMarket Research\nPublic Relations and Communications Services\nDesign Services\nNon-profit Organizations\nFundraising\nStrategic Management Services\nWriting and Editing\nStaffing and Recruiting\nWholesale Motor Vehicles and Parts\nProfessional Training and Coaching\nVenture Capital and Private Equity Principals\nPolitical Organizations\nTranslation and Localization\nComputer Games\nEvents Services\nRetail Art Supplies\nMuseums\nAppliances, Electrical, and Electronics Manufacturing\nOnline Audio and Video Media\nWholesale Furniture and Home Furnishings\nHistorical Sites\nNanotechnology Research\nRetail Art Dealers\nMusicians\nZoos and Botanical Gardens\nTransportation, Logistics, Supply Chain and Storage\nPlastics Manufacturing\nComputer and Network Security\nWireless Services\nAmusement Parks and Arcades\nAlternative Dispute Resolution\nSecurity and Investigations\nFacilities Services\nOutsourcing and Offshoring Consulting\nWellness and Fitness Services\nAlternative Medicine\nMedia Production\nAnimation and Post-production\nLeasing Non-residential Real Estate\nCapital Markets\nWholesale Photography Equipment and Supplies\nThink Tanks\nPhilanthropic Fundraising Services\nGolf Courses and Country Clubs\nE-Learning Providers\nWholesale\nWholesale Computer Equipment\nSkiing Facilities\nWholesale Import and Export\nIndustrial Machinery Manufacturing\nPhotography\nHuman Resources Services\nRetail Office Equipment\nMental Health Care\nGraphic Design\nInternational Trade and Development\nBeverage Manufacturing\nAccommodation and Food Services\nWholesale Metals and Minerals\nRetail Luxury Goods and Jewelry\nGlass, Ceramics and Concrete Manufacturing\nPackaging and Containers Manufacturing\nHotels and Motels\nAutomation Machinery Manufacturing\nWholesale Appliances, Electrical, and Electronics\nGovernment Relations Services\nBed-and-Breakfasts, Hostels, Homestays\nHorticulture\nWholesale Hardware, Plumbing, Heating Equipment\nWholesale Machinery\nCaterers\nMobile Food Services\nRenewable Energy Power Generation\nBars, Taverns, and Nightclubs\nRenewable Energy Equipment Manufacturing\nEngineering Services\nServices for Renewable Energy\nDigital Accessibility Services\nAccessible Hardware Manufacturing\nAccessible Architecture and Design\nRobot Manufacturing\nRobotics Engineering\nRepair and Maintenance\nSurveying and Mapping Services\nVehicle Repair and Maintenance\nRetail Pharmacies\nClimate Technology Product Manufacturing\nClimate Data and Analytics\nAlternative Fuel Vehicle Manufacturing\nWholesale Recyclable Materials\nSmart Meter Manufacturing\nFuel Cell Manufacturing\nWholesale Luxury Goods and Jewelry\nRegenerative Design\nFuneral Services\nDeath Care Services\nEnergy Technology\nWholesale Paper Products\nElectronic and Precision Equipment Maintenance\nWholesale Drugs and Sundries\nWholesale Apparel and Sewing Supplies\nCommercial and Industrial Machinery Maintenance\nFarming, Ranching, Forestry\nReupholstery and Furniture Repair\nWholesale Footwear\nWholesale Food and Beverage\nFootwear and Leather Goods Repair\nPersonal and Laundry Services\nPersonal Care Services\nLaundry and Drycleaning Services\nWholesale Raw Farm Products\nWholesale Chemical and Allied Products\nPet Services\nWholesale Petroleum and Petroleum Products\nWholesale Alcoholic Beverages\nRanching and Fisheries\nInternet Marketplace Platforms\nRetail Motor Vehicles\nHousehold Services\nRetail Furniture and Home Furnishings\nRetail Appliances, Electrical, and Electronic Equipment\nForestry and Logging\nRetail Building Materials and Garden Equipment\nHealth and Human Services\nPublic Health\nPublic Assistance Programs\nFood and Beverage Retail\nAir, Water, and Waste Program Management\nConservation Programs\nHousing and Community Development\nCommunity Development and Urban Planning\nEconomic Programs\nOil, Gas, and Mining\nRetail Health and Personal Care Products\nCoal Mining\nMilitary and International Affairs\nMetal Ore Mining\nRetail Gasoline\nOperations Consulting\nNonmetallic Mineral Mining\nElectric Power Transmission, Control, and Distribution\nRetail Musical Instruments\nElectric Power Generation\nHydroelectric Power Generation\nRetail Books and Printed News\nFossil Fuel Electric Power Generation\nNuclear Electric Power Generation\nSolar Electric Power Generation\nEnvironmental Quality Programs\nGeothermal Electric Power Generation\nBiomass Electric Power Generation\nNatural Gas Distribution\nWater, Waste, Steam, and Air Conditioning Services\nRetail Florists\nRetail Office Supplies and Gifts\nWater Supply and Irrigation Systems\nSteam and Air-Conditioning Supply\nBuilding Construction\nRetail Recyclable Materials & Used Merchandise\nResidential Building Construction\nData Infrastructure and Analytics\nNonresidential Building Construction\nUtility System Construction\nElectrical Equipment Manufacturing\nOnline and Mail Order Retail\nSubdivision of Land\nHighway, Street, and Bridge Construction\nSpecialty Trade Contractors\nBuilding Structure and Exterior Contractors\nWind Electric Power Generation\nWineries\nBuilding Equipment Contractors\nRail Transportation\nBuilding Finishing Contractors\nGround Passenger Transportation\nUrban Transit Services\nInterurban and Rural Bus Services\nTaxi and Limousine Services\nAnimal Feed Manufacturing\nSchool and Employee Bus Services\nShuttles and Special Needs Transportation Services\nSugar and Confectionery Product Manufacturing\nPipeline Transportation\nFruit and Vegetable Preserves Manufacturing\nSightseeing Transportation\nMeat Products Manufacturing\nSeafood Product Manufacturing\nBaked Goods Manufacturing\nPostal Services\nBreweries\nDistilleries\nTechnology, Information and Media\nPeriodical Publishing\nBook Publishing\nMovies and Sound Recording\nApparel Manufacturing\nSound Recording\nSheet Music Publishing\nRadio and Television Broadcasting\nFashion Accessories Manufacturing\nLeather Product Manufacturing\nCable and Satellite Programming\nTelecommunications Carriers\nFootwear Manufacturing\nSatellite Telecommunications\nWomen's Handbag Manufacturing\nCredit Intermediation\nSavings Institutions\nLoan Brokers\nOil and Coal Product Manufacturing\nSecurities and Commodity Exchanges\nChemical Raw Materials Manufacturing\nInvestment Advice\nInsurance Carriers\nArtificial Rubber and Synthetic Fiber Manufacturing\nAgricultural Chemical Manufacturing\nInsurance Agencies and Brokerages\nClaims Adjusting, Actuarial Services\nFunds and Trusts\nInsurance and Employee Benefit Funds\nPension Funds\nPaint, Coating, and Adhesive Manufacturing\nTrusts and Estates\nSoap and Cleaning Product Manufacturing\nReal Estate and Equipment Rental Services\nLeasing Residential Real Estate\nPlastics and Rubber Product Manufacturing\nReal Estate Agents and Brokers\nEquipment Rental Services\nConsumer Goods Rental\nRubber Products Manufacturing\nClay and Refractory Products Manufacturing\nCommercial and Industrial Equipment Rental\nGlass Product Manufacturing\nWood Product Manufacturing\nProfessional Services\nLime and Gypsum Products Manufacturing\nAbrasives and Nonmetallic Minerals Manufacturing\nPrimary Metal Manufacturing\nIT System Design Services\nMarketing Services\nFabricated Metal Products\nCutlery and Handtool Manufacturing\nArchitectural and Structural Metal Manufacturing\nBoilers, Tanks, and Shipping Container Manufacturing\nConstruction Hardware Manufacturing\nSpring and Wire Product Manufacturing\nTurned Products and Fastener Manufacturing\nHolding Companies\nMetal Treatments\nIndustry Associations\nLandscaping Services\nProfessional Organizations\nMetal Valve, Ball, and Roller Manufacturing\nAdministrative and Support Services\nOffice Administration\nExecutive Search Services\nTemporary Help Services\nAgriculture, Construction, Mining Machinery Manufacturing\nTelephone Call Centers\nCollection Agencies\nCommercial and Service Industry Machinery Manufacturing\nHVAC and Refrigeration Equipment Manufacturing\nMetalworking Machinery Manufacturing\nSecurity Guards and Patrol Services\nSecurity Systems Services\nEngines and Power Transmission Equipment Manufacturing\nJanitorial Services\nWaste Collection\nWaste Treatment and Disposal\nCommunications Equipment Manufacturing\nAudio and Video Equipment Manufacturing\nEducation\nMeasuring and Control Instrument Manufacturing\nSecretarial Schools\nTechnical and Vocational Training\nMagnetic and Optical Media Manufacturing\nCosmetology and Barber Schools\nFlight Training\nElectric Lighting Equipment Manufacturing\nFine Arts Schools\nSports and Recreation Instruction\nHousehold Appliance Manufacturing\nLanguage Schools\nPhysicians\nCourts of Law\nCorrectional Institutions\nDentists\nFire Protection\n\nAlways return the results as a valid JSON array with the exact following structure:\n[\n  {\n    \"salesNavigatorParams\": {\n      \"keywords\": \"additional keywords important to search but not mentioned in other fields\",\n      \"current_titles\": \"title1, title2\",\n      \"current_companies\": \"company1, company2\",\n      \"location\": \"location1\",\n      \"industry\": \"industry1\",\n      \"company_sizes\": [\"Self-employed\", \"1-10\", \"11-50\", \"51-200\", \"201-500\", \"501-1,000\", \"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n    },\n    \"count\": 50\n  },\n  {\n    \"salesNavigatorParams\": {\n      \"keywords\": \"additional keywords important to search but not mentioned in other fields\",\n      \"current_titles\": \"title1, title2\",\n      \"current_companies\": \"company1, company2\",\n      \"location\": \"location2\",\n      \"industry\": \"industry2\",\n      \"company_sizes\": [\"Self-employed\", \"1-10\", \"11-50\", \"51-200\", \"201-500\", \"501-1,000\", \"1,001-5,000\", \"5,001-10,000\", \"10,001+\"]\n    },\n    \"count\": 50\n  }\n]\n"}, "hasOutputParser": true}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1.8}, {"id": "e204ff25-3102-47bd-aabe-1754499bc69d", "name": "Company name is not empty", "type": "n8n-nodes-base.if", "position": [1180, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1e7e7c6d-401d-4b6e-ba9c-1e9caacb3f89", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json['Current company'] }}", "rightValue": ""}, {"id": "5fa79c84-9e9a-4cf9-a3ed-3a84a286b734", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Website }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "19229565-d4ba-4f8f-b9f2-eb3154642e23", "name": "Website is not empty", "type": "n8n-nodes-base.if", "position": [2120, 100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f21375a-b09c-464d-a82a-b37f7b6de87b", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.Website }}", "rightValue": ""}, {"id": "96105806-c0b8-461d-8cc1-975d8936814c", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Product Summary\"] }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "81e43024-76ac-4a73-8168-05f4e142896d", "name": "Post summary is empty", "type": "n8n-nodes-base.if", "position": [2140, 640], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f21375a-b09c-464d-a82a-b37f7b6de87b", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.URN }}", "rightValue": ""}, {"id": "96105806-c0b8-461d-8cc1-975d8936814c", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Posts summary\"] }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "08a8c5ae-044d-486c-9384-21fb8e9eb76d", "name": "Company news is empty", "type": "n8n-nodes-base.if", "position": [2140, 1100], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f21375a-b09c-464d-a82a-b37f7b6de87b", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.URN }}", "rightValue": ""}, {"id": "96105806-c0b8-461d-8cc1-975d8936814c", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Company News\"] }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "ca0cd95e-dbc0-4928-a70f-12d8e4950c62", "name": "Company post is empty", "type": "n8n-nodes-base.if", "position": [2140, 1600], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3f21375a-b09c-464d-a82a-b37f7b6de87b", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json[\"Company URN\"] }}", "rightValue": ":"}, {"id": "96105806-c0b8-461d-8cc1-975d8936814c", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Company post summary\"] }}", "rightValue": ""}, {"id": "44880d26-6f7d-4ee6-a71c-1cee7733368a", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json[\"Company URN\"] }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "967517ce-2933-439b-b76b-da8d8cd86ef5", "name": "Lead Score is empty", "type": "n8n-nodes-base.if", "position": [3800, 920], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "or", "conditions": [{"id": "140d284f-34c0-4bf4-bb88-b0a9bdbc80f7", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json[\"Lead Score\"] }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "a496b882-837b-412a-ada3-b8c0f00c14a8", "name": "Company Score Analysis", "type": "@n8n/n8n-nodes-langchain.openAi", "onError": "continueRegularOutput", "position": [4160, 920], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"role": "system", "content": "You are an expert in evaluating lead potential for our product.\nAnalyze the company’s and lead’s profile content, assessing their likelihood of interest in our product on a scale from 1 to 10 (where 1 indicates minimal potential, and 10 indicates maximum potential).\n\nKey evaluation criteria:\n\t•\tMentions of Hotels suppliers \n\t•\tMentions of Hotel Services\n\nEvaluation scale:\n\t•\t8–10 points: Clear mentions of both criteria, active engagement, and explicit interest.\n\t•\t5–7 points: Mention of one criterion or indirect indicators of interest in both criteria.\n\t•\t1–4 points: No clear mentions, or only weak indirect indicators of interest.\n\nYour answer must ONLY be a single number from 1 to 10, without any additional text."}, {"content": "=Lead posts summary:\n {{ $json[\"Posts summary\"] }}\nLead company website analysis:\n{{ $('Google Sheets11').item.json['Product Summary'] }}\n\nCompany news summary:\n{{ $('Google Sheets11').item.json['Company News'] }}\nCompany posts summary\n{{ $('Google Sheets11').item.json['Company post summary'] }}\nCompany descriptin\n{{ $json.Description }}"}]}}, "credentials": {"openAiApi": {"id": "ul99SH6843ii6CQj", "name": "OpenAi account"}}, "retryOnFail": true, "typeVersion": 1.8}, {"id": "41e39f9e-6bbc-4787-ad7c-5da6a4bff2d8", "name": "Split LinkedIn connections to items", "type": "n8n-nodes-base.code", "position": [4920, 1420], "parameters": {"jsCode": "const inputData = $input.all();\nconst connectionArray = inputData[0].json;\n\nreturn connectionArray.map(connection => {\n  return { json: connection };\n});"}, "typeVersion": 2}, {"id": "25082fc2-98d3-4359-898b-d61d1dac23d5", "name": "5s", "type": "n8n-nodes-base.wait", "position": [5880, 1340], "webhookId": "ec66fa04-e6bb-4fb1-956d-d78ae0ffae9a", "parameters": {}, "typeVersion": 1.1}, {"id": "d01bec66-a4e7-4fbe-98ac-48e71e0d21ac", "name": "Company name is not empty1", "type": "n8n-nodes-base.if", "position": [1040, 880], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1e7e7c6d-401d-4b6e-ba9c-1e9caacb3f89", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json['Current company'] }}", "rightValue": ""}, {"id": "5fa79c84-9e9a-4cf9-a3ed-3a84a286b734", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.Website }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "ce49dc57-6f21-4146-85e8-89c9f529ccf4", "name": "Google Sheets18", "type": "n8n-nodes-base.googleSheets", "position": [1680, 1160], "parameters": {"columns": {"value": {"URN": "={{ $('Google Sheets1').item.json.URN }}", "Website": "={{ $json.website }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URN", "type": "string", "display": true, "removed": false, "required": false, "displayName": "URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "img", "type": "string", "display": true, "removed": true, "required": false, "displayName": "img", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Headline", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Headline", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": true, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is premium", "type": "string", "display": true, "removed": true, "required": false, "displayName": "is premium", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Current company", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Current company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company URN", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Company URN", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "URL", "type": "string", "display": true, "required": false, "displayName": "URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Industry", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Industry", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Position", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Position", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Website", "type": "string", "display": true, "required": false, "displayName": "Website", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["URN"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}, {"id": "3752dc6c-a825-4cb3-85bd-5ced8a37dfad", "name": "Loop Over Items9", "type": "n8n-nodes-base.splitInBatches", "position": [1320, 800], "parameters": {"options": {"reset": false}}, "retryOnFail": true, "typeVersion": 3}, {"id": "dfd22a49-9a89-4c6d-9d51-ce11eb69ae71", "name": "HDW Get Company Website1", "type": "n8n-nodes-hdw.hdwLinkedin", "onError": "continueRegularOutput", "position": [1520, 1060], "parameters": {"query": "=\"{{ $json[\"Current company\"] }}\" company website", "resource": "google", "operation": "googleSearch"}, "credentials": {"hdwLinkedinApi": {"id": "D1F3OpJUjccbnXIQ", "name": "HDW LinkedIn account"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 1, "waitBetweenTries": 5000}, {"id": "34f45021-469e-4cef-b362-51b55ee16ae9", "name": "Google Sheets19", "type": "n8n-nodes-base.googleSheets", "maxTries": 5, "position": [880, 1040], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19n84gbJPp-VmAUz6fElLSQcMajh5btPvI7Lhf20u7hs/edit?usp=drivesdk", "cachedResultName": "HDW_OutReach"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "o7IF7bjd2sUY7NZB", "name": "Google Sheets account"}}, "retryOnFail": true, "typeVersion": 4.5, "waitBetweenTries": 5000}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Amsterdam", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveExecutionProgress": true}, "versionId": "b094bf9c-d3e0-418c-8803-3fb4c35a479a", "connections": {"5s": {"main": [[{"node": "HDW LinkedIn Send Message", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarise user posts", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Summarise company news", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "Summarise company posts", "type": "main", "index": 0}]]}, "HDW Parser": {"ai_tool": [[{"node": "Summarise company website", "type": "ai_tool", "index": 0}]]}, "HDW Site-map": {"ai_tool": [[{"node": "Summarise company website", "type": "ai_tool", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent: ICP -> LinkedIn search filters", "type": "ai_memory", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Company name is not empty", "type": "main", "index": 0}]]}, "Google Sheets2": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Google Sheets3": {"main": [[{"node": "Website is not empty", "type": "main", "index": 0}]]}, "Google Sheets4": {"main": [[{"node": "Post summary is empty", "type": "main", "index": 0}]]}, "Google Sheets5": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Google Sheets6": {"main": [[{"node": "Company news is empty", "type": "main", "index": 0}]]}, "Google Sheets7": {"main": [[{"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "Google Sheets8": {"main": [[{"node": "Company post is empty", "type": "main", "index": 0}]]}, "Google Sheets9": {"main": [[{"node": "Loop Over Items4", "type": "main", "index": 0}]]}, "Google Sheets10": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Google Sheets11": {"main": [[{"node": "Lead Score is empty", "type": "main", "index": 0}]]}, "Google Sheets12": {"main": [[{"node": "Loop Over Items6", "type": "main", "index": 0}]]}, "Google Sheets13": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Google Sheets14": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}]]}, "Google Sheets15": {"main": [[{"node": "Google Sheets16", "type": "main", "index": 0}]]}, "Google Sheets16": {"main": [[{"node": "Loop Over Items8", "type": "main", "index": 0}]]}, "Google Sheets17": {"main": [[{"node": "Loop Over Items8", "type": "main", "index": 0}]]}, "Google Sheets18": {"main": [[{"node": "Loop Over Items9", "type": "main", "index": 0}]]}, "Google Sheets19": {"main": [[{"node": "Company name is not empty1", "type": "main", "index": 0}]]}, "HDW LinkedIn SN": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Google Sheets19", "type": "main", "index": 0}], [{"node": "HDW Get Company Website", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[{"node": "Google Sheets4", "type": "main", "index": 0}], [{"node": "Summarise company website", "type": "main", "index": 0}]]}, "Loop Over Items2": {"main": [[{"node": "Google Sheets6", "type": "main", "index": 0}], [{"node": "HDW Get User Posts", "type": "main", "index": 0}]]}, "Loop Over Items3": {"main": [[{"node": "Google Sheets8", "type": "main", "index": 0}], [{"node": "HDW Get Company News", "type": "main", "index": 0}]]}, "Loop Over Items4": {"main": [[{"node": "Google Sheets11", "type": "main", "index": 0}], [{"node": "HDW Get Company Posts", "type": "main", "index": 0}]]}, "Loop Over Items5": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}], [{"node": "HDW LinkedIn SN", "type": "main", "index": 0}]]}, "Loop Over Items6": {"main": [[{"node": "Google Sheets13", "type": "main", "index": 0}], [{"node": "Company Score Analysis", "type": "main", "index": 0}]]}, "Loop Over Items7": {"main": [[], [{"node": "HDW Send LinkedIn Connection", "type": "main", "index": 0}]]}, "Loop Over Items8": {"main": [[], [{"node": "5s", "type": "main", "index": 0}]]}, "Loop Over Items9": {"main": [[{"node": "Google Sheets3", "type": "main", "index": 0}], [{"node": "HDW Get Company Website1", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Google Sheets13", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent: ICP -> LinkedIn search filters", "type": "ai_languageModel", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "HDW Get LinkedIn Profile Connections", "type": "main", "index": 0}]]}, "HDW Get User Posts": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Summarise company website", "type": "ai_languageModel", "index": 0}]]}, "Lead Score is empty": {"main": [[{"node": "Loop Over Items6", "type": "main", "index": 0}]]}, "HDW Get Company News": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "Summarise user posts": {"main": [[{"node": "Google Sheets5", "type": "main", "index": 0}]]}, "Website is not empty": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Company news is empty": {"main": [[{"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "Company post is empty": {"main": [[{"node": "Loop Over Items4", "type": "main", "index": 0}]]}, "HDW Get Company Posts": {"main": [[{"node": "Aggregate2", "type": "main", "index": 0}]]}, "Post summary is empty": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Company Score Analysis": {"main": [[{"node": "Google Sheets12", "type": "main", "index": 0}]]}, "Summarise company news": {"main": [[{"node": "Google Sheets7", "type": "main", "index": 0}]]}, "HDW Get Company Website": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Summarise company posts": {"main": [[{"node": "Google Sheets9", "type": "main", "index": 0}]]}, "HDW Get Company Website1": {"main": [[{"node": "Google Sheets18", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent: ICP -> LinkedIn search filters", "type": "ai_outputParser", "index": 0}]]}, "Company name is not empty": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HDW LinkedIn Send Message": {"main": [[{"node": "Google Sheets17", "type": "main", "index": 0}]]}, "Summarise company website": {"main": [[{"node": "Google Sheets10", "type": "main", "index": 0}]]}, "Company name is not empty1": {"main": [[{"node": "Loop Over Items9", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent: ICP -> LinkedIn search filters", "type": "main", "index": 0}]]}, "HDW Send LinkedIn Connection": {"main": [[{"node": "Google Sheets14", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[]]}, "Split LinkedIn connections to items": {"main": [[{"node": "Google Sheets15", "type": "main", "index": 0}]]}, "HDW Get LinkedIn Profile Connections": {"main": [[{"node": "Split LinkedIn connections to items", "type": "main", "index": 0}]]}, "AI Agent: ICP -> LinkedIn search filters": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}}}