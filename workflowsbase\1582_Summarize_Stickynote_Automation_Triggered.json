{"id": "aVTi7K9mFjK5OjAV", "meta": {"instanceId": "b3a8efae31a34c2224655b66499bee098263a56d266da574e8820468780b7ddd"}, "name": "OpenAI Assistant with custom n8n tools", "tags": [], "nodes": [{"id": "d15e7634-408b-43c5-a8d6-afcbc83479a9", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [600, 300], "parameters": {}, "typeVersion": 1.1}, {"id": "5d9ad043-adbe-4970-aa4e-b81dfcb9e255", "name": "OpenAI Assistant", "type": "@n8n/n8n-nodes-langchain.openAiAssistant", "position": [820, 300], "parameters": {"options": {}, "assistantId": "asst_BWy0154vMGMdrX7MjCYaYv6a"}, "credentials": {"openAiApi": {"id": "au6fQZN7it62DWlS", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "0c3aded2-886d-4c9f-8d6e-2729f12b6711", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [600, 960], "parameters": {}, "typeVersion": 1}, {"id": "c77010ac-82e6-40f2-92c4-c360d276b896", "name": "Mapping data", "type": "n8n-nodes-base.code", "position": [1080, 820], "parameters": {"jsCode": "return [\n {\n \"country\": \"Wakanda\",\n \"capital\": \"Birnin Zana\"\n },\n {\n \"country\": \"Narnia\",\n \"capital\": \"Cair Paravel\"\n },\n {\n \"country\": \"Gondor\",\n \"capital\": \"Minas Tirith\"\n },\n {\n \"country\": \"Oz\",\n \"capital\": \"The Emerald City\"\n },\n {\n \"country\": \"Westeros\",\n \"capital\": \"King's Landing\"\n },\n {\n \"country\": \"Panem\",\n \"capital\": \"The Capitol\"\n },\n {\n \"country\": \"Ruritania\",\n \"capital\": \"Strelsau\"\n },\n {\n \"country\": \"Mordor\",\n \"capital\": \"Barad-dûr\"\n },\n {\n \"country\": \"Latveria\",\n \"capital\": \"Doomstadt\"\n },\n {\n \"country\": \"Atlantis\",\n \"capital\": \"Poseidonis\"\n }\n]\n"}, "typeVersion": 2}, {"id": "3949d5d8-a8d6-4a21-8e34-fca558ee6a97", "name": "List countries?", "type": "n8n-nodes-base.if", "position": [840, 960], "parameters": {"conditions": {"string": [{"value1": "={{ $json.query }}", "value2": "list"}]}}, "executeOnce": false, "typeVersion": 1}, {"id": "23bd1672-f736-4ac0-abf6-65f5f6aeabac", "name": "Mapping data1", "type": "n8n-nodes-base.code", "position": [840, 1160], "parameters": {"jsCode": "return [\n {\n \"country\": \"Wakanda\",\n \"capital\": \"Birnin Zana\"\n },\n {\n \"country\": \"Narnia\",\n \"capital\": \"Cair Paravel\"\n },\n {\n \"country\": \"Gondor\",\n \"capital\": \"Minas Tirith\"\n },\n {\n \"country\": \"Oz\",\n \"capital\": \"The Emerald City\"\n },\n {\n \"country\": \"Westeros\",\n \"capital\": \"King's Landing\"\n },\n {\n \"country\": \"Panem\",\n \"capital\": \"The Capitol\"\n },\n {\n \"country\": \"Ruritania\",\n \"capital\": \"Strelsau\"\n },\n {\n \"country\": \"Mordor\",\n \"capital\": \"Barad-dûr\"\n },\n {\n \"country\": \"Latveria\",\n \"capital\": \"Doomstadt\"\n },\n {\n \"country\": \"Atlantis\",\n \"capital\": \"Poseidonis\"\n }\n]\n"}, "typeVersion": 2}, {"id": "ec16de2b-7945-4133-a73d-11d4e42355c2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [540, 741.6494845360827], "parameters": {"width": 1174.6162657502882, "height": 578.9520146851776, "content": "## Sub-workflow: Return the capitals of fictional countries\nIt can either list the countries it knows about or return the capital of a specific country"}, "typeVersion": 1}, {"id": "65e659a0-6e1b-4642-b263-59ed2e284ee8", "name": "Return country list", "type": "n8n-nodes-base.set", "position": [1520, 820], "parameters": {"fields": {"values": [{"name": "response", "stringValue": "={{ $json.concatenated_country }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "65fc898d-0361-461a-9055-9e29bf310336", "name": "Return specific capital", "type": "n8n-nodes-base.set", "position": [1520, 1060], "parameters": {"fields": {"values": [{"name": "response", "stringValue": "={{ $ifEmpty($json.capital, 'Capital not found') }}"}]}, "include": "none", "options": {}}, "typeVersion": 3.2}, {"id": "bdf7c927-deb4-4a73-a015-43797c6cf816", "name": "Tool to call the workflow below", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [880, 540], "parameters": {"name": "country_capitals_tool", "workflowId": "={{ $workflow.id }}", "description": "This tool has two modes:\n1. Pass 'list' to the tool to get a list of countries that the tool has the capitals for (one per line). This is useful if you can't find a match, to see if the country being asked about might have been misspelled.\n2. Pass one of the country names in the list to the tool to get the capital of that country. Note that the country must be spelled exactly as it is in the list of countries returned in mode 1"}, "typeVersion": 1}, {"id": "4e93323f-d4be-4a49-be24-3f49db39907b", "name": "Concatenate country names", "type": "n8n-nodes-base.summarize", "position": [1300, 820], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "country", "separateBy": "\n", "aggregation": "concatenate"}]}}, "typeVersion": 1}, {"id": "e2ec1eee-4bb2-4240-82cf-e109b87229eb", "name": "Get the matching country's details", "type": "n8n-nodes-base.merge", "position": [1080, 1060], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "query", "field2": "country"}]}}, "typeVersion": 2.1}, {"id": "ed2997be-c709-4eca-bcad-c987bbc160fc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [540, 200], "parameters": {"width": 1168.2339341502545, "height": 487.70693675217734, "content": "## Main workflow: Cha<PERSON> with OpenAI Assistant\nClick the 'Chat' button at the bottom of the screen to try"}, "typeVersion": 1}, {"id": "01ab30c3-3951-4652-b706-72af1cad4f22", "name": "Tool: Get current date and time", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [1080, 540], "parameters": {"name": "date_tool", "jsCode": "let now = DateTime.now()\nreturn now.toISO()", "description": "Call this tool to get the current timestamp (in ISO format). No parameters necessary"}, "typeVersion": 1}], "active": false, "pinData": {"Execute Workflow Trigger": [{"json": {"query": "list"}}]}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v0", "executionTimeout": -1, "saveManualExecutions": true}, "versionId": "c867ebb5-ceeb-45a7-ad29-7ee3f1102bed", "connections": {"Mapping data": {"main": [[{"node": "Concatenate country names", "type": "main", "index": 0}]]}, "Mapping data1": {"main": [[{"node": "Get the matching country's details", "type": "main", "index": 1}]]}, "List countries?": {"main": [[{"node": "Mapping data", "type": "main", "index": 0}], [{"node": "Get the matching country's details", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "List countries?", "type": "main", "index": 0}]]}, "Concatenate country names": {"main": [[{"node": "Return country list", "type": "main", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "OpenAI Assistant", "type": "main", "index": 0}]]}, "Tool to call the workflow below": {"ai_tool": [[{"node": "OpenAI Assistant", "type": "ai_tool", "index": 0}]]}, "Tool: Get current date and time": {"ai_tool": [[{"node": "OpenAI Assistant", "type": "ai_tool", "index": 0}]]}, "Get the matching country's details": {"main": [[{"node": "Return specific capital", "type": "main", "index": 0}]]}}}