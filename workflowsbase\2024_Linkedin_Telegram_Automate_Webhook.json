{"id": "yF1HNe2ucaE81fNl", "meta": {"instanceId": "52be616fc3b9990a95b5266574f084bd2127609e79ce7dbfc33a1224bcc79eee", "templateCredsSetupCompleted": true}, "name": "Linkedin Automation", "tags": [], "nodes": [{"id": "fa012332-1c95-4460-b1d1-9d54441c9179", "name": "Get List of records used", "type": "n8n-nodes-base.airtable", "position": [-40, -80], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appt6kHkRkLlUh033", "cachedResultUrl": "https://airtable.com/appt6kHkRkLlUh033", "cachedResultName": "<PERSON><PERSON>"}, "table": {"__rl": true, "mode": "list", "value": "tbliloartO26TD5TG", "cachedResultUrl": "https://airtable.com/appt6kHkRkLlUh033/tbliloartO26TD5TG", "cachedResultName": "Used Articles"}, "options": {}, "operation": "search"}, "credentials": {"airtableTokenApi": {"id": "9bPeAvakB1tkDxsW", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1, "alwaysOutputData": true}, {"id": "2d99b3b7-2fcd-46bf-8859-f41e94cb5ae1", "name": "Update the used node", "type": "n8n-nodes-base.airtable", "position": [200, 600], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appt6kHkRkLlUh033", "cachedResultUrl": "https://airtable.com/appt6kHkRkLlUh033", "cachedResultName": "<PERSON><PERSON>"}, "table": {"__rl": true, "mode": "list", "value": "tbliloartO26TD5TG", "cachedResultUrl": "https://airtable.com/appt6kHkRkLlUh033/tbliloartO26TD5TG", "cachedResultName": "Used Articles"}, "columns": {"value": {"id": "={{ $('download image for post').item.json.id }}", "value": "={{ $('download image for post').item.json.id }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "value", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "value", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "9bPeAvakB1tkDxsW", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "72abb016-8f58-4c4c-b492-9ba7a576441a", "name": "map used articls ids", "type": "n8n-nodes-base.code", "position": [200, -80], "parameters": {"jsCode": "let values = $input.all().map(item => item.json.value);\n\nreturn [\n    {\n      json: {\n        values: values\n      }\n    }\n  ];"}, "typeVersion": 2}, {"id": "c49d5db2-d1c9-4444-8fa8-f39197e2a472", "name": "filter only unused Ids", "type": "n8n-nodes-base.filter", "position": [640, -80], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "934a4ab8-bc6b-4d1b-b050-c1f19a03cc9f", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $('map used articls ids').item.json.values }}", "rightValue": "={{ $json.articles }}"}]}}, "typeVersion": 2.2}, {"id": "0b390b7d-8729-48e5-aadc-5aa9da8c7139", "name": "get random tags", "type": "n8n-nodes-base.code", "position": [-280, -80], "parameters": {"jsCode": "const devToTags = [\n  \"android\",\n        \"androiddev\",\n        \"kotlin\",\n        \"jetpack-compose\",\n        \"android-appdevelopment\",\n        \"app-development\"\n];\n\n\nfunction getRandomValuesAsObjects(list, count) {\n  const randomValues = [];\n  for (let i = 0; i < count; i++) {\n    const randomIndex = Math.floor(Math.random() * list.length);\n    randomValues.push({ json: { value: list[randomIndex] } });\n  }\n  return randomValues;\n}\n\nreturn getRandomValuesAsObjects(devToTags, 1);\n"}, "typeVersion": 2}, {"id": "6b16bc15-8d82-4aa0-9ee2-5a10f070d106", "name": "sent the status", "type": "n8n-nodes-base.telegram", "position": [520, 600], "webhookId": "9373d46a-d5ad-40f4-93c0-7a44ff5fea37", "parameters": {"text": "=Linkdin Post Sent Successfully  \n\n{{ $('If').item.json.title }}\n\nDb Status Id {{ $json.id }}", "chatId": "1199262493", "replyMarkup": "inlineKeyboard", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "R8nJZScHqw02haLU", "name": "Mr.<PERSON><PERSON><PERSON><PERSON> bot"}}, "typeVersion": 1.2}, {"id": "99c5ed96-4220-46b0-9a2a-628963393894", "name": "Morning  9 Clock", "type": "n8n-nodes-base.scheduleTrigger", "position": [-560, -80], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9,19 * * *"}]}}, "typeVersion": 1.2}, {"id": "c81c749a-e21b-4ba6-beae-2b8a21523c06", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-560, -600], "parameters": {"width": 920, "height": 400, "content": "# 📢 Auto-Post Medium Articles to LinkedIn with Telegram Alerts\n\nThis n8n workflow automates your LinkedIn posting by fetching articles from [medium.com](medium.com) twice a day (9:00 AM and 7:00 PM), ensuring consistent content sharing without manual effort.\n\nTo prevent duplicates, it stores posted article IDs in Airtable. It also sends a Telegram message after every successful post, so you stay updated.\n\n---\n"}, "typeVersion": 1}, {"id": "61171a34-53a3-448a-886c-b0cc83b75b33", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [400, -520], "parameters": {"width": 580, "height": 240, "content": "\n## ✅ Features\n\n- 🕒 Runs twice daily at 9:00 AM and 7:00 PM (customizable)\n- 📰 Fetches latest medium.com articles by tag\n- 📂 Uses Airtable to avoid reposting the same article\n- 📢 Posts to your LinkedIn profile or company page\n- 📬 Sends a Telegram notification after successful posting\n- ⚙️ Fully customizable schedule, tags, and post format"}, "typeVersion": 1}, {"id": "c6712f11-2852-49af-8fb9-235da0e4685c", "name": "fetch article ids from tag", "type": "n8n-nodes-base.httpRequest", "position": [420, -80], "parameters": {"url": "=https://medium2.p.rapidapi.com/search/articles?query={{ $('get random tags').first().json.value }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "medium2.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": ""}]}}, "typeVersion": 4.2}, {"id": "6382e23e-e214-48b4-8d93-06fc2c74e7cc", "name": "Fetch Medium post using Article Id", "type": "n8n-nodes-base.httpRequest", "position": [880, -80], "parameters": {"url": "=https://medium2.p.rapidapi.com/article/{{ $json.articles.randomItem() }}", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "medium2.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": ""}]}}, "typeVersion": 4.2}, {"id": "eb92a4b3-d468-4d0f-8488-e6edb122b1db", "name": "If", "type": "n8n-nodes-base.if", "position": [-200, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "69a60b53-f719-44e8-9ca4-97b99205a253", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.image_url }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "792507fc-f956-4bc7-9c56-80f1078643a1", "name": "make Linkedin post", "type": "n8n-nodes-base.linkedIn", "position": [740, 240], "parameters": {"text": "={{ $('Fetch Medium post content').item.json.content.substring(0, 600) }} ...\n\nArticle link : https://freedium.cfd/{{ $('If').item.json.url }}\n\n#AndroidDevelopment #MobileAppDevelopment #AppDevelopment #Programming #SoftwareEngineering #TechCommunity #DeveloperLife #Kotlin #LinkedInDevelopers \n#Mr4rogrammer #isharewhatilearn", "person": "BQYGc4bH9N", "additionalFields": {"title": "=💫 {{ $('If').item.json.title }} ⭐", "visibility": "PUBLIC"}, "shareMediaCategory": "IMAGE"}, "credentials": {"linkedInOAuth2Api": {"id": "TODMZHWKWUyYl0qb", "name": "LinkedIn account"}}, "typeVersion": 1}, {"id": "b5026d10-0bcf-4ef4-a42e-0d8162a7eccc", "name": "Fetch Medium post content", "type": "n8n-nodes-base.httpRequest", "position": [100, 240], "parameters": {"url": "=https://medium2.p.rapidapi.com/article/{{$json.id}}/content", "options": {}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "medium2.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": ""}]}}, "typeVersion": 4.2}, {"id": "d25bf5d7-0258-4f07-b0b7-54ace75ef697", "name": "download image for post", "type": "n8n-nodes-base.httpRequest", "position": [420, 240], "parameters": {"url": "={{ $('If').item.json.image_url }}", "options": {"allowUnauthorizedCerts": false}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "Mozilla/5.0"}]}}, "typeVersion": 4.2, "alwaysOutputData": false}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Kolkata", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "cc2275e5-a8d2-468c-be91-5e14ad568e3a", "connections": {"If": {"main": [[{"node": "Fetch Medium post content", "type": "main", "index": 0}], [{"node": "get random tags", "type": "main", "index": 0}]]}, "get random tags": {"main": [[{"node": "Get List of records used", "type": "main", "index": 0}]]}, "Morning  9 Clock": {"main": [[{"node": "get random tags", "type": "main", "index": 0}]]}, "make Linkedin post": {"main": [[{"node": "Update the used node", "type": "main", "index": 0}]]}, "Update the used node": {"main": [[{"node": "sent the status", "type": "main", "index": 0}]]}, "map used articls ids": {"main": [[{"node": "fetch article ids from tag", "type": "main", "index": 0}]]}, "filter only unused Ids": {"main": [[{"node": "Fetch Medium post using Article Id", "type": "main", "index": 0}]]}, "download image for post": {"main": [[{"node": "make Linkedin post", "type": "main", "index": 0}]]}, "Get List of records used": {"main": [[{"node": "map used articls ids", "type": "main", "index": 0}]]}, "Fetch Medium post content": {"main": [[{"node": "download image for post", "type": "main", "index": 0}]]}, "fetch article ids from tag": {"main": [[{"node": "filter only unused Ids", "type": "main", "index": 0}]]}, "Fetch Medium post using Article Id": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}}