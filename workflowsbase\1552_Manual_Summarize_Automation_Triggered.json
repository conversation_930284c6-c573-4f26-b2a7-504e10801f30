{"id": "MkZ77sIELEO2kQx1", "meta": {"instanceId": "d58ea5647f14a122a558f2a99ce9c999af3b31f43e8079989af146576e4a2268"}, "name": "SearchApi Youtube Video Summary", "tags": [], "nodes": [{"id": "2b0a439f-4b6e-4473-a6d5-9b0ec8db676b", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [20, 280], "parameters": {}, "typeVersion": 1}, {"id": "662f79e0-d450-4d9e-a537-0e8f1a0870b6", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [900, 280], "parameters": {"options": {}, "chunkingMode": "advanced"}, "typeVersion": 2}, {"id": "fe17b482-8031-4d46-829b-59fe69dc8786", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1080, 500], "parameters": {"options": {}, "chunkSize": 6000}, "typeVersion": 1}, {"id": "4829c2e9-c23a-452a-b409-7efc2e1e135d", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [460, 280], "parameters": {"options": {}, "fieldToSplitOut": "transcripts"}, "typeVersion": 1}, {"id": "6a48cee3-d2a1-417d-a278-e95394519864", "name": "Summarize", "type": "n8n-nodes-base.summarize", "position": [680, 280], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "text", "separateBy": " ", "aggregation": "concatenate"}]}}, "typeVersion": 1.1}, {"id": "f6d8f00c-ea89-4111-96fa-f1d8db468060", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 5, "width": 320, "content": "## Youtube Video Summary\nGiven a **video_id** from Youtube, we concatenate the data and pass it to a summarization chain. To run this workflow, you need to have the credentials for SearchApi.io and some LLM provider."}, "typeVersion": 1}, {"id": "4b3c0abf-e670-4dcb-b69d-a76e58db2b7e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [220, 500], "parameters": {"height": 120, "content": "## Tip \nYou can pass the **video_id** from previous nodes to make a better automation"}, "typeVersion": 1}, {"id": "f95d330f-ec72-4d26-9f42-63a8a34dff3d", "name": "SearchApi", "type": "@searchapi/n8n-nodes-searchapi.searchApi", "position": [240, 280], "parameters": {"parameters": {"parameter": [{"name": "video_id", "value": "aigDyaxGsRo"}]}, "requestOptions": {}}, "typeVersion": 1}, {"id": "84f8bce6-0d62-49bd-8169-936358ee3734", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [900, 500], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "23db14e8-b72c-43fc-b934-cf1733b66bc4", "connections": {"SearchApi": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "Summarization Chain", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Summarization Chain", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "SearchApi", "type": "main", "index": 0}]]}}}