{"id": "1dnr1k4MAVbDiBmO", "meta": {"instanceId": "6b614b231db1d70977d02e50f578fcb50ce3b81e1fa79a97b9351e948fbbd610", "templateCredsSetupCompleted": true}, "name": "Get event triggered notifications / updates on preferred messaging channels with TwentyCRM", "tags": [], "nodes": [{"id": "5e823dd0-f50a-49ad-9e9a-7d0aee656b9c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [620, 580], "parameters": {"color": 7, "width": 239.36440675415446, "height": 80, "content": "**1. ☝️ Set up `On new TwentyCRM event` <PERSON><PERSON>'s url at webhook in TwentyCRM**"}, "typeVersion": 1}, {"id": "0eb98b9a-2f47-4199-a7e5-fe1f9c112721", "name": "filter required data #eventType mandatory", "type": "n8n-nodes-base.set", "position": [860, 380], "parameters": {"options": {"dotNotation": true, "ignoreConversionErrors": true}, "assignments": {"assignments": [{"id": "9e24e3f4-e750-4b50-b467-24612717f6a0", "name": "eventName", "type": "string", "value": "={{ $json.body.eventName }}"}, {"id": "b6aa9813-39bf-4b3d-9df0-aa93fbf4dc73", "name": "objectMetadata.id", "type": "string", "value": "={{ $json.body.objectMetadata.id }}"}, {"id": "8bdff15a-a98a-41ad-89d0-e793c3edb14c", "name": "objectMetadata.nameSingular", "type": "string", "value": "={{ $json.body.objectMetadata.nameSingular }}"}, {"id": "0b81e0e6-e9c6-4c03-9b08-f27d1e36b56e", "name": "record.id", "type": "string", "value": "={{ $json.body.record.id }}"}, {"id": "71e164f5-d8a2-4ac2-b898-71221b26d92d", "name": "record.__typename", "type": "string", "value": "={{ $json.body.record.__typename }}"}]}}, "typeVersion": 3.4}, {"id": "2cf5a0df-17ff-43c8-a885-7e4657c8b912", "name": "events log", "type": "n8n-nodes-base.googleSheets", "position": [1160, 540], "parameters": {"operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "url", "value": ""}}, "typeVersion": 4.5}, {"id": "ade9d73e-109b-47a2-9d57-2c8a3c031a4c", "name": "message channel evaluation", "type": "n8n-nodes-base.if", "position": [1440, 380], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "effea083-18d0-4b56-8b77-8ca461a371b6", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.eventName.split(\".\")[1] }}", "rightValue": "delete"}]}}, "typeVersion": 2.2}, {"id": "37ab5d83-9112-470a-894f-bf508e4612b7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [780, 220], "parameters": {"color": 7, "width": 242.34738303232248, "height": 131.4798719116814, "content": "**Filter Data 👇**\nChange filter criteria here to determine what values are required for you but don't forget to include eventType as it is a functional requirement"}, "typeVersion": 1}, {"id": "be669d56-0323-48cf-a474-8d22b04148e0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1340, 580], "parameters": {"color": 7, "width": 200.3243983123301, "height": 95.26139957883888, "content": "**👈 event loggin**\nAll events are logged in the sheet with one entry per row"}, "typeVersion": 1}, {"id": "7db1418e-5eb1-4bdb-afa0-e9cb268af187", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1340, 240], "parameters": {"color": 7, "width": 194, "height": 100.99999999999997, "content": "**Evaluation 👇**\nBased on the conditions proper channel for messaging is selected"}, "typeVersion": 1}, {"id": "77a06749-e901-44d0-8b45-06bf90715ed2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [520, 220], "parameters": {"color": 6, "width": 226.64074289386136, "height": 128.58912785838194, "content": "### Get event triggered notifications / updates on preferred messaging channels with TwentyCRM ### \n"}, "typeVersion": 1}, {"id": "1a1854bb-84c3-48a7-99ac-cc2245b2fafa", "name": "on new twentycrm event", "type": "n8n-nodes-base.webhook", "position": [600, 380], "webhookId": "8118bda9-0e4f-44cd-bf64-31020b6d5ab5", "parameters": {"path": "8118bda9-0e4f-44cd-bf64-31020b6d5ab5", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "09e33fe9-e9cf-4370-9141-a74868447eff", "name": "email channel for delete eventType", "type": "n8n-nodes-base.gmail", "position": [1740, 200], "webhookId": "45e4872f-0723-416c-854d-769901010bf4", "parameters": {"message": "=<h2>Please find below the attached record details</h2><br/><br/> \n<ul>\n<li>\nobjectMetadata_id: {{ $json.objectMetadata.id }}\n</li>\n<li>\nrecord_id: {{ $json.record.id }}\n</li>\n</ul>", "options": {}, "subject": "Record Deleted in TwentyCRM"}, "typeVersion": 2.1}, {"id": "f732e7e9-8378-44e9-a4ba-ec509ae210f6", "name": "message channel for all other eventTypes", "type": "n8n-nodes-base.slack", "position": [1740, 540], "webhookId": "4ff4d697-aaeb-4092-8e4e-d7c1c3a9b3ff", "parameters": {"text": "=event: {{ $json.eventName }}\nevent_id: {{ $json.objectMetadata.id }}\nrecord_id: {{ $json.record.id }}", "select": "channel", "channelId": {"__rl": true, "mode": "url", "value": ""}, "otherOptions": {}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b37892dc-b121-4a42-a305-7d197c087266", "connections": {"events log": {"main": [[{"node": "message channel evaluation", "type": "main", "index": 0}]]}, "on new twentycrm event": {"main": [[{"node": "filter required data #eventType mandatory", "type": "main", "index": 0}]]}, "message channel evaluation": {"main": [[{"node": "email channel for delete eventType", "type": "main", "index": 0}], [{"node": "message channel for all other eventTypes", "type": "main", "index": 0}]]}, "filter required data #eventType mandatory": {"main": [[{"node": "events log", "type": "main", "index": 0}, {"node": "message channel evaluation", "type": "main", "index": 0}]]}}}