{"id": "ift5iHQG9G2lzJzP", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Linkedin to Airtable", "tags": [{"id": "1iR8rLF2nlFdk8Iy", "name": "Tool", "createdAt": "2025-04-10T20:38:51.198Z", "updatedAt": "2025-04-10T20:38:51.198Z"}, {"id": "kY9rLUshnq9TIJVU", "name": "<PERSON><PERSON>", "createdAt": "2025-04-11T17:35:46.605Z", "updatedAt": "2025-04-11T17:35:46.605Z"}], "nodes": [{"id": "623c5cf2-0c16-47fe-8ec0-fa66e7c32576", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-980, -520], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "f09f752b-162b-4d9d-a397-69f3ead78e45", "name": "Fetch LinkedIn Likes", "type": "n8n-nodes-base.httpRequest", "position": [-780, -520], "parameters": {"url": "https://linkedin-api8.p.rapidapi.com/get-profile-likes", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "username", "value": "< YOUR LINKEDIN USERNAME >"}, {"name": "start", "value": "0"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "linkedin-api8.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "< YOUR RAPID API KEY >"}]}}, "typeVersion": 4.2}, {"id": "f3f64d75-550e-4a32-99ea-ccea7d14694f", "name": "Split Liked Posts", "type": "n8n-nodes-base.splitOut", "position": [-560, -520], "parameters": {"options": {}, "fieldToSplitOut": "data.items"}, "typeVersion": 1}, {"id": "40cc1e38-c564-45ac-b150-58bf2d42353d", "name": "Filter Insightful & Recent", "type": "n8n-nodes-base.filter", "position": [-340, -520], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a1ee03bc-55c0-4e62-af66-280df7e24824", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.action }}", "rightValue": "insightful"}, {"id": "9b7fcecb-09c0-45f2-bb30-9b6ab565695c", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ new Date($json.postedDate).getTime() }}", "rightValue": "={{ new Date().getTime() - (7 * 24 * 60 * 60 * 1000) }}"}]}}, "typeVersion": 2.2}, {"id": "e56f96db-7179-4c48-bf2b-a8153c5ae623", "name": "Format Content Idea", "type": "n8n-nodes-base.set", "position": [-120, -520], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "93ebd033-743a-4f8c-837c-ed619d14895d", "name": "Title", "type": "string", "value": "=I just liked a linkedin post of {{ $json.author.username }}"}, {"id": "fdbde792-1dba-46e2-9c3a-2daf629151e3", "name": "description", "type": "string", "value": "={{ $json.text }}"}, {"id": "ecf9aca8-45ae-4037-8f78-06a2a7b5d076", "name": "source", "type": "string", "value": "={{ $json.postUrl }}"}]}}, "typeVersion": 3.4}, {"id": "364eba33-3b3a-44c7-af0f-bab130ea7193", "name": "Prepare for Airtable", "type": "n8n-nodes-base.splitOut", "position": [100, -520], "parameters": {"options": {}, "fieldToSplitOut": "Title, description, source"}, "typeVersion": 1}, {"id": "0f029e8e-0a82-49be-ae77-40bbb02088ab", "name": "Save to Airtable", "type": "n8n-nodes-base.airtable", "position": [320, -520], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appgNpFtbtaGHM4g0", "cachedResultUrl": "https://airtable.com/appgNpFtbtaGHM4g0", "cachedResultName": "Content Hub"}, "table": {"__rl": true, "mode": "list", "value": "tblwBVudDpOMkUGKL", "cachedResultUrl": "https://airtable.com/appgNpFtbtaGHM4g0/tblwBVudDpOMkUGKL", "cachedResultName": "Ideas"}, "columns": {"value": {"Type": "Linkedin", "Title": "={{ $json.Title }}", "Source": "={{ $json.source }}", "Status": false, "Description": "={{ $json.description }}"}, "schema": [{"id": "Title", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Description", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Main Idea", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Main Idea", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Takeaways", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Takeaways", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Source", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Source", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Type", "type": "options", "display": true, "options": [{"name": "Youtube Video", "value": "Youtube Video"}, {"name": "Web Article", "value": "Web Article"}, {"name": "Own Notes", "value": "Own Notes"}, {"name": "E-Book", "value": "E-Book"}, {"name": "Twitter", "value": "Twitter"}, {"name": "Linkedin", "value": "Linkedin"}], "removed": false, "readOnly": false, "required": false, "displayName": "Type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Draft", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Draft", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachment - Video", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Attachment - Video", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachment - Image", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Attachment - Image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Last Modified", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Last Modified", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "g540vJVYsNT8ZS11", "name": "Airtable Personal Access Token account"}}, "typeVersion": 2.1}, {"id": "afed2086-8fc3-4a94-933f-************", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-780, -1000], "parameters": {"width": 460, "height": 180, "content": "## 📝 Description\nAutomatically turn your insightful LinkedIn post reactions into structured content ideas saved in Airtable. This workflow fetches your recent *\"insightful\"* likes, filters for posts from the last 7 days, extracts relevant content, and logs it into Airtable for future content inspiration."}, "typeVersion": 1}, {"id": "ecea99a8-dee2-40a9-aa4d-3616eecb6d73", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-780, -800], "parameters": {"width": 460, "height": 180, "content": "## ⚙️ What It Does\n- **Fetches** recent liked posts from LinkedIn using RapidAPI.\n- **Filters** only *insightful* reactions from the past 7 days.\n- **Structures** each post into a title, description, and source URL.\n- **Stores** the content in a custom Airtable base."}, "typeVersion": 1}, {"id": "9279261b-acfc-4b35-ad24-8a058bf07987", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-280, -1000], "parameters": {"width": 500, "height": 380, "content": "## 🧰 Setup Instructions\n1. Clone this template into your n8n instance.\n2. Open the `Fetch LinkedIn Likes` node and enter:\n   - Your LinkedIn username.\n   - Your RapidAPI key in the headers.\n3. Open the `Save to Airtable` node and:\n   - Connect your Airtable account.\n   - Link the correct base (`Content Hub`) and table (`Ideas`).\n4. Set your desired schedule in the `Trigger` node.\n5. Activate the workflow and you're done!\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "623dce6f-9c95-44e7-994e-0da1f65ab1a6", "connections": {"Schedule Trigger": {"main": [[{"node": "Fetch LinkedIn Likes", "type": "main", "index": 0}]]}, "Split Liked Posts": {"main": [[{"node": "Filter Insightful & Recent", "type": "main", "index": 0}]]}, "Format Content Idea": {"main": [[{"node": "Prepare for Airtable", "type": "main", "index": 0}]]}, "Fetch LinkedIn Likes": {"main": [[{"node": "Split Liked Posts", "type": "main", "index": 0}]]}, "Prepare for Airtable": {"main": [[{"node": "Save to Airtable", "type": "main", "index": 0}]]}, "Filter Insightful & Recent": {"main": [[{"node": "Format Content Idea", "type": "main", "index": 0}]]}}}