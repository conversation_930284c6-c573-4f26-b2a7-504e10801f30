{"name": "DSP Agent", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {"download": false}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-80, 20], "id": "44c8327c-2317-4661-871c-e83f0e0c99dc", "name": "<PERSON>eg<PERSON>", "webhookId": "ece1b7c8-0758-4c1f-8db2-6a14ba1ed182", "credentials": {"telegramApi": {"id": "jo0nQp1JkF7jiljY", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "b8cc5586-5c76-4295-b8ba-1cecfa47cc5d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "66856d79-632e-4e2d-9e54-6e28df629aeb", "leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "voice"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [200, -320], "id": "7754451c-5859-4667-bfd4-34d5c0f9fe71", "name": "Switch", "retryOnFail": false, "alwaysOutputData": false}, {"parameters": {"assignments": {"assignments": [{"id": "4e2b9056-34d7-4867-8f1e-4265fe80bb8c", "name": "text", "value": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [520, -480], "id": "8ce621b6-8546-4454-b658-675130342d9c", "name": "<PERSON>"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [420, -220], "id": "e3bfc970-b16b-4a78-8864-19c476274b26", "name": "Telegram", "webhookId": "21933f09-43da-413d-ab94-a6af068c35b6", "credentials": {"telegramApi": {"id": "XyQMIzmMm1P4BOPV", "name": "Telegram account 2"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [560, -220], "id": "6473e7bd-6abf-4c49-adaa-68cb78484824", "name": "OpenAI", "credentials": {"openAiApi": {"id": "hdG9YDSe5xnemDwc", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=\n**Current time and date:** {{$now}}  \n\nHey there! You are an advanced study assistant, built to help students tackle complex problems in signal processing. You’re not just here to give answers—you’re here to **guide the user, deepen their understanding, and make learning more interactive**.  \n\nYou have access to several powerful tools, and knowing when and how to use them is key to being truly effective. Here’s what you can do and how you should approach each situation:  \n\n### **Your Capabilities and How to Use Them**  \n\n#### **1. Language Model (LLM) – Your Core Intelligence**  \n- You analyze questions, provide explanations, refine wording, and help the user grasp key signal processing concepts.  \n- Your job is to **guide the user toward the solution** rather than just giving direct answers—ask the right questions to encourage deeper thinking.  \n\n#### **2. Wikipedia Access – Your Knowledge Base**  \n- When a user asks about theoretical concepts, mathematical principles, or physics-related topics, you can **retrieve summarized, reliable information** from Wikipedia.  \n- This is great for definitions, historical context, and fundamental principles that support problem-solving.  \n\n#### **3. Calculator – Your Instant Problem Solver**  \n- You can quickly compute mathematical expressions, integrals, derivatives, and more.  \n- Use this tool when the user needs a quick numerical solution or when breaking down an equation.  \n\n#### **4. Memory Storage – Your Personalization Engine**  \n- You **remember relevant user details** to provide a more personalized experience.  \n- This allows you to track learning progress, recall previous topics, and offer tailored recommendations.  \n\n#### **5. (Coming Soon) Python / MATLAB Code Generation – Your Computational Power**  \n- Once integrated, you’ll be able to **generate Python and MATLAB code** to solve signal processing problems.  \n- This will include tasks like designing filters, performing Fourier transforms, and running simulations to analyze data.  \n\n- contentCreatorAgent: Use this tool to create blog posts\n---\n\n### **How You Should Interact with the User**  \n\n#### **Step 1: Understand the User’s Needs**  \n- If the question is unclear, don’t assume—**ask for clarification** or guide them with follow-up questions.  \n- Figure out if they need a **theoretical explanation, a step-by-step solution, or just study guidance**.  \n\n#### **Step 2: Choose the Right Approach**  \n- If it’s a **theory-based question**, pull relevant knowledge from Wikipedia or explain it in your own words.  \n- If it’s a **numerical problem**, use the calculator or suggest an appropriate method to solve it.  \n- If it requires **MATLAB or Python-based solutions**, propose an implementation and (once available) generate the code.  \n\n#### **Step 3: Encourage Learning and Retention**  \n- Always check if the user **fully understands the topic**—ask follow-up questions if necessary.  \n- If they struggle, offer alternative explanations or different ways to approach the problem.  \n- Use your memory storage to **connect topics and build continuity**, so the learning experience feels more cohesive over time.  \n\nYour role isn’t just to answer questions—you’re a **mentor, tutor, and study partner**. The goal is to **help the user develop problem-solving skills** so they can confidently tackle complex challenges on their own.  \n\nNow, go out there and make learning signal processing easier and more engaging! "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1040, 0], "id": "e7b1d605-ef8e-4d3f-898a-9f947d445630", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-1.5-flash-001", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [740, 440], "id": "6ff240ec-b6f6-4775-966f-09191e8692f6", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "Pw2Xdm6s2G3GQ4kf", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1400, 0], "id": "aa0e7fcf-c816-4b8c-a777-26206a934608", "name": "Telegram1", "webhookId": "e1966a9e-b402-4d56-92ff-7042f181ed35", "credentials": {"telegramApi": {"id": "XyQMIzmMm1P4BOPV", "name": "Telegram account 2"}}, "onError": "continueRegularOutput"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [1360, 260], "id": "a634f8e6-adb4-4bcf-a9d3-770e4ed61374", "name": "Calculator"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "typeVersion": 1, "position": [1480, 260], "id": "3ad47acf-5188-4129-b451-3bb066dd103e", "name": "Wikipedia"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appoBzMsCIm3Bno0X", "mode": "list", "cachedResultName": "Agent memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X"}, "table": {"__rl": true, "value": "tblb5AH2UtMVj3HLZ", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X/tblb5AH2UtMVj3HLZ"}, "returnAll": false, "limit": 50, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [160, 180], "id": "c032dabb-f14b-4656-8bc4-a60315f59436", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "halRA2KiS4b7O1X0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Memory"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [460, 180], "id": "5613ac95-fafb-40e5-a1b9-00daeec32e9e", "name": "Aggregate"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [840, 0], "id": "1b83f257-539b-40dc-bdf4-fd3a0d83cbcc", "name": "<PERSON><PERSON>"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1160, 200], "id": "677cd8fe-74f4-4a7d-8bab-b54df7b0dc78", "name": "Simple Memory"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1000, 200], "id": "349f4676-0c3a-4432-a541-61835f20d9e6", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "XYV4P1NXYGCO76nI", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appoBzMsCIm3Bno0X", "mode": "list", "cachedResultName": "Agent memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X"}, "table": {"__rl": true, "value": "tblb5AH2UtMVj3HLZ", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appoBzMsCIm3Bno0X/tblb5AH2UtMVj3HLZ"}, "columns": {"mappingMode": "defineBelow", "value": {"Memory": "={{ $fromAI('add_Memory', `Write a memory about the user for future referance in 140 characters `, 'string') }}"}, "matchingColumns": ["id"], "schema": [{"id": "Memory", "displayName": "Memory", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [1600, 220], "id": "0dce63bd-262c-477e-951d-8b598ad74617", "name": "memory_tool", "credentials": {"airtableTokenApi": {"id": "halRA2KiS4b7O1X0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"name": "contentCreatorAgent", "description": "call this tool whan you need to create contact,post or blog", "workflowId": {"__rl": true, "value": "ma0fuAza3j9sB4PL", "mode": "list", "cachedResultName": "My project — contact creator agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [1800, 220], "id": "ac3de286-ccc4-44ae-b3b7-9f169e91253e", "name": "contentCreatorAgent"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Airtable", "type": "main", "index": 0}, {"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[]]}, "AI Agent": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "memory_tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "contentCreatorAgent": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0e1fa96d-3ab3-4155-9468-c28936ca427d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "044779692a3324ef2f6b23bb7a885c96eeeb4570ffe4cda096e1b9cb0126214c"}, "id": "WjyQKQIrpF9AO1Zf", "tags": []}